#!/usr/bin/env node

/**
 * 生成测试用的背景音频文件
 * 这个脚本会创建简单的测试音频文件，用于验证背景音频功能
 * 
 * 使用方法:
 * node scripts/generate-test-audio.js
 */

const fs = require('fs');
const path = require('path');

// 音频文件配置
const audioConfigs = {
  'rain': {
    name: '雨声',
    duration: 10, // 秒
    description: '模拟雨声的白噪音'
  },
  'forest': {
    name: '森林',
    duration: 10,
    description: '模拟森林环境音'
  },
  'ocean': {
    name: '海浪',
    duration: 10,
    description: '模拟海浪声'
  },
  'cafe': {
    name: '咖啡厅',
    duration: 10,
    description: '模拟咖啡厅环境音'
  },
  'fireplace': {
    name: '壁炉',
    duration: 10,
    description: '模拟壁炉燃烧声'
  },
  'thunderstorm': {
    name: '雷雨',
    duration: 10,
    description: '模拟雷雨声'
  },
  'birds': {
    name: '鸟鸣',
    duration: 10,
    description: '模拟鸟鸣声'
  },
  'wind': {
    name: '微风',
    duration: 10,
    description: '模拟微风声'
  }
};

// 目标目录
const targetDir = path.join(__dirname, '../public/sounds/background');

// 创建目录
function ensureDirectoryExists(dirPath) {
  if (!fs.existsSync(dirPath)) {
    fs.mkdirSync(dirPath, { recursive: true });
    console.log(`创建目录: ${dirPath}`);
  }
}

// 生成简单的音频数据 (WAV格式)
function generateSimpleWav(duration, frequency = 440, sampleRate = 44100) {
  const numSamples = duration * sampleRate;
  const buffer = Buffer.alloc(44 + numSamples * 2); // WAV header + 16-bit samples
  
  // WAV header
  buffer.write('RIFF', 0);
  buffer.writeUInt32LE(36 + numSamples * 2, 4);
  buffer.write('WAVE', 8);
  buffer.write('fmt ', 12);
  buffer.writeUInt32LE(16, 16); // PCM format
  buffer.writeUInt16LE(1, 20);  // Audio format (PCM)
  buffer.writeUInt16LE(1, 22);  // Number of channels (mono)
  buffer.writeUInt32LE(sampleRate, 24); // Sample rate
  buffer.writeUInt32LE(sampleRate * 2, 28); // Byte rate
  buffer.writeUInt16LE(2, 32);  // Block align
  buffer.writeUInt16LE(16, 34); // Bits per sample
  buffer.write('data', 36);
  buffer.writeUInt32LE(numSamples * 2, 40);
  
  // Generate audio samples (simple noise or tone)
  for (let i = 0; i < numSamples; i++) {
    let sample;
    
    if (frequency === 0) {
      // White noise
      sample = (Math.random() * 2 - 1) * 0.1; // Low volume noise
    } else {
      // Simple sine wave
      sample = Math.sin(2 * Math.PI * frequency * i / sampleRate) * 0.1;
    }
    
    // Convert to 16-bit integer
    const intSample = Math.round(sample * 32767);
    buffer.writeInt16LE(intSample, 44 + i * 2);
  }
  
  return buffer;
}

// 生成不同类型的音频
function generateAudioForType(type, config) {
  let frequency = 0; // Default to noise
  
  switch (type) {
    case 'rain':
    case 'ocean':
    case 'wind':
      frequency = 0; // White noise
      break;
    case 'forest':
    case 'birds':
      frequency = 800; // Higher frequency for nature sounds
      break;
    case 'cafe':
      frequency = 0; // Noise
      break;
    case 'fireplace':
      frequency = 100; // Low frequency rumble
      break;
    case 'thunderstorm':
      frequency = 0; // Noise with low frequency
      break;
    default:
      frequency = 0;
  }
  
  return generateSimpleWav(config.duration, frequency);
}

// 创建说明文件
function createReadme() {
  const readmeContent = `# 背景音频测试文件

这些是自动生成的测试音频文件，用于验证 FocusOS 背景音频功能。

## 文件说明

${Object.entries(audioConfigs).map(([type, config]) => 
  `- **${type}.wav**: ${config.name} - ${config.description}`
).join('\n')}

## 注意事项

1. 这些是简单的测试音频，实际使用时建议替换为高质量的音频文件
2. 支持的格式：MP3, WAV, OGG
3. 建议音频时长：2-10分钟
4. 建议音频质量：128kbps 或更高

## 替换音频文件

要使用自己的音频文件，请：

1. 将音频文件放在此目录下
2. 确保文件名与配置中的名称匹配
3. 支持的格式：.mp3, .wav, .ogg
4. 系统会优先使用 .mp3 格式

## 音频来源建议

- Freesound.org - 免费音效库
- Zapsplat.com - 注册后免费下载
- BBC Sound Effects - BBC 免费音效
- YouTube Audio Library - YouTube 创作者音频库

生成时间: ${new Date().toISOString()}
`;

  fs.writeFileSync(path.join(targetDir, 'README.md'), readmeContent);
  console.log('创建说明文件: README.md');
}

// 主函数
function main() {
  console.log('开始生成背景音频测试文件...\n');
  
  // 确保目录存在
  ensureDirectoryExists(targetDir);
  
  // 生成音频文件
  Object.entries(audioConfigs).forEach(([type, config]) => {
    const filename = `${type}.wav`;
    const filepath = path.join(targetDir, filename);
    
    // 检查文件是否已存在
    if (fs.existsSync(filepath)) {
      console.log(`跳过已存在的文件: ${filename}`);
      return;
    }
    
    console.log(`生成音频文件: ${filename} (${config.name})`);
    
    try {
      const audioData = generateAudioForType(type, config);
      fs.writeFileSync(filepath, audioData);
      console.log(`✓ 成功生成: ${filename}`);
    } catch (error) {
      console.error(`✗ 生成失败: ${filename}`, error.message);
    }
  });
  
  // 创建说明文件
  createReadme();
  
  console.log('\n音频文件生成完成！');
  console.log(`文件位置: ${targetDir}`);
  console.log('\n注意: 这些是简单的测试音频，建议替换为高质量的音频文件。');
  console.log('详细说明请查看生成的 README.md 文件。');
}

// 运行脚本
if (require.main === module) {
  main();
}

module.exports = {
  generateSimpleWav,
  generateAudioForType,
  audioConfigs
};
