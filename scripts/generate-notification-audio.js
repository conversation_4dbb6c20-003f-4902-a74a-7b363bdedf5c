#!/usr/bin/env node

/**
 * 生成FocusOS通知音频文件
 * 这个脚本会创建简单的通知音频文件，用于各种提示音
 *
 * 使用方法:
 * node scripts/generate-notification-audio.js
 */

import fs from 'fs';
import path from 'path';
import { fileURLToPath } from 'url';

const __filename = fileURLToPath(import.meta.url);
const __dirname = path.dirname(__filename);

// 通知音频配置
const notificationConfigs = {
  'task-complete': {
    name: '任务完成',
    duration: 1.5,
    frequencies: [523, 659, 784], // C5, E5, G5 - 愉悦的和弦
    description: '任务完成时播放，给用户成就感'
  },
  'pomodoro-complete': {
    name: '番茄钟完成',
    duration: 2.0,
    frequencies: [440, 440, 440], // A4 三声短音
    description: '番茄钟工作时段结束时播放'
  },
  'break-complete': {
    name: '休息结束',
    duration: 1.8,
    frequencies: [349, 349], // F4 两声长音
    description: '休息时间结束，提醒开始工作'
  },
  'goal-achieved': {
    name: '目标达成',
    duration: 2.5,
    frequencies: [523, 587, 659, 698, 784], // C5-G5 上升音阶
    description: '重要目标达成时播放'
  },
  'system-alert': {
    name: '系统提示',
    duration: 0.8,
    frequencies: [880], // A5 单音调
    description: '系统重要通知时播放'
  },
  'gentle-reminder': {
    name: '轻柔提醒',
    duration: 1.2,
    frequencies: [392], // G4 温和音调
    description: '非紧急提醒时使用'
  }
};

// 目标目录
const targetDir = path.join(__dirname, '../packages/renderer/public/sounds');

// 创建目录
function ensureDirectoryExists(dirPath) {
  if (!fs.existsSync(dirPath)) {
    fs.mkdirSync(dirPath, { recursive: true });
    console.log(`创建目录: ${dirPath}`);
  }
}

// 生成WAV格式音频数据
function generateNotificationWav(config) {
  const sampleRate = 44100;
  const { duration, frequencies } = config;
  const numSamples = Math.floor(duration * sampleRate);
  const buffer = Buffer.alloc(44 + numSamples * 2);
  
  // WAV header
  buffer.write('RIFF', 0);
  buffer.writeUInt32LE(36 + numSamples * 2, 4);
  buffer.write('WAVE', 8);
  buffer.write('fmt ', 12);
  buffer.writeUInt32LE(16, 16);
  buffer.writeUInt16LE(1, 20);
  buffer.writeUInt16LE(1, 22);
  buffer.writeUInt32LE(sampleRate, 24);
  buffer.writeUInt32LE(sampleRate * 2, 28);
  buffer.writeUInt16LE(2, 32);
  buffer.writeUInt16LE(16, 34);
  buffer.write('data', 36);
  buffer.writeUInt32LE(numSamples * 2, 40);
  
  // 生成音频样本
  for (let i = 0; i < numSamples; i++) {
    const time = i / sampleRate;
    let sample = 0;
    
    // 根据时间选择频率
    const noteIndex = Math.floor(time / (duration / frequencies.length));
    const frequency = frequencies[Math.min(noteIndex, frequencies.length - 1)];
    
    // 生成正弦波
    sample = Math.sin(2 * Math.PI * frequency * time);
    
    // 添加包络（淡入淡出）
    const fadeTime = 0.1; // 100ms淡入淡出
    let envelope = 1;
    
    if (time < fadeTime) {
      envelope = time / fadeTime;
    } else if (time > duration - fadeTime) {
      envelope = (duration - time) / fadeTime;
    }
    
    sample *= envelope * 0.3; // 降低音量
    
    // 转换为16位整数
    const intSample = Math.round(sample * 32767);
    buffer.writeInt16LE(intSample, 44 + i * 2);
  }
  
  return buffer;
}

// 转换WAV到MP3 (简化版，实际上只是重命名)
function convertToMp3(wavBuffer, outputPath) {
  // 注意：这里实际上还是WAV格式，只是改了扩展名
  // 在实际项目中，你可能需要使用ffmpeg或其他工具进行真正的转换
  fs.writeFileSync(outputPath, wavBuffer);
}

// 主函数
function main() {
  console.log('开始生成FocusOS通知音频文件...\n');
  
  // 确保目录存在
  ensureDirectoryExists(targetDir);
  
  // 生成音频文件
  Object.entries(notificationConfigs).forEach(([type, config]) => {
    const filename = `${type}.mp3`;
    const filepath = path.join(targetDir, filename);
    
    console.log(`生成音频文件: ${filename} (${config.name})`);
    
    try {
      const audioData = generateNotificationWav(config);
      convertToMp3(audioData, filepath);
      console.log(`✓ 成功生成: ${filename}`);
    } catch (error) {
      console.error(`✗ 生成失败: ${filename}`, error.message);
    }
  });
  
  console.log('\n通知音频文件生成完成！');
  console.log(`文件位置: ${targetDir}`);
  console.log('\n注意: 这些是简单的测试音频，建议替换为高质量的音频文件。');
}

// 运行脚本
if (import.meta.url === `file://${process.argv[1]}`) {
  main();
}

export {
  generateNotificationWav,
  notificationConfigs
};
