name: Compile and test

on:
  workflow_call:
    inputs:
      renderer-template:
        description: Define what vite template should be used to create renderer in case if renderer package doesn't exist
        required: false
        type: string
        default: ''
      app-version:
        required: true
        type: string
      distribution-channel:
        required: true
        type: string

defaults:
  run:
    shell: 'bash'

permissions:
  contents: write
  id-token: write
  attestations: write

env:
  NODE_NO_WARNINGS: 1
  PLAYWRIGHT_SKIP_BROWSER_DOWNLOAD: 1
  npm_config_audit: false
  npm_config_fund: false

jobs:
  typeckeck:
    runs-on: ubuntu-latest
    steps:
      - uses: actions/checkout@v4
      - uses: ./.github/actions/init-template-with-renderer
        name: Setup boilerplate
        with:
          renderer-template: ${{inputs.renderer-template}}
      - run: npm run typecheck --if-present

  compile:
    strategy:
      fail-fast: false
      matrix:
        os:
          - windows-latest
          - ubuntu-latest
          - macos-latest
    runs-on: ${{ matrix.os }}
    steps:
      - uses: actions/checkout@v4
      - uses: ./.github/actions/init-template-with-renderer
        name: Setup boilerplate
        with:
          renderer-template: ${{inputs.renderer-template}}

      - run: npm version "${{inputs.app-version}}" --no-git-tag-version
      - run: npm run compile -- -p never
        env:
          VITE_DISTRIBUTION_CHANNEL: ${{inputs.distribution-channel}}

      - run: npm run test --if-present
        if: matrix.os != 'ubuntu-latest'

      - run: xvfb-run --auto-servernum --server-args="-screen 0 1280x960x24" -- npm run test --if-present
        if: matrix.os == 'ubuntu-latest'

      - uses: actions/attest-build-provenance@v2
        with:
          subject-path: "dist/root*, dist/latest*.yml"

      - name: Upload compiled app
        uses: actions/upload-artifact@v4
        with:
          name: ${{ matrix.os }}-${{inputs.distribution-channel}}
          path: dist
