name: Deploy

on:
  workflow_call:
    inputs:
      app-version:
        required: true
        type: string
      distribution-channel:
        required: true
        type: string

permissions:
  contents: write

defaults:
  run:
    shell: 'bash'

env:
  NODE_NO_WARNINGS: 1
  PLAYWRIGHT_SKIP_BROWSER_DOWNLOAD: 1
  npm_config_audit: false
  npm_config_fund: false

jobs:
  deploy:
    runs-on: ubuntu-latest
    environment:
      name: ${{inputs.distribution-channel}}
      url: https://github.com/${{github.repository}}/releases/tag/v${{inputs.app-version}}
    steps:
      - name: Download compiled app
        uses: actions/download-artifact@v4
        with:
          pattern: "*-${{inputs.distribution-channel}}"
          path: dist
          merge-multiple: true

      - run: gh release create v${{inputs.app-version}} dist/root* dist/latest*.yml --repo ${{github.repository}}
        env:
          GH_TOKEN: ${{ github.token }}
