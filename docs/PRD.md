
## 心流锚定系统 (FocusAnchor OS) - 产品需求文档 (PRD)

**版本：** 1.0
**日期：** 2025年06月22日
**文档负责人：** [STEP]

### 1. 引言与概述

**1.1 项目背景**
在信息爆炸和多任务并行成为常态的今天，用户（尤其是知识工作者、学生等）普遍面临注意力分散、难以持续专注的问题。这直接影响了工作效率、目标达成和个人成长。“心流锚定系统 (FocusAnchor OS)”旨在解决这一核心痛点。

**1.2 产品愿景**
帮助用户将宏大愿景落实为具体行动，通过结构化引导、智能辅助和持续反馈，克服分心，锚定专注，高效达成目标。

**1.3 目标用户**
*   **核心用户：** 易分心的个人用户，如学生、知识工作者、自由职业者、内容创作者、以及有注意力管理需求的个体（如轻度ADHD倾向用户）。
*   **扩展用户：** 任何希望提升个人生产力、改善时间管理和专注能力的用户。

**1.4 产品目标**
*   **短期目标 (MVP - Minimum Viable Product)：**
    *   实现核心的目标输入、分解、任务管理、番茄工作法和基础的专注辅助功能。
    *   验证核心用户对产品价值的认可度。
*   **中期目标：**
    *   完善智能提醒、专注力监控、数据反馈与个性化适应功能。
    *   提升用户粘性，形成稳定的用户群体。
*   **长期目标：**
    *   成为易分心用户首选的个人生产力操作系统。
    *   探索可能的社群或轻量协作功能。

### 2. 功能需求详述

**2.1 模块一：目标输入与解析 (Goal Intake & Interpretation Engine)**

*   **FR-GI-001: 结构化目标输入**
    *   **描述：** 用户可以通过结构化表单输入目标。
    *   **用户故事：** 作为一个用户，我希望能通过清晰的表单填写我的目标，以便系统更好地理解和管理它们。
    *   **输入字段：**
        *   `目标名称` (必填, 文本, ≤50字符)
        *   `目标描述` (必填, 富文本, SMART原则提示)
        *   `目标类型` (必填, 下拉选择: 长期, 短期, 习惯养成)
        *   `核心驱动力/“为什么” (Why Power)` (必填, 文本区域, ≥50字符引导)
        *   `关联领域/项目` (可选, 文本/标签输入, 可多选)
        *   `预计开始日期` (可选, 日期选择器)
        *   `预计截止日期` (可选, 日期选择器)
    *   **验收标准：**
        *   用户可以成功创建、编辑、删除目标。
        *   必填项未填写时有明确提示。
        *   SMART原则提示在“目标描述”区域可见。

*   **FR-GI-002: AI辅助目标解析 (可选高级功能, V1.1+)**
    *   **描述：** 用户可以通过输入自然语言描述目标，系统尝试解析并辅助填充结构化表单。
    *   **用户故事：** 作为一个用户，我希望能够直接用自然语言描述我的目标，让系统智能地帮我提取关键信息并填入表单，以节省我的时间。
    *   **功能点：**
        *   提供自由文本输入框。
        *   NLP引擎识别核心动词、名词、时间状语、数字等。
        *   将解析结果预填充到结构化表单的对应字段，用户可修改确认。
    *   **验收标准：**
        *   系统能对常见的目标描述格式进行有效解析。
        *   解析结果能被正确预填充到结构化表单。
        *   用户可以修改或拒绝AI的解析建议。

*   **FR-GI-003: 初步解析与标记**
    *   **描述：** 系统在目标创建后，自动进行初步的关键词提取和时间属性识别。
    *   **用户故事：** 作为一个用户，我希望系统能自动帮我识别目标中的关键词和时间信息，以便后续更好地进行分解和管理。
    *   **功能点：**
        *   **关键词提取：** 自动高亮或列出目标描述中的核心名词、动词、关键成果指标。
        *   **时间属性识别：** 解析描述中的时间信息，自动填充或建议截止日期。
        *   **驱动力关联：** 系统内部标记此目标与用户填写的“核心驱动力”的强关联，用于后续激励。
    *   **验收标准：**
        *   关键词能被有效提取并展示。
        *   时间信息能被识别并正确关联到截止日期建议。

**2.2 模块二：第一性原理目标分解 (First Principles Decomposition Module)**

*   **FR-FD-001: 引导式“剥洋葱”分解流程**
    *   **描述：** 系统通过一系列引导性问题，帮助用户将根目标层层分解为子目标、里程碑和最小任务单元。
    *   **用户故事：** 作为一个用户，我希望系统能引导我思考如何将一个大目标分解成可管理的小步骤，确保我不会遗漏关键环节。
    *   **功能点：**
        *   以根目标为起点。
        *   系统针对当前节点（目标/子目标）提供引导性提问（例如：“要实现[当前节点]，最核心的前提是什么？”）。
        *   用户基于提问输入分解出的子节点（子目标、里程碑、最小任务单元）。
        *   支持对每个新生成的子节点重复分解过程。
        *   明确定义“最小任务单元”为可在短时间内（如2小时内）完成的行动项。
    *   **验收标准：**
        *   用户可以流畅地按照引导进行多层级目标分解。
        *   分解出的节点类型（子目标、里程碑、任务）清晰可辨。

*   **FR-FD-002: 结构可视化**
    *   **描述：** 以树状结构图清晰展示目标分解的层级关系，支持交互操作。
    *   **用户故事：** 作为一个用户，我希望能够直观地看到我的目标是如何被分解的，并且能够方便地调整这些分解结构。
    *   **功能点：**
        *   **树状结构图：** 默认展示方式。
        *   **交互操作：** 支持拖拽调整节点层级、添加/删除节点、内联编辑节点内容。
        *   **网状结构图 (V1.2+)：** 允许定义任务间的依赖关系（前置/后置）。
    *   **验收标准：**
        *   树状图能正确反映目标层级。
        *   拖拽、增删改查操作流畅有效。

*   **FR-FD-003: 分解质量辅助**
    *   **描述：** 系统提供工具辅助用户提升分解任务的质量。
    *   **用户故事：** 作为一个用户，我希望系统能在我分解任务时提醒我遵循好的原则，确保每个任务都清晰可行。
    *   **功能点：**
        *   **SMART原则检查器：** 在编辑任务单元时，提供SMART原则的检查列表或提示。
        *   **“为什么这个任务重要？”提示：** 在创建任务时，鼓励用户思考其与上层目标的关联性。
    *   **验收标准：**
        *   SMART检查器提示清晰可见。
        *   关联性提示能在适当时候出现。

**2.3 模块三：任务管理 (Task Management Suite)**

*   **FR-TM-001: 任务属性定义**
    *   **描述：** 每个任务单元都应包含一组明确的属性。
    *   **用户故事：** 作为一个用户，我希望能为每个任务设置详细的属性，以便更好地追踪和管理它们。
    *   **属性字段：**
        *   `标题` (必填, 文本)
        *   `详细描述` (可选, 富文本)
        *   `预估完成时间 (ETC)` (可选, 数字输入, 单位：小时/番茄数)
        *   `实际花费时间 (ATC)` (系统记录/手动输入, 单位：小时/番茄数)
        *   `优先级` (可选, 下拉选择: 高, 中, 低; 或艾森豪威尔矩阵标签)
        *   `截止日期与时间` (可选, 日期时间选择器)
        *   `所属目标/里程碑` (自动继承/可手动关联)
        *   `标签 (Tags)` (可选, 文本/标签输入, 可多选)
        *   `状态` (必填, 下拉选择: 待办, 进行中, 已完成, 暂停, 已取消)
        *   `完成百分比` (可选, 0-100%滑块/输入, 针对长任务)
        *   `重复设置` (可选, 规则：每日, 每周X, 每月X日/第X个周X, 自定义间隔)
    *   **验收标准：**
        *   用户可以为任务设置和修改所有定义的属性。
        *   状态变更逻辑清晰（如“进行中”的任务不能直接删除，需先“取消”或“完成”）。

*   **FR-TM-002: 任务组织与筛选**
    *   **描述：** 提供多种视图和筛选方式来组织和查找任务。
    *   **用户故事：** 作为一个用户，我希望能根据不同的维度（如截止日期、优先级、状态）查看和筛选我的任务，以便快速找到我需要关注的内容。
    *   **功能点：**
        *   **视图切换：**
            *   列表视图 (默认)
            *   看板视图 (按状态或其他自定义列)
            *   日历视图 (按截止日期/计划执行日期)
            *   目标层级视图 (在目标分解树中直接管理任务)
        *   **筛选器：** 支持按优先级、截止日期范围、标签、状态、所属目标等单一或组合条件筛选。
        *   **排序：** 支持按创建时间、截止日期、优先级等升序/降序排序。
        *   **智能分组：** 系统预设分组，如“今日待办”、“本周即将截止”、“已逾期”。
    *   **验收标准：**
        *   各视图能正确展示任务信息。
        *   筛选和排序功能准确有效。
        *   智能分组逻辑符合预期。

*   **FR-TM-003: 进度追踪**
    *   **描述：** 用户可以追踪任务和项目的进度。
    *   **用户故事：** 作为一个用户，我希望能够方便地更新任务进度，并直观地看到我的整体进展。
    *   **功能点：**
        *   手动标记任务状态。
        *   手动输入完成百分比。
        *   **甘特图 (V1.2+)：** 可视化项目进度和任务依赖。
        *   **燃尽图 (V1.2+)：** 显示项目或里程碑的剩余工作量。
    *   **验收标准：**
        *   任务状态和完成百分比能被正确记录和展示。

**2.4 模块四：定时提醒系统 (Intelligent Reminder System)**

*   **FR-IR-001: 任务时间提醒**
    *   **描述：** 根据任务设定的时间点进行提醒。
    *   **用户故事：** 作为一个用户，我希望系统能在任务开始前和截止前提醒我，避免我忘记。
    *   **功能点：**
        *   **开始提醒：** 若任务设置了计划开始时间，可配置提前X分钟/小时提醒。
        *   **截止提醒：** 在截止日期前，可配置分级提醒（如提前24小时、3小时、1小时）。
        *   **自定义提醒：** 用户可为单个任务设置多个自定义提醒时间点和提醒内容。
        *   **提醒方式：** 系统通知 (桌面/移动端推送)，应用内弹窗。
    *   **验收标准：**
        *   提醒在预设时间点准确触发。
        *   用户可以自定义提醒偏好。

*   **FR-IR-002: 间隔提醒 (Focus Pacer)**
    *   **描述：** 在用户长时间专注一个任务时，定时提醒用户检查进度或休息。
    *   **用户故事：** 作为一个用户，我希望在我长时间工作后，系统能提醒我休息一下或者回顾下当前任务进度，防止我过度沉浸或偏离方向。
    *   **功能点：**
        *   用户可设定专注时段长度（如25, 45, 60分钟）。
        *   到点后，系统发出柔和提醒，内容可包括：
            *   “已专注[X]分钟，检查一下当前任务[任务名]的进度，是否需要调整？”
            *   “建议进行短暂休息（如眼部运动、喝水）。”
        *   提醒频率、内容模板可用户自定义。
    *   **验收标准：**
        *   间隔提醒按用户设定触发。
        *   提醒内容可配置。

*   **FR-IR-003: 不活跃提醒 (Re-engagement Nudge)**
    *   **描述：** 当用户在专注时段内长时间无操作或切换应用时，系统进行提醒。
    *   **用户故事：** 作为一个用户，如果我在开始一个任务后跑神或切换到其他应用太久，我希望系统能温和地提醒我回来继续工作。
    *   **功能点：**
        *   **检测机制：**
            *   系统内无键盘鼠标操作（阈值可设，如3-5分钟）。
            *   应用焦点切换到非白名单应用（需“环境监测”配合）。
        *   **提醒方式：**
            *   柔性提醒：“你似乎已离开当前任务[任务名]一段时间了，需要帮助返回吗？” (应用内/系统通知)
            *   升级提醒 (可选)：若多次柔性提醒无效，可发出更醒目的声音或视觉提示。
        *   **智能判断：**
            *   允许用户临时“暂停监测此任务”（如阅读长文档）。
            *   区分正常的应用切换（如查资料）和纯粹分心。
    *   **验收标准：**
        *   不活跃状态能被正确检测。
        *   提醒能按设定逻辑触发。
        *   用户可暂停监测。

**2.5 模块五：番茄工作法集成 (Pomodoro Flow)**

*   **FR-PF-001: 自定义番茄钟**
    *   **描述：** 提供可自定义的番茄钟功能。
    *   **用户故事：** 作为一个用户，我希望能使用番茄工作法来管理我的工作和休息节奏，并且可以根据我的习惯自定义时长。
    *   **功能点：**
        *   可自定义工作时段 (Pomodoro) 时长 (默认25分钟)。
        *   可自定义短休息时段时长 (默认5分钟)。
        *   可自定义长休息时段时长 (默认15分钟)。
        *   可自定义几个番茄钟后进行一次长休息 (默认4个)。
        *   番茄钟计时器界面清晰，显示当前状态（工作/休息）和剩余时间。
    *   **验收标准：**
        *   用户可以成功自定义所有番茄钟参数。
        *   计时器准确运行。

*   **FR-PF-002: 自动提醒与流转**
    *   **描述：** 番茄钟在各时段结束时自动提醒并引导流转。
    *   **用户故事：** 作为一个用户，我希望番茄钟结束后能自动提醒我，并帮我无缝切换到休息或下一个工作时段。
    *   **功能点：**
        *   工作时段结束：声音和视觉提醒，提示进入休息。
        *   休息时段结束：声音和视觉提醒，提示返回工作，可选项：自动开始下一个番茄钟。
        *   任务关联：每个番茄钟自动与当前选定的“进行中”任务关联，记录番茄数在该任务的ATC上。
    *   **验收标准：**
        *   提醒和流转按预期工作。
        *   番茄数能正确关联到任务。

*   **FR-PF-003: 严格番茄钟模式 (Optional Strict Mode, V1.1+)**
    *   **描述：** 提供更严格的番茄钟执行模式。
    *   **用户故事：** 作为一个用户，当我需要高度专注时，我希望能开启一个严格模式，强制我遵守番茄钟的节律，减少打扰。
    *   **功能点：**
        *   工作时段内：若“环境监测”开启，则更严格地限制对非白名单应用的访问。
        *   休息时段：强制休息，不允许提前结束休息返回工作（除非手动强制覆盖并记录原因）。
    *   **验收标准：**
        *   严格模式下，应用访问限制和休息强制功能生效。

**2.6 模块六：专注力辅助与监控措施 (Focus Enhancement & Monitoring Suite)**

*   **FR-FM-001: 环境监测 (Digital Sentinel - 桌面应用/浏览器插件)**
    *   **描述：** 监测用户在专注时段的应用/网站使用情况，并进行干预。
    *   **用户故事：** 作为一个用户，我希望系统能在我专注工作时，帮我阻止或警示我访问那些容易让我分心的应用和网站。
    *   **实现方式：** 需要操作系统级权限的桌面应用或浏览器插件。
    *   **功能点：**
        *   **黑白名单管理：** 用户可自定义“分心应用/网站列表”（黑名单）和“工作相关应用/网站列表”（白名单）。
        *   **监测与警告：**
            *   当用户在“专注时段”（如番茄钟工作时段、深潜模式）尝试访问黑名单内容时，触发即时弹窗警告：“警告：你正在访问[应用/网站名]，这可能会影响你对任务[任务名]的专注度。选项：[返回工作] / [允许本次访问并记录]”。
            *   可选：短暂延迟后自动切回FocusAnchor OS或白名单应用。
    *   **验收标准：**
        *   黑白名单功能正常。
        *   在专注时段访问黑名单内容时，警告系统正常触发。
        *   用户操作（返回/允许）能被正确响应。
        *   （需明确告知用户权限需求和数据处理方式）

*   **FR-FM-002: 定时专注检查 (Mindful Check-in)**
    *   **描述：** 在专注时段内，非侵入式地询问用户当前专注状态。
    *   **用户故事：** 作为一个用户，我希望系统能时不时地问我一下是否还在专注，帮助我提高自我觉察。
    *   **功能点：**
        *   **触发机制：** 用户可配置频率（如每15/30/45分钟）或随机触发。
        *   **交互方式：** 简洁非侵入式弹窗：“你是否仍然专注于任务：[当前任务名称]？”
        *   **用户反馈选项：**
            *   [是，继续专注] (弹窗消失)
            *   [否，我分心了] (引导至“分心记录”模块)
            *   [否，但我需要切换处理其他事务] (选项：暂停当前任务 / 记录临时切换原因后继续)
    *   **验收标准：**
        *   专注检查按设定触发。
        *   用户反馈选项清晰，并能正确引导至后续操作。

*   **FR-FM-003: 时间与进度洞察**
    *   **描述：** 自动追踪时间花费，并提供与预估的对比分析。
    *   **用户故事：** 作为一个用户，我希望系统能帮我记录在每个任务上实际花了多少时间，并对比我的预估，让我了解自己的时间感。
    *   **功能点：**
        *   **自动时间追踪：** 当任务标记为“进行中”且番茄钟或专注计时器激活时，自动累积ATC。
        *   **预估 vs. 实际对比：** 在任务详情页、报告中展示ETC与ATC。
        *   **分析报告 (V1.1+)：**
            *   用户在不同类型任务上的时间预估准确性分析。
            *   每日/周/月专注时长统计图表。
            *   最耗时任务、最高效时段（基于任务完成度和专注度数据）分析。
    *   **验收标准：**
        *   ATC记录准确。
        *   ETC vs ATC对比清晰展示。

*   **FR-FM-004: 分心记录 (Distraction Log)**
    *   **描述：** 用户可以记录分心事件，系统也可辅助记录。
    *   **用户故事：** 作为一个用户，我希望能记录下那些让我分心的事情，以便分析和改进。
    *   **功能点：**
        *   **手动记录入口：** 明显的“记录分心”按钮。
        *   **记录字段：** 分心源（预设选项+自定义输入）、预估分心时长、分心时情绪/状态（可选标签）。
        *   **半自动记录：**
            *   “环境监测”检测到切换至黑名单应用时，可提示用户是否记录为一次分心。
            *   “定时专注检查”用户反馈“否，我分心了”时，自动引导记录。
        *   **分心分析报告 (V1.1+)：** 统计常见分心源、分心时段、分心频率图表。
    *   **验收标准：**
        *   用户可成功手动记录分心事件。
        *   半自动记录流程顺畅。

*   **FR-FM-005: 强制性聚焦模式 (Deep Focus Mode - "Focus Shield", V1.1+)**
    *   **描述：** 用户可手动开启的高度专注模式，严格限制干扰。
    *   **用户故事：** 作为一个用户，当我要处理非常重要或困难的任务时，我希望能开启一个“超级专注”模式，屏蔽所有可能的干扰。
    *   **功能点：**
        *   **激活：** 用户针对特定任务手动开启。
        *   **应用/网站拦截：** 严格执行“环境监测”的黑名单策略，不允许临时例外。
        *   **通知静默：** 屏蔽所有系统通知和其他应用的通知（FocusAnchor OS自身的重要提醒除外，或用户设定的极少数白名单应用通知）。
        *   **界面极简化：** FocusAnchor OS界面切换到仅显示当前任务、倒计时和必要的控制按钮。
        *   **退出机制：** 可选“冷静期”退出，如需提前退出，需等待短时间（如60秒倒计时）或完成一个简单的小任务（如重复输入指定短语），以减少冲动退出。
    *   **验收标准：**
        *   开启深潜模式后，应用拦截、通知静默、界面简化功能生效。
        *   退出机制按设定工作。

*   **FR-FM-006: 目标可视化提醒 (Goal Beacon)**
    *   **描述：** 在特定时机提醒用户当前任务关联的更高层级目标和核心驱动力。
    *   **用户故事：** 作为一个用户，我希望系统能时不时地提醒我为什么要做当前的任务，以及它如何服务于我的大目标，以保持动力。
    *   **触发时机（可配置）：**
        *   番茄钟工作/休息转换间隙。
        *   “间隔提醒 (Focus Pacer)”触发时。
        *   每日首次打开系统时。
        *   用户自定义的固定时间间隔。
    *   **展示内容：**
        *   当前任务所属的上一层子目标/根目标名称。
        *   用户为此根目标填写的“核心驱动力/为什么”。
        *   用户可为每个根目标自定义的激励性图片或名言。
    *   **展示方式：** 短暂、非侵入式的屏幕提示条、桌面小部件内容更新、或系统通知。
    *   **验收标准：**
        *   目标灯塔在预设时机正确触发。
        *   展示内容准确，符合用户设定。

**2.7 模块七：用户反馈与适应 (Adaptive Learning & Personalization) (V1.2+)**

*   **FR-AP-001: 数据收集与用户告知**
    *   **描述：** 系统在用户同意的前提下，收集与生产力相关的使用数据。
    *   **用户故事：** 作为一个用户，我希望系统能通过学习我的使用习惯来提供更智能的帮助，但我需要明确知道哪些数据被收集以及如何使用。
    *   **收集数据点（示例）：** 工作时长、任务完成率、ETC/ATC偏差、分心记录（匿名化处理）、番茄钟参数及完成情况、对各类提醒的响应（忽略/采纳）。
    *   **隐私策略：**
        *   首次使用需明确告知数据收集范围和目的，并获得用户同意。
        *   提供数据匿名化选项。
        *   提供本地数据存储与同步选项（若涉及云同步）。
        *   用户可随时查看、导出或删除个人数据。
    *   **验收标准：**
        *   数据收集获得用户明确授权。
        *   隐私策略清晰易懂，用户可控。

*   **FR-AP-002: 智能调整与建议**
    *   **描述：** 系统基于收集的数据，向用户提供个性化的调整建议。
    *   **用户故事：** 作为一个用户，我希望系统能根据我的实际表现，给我一些关于如何更好地使用工具、调整工作习惯的建议。
    *   **功能点（示例）：**
        *   **提醒频率优化建议：** 若用户频繁忽略某类提醒，建议调整频率或关闭。
        *   **专注模式建议：** 根据分心日志，建议将高频分心源加入黑名单或在特定任务时启用深潜模式。
        *   **任务分解建议：** 若某任务多次延期且ATC远超ETC，建议进一步分解。
        *   **番茄钟参数建议：** 基于用户在不同番茄钟设置下的专注度和完成效率，推荐最优参数组合。
        *   **工作习惯洞察报告：** “你似乎在[特定时间段]效率最高。” “本周你最大的分心源是[X]，建议尝试[策略]。”
    *   **验收标准：**
        *   建议基于用户数据生成，具有相关性。
        *   用户可以采纳或忽略建议。

*   **FR-AP-003: 用户偏好设置**
    *   **描述：** 提供全面的设置选项，允许用户高度自定义系统行为。
    *   **用户故事：** 作为一个用户，我希望能完全控制系统的各项功能和提醒，即使系统有智能建议，我也要有最终决定权。
    *   **功能点：**
        *   所有提醒的开关、频率、内容模板自定义。
        *   所有监控措施的开关、参数（如不活跃阈值、专注检查频率）自定义。
        *   番茄钟默认参数自定义。
        *   数据收集与个性化建议的开关。
    *   **验收标准：**
        *   用户可以找到并成功修改所有可配置项。
        *   用户设置优先于系统智能建议。

### 3. 非功能性需求 (Non-Functional Requirements - NFRs)

*   **NFR-001: 性能**
    *   应用启动时间：冷启动 ≤ 5秒，热启动 ≤ 2秒。
    *   UI响应速度：主要交互操作反馈时间 ≤ 200毫秒。
    *   资源占用：空闲时CPU占用率 ≤ 5%，内存占用 ≤ 200MB（桌面端）。专注监测时对系统性能影响降至最低。
*   **NFR-002: 可用性 (Usability)**
    *   **易学性：** 新用户能在15分钟内掌握核心功能（目标输入、分解、任务管理、启动番茄钟）。
    *   **易用性：** 界面简洁直观，交互符合用户习惯，减少不必要的操作步骤。
    *   **容错性：** 提供撤销/重做功能，重要删除操作有确认提示。
    *   **帮助文档：** 提供清晰简洁的使用指南和FAQ。
*   **NFR-003: 可靠性 (Reliability)**
    *   **稳定性：** 应用崩溃率 ≤ 0.1% (每1000次会话)。
    *   **数据完整性：** 用户数据（目标、任务、记录等）不丢失、不损坏。提供本地备份或可选云同步机制。
*   **NFR-004: 安全性与隐私 (Security & Privacy)**
    *   用户数据本地存储时需加密。
    *   若涉及云同步，传输过程和云端存储均需加密。
    *   严格遵守用户隐私协议，不滥用用户数据。
    *   “环境监测”功能需明确告知权限获取原因，并确保仅用于专注辅助，不窥探用户隐私内容。
*   **NFR-005: 兼容性 (Compatibility)**
    *   **桌面端 (MVP)：** Windows 10+, macOS 10.15+。
    *   **浏览器插件 (若Digital Sentinel采用此方案)：** Chrome, Firefox, Edge 最新稳定版。
    *   **移动端 (未来)：** iOS, Android 最新及次新版本。
*   **NFR-006: 可维护性 (Maintainability)**
    *   代码结构清晰，模块化设计，注释良好。
    *   便于后续功能迭代和Bug修复。
*   **NFR-007: 可扩展性 (Scalability)**
    *   架构设计应考虑未来用户量增长和功能扩展的可能性（尤其针对云同步和AI分析部分）。
*   **NFR-008: 可访问性 (Accessibility - a11y) (V1.1+)**
    *   支持键盘导航。
    *   良好的色彩对比度。
    *   对屏幕阅读器友好（关键功能）。

### 4. 系统界面与用户体验 (UI/UX) 概要

*   **设计原则：** 简洁、专注、引导性强、减少干扰。
*   **主色调：** 建议采用有助于平静和专注的色系（如蓝色、绿色系），避免过于鲜艳刺激的颜色。
*   **布局：** 清晰的层级结构，常用功能易于访问。
*   **反馈：** 操作有即时反馈，状态变更明确。
*   **引导：** 对于复杂功能（如目标分解、深潜模式设置），提供步骤式引导或提示。
*   **个性化：** 允许用户自定义主题（明亮/暗黑模式）、字体大小等。

### 5. 数据模型概要 (Data Model Sketch)

*   `User` (用户ID, 偏好设置)
*   `Goal` (目标ID, 用户ID, 名称, 描述, 类型, WhyPower, 关联领域, 创建日期, 截止日期, 状态)
*   `SubGoal/Milestone` (节点ID, 父节点ID, 类型, 名称, 描述, 状态)
*   `Task` (任务ID, 所属节点ID, 标题, 描述, ETC, ATC, 优先级, 截止日期, 标签, 状态, 完成百分比, 重复规则)
*   `PomodoroLog` (记录ID, 任务ID, 开始时间, 结束时间, 时长, 类型:工作/休息)
*   `DistractionLog` (记录ID, 用户ID, 时间, 分心源, 时长, 情绪)
*   `FocusSession` (会话ID, 开始时间, 结束时间, 专注时长, 使用模式:番茄/深潜)
*   `AppSettings` (用户ID, 通知设置, 黑白名单列表, 个性化建议开关)

### 6. 发布与迭代计划 (High-Level Roadmap)

*   **Phase 1: MVP (3-4个月)**
    *   核心功能：FR-GI-001, FR-FD-001, FR-FD-002 (树状图), FR-TM-001, FR-TM-002 (列表/看板), FR-TM-003 (手动), FR-IR-001, FR-PF-001, FR-PF-002。
    *   基础NFRs：性能、可用性、可靠性、基础安全。
    *   平台：桌面端 (Windows & macOS)。
*   **Phase 2: V1.1 (MVP后2-3个月)**
    *   增强功能：FR-GI-002 (AI解析初步), FR-FM-001 (环境监测基础版), FR-FM-002, FR-FM-003 (基础报告), FR-FM-004, FR-FM-005 (深潜模式), FR-FM-006。
    *   可选：FR-PF-003 (严格番茄钟)。
    *   NFRs：完善安全性、兼容性、可访问性初步。
*   **Phase 3: V1.2 (V1.1后3-4个月)**
    *   智能与个性化：FR-AP-001, FR-AP-002, FR-AP-003。
    *   高级可视化：FR-FD-002 (网状图), FR-TM-003 (甘特图/燃尽图)。
    *   NFRs：可扩展性、可维护性优化。
*   **Future Considerations:** 移动端支持、团队协作功能、更高级的AI洞察、社区功能。
