# 心流锚定系统 - 开发文档

**版本**: 1.0  
**更新日期**: 2024-12-19  

## 1. 开发环境设置

### 1.1 环境要求
- **Node.js**: 18.x LTS 或更高版本
- **包管理器**: pnpm 8.x (推荐) 或 npm 9.x
- **操作系统**: Windows 10+ 或 macOS 10.15+
- **IDE**: VS Code (推荐) + TypeScript插件

### 1.2 项目初始化
```bash
# 克隆项目
git clone https://github.com/your-repo/focus-anchor-os.git
cd focus-anchor-os

# 安装依赖
pnpm install

# 启动开发服务器
pnpm run dev
```

## 2. 项目结构

```
focus-anchor-os/
├── src/
│   ├── main/                     # Electron主进程
│   │   ├── services/             # 业务服务
│   │   ├── database/             # 数据库相关
│   │   └── main.ts               # 主进程入口
│   ├── renderer/                 # 渲染进程
│   │   ├── components/           # React组件
│   │   ├── pages/                # 页面组件
│   │   ├── hooks/                # 自定义Hooks
│   │   ├── store/                # Redux状态管理
│   │   └── App.tsx               # 应用入口
│   └── shared/                   # 共享代码
├── docs/                         # 文档目录
├── tests/                        # 测试文件
└── package.json                  # 项目配置
```

## 3. 技术栈

### 3.1 核心技术
- **应用框架**: Electron 27+
- **前端框架**: React 18+ with TypeScript
- **状态管理**: Redux Toolkit
- **UI组件**: Ant Design 5.x
- **数据库**: SQLite 3.x
- **构建工具**: Vite 5.x

### 3.2 开发工具
- **测试框架**: Jest + Playwright
- **代码规范**: ESLint + Prettier
- **类型检查**: TypeScript strict mode

## 4. 核心模块开发

### 4.1 数据模型
```typescript
// 目标数据模型
export interface Goal {
  id: string;
  userId: string;
  name: string;
  description: string;
  type: 'long-term' | 'short-term' | 'habit';
  whyPower: string;
  status: 'active' | 'completed' | 'paused';
  createdAt: Date;
  updatedAt: Date;
}

// 任务数据模型  
export interface Task {
  id: string;
  goalNodeId: string;
  title: string;
  estimatedTime?: number;
  actualTime: number;
  priority: 'high' | 'medium' | 'low';
  status: 'todo' | 'in-progress' | 'completed';
  createdAt: Date;
  updatedAt: Date;
}
```

### 4.2 服务层示例
```typescript
// 目标服务
export class GoalService {
  constructor(private database: Database) {}

  async createGoal(goalData: CreateGoalDto): Promise<Goal> {
    const goal: Goal = {
      id: generateId(),
      userId: getCurrentUserId(),
      ...goalData,
      status: 'active',
      createdAt: new Date(),
      updatedAt: new Date()
    };

    await this.saveGoal(goal);
    return goal;
  }
}
```

### 4.3 前端组件示例
```typescript
// 目标表单组件
export const GoalForm: React.FC = () => {
  const [form] = Form.useForm();
  const dispatch = useAppDispatch();

  const handleSubmit = async (values: any) => {
    try {
      await dispatch(createGoal(values)).unwrap();
      form.resetFields();
    } catch (error) {
      console.error('创建目标失败:', error);
    }
  };

  return (
    <Form form={form} onFinish={handleSubmit}>
      <Form.Item name="name" label="目标名称" rules={[{ required: true }]}>
        <Input />
      </Form.Item>
      <Form.Item name="whyPower" label="核心驱动力" rules={[{ required: true, min: 50 }]}>
        <Input.TextArea rows={3} />
      </Form.Item>
      <Button type="primary" htmlType="submit">创建目标</Button>
    </Form>
  );
};
```

## 5. 数据库设计

```sql
-- 目标表
CREATE TABLE goals (
    id TEXT PRIMARY KEY,
    user_id TEXT NOT NULL,
    name TEXT NOT NULL,
    description TEXT,
    type TEXT NOT NULL,
    why_power TEXT NOT NULL,
    status TEXT DEFAULT 'active',
    created_at DATETIME DEFAULT CURRENT_TIMESTAMP
);

-- 任务表
CREATE TABLE tasks (
    id TEXT PRIMARY KEY,
    goal_id TEXT NOT NULL,
    title TEXT NOT NULL,
    estimated_time INTEGER,
    actual_time INTEGER DEFAULT 0,
    status TEXT DEFAULT 'todo',
    FOREIGN KEY (goal_id) REFERENCES goals(id)
);
```

## 6. 测试

### 6.1 单元测试
```typescript
describe('GoalService', () => {
  it('should create goal', async () => {
    const goalData = {
      name: '学习TypeScript',
      description: '掌握基础',
      type: 'short-term',
      whyPower: '提升技能'
    };

    const result = await goalService.createGoal(goalData);
    expect(result.id).toBeDefined();
  });
});
```

### 6.2 E2E测试
```typescript
test('create goal flow', async ({ page }) => {
  await page.goto('/');
  await page.click('[data-testid="create-goal-button"]');
  await page.fill('[data-testid="goal-name"]', '测试目标');
  await page.click('[data-testid="save-button"]');
  
  await expect(page.locator('[data-testid="goal-list"]')).toContainText('测试目标');
});
```

## 7. 构建和部署

### 7.1 开发命令
```bash
pnpm run dev          # 开发环境
pnpm run build        # 生产构建
pnpm run test         # 运行测试
pnpm run lint         # 代码检查
```

### 7.2 打包发布
```bash
pnpm run dist         # 打包所有平台
pnpm run dist:win     # 仅Windows
pnpm run dist:mac     # 仅macOS
```

## 8. 开发规范

### 8.1 代码规范
- 使用TypeScript严格模式
- 遵循ESLint和Prettier配置
- 组件使用PascalCase命名
- 文件使用kebab-case命名

### 8.2 Git规范
```bash
# 提交信息格式
feat: 新功能
fix: 修复bug
docs: 文档更新
style: 代码格式
refactor: 重构
test: 测试相关
chore: 构建配置
```

### 8.3 分支策略
- `main`: 主分支，稳定版本
- `develop`: 开发分支
- `feature/*`: 功能分支
- `hotfix/*`: 热修复分支

## 9. 调试指南

### 9.1 开发工具
- Chrome DevTools (渲染进程)
- VS Code调试器 (主进程)
- React Developer Tools
- Redux DevTools

### 9.2 日志系统
```typescript
import winston from 'winston';

export const logger = winston.createLogger({
  level: 'info',
  format: winston.format.json(),
  transports: [
    new winston.transports.File({ filename: 'app.log' }),
    new winston.transports.Console()
  ]
});
```

## 10. 性能优化

### 10.1 前端优化
- 组件懒加载
- 虚拟化长列表
- 状态选择器缓存
- 图片资源优化

### 10.2 后端优化
- 数据库索引
- 查询优化
- 内存缓存
- 异步处理

---

更多详细信息请参考项目wiki和源码注释。 