# AI API 管理功能

## 功能概述

FocusOS 现在支持管理第三方AI服务提供商的API配置，用户可以在设置页面中添加、编辑和管理多个AI提供商。

## 功能特性

### 🔧 核心功能
- ✅ 添加/编辑/删除 AI 提供商
- ✅ 启用/禁用 AI 提供商
- ✅ API Key 安全显示（支持显示/隐藏）
- ✅ 连接测试功能
- ✅ 预定义模板快速配置
- ✅ 支持多种AI服务商

### 🎨 界面设计
- ✅ 参考图片的左侧列表布局
- ✅ 统一的配色风格
- ✅ 毛玻璃效果卡片
- ✅ 响应式设计
- ✅ 图标和状态标签

### 🔒 安全特性
- ✅ API Key 加密存储
- ✅ 敏感信息脱敏显示
- ✅ 数据库安全存储

## 支持的AI提供商

### 预定义模板
1. **OpenAI** 🤖
   - GPT-4, GPT-3.5-turbo
   - Base URL: `https://api.openai.com/v1`

2. **Google AI** 🔍
   - Gemini Pro, Gemini Pro Vision
   - Base URL: `https://ai-proxy.chatwise.app/generativelanguage/v1beta`

3. **Anthropic** 🧠
   - Claude-3 系列模型
   - Base URL: `https://api.anthropic.com/v1`

4. **Azure OpenAI** ☁️
   - Azure 托管的 OpenAI 服务
   - 自定义 Base URL

5. **阿里云通义千问** 🌐
   - Qwen 系列模型
   - Base URL: `https://dashscope.aliyuncs.com/api/v1`

## 使用方法

### 1. 访问AI API管理
1. 打开应用设置页面
2. 点击 "AI API 管理" 标签页
3. 查看当前配置的AI提供商列表

### 2. 添加新的AI提供商
1. 点击 "添加提供商" 按钮
2. 选择预定义模板或手动填写
3. 填写必要信息：
   - 提供商名称
   - 图标（emoji）
   - 描述
   - API Base URL
   - API Key
   - 支持的模型列表
   - 最大Token数
   - Temperature参数

### 3. 编辑现有提供商
1. 点击提供商卡片右侧的编辑按钮
2. 修改相关配置
3. 保存更改

### 4. 测试连接
1. 点击提供商卡片右侧的测试按钮
2. 系统会验证API配置的有效性
3. 显示测试结果

### 5. 启用/禁用提供商
1. 使用提供商卡片右侧的开关
2. 禁用的提供商不会在其他功能中显示

## 技术实现

### 数据库结构
```sql
CREATE TABLE ai_providers (
    id TEXT PRIMARY KEY,
    name TEXT NOT NULL,
    icon TEXT NOT NULL,
    description TEXT NOT NULL,
    base_url TEXT NOT NULL,
    api_key TEXT NOT NULL,
    enabled BOOLEAN DEFAULT TRUE,
    models TEXT, -- JSON字符串存储数组
    max_tokens INTEGER,
    temperature REAL,
    created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
    updated_at DATETIME DEFAULT CURRENT_TIMESTAMP
);
```

### API接口
- `DatabaseAPI.createAIProvider(data)` - 创建AI提供商
- `DatabaseAPI.updateAIProvider(id, updates)` - 更新AI提供商
- `DatabaseAPI.deleteAIProvider(id)` - 删除AI提供商
- `DatabaseAPI.getAIProviders()` - 获取所有AI提供商
- `DatabaseAPI.getAIProviderById(id)` - 获取指定AI提供商
- `DatabaseAPI.testAIProvider(id)` - 测试AI提供商连接

### 组件结构
- `AIProviders.tsx` - 主要的AI提供商管理组件
- `Settings.tsx` - 集成了AI API管理的设置页面
- 使用 Ant Design 组件库构建界面

## 配色和样式

### 主题适配
- 自动适配当前应用主题
- 支持所有预定义主题配色
- 毛玻璃效果与主题色彩协调

### 视觉元素
- 卡片式布局
- 图标和状态标签
- 响应式网格系统
- 平滑的动画过渡

## 安全考虑

### API Key 保护
- 数据库中加密存储
- 界面上默认脱敏显示
- 支持显示/隐藏切换

### 数据验证
- 表单输入验证
- URL格式检查
- 必填字段验证

## 扩展性

### 未来功能
- [ ] API使用统计
- [ ] 费用监控
- [ ] 批量导入/导出配置
- [ ] 更多AI服务商支持
- [ ] 高级配置选项

### 集成计划
- [ ] 与目标分析功能集成
- [ ] 智能建议功能
- [ ] 自动化工作流

## 开发说明

### 文件位置
- 前端组件: `packages/renderer/src/pages/AIProviders.tsx`
- 类型定义: `packages/renderer/src/types/index.ts`
- API服务: `packages/renderer/src/services/api.ts`
- 数据库操作: `packages/main/src/database/DatabaseManager.ts`
- IPC处理: `packages/main/src/services/DatabaseService.ts`

### 开发环境测试
1. 启动应用: `npm start`
2. 访问设置页面
3. 切换到 "AI API 管理" 标签页
4. 测试添加、编辑、删除功能

这个功能为FocusOS提供了强大的AI服务集成能力，用户可以根据需要配置不同的AI提供商，为后续的智能功能奠定了基础。
