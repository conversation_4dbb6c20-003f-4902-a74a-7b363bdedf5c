# FR-FM-001: 智能专注力监控系统详细功能规范

**版本：** 2.0  
**日期：** 2025年06月24日  
**状态：** 详细设计阶段  
**优先级：** 高（中期目标核心功能）

## 📋 功能概述

**FR-FM-001: 智能专注力监控系统 (Focus Shield - Digital Sentinel)**

实现跨平台的应用/网站监控和智能干预系统，作为FocusOS专注力提升的核心功能之一，与现有的番茄钟、任务管理、AI目标分解等功能深度集成。

**用户故事：**
> 作为一个用户，我希望系统能在我专注工作时，智能地监控和管理我的数字环境，帮我阻止或警示访问分心内容，并提供个性化的专注力提升建议。

## 🏗 1. 技术架构设计

### 1.1 系统架构概览

```mermaid
graph TB
    A[FocusOS 主应用] --> B[智能专注力监控系统]
    B --> C[监控引擎]
    B --> D[干预系统]
    B --> E[数据分析]
    
    C --> F[桌面监控服务]
    C --> G[浏览器扩展]
    C --> H[系统API集成]
    
    D --> I[分心检测算法]
    D --> J[渐进式干预]
    D --> K[专注恢复机制]
    
    E --> L[行为分析]
    E --> M[模式识别]
    E --> N[个性化建议]
    
    F --> O[macOS监控]
    F --> P[Windows监控]
    F --> Q[Linux监控]
    
    G --> R[Chrome扩展]
    G --> S[Firefox扩展]
    G --> T[Edge扩展]
```

### 1.2 桌面端监控实现方案

#### **技术选型**
- **macOS**: `node-mac-permissions` + AppleScript + Accessibility API
- **Windows**: `node-ffi-napi` + Win32 API + PowerShell
- **Linux**: `x11` + `wmctrl` + D-Bus

#### **核心服务架构**
```typescript
// packages/main/src/services/ApplicationMonitor.ts
export class ApplicationMonitor {
  private isMonitoring: boolean = false;
  private currentApp: string = '';
  private focusSession: FocusSession | null = null;
  private blacklistManager: BlacklistManager;
  private interventionEngine: InterventionEngine;
  private performanceMetrics: PerformanceMetrics;
  
  // 监控生命周期管理
  async startMonitoring(session: FocusSession): Promise<void> {
    this.focusSession = session;
    this.isMonitoring = true;
    await this.initializeMonitoringComponents();
    this.startMonitoringLoop();
  }
  
  async stopMonitoring(): Promise<void> {
    this.isMonitoring = false;
    await this.cleanupMonitoringComponents();
    this.focusSession = null;
  }
  
  // 应用检测和分析
  async detectActiveApplication(): Promise<AppInfo> {
    if (process.platform === 'darwin') {
      return this.detectMacOSApplication();
    } else if (process.platform === 'win32') {
      return this.detectWindowsApplication();
    } else {
      return this.detectLinuxApplication();
    }
  }
  
  async analyzeApplicationUsage(app: AppInfo): Promise<UsageAnalysis> {
    const category = await this.categorizeApplication(app);
    const riskLevel = this.calculateDistractionRisk(app, category);
    const contextRelevance = this.assessContextRelevance(app, this.focusSession);
    
    return {
      app,
      category,
      riskLevel,
      contextRelevance,
      timestamp: Date.now()
    };
  }
  
  // 事件处理
  private async handleAppSwitch(fromApp: string, toApp: string): Promise<void> {
    const appInfo = await this.detectActiveApplication();
    const analysis = await this.analyzeApplicationUsage(appInfo);
    
    if (analysis.riskLevel > 0.7) {
      await this.interventionEngine.triggerIntervention(analysis);
    }
    
    // 记录应用使用数据
    await this.recordAppUsage(fromApp, toApp, analysis);
  }
}
```

#### **平台特定实现**

**macOS监控实现：**
```typescript
class MacOSMonitor {
  async detectActiveApplication(): Promise<AppInfo> {
    // 使用AppleScript获取活动应用
    const script = `
      tell application "System Events"
        set frontApp to first application process whose frontmost is true
        set appName to name of frontApp
        set appBundle to bundle identifier of frontApp
        return appName & "|" & appBundle
      end tell
    `;
    
    const result = await this.executeAppleScript(script);
    const [name, bundleId] = result.split('|');
    
    return {
      name,
      bundleId,
      platform: 'darwin',
      windowTitle: await this.getActiveWindowTitle(),
      processId: await this.getProcessId(bundleId)
    };
  }
  
  async requestAccessibilityPermission(): Promise<boolean> {
    const { systemPreferences } = await import('electron');
    
    // 检查当前权限状态
    const hasPermission = systemPreferences.isTrustedAccessibilityClient(false);
    if (hasPermission) return true;
    
    // 显示权限请求对话框
    const userConsent = await this.showPermissionDialog();
    if (!userConsent) return false;
    
    // 引导用户到系统设置
    await this.openSystemPreferences();
    
    // 等待权限授予
    return this.waitForPermissionGrant();
  }
}
```

**Windows监控实现：**
```typescript
class WindowsMonitor {
  private ffi: any;
  private user32: any;
  
  constructor() {
    this.ffi = require('ffi-napi');
    this.user32 = this.ffi.Library('user32', {
      'GetForegroundWindow': ['pointer', []],
      'GetWindowTextW': ['int', ['pointer', 'pointer', 'int']],
      'GetWindowThreadProcessId': ['int', ['pointer', 'pointer']]
    });
  }
  
  async detectActiveApplication(): Promise<AppInfo> {
    const hwnd = this.user32.GetForegroundWindow();
    const windowTitle = this.getWindowTitle(hwnd);
    const processId = this.getProcessId(hwnd);
    const processName = await this.getProcessName(processId);
    
    return {
      name: processName,
      bundleId: processName.toLowerCase(),
      platform: 'win32',
      windowTitle,
      processId
    };
  }
  
  private getWindowTitle(hwnd: any): string {
    const buffer = Buffer.alloc(512);
    const length = this.user32.GetWindowTextW(hwnd, buffer, 256);
    return buffer.toString('utf16le', 0, length * 2);
  }
}
```

### 1.3 浏览器扩展架构设计

#### **Chrome扩展技术栈**
- **Manifest V3**: 现代扩展标准
- **Service Worker**: 后台监控服务
- **Content Scripts**: 页面内容分析
- **WebSocket**: 与主应用实时通信

#### **扩展架构实现**
```javascript
// browser-extension/manifest.json
{
  "manifest_version": 3,
  "name": "FocusOS Digital Sentinel",
  "version": "1.0.0",
  "permissions": [
    "activeTab",
    "tabs",
    "storage",
    "background",
    "webNavigation"
  ],
  "background": {
    "service_worker": "background.js"
  },
  "content_scripts": [{
    "matches": ["<all_urls>"],
    "js": ["content.js"]
  }],
  "action": {
    "default_popup": "popup.html"
  }
}

// browser-extension/src/background.js
class BrowserMonitor {
  constructor() {
    this.focusSession = null;
    this.websocket = null;
    this.distractionDetector = new DistractionDetector();
    this.setupEventListeners();
    this.connectToMainApp();
  }
  
  setupEventListeners() {
    chrome.tabs.onActivated.addListener(this.handleTabActivated.bind(this));
    chrome.tabs.onUpdated.addListener(this.handleTabUpdated.bind(this));
    chrome.webNavigation.onCompleted.addListener(this.handleNavigationCompleted.bind(this));
  }
  
  async handleTabActivated(activeInfo) {
    const tab = await chrome.tabs.get(activeInfo.tabId);
    const analysis = await this.analyzeWebsite(tab.url, tab.title);
    
    if (analysis.isDistraction && this.focusSession) {
      await this.triggerWebsiteIntervention(tab, analysis);
    }
    
    // 发送数据到主应用
    this.sendToMainApp({
      type: 'tab_activated',
      data: { tab, analysis, timestamp: Date.now() }
    });
  }
  
  async analyzeWebsite(url, title) {
    try {
      const domain = new URL(url).hostname;
      const category = await this.categorizeWebsite(domain, title);
      const riskLevel = this.calculateDistractionRisk(category, url, title);
      const timeSpent = await this.getTimeSpentOnDomain(domain);
      
      return {
        url,
        domain,
        title,
        category,
        riskLevel,
        timeSpent,
        isDistraction: riskLevel > 0.7,
        timestamp: Date.now()
      };
    } catch (error) {
      console.error('Website analysis failed:', error);
      return { url, isDistraction: false, error: error.message };
    }
  }
  
  async categorizeWebsite(domain, title) {
    // 1. 检查本地黑白名单
    const localCategory = await this.checkLocalBlacklist(domain);
    if (localCategory) return localCategory;
    
    // 2. 基于域名模式匹配
    const domainCategory = this.categorizeDomain(domain);
    if (domainCategory) return domainCategory;
    
    // 3. 基于页面标题分析
    const titleCategory = this.categorizeByTitle(title);
    
    return titleCategory || 'unknown';
  }
  
  connectToMainApp() {
    try {
      this.websocket = new WebSocket('ws://localhost:8080/browser-monitor');
      
      this.websocket.onopen = () => {
        console.log('Connected to FocusOS main app');
        this.sendHandshake();
      };
      
      this.websocket.onmessage = (event) => {
        const message = JSON.parse(event.data);
        this.handleMainAppMessage(message);
      };
      
      this.websocket.onclose = () => {
        console.log('Disconnected from FocusOS main app');
        // 尝试重连
        setTimeout(() => this.connectToMainApp(), 5000);
      };
    } catch (error) {
      console.error('Failed to connect to main app:', error);
    }
  }
}
```

### 1.4 通信机制设计

#### **通信协议定义**
```typescript
interface FocusMessage {
  type: 'session_start' | 'session_end' | 'distraction_detected' | 'focus_restored' | 'config_update';
  payload: {
    sessionId?: string;
    taskId?: string;
    appName?: string;
    url?: string;
    timestamp: number;
    metadata?: Record<string, any>;
  };
}

interface DistractionEvent {
  id: string;
  type: 'application' | 'website';
  source: string; // app name or URL
  category: string;
  riskLevel: number;
  sessionId: string;
  taskId?: string;
  timestamp: number;
  duration?: number;
  userResponse?: 'blocked' | 'allowed' | 'ignored';
}
```

#### **WebSocket服务器实现**
```typescript
// packages/main/src/services/FocusWebSocketServer.ts
export class FocusWebSocketServer {
  private server: WebSocket.Server;
  private connectedClients: Set<WebSocket> = new Set();
  private messageHandlers: Map<string, MessageHandler> = new Map();
  
  constructor(port: number = 8080) {
    this.server = new WebSocket.Server({ port });
    this.setupEventHandlers();
    this.registerMessageHandlers();
  }
  
  async broadcastFocusSession(session: FocusSession): Promise<void> {
    const message: FocusMessage = {
      type: 'session_start',
      payload: {
        sessionId: session.id,
        taskId: session.taskId,
        timestamp: Date.now(),
        metadata: {
          taskName: session.taskName,
          blacklist: session.blacklistRules,
          interventionLevel: session.interventionLevel
        }
      }
    };
    
    this.broadcast(message);
  }
  
  async broadcastConfigUpdate(config: BlacklistConfig): Promise<void> {
    const message: FocusMessage = {
      type: 'config_update',
      payload: {
        timestamp: Date.now(),
        metadata: {
          blacklist: config.blacklist,
          whitelist: config.whitelist,
          timeRules: config.timeBasedRules
        }
      }
    };
    
    this.broadcast(message);
  }
  
  private broadcast(message: FocusMessage): void {
    const messageStr = JSON.stringify(message);
    this.connectedClients.forEach(client => {
      if (client.readyState === WebSocket.OPEN) {
        client.send(messageStr);
      }
    });
  }
}
```

### 1.5 权限申请和用户授权流程

#### **权限管理策略**
1. **渐进式权限请求**: 仅在用户首次使用相关功能时请求
2. **透明化说明**: 清晰解释每项权限的用途和必要性
3. **最小权限原则**: 只请求功能必需的最小权限集合
4. **用户控制**: 允许用户随时撤销或重新授权

#### **权限管理器实现**
```typescript
// packages/main/src/services/PermissionManager.ts
export class PermissionManager {
  private permissionStatus: Map<string, boolean> = new Map();
  
  async requestAllRequiredPermissions(): Promise<PermissionResult> {
    const results: PermissionResult = {
      accessibility: false,
      screenRecording: false,
      notifications: false,
      fullDiskAccess: false
    };
    
    // 1. 辅助功能权限（应用监控必需）
    results.accessibility = await this.requestAccessibilityPermission();
    
    // 2. 通知权限
    results.notifications = await this.requestNotificationPermission();
    
    // 3. 屏幕录制权限（可选，用于高级监控）
    if (await this.shouldRequestScreenRecording()) {
      results.screenRecording = await this.requestScreenRecordingPermission();
    }
    
    return results;
  }
  
  async requestAccessibilityPermission(): Promise<boolean> {
    // 检查当前权限状态
    const hasPermission = await this.checkAccessibilityPermission();
    if (hasPermission) return true;
    
    // 显示权限说明对话框
    const userConsent = await this.showPermissionDialog({
      title: '辅助功能权限',
      message: 'FocusOS需要辅助功能权限来监控应用切换，帮助您保持专注。',
      benefits: [
        '智能检测分心应用',
        '提供及时的专注提醒',
        '生成专注力分析报告'
      ],
      privacy: '所有监控数据仅在本地存储，不会上传到服务器。',
      risks: [
        '可以读取当前活动应用的名称',
        '可以检测应用切换事件'
      ]
    });
    
    if (!userConsent) return false;
    
    // 引导用户到系统设置
    await this.openSystemPreferences('security-privacy-accessibility');
    
    // 轮询检查权限状态
    return this.waitForPermissionGrant('accessibility', 30000);
  }
  
  private async showPermissionDialog(options: PermissionDialogOptions): Promise<boolean> {
    const { dialog } = await import('electron');
    
    const result = await dialog.showMessageBox({
      type: 'info',
      title: options.title,
      message: options.message,
      detail: this.formatPermissionDetails(options),
      buttons: ['授予权限', '稍后设置', '了解更多'],
      defaultId: 0,
      cancelId: 1
    });
    
    if (result.response === 2) {
      // 打开帮助文档
      await this.openPermissionHelp();
      return this.showPermissionDialog(options);
    }
    
    return result.response === 0;
  }
  
  private formatPermissionDetails(options: PermissionDialogOptions): string {
    let details = '\n权限用途：\n';
    options.benefits.forEach(benefit => {
      details += `• ${benefit}\n`;
    });
    
    details += '\n隐私保护：\n';
    details += `• ${options.privacy}\n`;
    
    if (options.risks && options.risks.length > 0) {
      details += '\n权限范围：\n';
      options.risks.forEach(risk => {
        details += `• ${risk}\n`;
      });
    }
    
    return details;
  }
}
```

## 🎯 2. 黑白名单管理系统

### 2.1 用户界面设计规范

#### **设计原则**
- **Apple简约风格**: 清晰的层次结构，充足的留白，优雅的动画
- **直观操作**: 拖拽添加，一键分类，智能建议
- **即时反馈**: 实时预览，状态指示，操作确认

#### **界面组件架构**
```typescript
// packages/renderer/src/components/BlacklistManager.tsx
const BlacklistManager: React.FC = () => {
  const { theme } = useTheme();
  const [selectedCategory, setSelectedCategory] = useState<string>('all');
  const [searchQuery, setSearchQuery] = useState<string>('');
  const [filteredApps, setFilteredApps] = useState<AppRule[]>([]);
  const [selectedApp, setSelectedApp] = useState<AppRule | null>(null);
  
  return (
    <div className="blacklist-manager" style={{ background: theme.colors.background }}>
      {/* 顶部工具栏 */}
      <div className="toolbar">
        <div className="search-section">
          <SearchInput 
            placeholder="搜索应用或网站..." 
            value={searchQuery}
            onChange={setSearchQuery}
            style={{ width: '300px' }}
          />
        </div>
        
        <div className="action-section">
          <ButtonGroup>
            <Button 
              type="primary" 
              icon={<PlusOutlined />}
              onClick={handleAddRule}
            >
              添加规则
            </Button>
            <Dropdown overlay={importMenu} trigger={['click']}>
              <Button icon={<ImportOutlined />}>
                导入模板 <DownOutlined />
              </Button>
            </Dropdown>
            <Button 
              icon={<SettingOutlined />}
              onClick={handleAdvancedSettings}
            >
              高级设置
            </Button>
          </ButtonGroup>
        </div>
      </div>
      
      {/* 主要内容区域 */}
      <div className="content-area">
        {/* 左侧：分类导航 */}
        <div className="category-sidebar">
          <CategoryTree 
            categories={APP_CATEGORIES}
            selectedCategory={selectedCategory}
            onCategorySelect={setSelectedCategory}
            showCounts={true}
          />
          
          <div className="quick-actions">
            <Button 
              type="text" 
              icon={<ExportOutlined />}
              onClick={handleExportRules}
            >
              导出规则
            </Button>
            <Button 
              type="text" 
              icon={<SyncOutlined />}
              onClick={handleSyncRules}
            >
              同步设置
            </Button>
          </div>
        </div>
        
        {/* 中间：应用/网站列表 */}
        <div className="app-list">
          <div className="list-header">
            <div className="filter-controls">
              <Select
                value={filterType}
                onChange={setFilterType}
                style={{ width: 120 }}
              >
                <Option value="all">全部</Option>
                <Option value="applications">应用</Option>
                <Option value="websites">网站</Option>
              </Select>
              
              <Select
                value={riskLevel}
                onChange={setRiskLevel}
                style={{ width: 120 }}
              >
                <Option value="all">所有风险</Option>
                <Option value="high">高风险</Option>
                <Option value="medium">中风险</Option>
                <Option value="low">低风险</Option>
              </Select>
            </div>
            
            <div className="view-controls">
              <Radio.Group value={viewMode} onChange={e => setViewMode(e.target.value)}>
                <Radio.Button value="list">列表</Radio.Button>
                <Radio.Button value="grid">网格</Radio.Button>
              </Radio.Group>
            </div>
          </div>
          
          <DragDropList
            items={filteredApps}
            viewMode={viewMode}
            onItemMove={handleItemMove}
            onItemEdit={handleItemEdit}
            onItemDelete={handleItemDelete}
            renderItem={renderAppItem}
            emptyState={<EmptyState message="暂无规则，点击添加规则开始配置" />}
          />
        </div>
        
        {/* 右侧：详情面板 */}
        <div className="details-panel">
          {selectedApp ? (
            <AppDetailsPanel
              app={selectedApp}
              onRuleUpdate={handleRuleUpdate}
              onClose={() => setSelectedApp(null)}
            />
          ) : (
            <EmptyDetailsPanel />
          )}
        </div>
      </div>
    </div>
  );
};
```

#### **应用项目渲染组件**
```typescript
const AppRuleItem: React.FC<AppRuleItemProps> = ({ 
  rule, 
  onEdit, 
  onDelete, 
  onToggle,
  viewMode = 'list' 
}) => {
  const { theme } = useTheme();
  
  const getRiskColor = (level: string) => {
    switch (level) {
      case 'high': return '#ff4d4f';
      case 'medium': return '#faad14';
      case 'low': return '#52c41a';
      default: return '#8c8c8c';
    }
  };
  
  const getActionIcon = (action: string) => {
    switch (action) {
      case 'block': return <StopOutlined style={{ color: '#ff4d4f' }} />;
      case 'warn': return <ExclamationOutlined style={{ color: '#faad14' }} />;
      case 'allow': return <CheckOutlined style={{ color: '#52c41a' }} />;
      default: return <QuestionOutlined />;
    }
  };
  
  if (viewMode === 'grid') {
    return (
      <Card
        className="app-rule-card"
        size="small"
        hoverable
        onClick={() => onEdit(rule)}
        actions={[
          <Switch 
            checked={rule.isActive} 
            onChange={(checked) => onToggle(rule.id, checked)}
            size="small"
          />,
          <Button 
            type="text" 
            icon={<EditOutlined />} 
            onClick={(e) => { e.stopPropagation(); onEdit(rule); }}
          />,
          <Button 
            type="text" 
            danger 
            icon={<DeleteOutlined />} 
            onClick={(e) => { e.stopPropagation(); onDelete(rule.id); }}
          />
        ]}
      >
        <div className="card-content">
          <div className="app-icon">
            {rule.type === 'application' ? 
              <AppstoreOutlined style={{ fontSize: '24px' }} /> : 
              <GlobalOutlined style={{ fontSize: '24px' }} />
            }
          </div>
          <div className="app-info">
            <div className="app-name" title={rule.pattern}>
              {rule.displayName || rule.pattern}
            </div>
            <div className="app-meta">
              <Tag color={getRiskColor(rule.severity)} size="small">
                {rule.severity}
              </Tag>
              <span className="action-indicator">
                {getActionIcon(rule.action)}
              </span>
            </div>
          </div>
        </div>
      </Card>
    );
  }
  
  return (
    <div className="app-rule-item">
      <div className="item-content">
        <div className="app-basic-info">
          <div className="app-icon">
            {rule.type === 'application' ? 
              <AppstoreOutlined /> : 
              <GlobalOutlined />
            }
          </div>
          <div className="app-details">
            <div className="app-name">{rule.displayName || rule.pattern}</div>
            <div className="app-pattern">{rule.pattern}</div>
          </div>
        </div>
        
        <div className="rule-info">
          <Tag color={getRiskColor(rule.severity)} size="small">
            {rule.severity}
          </Tag>
          <span className="action-indicator">
            {getActionIcon(rule.action)}
          </span>
          <span className="category-tag">
            {rule.category}
          </span>
        </div>
        
        <div className="item-controls">
          <Switch 
            checked={rule.isActive} 
            onChange={(checked) => onToggle(rule.id, checked)}
            size="small"
          />
          <Button 
            type="text" 
            icon={<EditOutlined />} 
            onClick={() => onEdit(rule)}
          />
          <Button 
            type="text" 
            danger 
            icon={<DeleteOutlined />} 
            onClick={() => onDelete(rule.id)}
          />
        </div>
      </div>
      
      {rule.timeConstraints && (
        <div className="time-constraints">
          <ClockCircleOutlined style={{ marginRight: '4px' }} />
          <span>时间限制已设置</span>
        </div>
      )}
    </div>
  );
};
```

### 2.2 智能分类建议系统

#### **分类算法架构**
```typescript
// packages/renderer/src/services/IntelligentCategorizer.ts
export class IntelligentCategorizer {
  private categoryModels: Map<string, CategoryModel> = new Map();
  private nlpProcessor: NLPProcessor;
  private behaviorAnalyzer: BehaviorAnalyzer;

  constructor() {
    this.nlpProcessor = new NLPProcessor();
    this.behaviorAnalyzer = new BehaviorAnalyzer();
    this.initializeCategoryModels();
  }

  async categorizeApplication(appInfo: AppInfo): Promise<CategorySuggestion[]> {
    const features = this.extractFeatures(appInfo);
    const suggestions: CategorySuggestion[] = [];

    // 1. 基于应用名称和描述的NLP分析
    const nlpCategory = await this.nlpCategorize(appInfo.name, appInfo.description);
    suggestions.push({
      category: nlpCategory.category,
      confidence: nlpCategory.confidence,
      reason: '基于应用名称和描述分析',
      evidence: nlpCategory.keywords
    });

    // 2. 基于用户历史行为模式
    const behaviorCategory = await this.behaviorBasedCategorize(appInfo, features);
    suggestions.push({
      category: behaviorCategory.category,
      confidence: behaviorCategory.confidence,
      reason: '基于您的使用模式分析',
      evidence: behaviorCategory.patterns
    });

    // 3. 基于社区数据（匿名化）
    const communityCategory = await this.communityCategorize(appInfo.bundleId);
    if (communityCategory) {
      suggestions.push({
        category: communityCategory.category,
        confidence: communityCategory.confidence,
        reason: '基于社区用户分类',
        evidence: communityCategory.stats
      });
    }

    return suggestions
      .sort((a, b) => b.confidence - a.confidence)
      .slice(0, 3); // 返回前3个建议
  }

  private extractFeatures(appInfo: AppInfo): AppFeatures {
    return {
      usageFrequency: appInfo.dailyUsageMinutes / 60,
      sessionDuration: appInfo.averageSessionMinutes,
      timeOfDayPattern: this.analyzeTimePattern(appInfo.usageHistory),
      contextSwitchRate: this.calculateContextSwitches(appInfo.usageHistory),
      productivityCorrelation: this.calculateProductivityImpact(appInfo),
      weekdayVsWeekendUsage: this.analyzeWeekdayPattern(appInfo.usageHistory),
      focusSessionInterruptions: this.countFocusInterruptions(appInfo.usageHistory)
    };
  }
}
```

#### **预定义分类体系**
```typescript
const APP_CATEGORIES = {
  WORK: {
    id: 'work',
    name: '工作相关',
    color: '#52c41a',
    icon: 'laptop',
    description: '提升工作效率的应用和网站',
    subcategories: [
      { id: 'development', name: '开发工具', keywords: ['code', 'git', 'terminal', 'ide'] },
      { id: 'design', name: '设计软件', keywords: ['photoshop', 'sketch', 'figma'] },
      { id: 'office', name: '办公套件', keywords: ['word', 'excel', 'powerpoint'] },
      { id: 'project', name: '项目管理', keywords: ['jira', 'trello', 'asana'] },
      { id: 'communication', name: '工作通信', keywords: ['slack', 'teams', 'zoom'] }
    ],
    defaultAction: 'allow',
    defaultSeverity: 'low'
  },

  ENTERTAINMENT: {
    id: 'entertainment',
    name: '娱乐休闲',
    color: '#ff4d4f',
    icon: 'play-circle',
    description: '娱乐和休闲相关的应用和网站',
    subcategories: [
      { id: 'video', name: '视频娱乐', keywords: ['youtube', 'netflix', 'bilibili'] },
      { id: 'gaming', name: '游戏', keywords: ['game', 'steam', 'epic'] },
      { id: 'music', name: '音乐', keywords: ['spotify', 'music', 'apple music'] },
      { id: 'social', name: '社交媒体', keywords: ['facebook', 'twitter', 'instagram'] }
    ],
    defaultAction: 'warn',
    defaultSeverity: 'high'
  },

  LEARNING: {
    id: 'learning',
    name: '学习教育',
    color: '#1890ff',
    icon: 'book',
    description: '学习和教育相关的应用和网站',
    subcategories: [
      { id: 'courses', name: '在线课程', keywords: ['coursera', 'udemy', 'edx'] },
      { id: 'documentation', name: '技术文档', keywords: ['docs', 'documentation'] },
      { id: 'programming', name: '编程学习', keywords: ['stackoverflow', 'github'] }
    ],
    defaultAction: 'allow',
    defaultSeverity: 'low'
  }
};
```

## 🔍 3. 监测与干预机制

### 3.1 实时监控技术实现

#### **监控服务架构**
```typescript
class RealTimeMonitor {
  private monitoringInterval: NodeJS.Timeout | null = null;
  private eventEmitter: EventEmitter = new EventEmitter();
  private performanceMetrics: PerformanceMetrics = new PerformanceMetrics();

  async startRealTimeMonitoring(config: MonitoringConfig): Promise<void> {
    // 1. 初始化监控组件
    await this.initializeMonitoringComponents();

    // 2. 启动高频监控循环
    this.monitoringInterval = setInterval(async () => {
      try {
        await this.performMonitoringCycle();
      } catch (error) {
        this.handleMonitoringError(error);
      }
    }, config.intervalMs || 1000);

    // 3. 注册系统事件监听
    this.registerSystemEventListeners();
  }

  private async performMonitoringCycle(): Promise<void> {
    const startTime = performance.now();

    // 并行执行多种检测
    const [appInfo, windowInfo, networkActivity, inputActivity] = await Promise.all([
      this.detectActiveApplication(),
      this.detectActiveWindow(),
      this.detectNetworkActivity(),
      this.detectInputActivity()
    ]);

    // 综合分析
    const analysis = await this.analyzeCurrentState({
      appInfo,
      windowInfo,
      networkActivity,
      inputActivity,
      timestamp: Date.now()
    });

    // 触发相应事件
    if (analysis.distractionDetected) {
      this.eventEmitter.emit('distraction_detected', analysis);
    }

    // 性能监控
    const endTime = performance.now();
    this.performanceMetrics.recordCycleTime(endTime - startTime);
  }
}
```

### 3.2 分心检测算法

#### **多维度分心检测**
```typescript
class DistractionDetector {
  private riskThresholds = {
    high: 0.8,
    medium: 0.5,
    low: 0.3
  };

  async detectDistraction(context: MonitoringContext): Promise<DistractionAnalysis> {
    const factors: DistractionFactor[] = [];

    // 1. 应用类别风险评估
    const categoryRisk = this.assessCategoryRisk(context.appInfo);
    factors.push({
      type: 'category',
      weight: 0.4,
      score: categoryRisk.score,
      evidence: categoryRisk.evidence
    });

    // 2. 时间上下文分析
    const timeRisk = this.assessTimeContextRisk(context);
    factors.push({
      type: 'time_context',
      weight: 0.2,
      score: timeRisk.score,
      evidence: timeRisk.evidence
    });

    // 3. 用户行为模式分析
    const behaviorRisk = await this.assessBehaviorRisk(context);
    factors.push({
      type: 'behavior',
      weight: 0.3,
      score: behaviorRisk.score,
      evidence: behaviorRisk.evidence
    });

    // 4. 专注会话相关性
    const sessionRisk = this.assessSessionRelevance(context);
    factors.push({
      type: 'session_relevance',
      weight: 0.1,
      score: sessionRisk.score,
      evidence: sessionRisk.evidence
    });

    // 综合评分
    const overallRisk = this.calculateOverallRisk(factors);

    return {
      isDistraction: overallRisk.score > this.riskThresholds.medium,
      riskLevel: this.categorizeRiskLevel(overallRisk.score),
      confidence: overallRisk.confidence,
      factors,
      recommendations: this.generateRecommendations(factors, overallRisk)
    };
  }

  private assessCategoryRisk(appInfo: AppInfo): RiskAssessment {
    const category = appInfo.category || 'unknown';
    const baseRisk = this.getCategoryBaseRisk(category);

    // 根据应用特征调整风险
    let adjustedRisk = baseRisk;

    // 检查是否在黑名单中
    if (this.isInBlacklist(appInfo.name)) {
      adjustedRisk = Math.min(1.0, adjustedRisk + 0.3);
    }

    // 检查是否在白名单中
    if (this.isInWhitelist(appInfo.name)) {
      adjustedRisk = Math.max(0.0, adjustedRisk - 0.4);
    }

    return {
      score: adjustedRisk,
      confidence: 0.9,
      evidence: [`应用类别: ${category}`, `基础风险: ${baseRisk}`]
    };
  }

  private async assessBehaviorRisk(context: MonitoringContext): Promise<RiskAssessment> {
    const appInfo = context.appInfo;
    const userHistory = await this.getUserHistory(appInfo.name);

    let riskScore = 0;
    const evidence: string[] = [];

    // 分析使用频率
    if (userHistory.dailyUsageMinutes > 120) { // 超过2小时
      riskScore += 0.2;
      evidence.push('高频使用应用');
    }

    // 分析会话切换频率
    if (userHistory.averageSessionMinutes < 5) {
      riskScore += 0.3;
      evidence.push('频繁切换，可能分心');
    }

    // 分析专注会话中断历史
    const interruptionRate = userHistory.focusInterruptions / userHistory.totalFocusSessions;
    if (interruptionRate > 0.3) {
      riskScore += 0.4;
      evidence.push('经常在专注时使用此应用');
    }

    return {
      score: Math.min(1.0, riskScore),
      confidence: 0.8,
      evidence
    };
  }
}
```

### 3.3 渐进式干预策略

#### **干预级别定义**
```typescript
enum InterventionLevel {
  NONE = 0,        // 无干预
  GENTLE = 1,      // 温和提醒
  MODERATE = 2,    // 明显警告
  STRONG = 3,      // 强制干预
  BLOCK = 4        // 完全阻止
}

interface InterventionStrategy {
  level: InterventionLevel;
  delay: number;           // 延迟时间（毫秒）
  duration: number;        // 显示时长（毫秒）
  allowBypass: boolean;    // 是否允许绕过
  escalationTime: number;  // 升级时间（毫秒）
}

class InterventionEngine {
  private strategies: Map<InterventionLevel, InterventionStrategy> = new Map([
    [InterventionLevel.GENTLE, {
      level: InterventionLevel.GENTLE,
      delay: 0,
      duration: 3000,
      allowBypass: true,
      escalationTime: 30000
    }],
    [InterventionLevel.MODERATE, {
      level: InterventionLevel.MODERATE,
      delay: 1000,
      duration: 5000,
      allowBypass: true,
      escalationTime: 15000
    }],
    [InterventionLevel.STRONG, {
      level: InterventionLevel.STRONG,
      delay: 3000,
      duration: 10000,
      allowBypass: false,
      escalationTime: 10000
    }],
    [InterventionLevel.BLOCK, {
      level: InterventionLevel.BLOCK,
      delay: 0,
      duration: -1, // 持续显示
      allowBypass: false,
      escalationTime: 0
    }]
  ]);

  async triggerIntervention(analysis: DistractionAnalysis): Promise<InterventionResult> {
    const level = this.determineInterventionLevel(analysis);
    const strategy = this.strategies.get(level)!;

    // 记录干预事件
    const interventionId = await this.logInterventionEvent(analysis, level);

    // 延迟执行（给用户反应时间）
    if (strategy.delay > 0) {
      await this.delay(strategy.delay);

      // 检查用户是否已经切换回来
      const currentApp = await this.getCurrentApplication();
      if (!this.isStillDistracted(currentApp, analysis.appInfo)) {
        return { success: true, userSelfCorrected: true };
      }
    }

    // 执行干预
    const result = await this.executeIntervention(strategy, analysis, interventionId);

    // 设置升级定时器
    if (strategy.escalationTime > 0 && result.userResponse !== 'corrected') {
      this.scheduleEscalation(interventionId, strategy.escalationTime);
    }

    return result;
  }

  private async executeIntervention(
    strategy: InterventionStrategy,
    analysis: DistractionAnalysis,
    interventionId: string
  ): Promise<InterventionResult> {
    switch (strategy.level) {
      case InterventionLevel.GENTLE:
        return this.showGentleReminder(analysis, strategy);

      case InterventionLevel.MODERATE:
        return this.showModeratWarning(analysis, strategy);

      case InterventionLevel.STRONG:
        return this.showStrongIntervention(analysis, strategy);

      case InterventionLevel.BLOCK:
        return this.executeBlocking(analysis, strategy);

      default:
        return { success: false, error: 'Unknown intervention level' };
    }
  }

  private async showGentleReminder(
    analysis: DistractionAnalysis,
    strategy: InterventionStrategy
  ): Promise<InterventionResult> {
    const notification = await this.createNotification({
      type: 'gentle',
      title: '专注提醒',
      message: `您正在使用 ${analysis.appInfo.name}，记得保持专注哦 🎯`,
      icon: 'info',
      duration: strategy.duration,
      actions: [
        { id: 'continue', label: '继续使用', style: 'secondary' },
        { id: 'return', label: '返回工作', style: 'primary' }
      ]
    });

    const userResponse = await this.waitForUserResponse(notification, strategy.duration);

    return {
      success: true,
      userResponse: userResponse || 'timeout',
      interventionType: 'gentle_reminder'
    };
  }

  private async showModeratWarning(
    analysis: DistractionAnalysis,
    strategy: InterventionStrategy
  ): Promise<InterventionResult> {
    const modal = await this.createModal({
      type: 'warning',
      title: '专注警告',
      content: this.buildWarningContent(analysis),
      maskClosable: strategy.allowBypass,
      duration: strategy.duration,
      actions: [
        { id: 'return', label: '返回工作', style: 'primary', autoFocus: true },
        { id: 'allow_5min', label: '允许5分钟', style: 'default' },
        { id: 'allow_session', label: '本次允许', style: 'text' }
      ]
    });

    const userResponse = await this.waitForUserResponse(modal, strategy.duration);

    // 根据用户选择执行相应操作
    if (userResponse === 'allow_5min') {
      await this.setTemporaryAllowance(analysis.appInfo.name, 5 * 60 * 1000);
    } else if (userResponse === 'allow_session') {
      await this.setSessionAllowance(analysis.appInfo.name);
    }

    return {
      success: true,
      userResponse: userResponse || 'timeout',
      interventionType: 'moderate_warning'
    };
  }
}
```

## 🎨 4. 用户体验设计

### 4.1 干预弹窗的UI/UX设计

#### **设计原则**
- **非侵入性**: 不完全阻断用户操作，保持工作流的连续性
- **渐进式**: 从温和提醒到强制干预，给用户适应时间
- **信息透明**: 清晰说明为什么触发干预，帮助用户理解
- **快速响应**: 提供快捷操作选项，减少决策时间

#### **干预界面组件**
```typescript
// packages/renderer/src/components/InterventionModal.tsx
const InterventionModal: React.FC<InterventionModalProps> = ({
  type,
  analysis,
  onResponse,
  autoCloseTime
}) => {
  const { theme } = useTheme();
  const [timeLeft, setTimeLeft] = useState(autoCloseTime);
  const [userChoice, setUserChoice] = useState<string | null>(null);

  const getModalConfig = (type: InterventionType) => {
    switch (type) {
      case 'gentle':
        return {
          icon: <InfoCircleOutlined style={{ color: '#1890ff' }} />,
          title: '专注提醒',
          severity: 'info',
          primaryAction: '返回工作',
          secondaryAction: '继续使用'
        };
      case 'moderate':
        return {
          icon: <ExclamationCircleOutlined style={{ color: '#faad14' }} />,
          title: '专注警告',
          severity: 'warning',
          primaryAction: '返回工作',
          secondaryAction: '允许5分钟'
        };
      case 'strong':
        return {
          icon: <StopOutlined style={{ color: '#ff4d4f' }} />,
          title: '强制干预',
          severity: 'error',
          primaryAction: '必须返回工作',
          secondaryAction: null
        };
    }
  };

  const config = getModalConfig(type);

  return (
    <Modal
      open={true}
      centered
      closable={type !== 'strong'}
      maskClosable={type === 'gentle'}
      width={480}
      className={`intervention-modal intervention-${type}`}
      footer={null}
      style={{
        background: theme.colors.cardBackground,
        borderRadius: '16px',
        overflow: 'hidden'
      }}
    >
      <div className="intervention-content">
        {/* 头部区域 */}
        <div className="intervention-header">
          <div className="icon-section">
            {config.icon}
          </div>
          <div className="title-section">
            <h3 style={{ color: theme.colors.text, margin: 0 }}>
              {config.title}
            </h3>
            {timeLeft > 0 && (
              <div className="countdown">
                <ClockCircleOutlined style={{ marginRight: '4px' }} />
                {Math.ceil(timeLeft / 1000)}秒后自动关闭
              </div>
            )}
          </div>
        </div>

        {/* 内容区域 */}
        <div className="intervention-body">
          <div className="app-info">
            <div className="app-icon">
              {analysis.appInfo.type === 'application' ?
                <AppstoreOutlined /> :
                <GlobalOutlined />
              }
            </div>
            <div className="app-details">
              <div className="app-name">{analysis.appInfo.name}</div>
              <div className="app-category">
                <Tag color={this.getCategoryColor(analysis.appInfo.category)}>
                  {analysis.appInfo.category}
                </Tag>
                <Tag color={this.getRiskColor(analysis.riskLevel)}>
                  {analysis.riskLevel} 风险
                </Tag>
              </div>
            </div>
          </div>

          <div className="distraction-analysis">
            <div className="risk-indicator">
              <Progress
                type="circle"
                size={60}
                percent={analysis.confidence * 100}
                strokeColor={this.getRiskColor(analysis.riskLevel)}
                format={() => `${Math.round(analysis.confidence * 100)}%`}
              />
              <span className="risk-label">分心风险</span>
            </div>

            <div className="analysis-details">
              <h4>检测到的分心因素：</h4>
              <ul>
                {analysis.factors.map((factor, index) => (
                  <li key={index}>
                    <span className="factor-type">{factor.type}:</span>
                    <span className="factor-score">
                      {Math.round(factor.score * 100)}%
                    </span>
                  </li>
                ))}
              </ul>
            </div>
          </div>

          {analysis.recommendations && (
            <div className="recommendations">
              <h4>建议：</h4>
              <ul>
                {analysis.recommendations.map((rec, index) => (
                  <li key={index}>{rec}</li>
                ))}
              </ul>
            </div>
          )}
        </div>

        {/* 操作区域 */}
        <div className="intervention-actions">
          <Space size="middle">
            <Button
              type="primary"
              size="large"
              onClick={() => onResponse('return_to_work')}
              autoFocus
            >
              {config.primaryAction}
            </Button>

            {config.secondaryAction && (
              <Button
                size="large"
                onClick={() => onResponse('allow_temporary')}
              >
                {config.secondaryAction}
              </Button>
            )}

            {type === 'moderate' && (
              <Button
                type="text"
                size="large"
                onClick={() => onResponse('allow_session')}
              >
                本次专注会话允许
              </Button>
            )}
          </Space>
        </div>

        {/* 学习模式选项 */}
        {type !== 'strong' && (
          <div className="learning-options">
            <Checkbox
              checked={userChoice === 'add_to_whitelist'}
              onChange={(e) => setUserChoice(e.target.checked ? 'add_to_whitelist' : null)}
            >
              将此应用添加到工作白名单
            </Checkbox>
            <Checkbox
              checked={userChoice === 'adjust_sensitivity'}
              onChange={(e) => setUserChoice(e.target.checked ? 'adjust_sensitivity' : null)}
            >
              降低此类应用的检测敏感度
            </Checkbox>
          </div>
        )}
      </div>
    </Modal>
  );
};
```

### 4.2 非侵入式提醒机制

#### **渐进式提醒系统**
```typescript
class ProgressiveReminderSystem {
  private reminderQueue: ReminderEvent[] = [];
  private activeReminders: Map<string, ActiveReminder> = new Map();

  async scheduleReminder(event: DistractionEvent): Promise<void> {
    const reminderPlan = this.createReminderPlan(event);

    for (const step of reminderPlan.steps) {
      setTimeout(async () => {
        if (this.shouldExecuteReminder(event.id, step)) {
          await this.executeReminderStep(event, step);
        }
      }, step.delay);
    }
  }

  private createReminderPlan(event: DistractionEvent): ReminderPlan {
    const baseDelay = 5000; // 5秒基础延迟
    const escalationFactor = 2;

    return {
      eventId: event.id,
      steps: [
        {
          type: 'ambient',
          delay: baseDelay,
          duration: 3000,
          intensity: 'low'
        },
        {
          type: 'notification',
          delay: baseDelay * escalationFactor,
          duration: 5000,
          intensity: 'medium'
        },
        {
          type: 'modal',
          delay: baseDelay * escalationFactor * 2,
          duration: 10000,
          intensity: 'high'
        }
      ]
    };
  }

  private async executeReminderStep(
    event: DistractionEvent,
    step: ReminderStep
  ): Promise<void> {
    switch (step.type) {
      case 'ambient':
        await this.showAmbientReminder(event, step);
        break;
      case 'notification':
        await this.showNotificationReminder(event, step);
        break;
      case 'modal':
        await this.showModalReminder(event, step);
        break;
    }
  }

  private async showAmbientReminder(
    event: DistractionEvent,
    step: ReminderStep
  ): Promise<void> {
    // 环境式提醒：菜单栏图标变化、轻微的视觉提示
    const ambientIndicator = new AmbientIndicator({
      type: 'menu_bar_pulse',
      color: '#faad14',
      duration: step.duration,
      intensity: step.intensity
    });

    await ambientIndicator.show();

    // 记录用户是否注意到提醒
    this.trackReminderEffectiveness(event.id, step.type, 'shown');
  }

  private async showNotificationReminder(
    event: DistractionEvent,
    step: ReminderStep
  ): Promise<void> {
    // 系统通知提醒
    const notification = await this.notificationService.sendNotification(
      '专注提醒',
      {
        body: `您正在使用 ${event.source}，这可能会影响专注`,
        icon: 'focus-reminder',
        duration: step.duration,
        actions: [
          { action: 'return', title: '返回工作' },
          { action: 'snooze', title: '稍后提醒' }
        ]
      }
    );

    // 监听用户响应
    notification.onclick = () => {
      this.handleReminderResponse(event.id, 'return');
    };
  }
}
```

### 4.3 专注会话恢复机制

#### **智能恢复系统**
```typescript
class FocusRecoverySystem {
  private recoveryStrategies: Map<string, RecoveryStrategy> = new Map();

  async handleFocusInterruption(interruption: FocusInterruption): Promise<RecoveryResult> {
    // 1. 分析中断类型和严重程度
    const analysis = await this.analyzeInterruption(interruption);

    // 2. 选择恢复策略
    const strategy = this.selectRecoveryStrategy(analysis);

    // 3. 执行恢复流程
    const result = await this.executeRecovery(strategy, interruption);

    // 4. 学习和优化
    await this.updateRecoveryModel(interruption, result);

    return result;
  }

  private async analyzeInterruption(interruption: FocusInterruption): Promise<InterruptionAnalysis> {
    return {
      severity: this.calculateSeverity(interruption),
      duration: interruption.duration,
      frequency: await this.getInterruptionFrequency(interruption.source),
      userPattern: await this.getUserInterruptionPattern(interruption.userId),
      contextRelevance: this.assessContextRelevance(interruption)
    };
  }

  private selectRecoveryStrategy(analysis: InterruptionAnalysis): RecoveryStrategy {
    if (analysis.severity === 'low' && analysis.duration < 30000) {
      return this.recoveryStrategies.get('gentle_nudge')!;
    } else if (analysis.severity === 'medium') {
      return this.recoveryStrategies.get('guided_return')!;
    } else {
      return this.recoveryStrategies.get('full_reset')!;
    }
  }

  private async executeRecovery(
    strategy: RecoveryStrategy,
    interruption: FocusInterruption
  ): Promise<RecoveryResult> {
    switch (strategy.type) {
      case 'gentle_nudge':
        return this.executeGentleNudge(interruption);
      case 'guided_return':
        return this.executeGuidedReturn(interruption);
      case 'full_reset':
        return this.executeFullReset(interruption);
      default:
        throw new Error(`Unknown recovery strategy: ${strategy.type}`);
    }
  }

  private async executeGentleNudge(interruption: FocusInterruption): Promise<RecoveryResult> {
    // 轻柔的恢复提示
    const nudge = await this.createRecoveryNudge({
      title: '准备好回到工作了吗？',
      message: `您刚才查看了 ${interruption.source}，现在可以继续专注于 ${interruption.originalTask}`,
      actions: [
        { id: 'continue', label: '继续专注', primary: true },
        { id: 'break', label: '休息一下', secondary: true }
      ],
      autoFocus: true,
      timeout: 10000
    });

    const response = await this.waitForUserResponse(nudge);

    if (response === 'continue') {
      await this.restoreFocusContext(interruption.originalTask);
      return { success: true, method: 'gentle_nudge', userEngagement: 'high' };
    } else {
      await this.suggestBreakActivity();
      return { success: true, method: 'gentle_nudge', userEngagement: 'medium' };
    }
  }

  private async executeGuidedReturn(interruption: FocusInterruption): Promise<RecoveryResult> {
    // 引导式恢复流程
    const guidedFlow = new GuidedRecoveryFlow({
      steps: [
        {
          type: 'reflection',
          title: '刚才发生了什么？',
          content: '花一点时间思考刚才的中断是否必要',
          duration: 5000
        },
        {
          type: 'intention_setting',
          title: '重新设定专注意图',
          content: `让我们回到 ${interruption.originalTask}`,
          actions: ['我准备好了', '需要调整任务']
        },
        {
          type: 'environment_check',
          title: '优化专注环境',
          content: '检查并调整可能的干扰源',
          checklist: ['关闭不必要的应用', '调整通知设置', '准备必要工具']
        }
      ]
    });

    const flowResult = await guidedFlow.execute();

    if (flowResult.completed) {
      await this.restoreFocusContext(interruption.originalTask);
      return { success: true, method: 'guided_return', userEngagement: 'high' };
    } else {
      return { success: false, method: 'guided_return', userEngagement: 'low' };
    }
  }
}
```

## 🔒 5. 数据隐私和安全

### 5.1 本地数据存储策略

#### **数据分类和存储**
```typescript
interface DataClassification {
  category: 'public' | 'private' | 'sensitive' | 'critical';
  retention: number; // 保留天数
  encryption: boolean;
  backup: boolean;
  sharing: 'never' | 'anonymous' | 'explicit_consent';
}

const DATA_CLASSIFICATIONS: Record<string, DataClassification> = {
  // 公开数据：应用名称、网站域名等
  app_metadata: {
    category: 'public',
    retention: 365,
    encryption: false,
    backup: true,
    sharing: 'anonymous'
  },

  // 私有数据：使用时长、频率等
  usage_statistics: {
    category: 'private',
    retention: 90,
    encryption: true,
    backup: true,
    sharing: 'anonymous'
  },

  // 敏感数据：窗口标题、URL等
  content_metadata: {
    category: 'sensitive',
    retention: 30,
    encryption: true,
    backup: false,
    sharing: 'never'
  },

  // 关键数据：用户配置、个人设置等
  user_configuration: {
    category: 'critical',
    retention: -1, // 永久保留
    encryption: true,
    backup: true,
    sharing: 'never'
  }
};

class SecureDataManager {
  private encryptionKey: string;
  private storage: SecureStorage;

  constructor() {
    this.encryptionKey = this.generateOrRetrieveKey();
    this.storage = new SecureStorage(this.encryptionKey);
  }

  async storeData(type: string, data: any): Promise<void> {
    const classification = DATA_CLASSIFICATIONS[type];
    if (!classification) {
      throw new Error(`Unknown data type: ${type}`);
    }

    // 数据预处理
    const processedData = await this.preprocessData(data, classification);

    // 加密存储
    if (classification.encryption) {
      const encryptedData = await this.encrypt(processedData);
      await this.storage.store(type, encryptedData);
    } else {
      await this.storage.store(type, processedData);
    }

    // 设置过期时间
    if (classification.retention > 0) {
      const expiryDate = new Date(Date.now() + classification.retention * 24 * 60 * 60 * 1000);
      await this.storage.setExpiry(type, expiryDate);
    }
  }

  private async preprocessData(data: any, classification: DataClassification): Promise<any> {
    switch (classification.category) {
      case 'sensitive':
        // 敏感数据脱敏处理
        return this.sanitizeSensitiveData(data);
      case 'private':
        // 私有数据匿名化
        return this.anonymizePrivateData(data);
      default:
        return data;
    }
  }

  private sanitizeSensitiveData(data: any): any {
    // 移除或模糊化敏感信息
    if (data.windowTitle) {
      data.windowTitle = this.maskSensitiveText(data.windowTitle);
    }
    if (data.url) {
      data.url = this.maskURL(data.url);
    }
    return data;
  }

  private maskSensitiveText(text: string): string {
    // 保留前3个和后3个字符，中间用*替代
    if (text.length <= 6) return '***';
    return text.substring(0, 3) + '*'.repeat(text.length - 6) + text.substring(text.length - 3);
  }
}
```

### 5.2 用户隐私保护措施

#### **隐私保护框架**
```typescript
class PrivacyProtectionFramework {
  private privacySettings: PrivacySettings;
  private dataProcessor: DataProcessor;

  async initializePrivacyProtection(): Promise<void> {
    // 1. 加载用户隐私偏好
    this.privacySettings = await this.loadPrivacySettings();

    // 2. 初始化数据处理器
    this.dataProcessor = new DataProcessor(this.privacySettings);

    // 3. 设置数据收集边界
    await this.setupDataCollectionBoundaries();

    // 4. 启动隐私监控
    this.startPrivacyMonitoring();
  }

  async processDataCollection(dataType: string, rawData: any): Promise<ProcessedData | null> {
    // 检查是否允许收集此类数据
    if (!this.isDataCollectionAllowed(dataType)) {
      return null;
    }

    // 应用隐私过滤器
    const filteredData = await this.applyPrivacyFilters(rawData, dataType);

    // 数据最小化处理
    const minimizedData = this.minimizeData(filteredData, dataType);

    // 匿名化处理
    const anonymizedData = this.anonymizeData(minimizedData, dataType);

    return {
      type: dataType,
      data: anonymizedData,
      timestamp: Date.now(),
      privacyLevel: this.calculatePrivacyLevel(dataType)
    };
  }

  private isDataCollectionAllowed(dataType: string): boolean {
    const setting = this.privacySettings.dataCollection[dataType];
    return setting?.enabled === true;
  }

  private async applyPrivacyFilters(data: any, dataType: string): Promise<any> {
    const filters = this.privacySettings.filters[dataType] || [];

    let filteredData = { ...data };

    for (const filter of filters) {
      switch (filter.type) {
        case 'exclude_field':
          delete filteredData[filter.field];
          break;
        case 'mask_field':
          filteredData[filter.field] = this.maskValue(filteredData[filter.field]);
          break;
        case 'hash_field':
          filteredData[filter.field] = this.hashValue(filteredData[filter.field]);
          break;
      }
    }

    return filteredData;
  }

  private minimizeData(data: any, dataType: string): any {
    // 数据最小化：只保留功能必需的字段
    const essentialFields = this.getEssentialFields(dataType);
    const minimizedData: any = {};

    for (const field of essentialFields) {
      if (data[field] !== undefined) {
        minimizedData[field] = data[field];
      }
    }

    return minimizedData;
  }

  private anonymizeData(data: any, dataType: string): any {
    // 移除或替换可识别信息
    const anonymizedData = { ...data };

    // 移除用户标识符
    delete anonymizedData.userId;
    delete anonymizedData.deviceId;
    delete anonymizedData.sessionId;

    // 替换为匿名标识符
    anonymizedData.anonymousId = this.generateAnonymousId(data);

    return anonymizedData;
  }
}
```

### 5.3 权限最小化原则

#### **权限管理系统**
```typescript
class MinimalPermissionManager {
  private grantedPermissions: Set<string> = new Set();
  private permissionRequests: Map<string, PermissionRequest> = new Map();

  async requestPermission(
    permission: string,
    justification: PermissionJustification
  ): Promise<boolean> {
    // 检查是否已经拥有权限
    if (this.grantedPermissions.has(permission)) {
      return true;
    }

    // 检查权限是否真正必要
    const necessity = await this.assessPermissionNecessity(permission, justification);
    if (necessity.level < 0.7) {
      console.warn(`Permission ${permission} may not be necessary:`, necessity.reason);
      return false;
    }

    // 寻找替代方案
    const alternatives = await this.findAlternatives(permission, justification);
    if (alternatives.length > 0) {
      const chosen = await this.proposeAlternatives(alternatives);
      if (chosen) {
        return this.requestPermission(chosen.permission, chosen.justification);
      }
    }

    // 请求最小化权限
    const minimalPermission = this.minimizePermissionScope(permission, justification);

    // 向用户请求权限
    const granted = await this.requestUserConsent(minimalPermission, justification);

    if (granted) {
      this.grantedPermissions.add(minimalPermission.name);

      // 设置权限过期时间
      if (minimalPermission.temporary) {
        setTimeout(() => {
          this.revokePermission(minimalPermission.name);
        }, minimalPermission.duration);
      }
    }

    return granted;
  }

  private async assessPermissionNecessity(
    permission: string,
    justification: PermissionJustification
  ): Promise<NecessityAssessment> {
    const permissionInfo = PERMISSION_REGISTRY[permission];
    if (!permissionInfo) {
      return { level: 0, reason: 'Unknown permission' };
    }

    // 评估功能重要性
    const functionalImportance = this.assessFunctionalImportance(justification.feature);

    // 评估替代方案可行性
    const alternativeFeasibility = await this.assessAlternativeFeasibility(permission);

    // 评估用户价值
    const userValue = this.assessUserValue(justification.userBenefit);

    const overallNecessity = (functionalImportance * 0.4 +
                             (1 - alternativeFeasibility) * 0.3 +
                             userValue * 0.3);

    return {
      level: overallNecessity,
      reason: this.generateNecessityReason(functionalImportance, alternativeFeasibility, userValue)
    };
  }

  private minimizePermissionScope(
    permission: string,
    justification: PermissionJustification
  ): MinimalPermission {
    const basePermission = PERMISSION_REGISTRY[permission];

    return {
      name: permission,
      scope: this.calculateMinimalScope(basePermission, justification),
      duration: this.calculateOptimalDuration(justification),
      temporary: justification.sessionOnly || false,
      conditions: this.generatePermissionConditions(justification)
    };
  }

  private async requestUserConsent(
    permission: MinimalPermission,
    justification: PermissionJustification
  ): Promise<boolean> {
    const consentDialog = new PermissionConsentDialog({
      permission,
      justification,
      alternatives: await this.findAlternatives(permission.name, justification),
      risks: this.assessPermissionRisks(permission),
      benefits: justification.userBenefit
    });

    const result = await consentDialog.show();

    // 记录用户决策用于改进
    await this.logPermissionDecision(permission, justification, result);

    return result.granted;
  }
}
```
