# FocusOS 背景音频功能设置指南

## 功能概述

FocusOS 的背景音频功能为番茄钟专注时段提供了丰富的背景音效选择，帮助用户更好地集中注意力，屏蔽外界干扰。

## 音频文件准备

### 1. 音频文件目录结构

请在项目的 `public/sounds/background/` 目录下放置音频文件：

```
public/
└── sounds/
    └── background/
        ├── rain.mp3          # 雨声
        ├── forest.mp3        # 森林音效
        ├── ocean.mp3         # 海浪声
        ├── cafe.mp3          # 咖啡厅环境音
        ├── fireplace.mp3     # 壁炉燃烧声
        ├── thunderstorm.mp3  # 雷雨声
        ├── birds.mp3         # 鸟鸣声
        └── wind.mp3          # 微风声
```

### 2. 音频文件要求

- **格式**: MP3 格式（推荐）
- **时长**: 建议 2-10 分钟，系统会自动循环播放
- **音质**: 建议 128kbps 或更高，平衡文件大小和音质
- **音量**: 建议音频文件本身音量适中，用户可通过界面调节
- **内容**: 应为循环友好的音效，开头和结尾能自然衔接

### 3. 推荐音频资源

#### 免费资源：
- **Freesound.org**: 大量免费环境音效
- **Zapsplat**: 注册后可免费下载
- **BBC Sound Effects**: BBC 提供的免费音效库
- **YouTube Audio Library**: YouTube 创作者音频库

#### 付费资源：
- **Epidemic Sound**: 专业音效库
- **AudioJungle**: Envato 旗下音效市场
- **Pond5**: 高质量音效资源

### 4. 音效分类说明

#### 🌿 自然音效 (Nature)
- **雨声 (rain.mp3)**: 轻柔的雨滴声，适合需要白噪音的场景
- **森林 (forest.mp3)**: 鸟鸣和树叶沙沙声，营造自然环境
- **海浪 (ocean.mp3)**: 舒缓的海浪声，有助于放松
- **雷雨 (thunderstorm.mp3)**: 远处的雷声和雨声
- **鸟鸣 (birds.mp3)**: 清晨的鸟儿歌声
- **微风 (wind.mp3)**: 轻柔的风声

#### 🏠 环境音效 (Ambient)
- **壁炉 (fireplace.mp3)**: 温暖的壁炉燃烧声

#### 🏙️ 城市音效 (Urban)
- **咖啡厅 (cafe.mp3)**: 咖啡厅的环境音，包含轻微的人声和器具声

#### 📻 噪音音效 (Noise)
- 白噪音、棕噪音、粉噪音通过程序生成，无需音频文件

## 功能特性

### 1. 智能音效切换
- **工作时段音效**: 可设置专门用于工作专注的背景音
- **休息时段音效**: 可设置用于休息放松的背景音
- **自动切换**: 番茄钟在工作和休息时段自动切换对应音效

### 2. 音效控制
- **音量调节**: 0-100% 音量控制
- **淡入淡出**: 可设置音效开始和结束的淡入淡出时长
- **即时预览**: 设置界面提供音效试听功能

### 3. 降级机制
如果预置音频文件不可用，系统会自动使用程序生成的合成音效作为备用方案，确保功能正常运行。

## 使用方法

### 1. 基础设置
1. 打开番茄钟页面
2. 点击"设置"按钮
3. 切换到"背景音效"标签页
4. 开启"启用背景音效"开关

### 2. 音效选择
1. 在音效卡片中选择喜欢的背景音
2. 点击"试听"按钮预览效果
3. 调节音量到合适大小

### 3. 智能切换设置
1. 开启"智能切换音效"
2. 分别设置工作时段和休息时段的音效
3. 系统会在番茄钟切换时自动更换音效

### 4. 高级设置
- **淡入时长**: 音效开始时的渐入时间
- **淡出时长**: 音效结束时的渐出时间

## 技术实现

### 1. 音频播放技术
- 使用 Web Audio API 实现高质量音频播放
- 支持音频循环、音量控制、淡入淡出效果
- 兼容现代浏览器和 Electron 环境

### 2. 合成音效算法
- 白噪音: 均匀频率分布的随机噪音
- 棕噪音: 低频为主的温和噪音
- 粉噪音: 平衡的频率分布噪音

### 3. 性能优化
- 音频文件按需加载，减少初始加载时间
- 智能缓存机制，避免重复下载
- 错误处理和降级方案，确保功能稳定性

## 故障排除

### 1. 音效无法播放
- 检查音频文件是否存在于正确路径
- 确认浏览器支持音频播放
- 检查系统音量设置

### 2. 音效质量问题
- 确认音频文件格式和质量
- 检查音频文件是否损坏
- 尝试重新下载音频文件

### 3. 性能问题
- 减少音频文件大小
- 关闭不必要的音效功能
- 检查系统资源使用情况

## 自定义音效

### 1. 添加新音效
1. 将音频文件放入 `public/sounds/background/` 目录
2. 在 `BackgroundAudioService.ts` 中添加新的音效配置
3. 更新音效分类和描述信息

### 2. 音效配置示例
```typescript
'custom-sound': {
  name: '自定义音效',
  description: '您的自定义背景音效',
  category: 'ambient',
  icon: '🎵',
  color: '#1890ff',
  audioFile: '/sounds/background/custom-sound.mp3'
}
```

## 注意事项

1. **版权问题**: 确保使用的音频文件具有合法使用权
2. **文件大小**: 控制音频文件大小，避免影响应用加载速度
3. **用户体验**: 选择不会干扰专注的温和音效
4. **兼容性**: 测试在不同设备和浏览器上的播放效果

## 更新日志

### v1.0.0 (2025-06-24)
- 初始版本发布
- 支持 12 种预设音效
- 智能切换功能
- 音量和淡入淡出控制
- 程序生成噪音备用方案
