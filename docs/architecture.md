# 心流锚定系统 - 系统架构文档

**版本**: 1.0  
**更新日期**: 2024-12-19  
**文档类型**: 系统架构设计

## 1. 架构概述

### 1.1 架构目标
- **高性能**: 低延迟响应，流畅的用户体验
- **高可用**: 本地优先架构，减少外部依赖
- **可扩展**: 模块化设计，便于功能扩展
- **安全性**: 数据本地存储，隐私保护优先
- **跨平台**: 支持Windows和macOS桌面环境

### 1.2 技术栈选择

#### 核心技术栈
- **应用框架**: Electron 27+ (支持最新的Chrome和Node.js特性)
- **前端框架**: React 18+ with TypeScript
- **状态管理**: Redux Toolkit + RTK Query
- **UI组件库**: Ant Design 5.x
- **构建工具**: Vite 5.x
- **包管理**: pnpm (性能和磁盘空间优化)

#### 数据层技术
- **本地数据库**: SQLite 3.x (通过better-sqlite3)
- **数据迁移**: Knex.js
- **缓存层**: Node.js内存缓存 + IndexedDB (前端缓存)
- **数据加密**: crypto-js (AES-256-GCM)

#### 系统集成技术
- **系统通知**: electron-notifications
- **文件系统**: Node.js fs-extra
- **进程监控**: 
  - Windows: windows-process-tree
  - macOS: node-mac-permissions, applescript
- **键盘快捷键**: electron-shortcuts

## 2. 系统架构设计

### 2.1 整体架构图
```
┌─────────────────────────────────────────────┐
│               用户界面层                        │
│  React Components + Redux Store            │
└─────────────────┬───────────────────────────┘
                  │ IPC Communication
┌─────────────────┴───────────────────────────┐
│               业务逻辑层                        │
│  Goal Manager | Task Manager | Focus Monitor│
└─────────────────┬───────────────────────────┘
                  │
┌─────────────────┴───────────────────────────┐
│               数据访问层                        │
│  SQLite + Encryption + Cache               │
└─────────────────────────────────────────────┘
```

### 2.2 模块架构设计

#### 前端模块结构
```
src/
├── components/           # 可复用UI组件
│   ├── common/          # 通用组件
│   ├── forms/           # 表单组件
│   ├── charts/          # 图表组件
│   └── layouts/         # 布局组件
├── pages/               # 页面组件
│   ├── Dashboard/       # 仪表盘
│   ├── Goals/           # 目标管理
│   ├── Tasks/           # 任务管理
│   ├── Focus/           # 专注模式
│   ├── Analytics/       # 数据分析
│   └── Settings/        # 设置页面
├── hooks/               # 自定义React Hooks
├── store/               # Redux状态管理
│   ├── slices/          # Redux Toolkit切片
│   ├── api/             # RTK Query API
│   └── middleware/      # 中间件
├── utils/               # 工具函数
├── types/               # TypeScript类型定义
└── constants/           # 常量定义
```

#### 核心数据模型
```typescript
interface Goal {
  id: string;
  userId: string;
  name: string;
  description: string;
  type: 'long-term' | 'short-term' | 'habit';
  whyPower: string;
  domains: string[];
  startDate?: Date;
  deadline?: Date;
  status: 'active' | 'completed' | 'paused' | 'cancelled';
  createdAt: Date;
  updatedAt: Date;
}

interface Task {
  id: string;
  goalNodeId: string;
  title: string;
  description?: string;
  estimatedTime?: number;
  actualTime: number;
  priority: 'high' | 'medium' | 'low';
  deadline?: Date;
  tags: string[];
  status: 'todo' | 'in-progress' | 'completed' | 'paused';
  completionPercentage: number;
  createdAt: Date;
  updatedAt: Date;
}

interface PomodoroSession {
  id: string;
  taskId: string;
  type: 'work' | 'short-break' | 'long-break';
  startTime: Date;
  endTime?: Date;
  duration: number;
  isCompleted: boolean;
  wasInterrupted: boolean;
}
```

## 3. 数据库设计

### 3.1 核心表结构
```sql
-- 目标表
CREATE TABLE goals (
    id TEXT PRIMARY KEY,
    user_id TEXT NOT NULL,
    parent_id TEXT,
    name TEXT NOT NULL,
    description TEXT,
    type TEXT NOT NULL,
    why_power TEXT NOT NULL,
    domains TEXT,
    start_date DATETIME,
    deadline DATETIME,
    status TEXT DEFAULT 'active',
    level INTEGER DEFAULT 0,
    created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
    updated_at DATETIME DEFAULT CURRENT_TIMESTAMP
);

-- 任务表
CREATE TABLE tasks (
    id TEXT PRIMARY KEY,
    goal_id TEXT NOT NULL,
    title TEXT NOT NULL,
    description TEXT,
    estimated_time INTEGER,
    actual_time INTEGER DEFAULT 0,
    priority TEXT DEFAULT 'medium',
    deadline DATETIME,
    tags TEXT,
    status TEXT DEFAULT 'todo',
    completion_percentage INTEGER DEFAULT 0,
    created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
    updated_at DATETIME DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (goal_id) REFERENCES goals(id)
);

-- 番茄钟会话表
CREATE TABLE pomodoro_sessions (
    id TEXT PRIMARY KEY,
    task_id TEXT NOT NULL,
    type TEXT NOT NULL,
    start_time DATETIME NOT NULL,
    end_time DATETIME,
    duration INTEGER NOT NULL,
    is_completed BOOLEAN DEFAULT FALSE,
    was_interrupted BOOLEAN DEFAULT FALSE,
    created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (task_id) REFERENCES tasks(id)
);
```

## 4. 服务层架构

### 4.1 核心服务接口
```typescript
interface IGoalService {
  createGoal(goal: CreateGoalDto): Promise<Goal>;
  updateGoal(id: string, updates: Partial<Goal>): Promise<Goal>;
  deleteGoal(id: string): Promise<void>;
  getGoalTree(goalId: string): Promise<GoalNode[]>;
}

interface IPomodoroService {
  startSession(taskId: string, type: SessionType): Promise<PomodoroSession>;
  pauseSession(sessionId: string): Promise<void>;
  resumeSession(sessionId: string): Promise<void>;
  completeSession(sessionId: string): Promise<void>;
}

interface IFocusMonitorService {
  startMonitoring(config: MonitoringConfig): Promise<void>;
  stopMonitoring(): Promise<void>;
  getCurrentActivity(): Promise<ActivityInfo>;
  getActivityHistory(period: TimePeriod): Promise<ActivityLog[]>;
}
```

## 5. 安全架构

### 5.1 数据加密
```typescript
class EncryptionService {
  private readonly algorithm = 'aes-256-gcm';
  private masterKey: Buffer;
  
  async encryptData(data: any): Promise<EncryptedData> {
    const iv = crypto.randomBytes(16);
    const cipher = crypto.createCipher(this.algorithm, this.masterKey);
    
    let encrypted = cipher.update(JSON.stringify(data), 'utf8', 'hex');
    encrypted += cipher.final('hex');
    
    return {
      encrypted,
      iv: iv.toString('hex'),
      authTag: cipher.getAuthTag().toString('hex')
    };
  }
}
```

### 5.2 权限管理
- 系统通知权限
- 辅助功能权限 (macOS)
- 进程访问权限 (Windows)
- 屏幕录制权限 (macOS)

## 6. 性能优化

### 6.1 渲染优化
- 组件懒加载
- 虚拟化长列表
- 状态选择器优化
- 内存泄漏防护

### 6.2 数据库优化
```sql
-- 关键索引
CREATE INDEX idx_tasks_status ON tasks(status);
CREATE INDEX idx_tasks_deadline ON tasks(deadline);
CREATE INDEX idx_tasks_goal_id ON tasks(goal_id);
CREATE INDEX idx_pomodoro_sessions_task_id ON pomodoro_sessions(task_id);
```

## 7. 部署架构

### 7.1 构建配置
- Electron Builder
- 代码签名
- 自动更新机制
- 多平台打包

### 7.2 分发策略
- GitHub Releases
- 官网下载
- 应用商店 (未来)

## 8. 监控和日志

### 8.1 错误处理
- 全局错误捕获
- 日志记录
- 错误报告
- 用户友好提示

### 8.2 性能监控
- 启动时间监控
- 内存使用监控
- 响应时间统计
- 崩溃率跟踪

---

**文档维护**: 本架构文档随系统演进持续更新，重大架构变更需要技术评审。 