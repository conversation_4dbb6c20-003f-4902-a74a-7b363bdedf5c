# OpenRouter API 配置指南

## 概述

OpenRouter 是一个统一的AI模型API平台，提供对数百个AI模型的访问。本指南将帮助您在FocusOS中正确配置OpenRouter API。

## 🔧 正确的配置参数

### Base URL
```
https://openrouter.ai/api/v1
```

### 支持的模型ID格式
OpenRouter使用 `提供商/模型名` 的格式：

#### 推荐模型
- `openai/gpt-4o` - GPT-4 Omni (最新)
- `openai/gpt-4` - GPT-4
- `openai/gpt-3.5-turbo` - GPT-3.5 Turbo (经济实惠)
- `anthropic/claude-3.5-sonnet` - Claude 3.5 Sonnet
- `anthropic/claude-3-opus` - Claude 3 Opus
- `google/gemini-2.0-flash-exp` - Gemini 2.0 Flash
- `meta-llama/llama-3.1-405b-instruct` - Llama 3.1 405B
- `openrouter/auto` - 自动路由 (推荐新手使用)

### API Key 格式
```
sk-or-v1-xxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxx
```

## 📝 配置步骤

### 1. 获取API Key
1. 访问 [OpenRouter官网](https://openrouter.ai/)
2. 注册账户并登录
3. 前往 [API Keys页面](https://openrouter.ai/keys)
4. 创建新的API Key
5. 复制以 `sk-or-v1-` 开头的API Key

### 2. 在FocusOS中配置
1. 打开FocusOS设置页面
2. 选择"AI API管理"标签
3. 点击"添加提供商"
4. 选择"OpenRouter"模板或手动输入：
   - **提供商名称**: OpenRouter
   - **Base URL**: `https://openrouter.ai/api/v1`
   - **API Key**: 您的OpenRouter API Key
   - **模型ID**: 选择上述推荐模型之一

### 3. 测试连接
点击"测试连接"按钮验证配置是否正确。

## 🚨 常见错误及解决方案

### 错误1: "API地址不存在" (404错误)
**原因**: 模型ID格式错误
**解决方案**: 
- 确保使用正确的格式：`提供商/模型名`
- 检查模型名是否拼写正确
- 推荐使用 `openrouter/auto` 进行测试

### 错误2: "API Key无效" (401错误)
**原因**: API Key格式错误或已过期
**解决方案**:
- 确保API Key以 `sk-or-v1-` 开头
- 检查API Key是否完整复制
- 在OpenRouter控制台验证API Key状态

### 错误3: "API配额已用完" (429错误)
**原因**: 超出使用限制
**解决方案**:
- 检查OpenRouter账户余额
- 升级到付费计划
- 等待配额重置

## 🎯 推荐配置

### 新手用户
```
提供商名称: OpenRouter
Base URL: https://openrouter.ai/api/v1
模型ID: openrouter/auto
API Key: 您的OpenRouter API Key
```

### 高级用户
```
提供商名称: OpenRouter GPT-4
Base URL: https://openrouter.ai/api/v1
模型ID: openai/gpt-4o
API Key: 您的OpenRouter API Key
```

## 🔍 验证配置

成功配置后，您应该看到：
- ✅ 连接测试成功
- ✅ 响应时间显示
- ✅ 状态显示为"已启用"

## 📚 更多资源

- [OpenRouter官方文档](https://openrouter.ai/docs)
- [支持的模型列表](https://openrouter.ai/models)
- [API参考文档](https://openrouter.ai/docs/api-reference)

## 🆘 故障排除

如果仍然遇到问题：
1. 检查网络连接
2. 验证API Key权限
3. 尝试使用 `openrouter/auto` 模型
4. 查看FocusOS控制台日志
5. 联系OpenRouter支持团队
