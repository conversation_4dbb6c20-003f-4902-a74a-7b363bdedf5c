# FocusOS 级联删除功能实现文档

## 概述

FocusOS现已实现完整的级联删除机制，当用户删除目标时，系统会自动删除所有关联的子目标、任务、里程碑和相关数据，确保数据一致性和完整性。

## 功能特性

### 1. 级联删除范围

删除目标时会同时删除：
- **目标本身** - 主目标记录
- **子目标** - 该目标下的所有子目标
- **里程碑** - 子目标下的所有里程碑
- **任务** - 关联的所有任务（包括子目标和里程碑下的任务）
- **番茄钟会话** - 关联任务的所有番茄钟记录
- **AI分解记录** - 相关的AI分解会话和历史数据
- **用户修改记录** - 分解过程中的用户修改历史

### 2. 用户确认机制

#### 删除前分析
- 自动分析删除影响，统计将要删除的项目数量
- 显示详细的删除预览，包括具体的子目标、里程碑和任务列表
- 提供清晰的数据统计图表和风险评估

#### 确认对话框功能
- **影响统计** - 直观显示删除数量（目标、子目标、里程碑、任务等）
- **详细列表** - 展示所有将被删除的具体项目
- **风险提示** - 明确告知操作不可撤销
- **取消选项** - 允许用户重新考虑决定

### 3. 数据完整性保障

#### 数据库事务
- 使用SQLite事务确保原子性操作
- 要么全部删除成功，要么全部回滚
- 外键约束自动维护数据一致性

#### 删除顺序
1. 番茄钟会话（最底层数据）
2. 任务记录
3. 里程碑记录
4. 用户修改记录
5. AI分解会话
6. 子目标记录
7. 目标本身（包括子目标层级）

### 4. 软删除和恢复功能

#### 软删除机制
- 目标被标记为"已删除"状态，而非立即物理删除
- 30天恢复期限，期间用户可以恢复误删的目标
- 自动清理过期的软删除项目

#### 回收站功能
- 独立的回收站界面，管理所有软删除项目
- 显示删除时间、过期时间和剩余恢复时间
- 支持恢复和永久删除操作
- 过期项目自动变为不可恢复状态

### 5. 用户体验优化

#### 加载状态指示
- 分析删除影响时显示加载动画
- 执行删除操作时显示进度反馈
- 清晰的成功/失败状态提示

#### 错误处理
- 完整的错误捕获和用户友好的错误信息
- 网络错误、权限错误、数据错误的分类处理
- 操作失败时提供重试选项

#### 反馈机制
- 删除成功后显示删除统计信息
- 恢复成功后刷新目标列表
- 操作完成后清理相关缓存

## 技术实现

### 核心组件

#### 1. CascadeDeleteConfirmDialog
```typescript
// 级联删除确认对话框
interface CascadeDeleteConfirmDialogProps {
  visible: boolean;
  analysis: DeleteAnalysis | null;
  loading: boolean;
  onConfirm: () => void;
  onCancel: () => void;
}
```

**功能特性：**
- 响应式设计，适配不同屏幕尺寸
- 分层展示删除信息（统计 → 详细列表 → 风险提示）
- 支持大量数据的虚拟滚动
- 主题适配和无障碍访问支持

#### 2. SoftDeleteRecoveryPanel
```typescript
// 软删除恢复面板
interface SoftDeleteRecoveryPanelProps {
  visible: boolean;
  onClose: () => void;
  onGoalRestored?: (goalId: string) => void;
}
```

**功能特性：**
- 过期状态智能识别和显示
- 批量操作支持（清理过期项目）
- 恢复操作的二次确认机制
- 实时更新恢复期限倒计时

#### 3. GoalCascadeDeleteService
```typescript
// 级联删除服务类
export class GoalCascadeDeleteService {
  // 分析删除影响
  static async analyzeGoalDeletion(goalId: string): Promise<DeleteAnalysis>
  
  // 执行级联删除
  static async cascadeDeleteGoal(goalId: string): Promise<DeleteResult>
  
  // 软删除功能
  static async softDeleteGoal(goalId: string): Promise<SoftDeleteInfo>
  
  // 恢复软删除
  static async restoreSoftDeletedGoal(goalId: string): Promise<boolean>
}
```

### 数据库层实现

#### 外键约束
```sql
-- 目标表外键约束
FOREIGN KEY (parent_id) REFERENCES goals(id) ON DELETE CASCADE

-- 子目标表外键约束  
FOREIGN KEY (parent_goal_id) REFERENCES goals(id) ON DELETE CASCADE

-- 里程碑表外键约束
FOREIGN KEY (sub_goal_id) REFERENCES sub_goals(id) ON DELETE CASCADE

-- 任务表（通过应用层处理级联）
-- 番茄钟会话表
FOREIGN KEY (task_id) REFERENCES tasks(id) ON DELETE CASCADE
```

#### 事务处理
```typescript
const transaction = this.db.transaction(() => {
  // 1. 记录回滚信息
  const rollbackInfo = this.captureRollbackInfo(goalId);
  
  // 2. 执行级联删除
  const deletedItems = this.performCascadeDelete(goalId);
  
  return { success: true, deletedItems, rollbackInfo };
});
```

### API接口

#### 渲染进程API
```typescript
// 分析删除影响
DatabaseAPI.analyzeGoalDeletion(goalId: string): Promise<DeleteAnalysis>

// 执行级联删除
DatabaseAPI.cascadeDeleteGoal(goalId: string): Promise<DeleteResult>
```

#### IPC通信
```typescript
// 主进程IPC处理器
ipcMain.handle('db:goals:analyze-deletion', async (_, goalId) => {
  return await cascadeDeleteService.analyzeGoalDeletion(goalId);
});

ipcMain.handle('db:goals:cascade-delete', async (_, goalId) => {
  return await cascadeDeleteService.cascadeDeleteGoal(goalId);
});
```

## 使用指南

### 用户操作流程

#### 1. 删除目标
1. 在目标管理界面找到要删除的目标
2. 点击删除按钮（垃圾桶图标）
3. 系统自动分析删除影响（显示加载动画）
4. 查看删除确认对话框中的影响统计和详细列表
5. 确认后点击"确认删除"按钮
6. 查看删除成功提示和统计信息

#### 2. 使用回收站
1. 在目标管理界面点击"回收站"按钮
2. 查看所有软删除的项目列表
3. 选择要恢复的项目，点击"恢复"按钮
4. 或选择要永久删除的项目，点击"永久删除"按钮
5. 使用"清理过期项目"功能批量清理

### 开发者集成

#### 1. 在组件中集成级联删除
```typescript
// 导入必要的组件和服务
import CascadeDeleteConfirmDialog from '../components/CascadeDeleteConfirmDialog';
import { GoalCascadeDeleteService } from '../services/GoalCascadeDeleteService';

// 在组件中添加状态管理
const [deleteAnalysis, setDeleteAnalysis] = useState<DeleteAnalysis | null>(null);
const [isDeleteDialogVisible, setIsDeleteDialogVisible] = useState(false);

// 实现删除处理函数
const handleDeleteGoal = async (goalId: string) => {
  const analysis = await GoalCascadeDeleteService.analyzeGoalDeletion(goalId);
  setDeleteAnalysis(analysis);
  setIsDeleteDialogVisible(true);
};
```

#### 2. 扩展删除分析功能
```typescript
// 添加自定义分析规则
const customAnalysis = await GoalCascadeDeleteService.getDeletionImpactAssessment(goalId);

// 检查目标依赖关系
const dependencies = await GoalCascadeDeleteService.checkGoalDependencies(goalId);

// 批量删除操作
const result = await GoalCascadeDeleteService.batchDeleteGoals(goalIds);
```

## 安全性和性能

### 安全性措施
- **双重确认** - 分析 + 确认两步操作防止误删
- **权限检查** - 确保用户有删除权限
- **数据备份** - 软删除机制提供数据恢复能力
- **操作日志** - 记录所有删除操作供审计

### 性能优化
- **异步操作** - 分析和删除操作均为异步，不阻塞UI
- **批量处理** - 使用事务批量处理相关删除操作
- **缓存清理** - 删除后自动清理相关缓存
- **虚拟滚动** - 大量数据列表使用虚拟滚动优化性能

## 错误处理和调试

### 常见错误类型
1. **网络错误** - IPC通信失败
2. **数据库错误** - 约束冲突、锁定等
3. **权限错误** - 用户无删除权限
4. **业务错误** - 目标不存在、已被删除等

### 调试功能
- **详细日志** - 记录删除过程中的每个步骤
- **错误堆栈** - 保留完整的错误信息用于调试
- **状态追踪** - 跟踪删除操作的各个阶段

### 错误恢复
- **自动重试** - 临时错误时提供重试机制
- **回滚机制** - 操作失败时自动回滚到原始状态
- **用户反馈** - 清晰的错误信息和解决建议

## 未来增强

### 计划中的功能
1. **批量操作** - 支持同时删除多个目标
2. **删除历史** - 查看历史删除操作记录
3. **数据导出** - 删除前导出数据备份
4. **智能建议** - 根据使用模式提供删除建议
5. **撤销功能** - 短时间内撤销删除操作

### 性能优化计划
1. **增量分析** - 只分析变更的部分
2. **后台清理** - 定期自动清理过期数据
3. **压缩存储** - 优化软删除数据存储
4. **缓存策略** - 智能缓存分析结果

## 总结

FocusOS的级联删除功能提供了完整、安全、用户友好的数据删除解决方案。通过详细的影响分析、确认机制、软删除恢复和完善的错误处理，确保用户可以安全地管理目标数据，同时保持数据的完整性和一致性。

该功能完全集成到现有的目标管理系统中，与AI目标分解、任务管理和番茄钟功能无缝协作，为用户提供了强大而可靠的目标管理体验。