# 心流锚定系统 - 系统架构文档 (修正版)

**版本**: 1.1  
**更新日期**: 2024-12-19  
**修正说明**: 解决技术栈矛盾和性能目标问题

## 1. 架构概述

### 1.1 架构目标
- **可用性优先**: 核心功能不依赖系统权限
- **渐进增强**: 高级功能可选启用
- **性能现实**: 基于Electron应用实际限制设定目标
- **跨平台一致**: 统一的用户体验，平台特定功能可选

### 1.2 技术栈选择 (修正)

#### 核心技术栈
- **应用框架**: Electron 27+
- **前端框架**: React 18+ with TypeScript
- **构建工具**: Vite 5.x + electron-builder (统一选择)
- **状态管理**: Redux Toolkit + RTK Query
- **UI组件库**: Ant Design 5.x
- **包管理**: pnpm (性能优化)

#### 数据层技术
- **本地数据库**: SQLite 3.x (通过better-sqlite3)
- **数据加密**: 可选启用 (影响性能时可关闭)
- **缓存层**: 分层缓存策略

#### 系统集成技术 (修正)
- **基础功能**: 无需特殊权限
- **增强功能**: 需要用户明确授权
  - Windows: PowerShell脚本监控
  - macOS: AppleScript + Accessibility API
- **降级策略**: 权限拒绝时的替代方案

## 2. 修正的性能目标

### 2.1 现实的性能指标
- **启动时间**: 冷启动 ≤ 8秒，热启动 ≤ 3秒
- **响应时间**: 
  - 基础UI交互 ≤ 200ms
  - 数据库查询 ≤ 500ms
  - 复杂计算 ≤ 2秒
- **资源占用**: 
  - 基础内存 ≤ 300MB (Electron现实基线)
  - 启用监控后 ≤ 400MB
  - 空闲CPU ≤ 10% (包含监控开销)

### 2.2 可扩展性限制
- **目标树节点**: 500个节点流畅，1000个节点需虚拟化
- **任务数量**: 5000个任务，超出需分页
- **历史数据**: 1年数据，自动归档旧数据

## 3. 分层权限架构

### 3.1 核心层 (无需权限)
```typescript
interface CoreFeatures {
  goalManagement: true;
  taskManagement: true;
  pomodoroTimer: true;
  basicReminders: true;
  localDataStorage: true;
}
```

### 3.2 增强层 (需要权限)
```typescript
interface EnhancedFeatures {
  systemMonitoring?: boolean;  // 可选
  deepFocusMode?: boolean;     // 可选
  smartReminders?: boolean;    // 可选
}

interface PermissionStrategy {
  requestGradually: true;      // 渐进式权限申请
  gracefulDegradation: true;   // 拒绝时降级
  userEducation: true;         // 说明权限用途
}
```

### 3.3 平台差异处理
```typescript
interface PlatformSpecific {
  windows: {
    monitoring: 'powershell-script';
    permissions: 'process-access';
    fallback: 'user-manual-report';
  };
  macos: {
    monitoring: 'applescript + accessibility';
    permissions: 'accessibility + screen-recording';
    fallback: 'timer-based-reminder';
  };
}
```

## 4. 数据流架构 (新增)

### 4.1 实时数据同步
```typescript
// 番茄钟状态同步
interface TimerStateFlow {
  MainProcess: PomodoroService;
  IPC: TimerEvents;
  RendererProcess: TimerReducer;
  Components: TimerDisplay[];
}

// 事件流
const timerEvents = {
  'timer:started' → 'ui:update-display',
  'timer:paused' → 'ui:show-pause-state',
  'timer:completed' → 'ui:show-completion + notification'
};
```

### 4.2 数据持久化策略
```typescript
interface DataPersistence {
  immediate: ['timer-state', 'user-input'];
  batched: ['activity-logs', 'analytics-data'];
  encrypted: ['sensitive-settings']; // 可选
  cached: ['frequent-queries'];
}
```

## 5. 错误处理和降级策略

### 5.1 权限被拒绝的处理
```typescript
class PermissionFallback {
  async handleMonitoringDenied(): Promise<void> {
    // 切换到手动模式
    this.enableManualDistrationLogging();
    this.showUserGuidance('manual-focus-tips');
    this.disableAutoFeatures(['app-blocking', 'activity-detection']);
  }

  async handleNotificationDenied(): Promise<void> {
    // 使用应用内提醒
    this.switchToInAppNotifications();
    this.enableVisualAlerts();
  }
}
```

### 5.2 性能降级策略
```typescript
class PerformanceDegradation {
  async handleLowMemory(): Promise<void> {
    // 减少缓存
    this.clearNonEssentialCache();
    this.enableDataPagination();
    this.reduceVisualizationComplexity();
  }

  async handleSlowResponse(): Promise<void> {
    // 异步加载
    this.enableProgressiveLoading();
    this.showLoadingStates();
    this.optimizeQueries();
  }
}
```

## 6. 修正的构建配置

### 6.1 统一的构建流程
```json
{
  "scripts": {
    "dev": "concurrently \"npm run dev:main\" \"npm run dev:renderer\"",
    "dev:main": "electron -r ts-node/register src/main/main.ts",
    "dev:renderer": "vite dev",
    "build": "npm run build:renderer && npm run build:main",
    "build:main": "tsc -p tsconfig.main.json",
    "build:renderer": "vite build",
    "package": "electron-builder"
  }
}
```

### 6.2 Vite配置
```typescript
// vite.config.ts
export default defineConfig({
  plugins: [react()],
  build: {
    outDir: 'dist/renderer',
    rollupOptions: {
      external: ['electron']
    }
  },
  optimizeDeps: {
    exclude: ['electron']
  }
});
```

## 7. 监控和诊断

### 7.1 性能监控
```typescript
class PerformanceMonitor {
  private metrics = {
    memoryUsage: () => process.memoryUsage(),
    cpuUsage: () => process.cpuUsage(),
    responseTime: new Map<string, number>()
  };

  async checkHealth(): Promise<HealthStatus> {
    const memory = this.metrics.memoryUsage();
    const isHealthy = memory.heapUsed < 300 * 1024 * 1024; // 300MB
    
    return {
      status: isHealthy ? 'healthy' : 'degraded',
      memory: memory.heapUsed,
      suggestions: isHealthy ? [] : this.getOptimizationSuggestions()
    };
  }
}
```

## 8. 架构演进路径 (修正)

### 8.1 MVP阶段 (现实目标)
- 核心功能稳定运行
- 基础性能指标达标
- 无权限依赖的功能完整

### 8.2 增强阶段 (V1.1)
- 可选系统集成功能
- 用户可控的权限申请
- 性能优化和降级策略

### 8.3 优化阶段 (V1.2)
- 基于实际使用数据的性能调优
- 智能化的用户体验优化
- 扩展性架构重构

---

**修正原则**: 
1. 技术选择保持一致性
2. 性能目标基于现实限制
3. 功能分层，渐进增强
4. 权限可选，降级友好 