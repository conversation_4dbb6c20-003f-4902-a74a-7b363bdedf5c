# FocusOS 技术实施指南

**版本：** 1.0  
**日期：** 2025年06月23日  
**目标：** 为FocusOS中期和长期功能开发提供详细的技术实施指导

## 🏗 系统架构演进

### 当前架构分析

**现有技术栈：**
- **前端**：React + TypeScript + Vite
- **后端**：Node.js + Electron
- **数据库**：SQLite (本地存储)
- **UI框架**：Ant Design
- **状态管理**：React Context + Hooks
- **AI集成**：OpenAI API + 自定义提示工程

**架构优势：**
- 轻量级，启动快速
- 跨平台兼容性好
- 开发效率高
- 本地数据安全

**架构限制：**
- 缺乏云同步能力
- AI功能依赖外部服务
- 系统级监控能力有限
- 扩展性受限

### 目标架构设计

```mermaid
graph TB
    A[FocusOS Client] --> B[Local Services Layer]
    A --> C[Cloud Services Layer]
    
    B --> D[Application Monitor]
    B --> E[Focus Engine]
    B --> F[Data Analytics]
    B --> G[Local AI Cache]
    
    C --> H[User Sync Service]
    C --> I[AI Processing Service]
    C --> J[Analytics Service]
    C --> K[Notification Service]
    
    D --> L[System APIs]
    E --> M[Browser Extensions]
    F --> N[Local Database]
    G --> O[Model Cache]
```

## 🔍 核心功能技术实施

### 1. 智能专注力监控系统

#### 1.1 应用监控服务

**技术选型：**
- **macOS**: `node-mac-permissions` + AppleScript
- **Windows**: `node-ffi-napi` + Win32 API
- **Linux**: `x11` + `wmctrl`

**实现示例：**
```typescript
// packages/main/src/services/ApplicationMonitor.ts
import { exec } from 'child_process';
import { promisify } from 'util';

const execAsync = promisify(exec);

export class ApplicationMonitor {
  private isMonitoring = false;
  private currentApp: string = '';
  private blacklist: Set<string> = new Set();
  private whitelist: Set<string> = new Set();
  
  async startMonitoring(): Promise<void> {
    this.isMonitoring = true;
    this.monitorLoop();
  }
  
  private async monitorLoop(): Promise<void> {
    while (this.isMonitoring) {
      try {
        const activeApp = await this.getCurrentActiveApp();
        
        if (activeApp !== this.currentApp) {
          await this.handleAppSwitch(this.currentApp, activeApp);
          this.currentApp = activeApp;
        }
        
        await this.sleep(1000); // 检查间隔1秒
      } catch (error) {
        console.error('监控循环错误:', error);
      }
    }
  }
  
  private async getCurrentActiveApp(): Promise<string> {
    if (process.platform === 'darwin') {
      return this.getMacActiveApp();
    } else if (process.platform === 'win32') {
      return this.getWindowsActiveApp();
    } else {
      return this.getLinuxActiveApp();
    }
  }
  
  private async getMacActiveApp(): Promise<string> {
    const script = `
      tell application "System Events"
        set frontApp to name of first application process whose frontmost is true
        return frontApp
      end tell
    `;
    
    const { stdout } = await execAsync(`osascript -e '${script}'`);
    return stdout.trim();
  }
  
  private async getWindowsActiveApp(): Promise<string> {
    // 使用PowerShell获取活动窗口
    const script = `
      Add-Type @"
        using System;
        using System.Runtime.InteropServices;
        public class Win32 {
          [DllImport("user32.dll")]
          public static extern IntPtr GetForegroundWindow();
          [DllImport("user32.dll")]
          public static extern int GetWindowText(IntPtr hWnd, System.Text.StringBuilder text, int count);
        }
      "@
      $handle = [Win32]::GetForegroundWindow()
      $title = New-Object System.Text.StringBuilder 256
      [Win32]::GetWindowText($handle, $title, 256)
      $title.ToString()
    `;
    
    const { stdout } = await execAsync(`powershell -Command "${script}"`);
    return stdout.trim();
  }
  
  private async handleAppSwitch(fromApp: string, toApp: string): Promise<void> {
    // 记录应用切换事件
    const switchEvent = {
      timestamp: Date.now(),
      fromApp,
      toApp,
      isDistraction: this.blacklist.has(toApp)
    };
    
    // 保存到数据库
    await this.saveAppSwitchEvent(switchEvent);
    
    // 如果切换到黑名单应用，触发干预
    if (this.blacklist.has(toApp)) {
      await this.triggerDistractionIntervention(toApp);
    }
  }
  
  private async triggerDistractionIntervention(appName: string): Promise<void> {
    // 显示干预弹窗
    const { ipcMain } = await import('electron');
    ipcMain.emit('show-distraction-warning', {
      appName,
      message: `检测到您正在使用 ${appName}，这可能会影响专注。`,
      options: ['返回工作', '允许5分钟', '添加到白名单']
    });
  }
}
```

#### 1.2 浏览器扩展开发

**Chrome扩展架构：**
```javascript
// manifest.json
{
  "manifest_version": 3,
  "name": "FocusOS Browser Monitor",
  "version": "1.0",
  "permissions": [
    "activeTab",
    "storage",
    "background",
    "tabs"
  ],
  "background": {
    "service_worker": "background.js"
  },
  "content_scripts": [{
    "matches": ["<all_urls>"],
    "js": ["content.js"]
  }],
  "action": {
    "default_popup": "popup.html"
  }
}

// background.js
class BrowserMonitor {
  constructor() {
    this.setupEventListeners();
    this.connectToMainApp();
  }
  
  setupEventListeners() {
    chrome.tabs.onActivated.addListener(this.handleTabSwitch.bind(this));
    chrome.tabs.onUpdated.addListener(this.handleTabUpdate.bind(this));
    chrome.windows.onFocusChanged.addListener(this.handleWindowFocus.bind(this));
  }
  
  async handleTabSwitch(activeInfo) {
    const tab = await chrome.tabs.get(activeInfo.tabId);
    const url = new URL(tab.url);
    
    // 检查是否为分心网站
    const isDistraction = await this.checkIfDistraction(url.hostname);
    
    if (isDistraction) {
      await this.triggerDistractionWarning(tab);
    }
    
    // 发送数据到主应用
    this.sendToMainApp({
      type: 'tab_switch',
      url: tab.url,
      title: tab.title,
      timestamp: Date.now()
    });
  }
  
  async checkIfDistraction(hostname) {
    const { blacklist } = await chrome.storage.sync.get(['blacklist']);
    return blacklist && blacklist.includes(hostname);
  }
  
  connectToMainApp() {
    // 通过WebSocket连接主应用
    this.ws = new WebSocket('ws://localhost:8080/browser-monitor');
    
    this.ws.onopen = () => {
      console.log('已连接到FocusOS主应用');
    };
    
    this.ws.onmessage = (event) => {
      const data = JSON.parse(event.data);
      this.handleMainAppMessage(data);
    };
  }
  
  sendToMainApp(data) {
    if (this.ws && this.ws.readyState === WebSocket.OPEN) {
      this.ws.send(JSON.stringify(data));
    }
  }
}

new BrowserMonitor();
```

### 2. AI驱动的目标分解优化

#### 2.1 增强的AI分解引擎

```typescript
// packages/renderer/src/services/EnhancedAIService.ts
export class EnhancedAIService {
  private conversationHistory: ConversationMessage[] = [];
  private userProfile: UserProfile;
  
  async decomposeGoalWithContext(
    goal: Goal,
    userHistory: UserHistory,
    preferences: UserPreferences
  ): Promise<DecompositionResult> {
    // 1. 构建个性化上下文
    const context = await this.buildPersonalizedContext(goal, userHistory, preferences);
    
    // 2. 生成初始分解
    const initialDecomposition = await this.generateInitialDecomposition(goal, context);
    
    // 3. 质量评估和优化
    const optimizedDecomposition = await this.optimizeDecomposition(initialDecomposition, context);
    
    // 4. 生成多个方案供用户选择
    const alternatives = await this.generateAlternativeDecompositions(optimizedDecomposition, context);
    
    return {
      primary: optimizedDecomposition,
      alternatives,
      confidence: this.calculateConfidence(optimizedDecomposition),
      suggestions: this.generateImprovementSuggestions(optimizedDecomposition)
    };
  }
  
  private async buildPersonalizedContext(
    goal: Goal,
    userHistory: UserHistory,
    preferences: UserPreferences
  ): Promise<PersonalizationContext> {
    // 分析用户历史成功模式
    const successPatterns = this.analyzeSuccessPatterns(userHistory);
    
    // 识别用户偏好的任务粒度
    const preferredGranularity = this.analyzeTaskGranularity(userHistory);
    
    // 提取有效的分解策略
    const effectiveStrategies = this.extractEffectiveStrategies(userHistory);
    
    return {
      successPatterns,
      preferredGranularity,
      effectiveStrategies,
      workingStyle: preferences.workingStyle,
      timeConstraints: preferences.timeConstraints,
      skillLevel: this.assessSkillLevel(goal.domain, userHistory)
    };
  }
  
  private async generateInitialDecomposition(
    goal: Goal,
    context: PersonalizationContext
  ): Promise<DecompositionTree> {
    const prompt = this.buildEnhancedPrompt(goal, context);
    
    const response = await this.callAIService({
      model: 'gpt-4',
      messages: [
        {
          role: 'system',
          content: this.getSystemPrompt(context)
        },
        {
          role: 'user',
          content: prompt
        }
      ],
      temperature: 0.7,
      max_tokens: 2000
    });
    
    return this.parseDecompositionResponse(response);
  }
  
  private buildEnhancedPrompt(goal: Goal, context: PersonalizationContext): string {
    return `
基于以下信息，请帮我分解目标：

目标信息：
- 名称：${goal.name}
- 描述：${goal.description}
- 类型：${goal.type}
- 截止日期：${goal.deadline}
- 核心驱动力：${goal.whyPower}

用户特征：
- 工作风格：${context.workingStyle}
- 偏好的任务粒度：${context.preferredGranularity}
- 在该领域的技能水平：${context.skillLevel}
- 时间约束：${context.timeConstraints}

历史成功模式：
${context.successPatterns.map(pattern => `- ${pattern.description}`).join('\n')}

请按照以下要求进行分解：
1. 采用第一性原理思维，从根本需求出发
2. 考虑用户的工作风格和偏好
3. 确保每个任务都符合SMART原则
4. 任务粒度适合用户的习惯（${context.preferredGranularity}）
5. 考虑任务间的依赖关系
6. 提供清晰的里程碑和检查点

请以JSON格式返回分解结果。
    `;
  }
  
  async startConversationalDecomposition(goal: Goal): Promise<ConversationSession> {
    const sessionId = this.generateSessionId();
    
    const initialQuestion = await this.generateInitialQuestion(goal);
    
    const session: ConversationSession = {
      id: sessionId,
      goal,
      messages: [{
        role: 'assistant',
        content: initialQuestion,
        timestamp: Date.now()
      }],
      currentDecomposition: null,
      status: 'active'
    };
    
    this.conversationHistory.push(session);
    return session;
  }
  
  async processConversationResponse(
    sessionId: string,
    userResponse: string
  ): Promise<ConversationStep> {
    const session = this.findSession(sessionId);
    if (!session) {
      throw new Error('会话不存在');
    }
    
    // 添加用户回应到历史
    session.messages.push({
      role: 'user',
      content: userResponse,
      timestamp: Date.now()
    });
    
    // 分析用户回应
    const analysis = await this.analyzeUserResponse(userResponse, session);
    
    // 更新分解结构
    if (analysis.decompositionUpdate) {
      session.currentDecomposition = this.updateDecomposition(
        session.currentDecomposition,
        analysis.decompositionUpdate
      );
    }
    
    // 生成下一步
    const nextStep = await this.generateNextStep(analysis, session);
    
    // 添加AI回应到历史
    session.messages.push({
      role: 'assistant',
      content: nextStep.message,
      timestamp: Date.now()
    });
    
    return nextStep;
  }
}
```

#### 2.2 智能任务建议系统

```typescript
// packages/renderer/src/services/TaskSuggestionService.ts
export class TaskSuggestionService {
  async generateTaskSuggestions(
    goal: Goal,
    existingTasks: Task[],
    userContext: UserContext
  ): Promise<TaskSuggestion[]> {
    const suggestions: TaskSuggestion[] = [];
    
    // 1. 基于目标类型的模板建议
    const templateSuggestions = await this.getTemplateSuggestions(goal.type);
    
    // 2. 基于用户历史的个性化建议
    const personalizedSuggestions = await this.getPersonalizedSuggestions(goal, userContext);
    
    // 3. 基于当前进度的动态建议
    const progressBasedSuggestions = await this.getProgressBasedSuggestions(goal, existingTasks);
    
    // 4. 基于时间和优先级的智能建议
    const timeSensitiveSuggestions = await this.getTimeSensitiveSuggestions(goal, userContext);
    
    return [
      ...templateSuggestions,
      ...personalizedSuggestions,
      ...progressBasedSuggestions,
      ...timeSensitiveSuggestions
    ].sort((a, b) => b.relevanceScore - a.relevanceScore);
  }
  
  private async getPersonalizedSuggestions(
    goal: Goal,
    userContext: UserContext
  ): Promise<TaskSuggestion[]> {
    // 分析用户在类似目标上的成功任务模式
    const similarGoals = await this.findSimilarGoals(goal, userContext.goalHistory);
    const successfulTasks = this.extractSuccessfulTasks(similarGoals);
    
    return successfulTasks.map(task => ({
      id: this.generateId(),
      title: this.adaptTaskTitle(task.title, goal),
      description: this.adaptTaskDescription(task.description, goal),
      estimatedTime: task.averageCompletionTime,
      priority: this.calculatePriority(task, goal),
      relevanceScore: this.calculateRelevance(task, goal),
      source: 'user_history',
      reasoning: `基于您在"${task.parentGoal.name}"中的成功经验`
    }));
  }
  
  async suggestTaskOptimizations(tasks: Task[]): Promise<OptimizationSuggestion[]> {
    const suggestions: OptimizationSuggestion[] = [];
    
    for (const task of tasks) {
      // 检查任务粒度
      if (task.estimatedTime > 4) { // 超过4小时的任务
        suggestions.push({
          type: 'split_task',
          taskId: task.id,
          title: '建议拆分大任务',
          description: `任务"${task.title}"预计需要${task.estimatedTime}小时，建议拆分为更小的任务单元`,
          impact: 'high',
          implementation: await this.generateTaskSplitSuggestion(task)
        });
      }
      
      // 检查任务依赖
      const dependencies = await this.analyzeDependencies(task, tasks);
      if (dependencies.length > 0) {
        suggestions.push({
          type: 'reorder_tasks',
          taskId: task.id,
          title: '优化任务顺序',
          description: `建议先完成依赖任务：${dependencies.map(d => d.title).join('、')}`,
          impact: 'medium',
          implementation: this.generateReorderSuggestion(task, dependencies)
        });
      }
      
      // 检查时间估算准确性
      const estimationAccuracy = await this.checkEstimationAccuracy(task);
      if (estimationAccuracy.deviation > 0.5) {
        suggestions.push({
          type: 'adjust_estimation',
          taskId: task.id,
          title: '调整时间估算',
          description: `基于历史数据，建议将预估时间调整为${estimationAccuracy.suggestedTime}小时`,
          impact: 'low',
          implementation: { newEstimatedTime: estimationAccuracy.suggestedTime }
        });
      }
    }
    
    return suggestions;
  }
}
```

### 3. Focus Shield深度专注模式

#### 3.1 系统级应用拦截

```typescript
// packages/main/src/services/FocusShieldService.ts
export class FocusShieldService {
  private isActive = false;
  private blockedApps: Set<string> = new Set();
  private originalSettings: SystemSettings;
  private blockingProcesses: Map<string, NodeJS.Timeout> = new Map();
  
  async activateDeepFocus(config: FocusShieldConfig): Promise<void> {
    if (this.isActive) {
      throw new Error('Focus Shield已经激活');
    }
    
    try {
      // 1. 保存当前系统状态
      this.originalSettings = await this.captureSystemSettings();
      
      // 2. 应用专注环境设置
      await this.applyFocusEnvironment(config);
      
      // 3. 启动应用监控和拦截
      await this.startApplicationBlocking(config.blockedApps);
      
      // 4. 配置通知管理
      await this.configureNotificationSilencing(config.allowedNotifications);
      
      // 5. 启动界面简化模式
      await this.activateMinimalInterface();
      
      this.isActive = true;
      
      // 6. 设置自动退出定时器（如果配置了）
      if (config.duration) {
        setTimeout(() => {
          this.deactivateDeepFocus();
        }, config.duration * 60 * 1000);
      }
      
    } catch (error) {
      // 如果激活失败，恢复原始状态
      await this.restoreSystemSettings();
      throw error;
    }
  }
  
  private async startApplicationBlocking(blockedApps: string[]): Promise<void> {
    this.blockedApps = new Set(blockedApps);
    
    if (process.platform === 'darwin') {
      await this.startMacOSBlocking();
    } else if (process.platform === 'win32') {
      await this.startWindowsBlocking();
    } else {
      await this.startLinuxBlocking();
    }
  }
  
  private async startMacOSBlocking(): Promise<void> {
    // 使用LaunchServices API监控应用启动
    const { spawn } = await import('child_process');
    
    // 创建AppleScript监控脚本
    const monitorScript = `
      on run
        repeat
          try
            tell application "System Events"
              set runningApps to name of every application process
              repeat with appName in runningApps
                if "${Array.from(this.blockedApps).join('" contains appName or "')}" contains appName then
                  tell application appName to quit
                  display notification "应用 " & appName & " 已被Focus Shield阻止" with title "FocusOS"
                end if
              end repeat
            end tell
          end try
          delay 2
        end repeat
      end run
    `;
    
    const process = spawn('osascript', ['-e', monitorScript]);
    this.blockingProcesses.set('macos_monitor', process);
  }
  
  private async startWindowsBlocking(): Promise<void> {
    // 使用PowerShell监控和终止进程
    const { spawn } = await import('child_process');
    
    const monitorScript = `
      while ($true) {
        $blockedApps = @(${Array.from(this.blockedApps).map(app => `"${app}"`).join(', ')})
        
        foreach ($app in $blockedApps) {
          $processes = Get-Process -Name $app -ErrorAction SilentlyContinue
          if ($processes) {
            $processes | Stop-Process -Force
            [System.Windows.Forms.MessageBox]::Show("应用 $app 已被Focus Shield阻止", "FocusOS")
          }
        }
        
        Start-Sleep -Seconds 2
      }
    `;
    
    const process = spawn('powershell', ['-Command', monitorScript]);
    this.blockingProcesses.set('windows_monitor', process);
  }
  
  async handleBlockedAppAttempt(appName: string): Promise<BlockingAction> {
    // 记录拦截事件
    await this.logBlockingEvent({
      appName,
      timestamp: Date.now(),
      action: 'blocked'
    });
    
    // 显示拦截通知
    const { dialog } = await import('electron');
    const result = await dialog.showMessageBox({
      type: 'warning',
      title: 'Focus Shield',
      message: `应用 "${appName}" 已被阻止`,
      detail: '您正在深度专注模式中。是否要允许此应用？',
      buttons: ['返回专注', '允许5分钟', '允许本次', '退出专注模式'],
      defaultId: 0,
      cancelId: 0
    });
    
    switch (result.response) {
      case 0: // 返回专注
        return { action: 'block', duration: 0 };
      case 1: // 允许5分钟
        return { action: 'allow', duration: 5 * 60 * 1000 };
      case 2: // 允许本次
        return { action: 'allow', duration: -1 };
      case 3: // 退出专注模式
        await this.deactivateDeepFocus();
        return { action: 'exit_focus', duration: 0 };
      default:
        return { action: 'block', duration: 0 };
    }
  }
  
  async deactivateDeepFocus(): Promise<void> {
    if (!this.isActive) {
      return;
    }
    
    try {
      // 1. 停止应用拦截
      this.stopApplicationBlocking();
      
      // 2. 恢复系统设置
      await this.restoreSystemSettings();
      
      // 3. 恢复通知设置
      await this.restoreNotificationSettings();
      
      // 4. 退出简化界面模式
      await this.deactivateMinimalInterface();
      
      this.isActive = false;
      
      // 5. 记录专注会话结束
      await this.logFocusSessionEnd();
      
    } catch (error) {
      console.error('退出Focus Shield时发生错误:', error);
    }
  }
  
  private stopApplicationBlocking(): void {
    for (const [name, process] of this.blockingProcesses) {
      if (process && !process.killed) {
        process.kill();
      }
    }
    this.blockingProcesses.clear();
  }
}
```

这个技术实施指南为FocusOS的中期和长期发展提供了详细的技术路径。每个功能都有具体的代码示例和实现策略，可以作为开发团队的技术参考文档。

主要特点：
1. **渐进式架构演进** - 从当前简单架构逐步向微服务架构演进
2. **跨平台兼容性** - 考虑了Windows、macOS、Linux的不同实现方式
3. **AI集成优化** - 提供了更智能的目标分解和任务建议
4. **系统级集成** - 实现了深度的系统监控和控制功能
5. **用户体验优先** - 所有技术实现都围绕用户体验优化

这个指南可以帮助您按照优先级逐步实施各项功能，确保FocusOS能够成为真正智能的专注力提升工具。
