# FocusOS AI 调试日志系统使用指南

## 概述

FocusOS现在集成了完整的AI调试日志系统，提供详细的AI API调用追踪和调试信息，帮助开发者和用户快速定位和解决AI服务相关问题。

## 功能特性

### 🎯 核心功能
- **完整的AI API调用追踪** - 记录每个AI请求的完整生命周期
- **多层次日志级别** - 从ERROR到VERBOSE的5个级别控制
- **智能响应处理** - 支持推理模型（如DeepSeek R1）的special格式
- **性能监控** - 自动记录响应时间和Token使用情况
- **错误诊断** - 详细的错误信息和上下文记录
- **统一格式** - 主进程和渲染器进程的一致日志格式

### 📊 支持的AI服务
- **AITestService** - AI提供商连接测试
- **AIDecompositionService** - 目标分解服务
- **所有AI提供商** - OpenAI、OpenRouter、Google AI、Anthropic等

## 日志级别说明

| 级别 | 数值 | 描述 | 适用场景 |
|------|------|------|----------|
| ERROR | 0 | 只记录错误信息 | 生产环境故障排查 |
| WARN | 1 | 记录警告和错误 | 生产环境监控 |
| INFO | 2 | 记录基本操作信息 | 生产环境默认（推荐） |
| DEBUG | 3 | 记录详细调试信息 | 开发环境默认 |
| VERBOSE | 4 | 记录最详细信息 | 深度故障排查 |

## 配置方法

### 环境变量配置
```bash
# 设置日志级别
export AI_LOG_LEVEL=3  # DEBUG级别

# 开发环境
export NODE_ENV=development  # 自动启用DEBUG级别

# 生产环境
export NODE_ENV=production   # 自动使用INFO级别
```

### 代码中动态配置
```typescript
import { AILogConfig } from './config/aiLogConfig';

// 启用开发模式调试
AILogConfig.enableDevMode();

// 启用生产模式
AILogConfig.enableProdMode();

// 启用详细模式（故障排查）
AILogConfig.enableVerboseMode();

// 自定义级别
AILogConfig.setLogLevel(AILogLevel.DEBUG);

// 显示帮助信息
AILogConfig.showHelp();
```

## 日志输出示例

### AI API请求日志
```
🤖📤 [2025-01-26 10:30:15.123] AI API 请求开始
🔍 请求ID: a1b2c3d4
🏢 提供商: OpenRouter
🧠 模型: deepseek/deepseek-r1-0528:free
🔍 端点: https://openrouter.ai/api/v1/chat/completions
⏱️ 最大tokens: 4096
🔍 温度: 0.7
🔍 上下文: AI分解服务
💭 提示词预览: 请帮我分解以下目标为具体的子目标和任务...
```

### AI API响应日志
```
🤖📥 [2025-01-26 10:30:16.789] AI API 响应 ✅
🔍 请求ID: a1b2c3d4
🔍 状态: 成功
🔍 HTTP状态码: 200
⏱️ 响应时间: 1666ms
🔍 响应长度: 2456 字符
📊 Token使用情况:
  - 提示词tokens: 1234
  - 完成tokens: 2456
  - 总tokens: 3690
🧮 推理过程预览: 用户希望将一个大目标分解为可执行的子任务...
📝 响应内容预览: { "subGoals": [{"name": "市场调研"...
```

### 操作总结日志
```
✅ [2025-01-26 10:30:16.789] OpenRouter/deepseek-r1-0528 调用成功 (a1b2c3d4) - 1666ms
```

### 错误日志
```
❌ [2025-01-26 10:30:20.123] AI错误: OpenRouter API调用失败
❌ 详细错误: AxiosError: Request failed with status code 401
🔍 上下文: {provider: "OpenRouter", modelId: "deepseek-r1-0528"}
```

## 特殊功能

### 推理模型支持
自动检测和处理推理模型（如DeepSeek R1）的特殊响应格式：
```
🔍 检测到推理模型响应，使用reasoning字段
🧮 推理过程预览: 这是一个复杂的目标，需要分解为以下几个步骤...
```

### Token使用监控
自动记录Token使用情况，帮助优化成本：
```
📊 Token使用情况:
  - 提示词tokens: 1234
  - 完成tokens: 2456  
  - 总tokens: 3690
```

### 响应截断处理
检测和处理被截断的AI响应：
```
⚠️ Google AI响应被截断 (MAX_TOKENS)
🔍 截断响应详情: {responseLength: 4096, finishReason: "MAX_TOKENS"}
```

## 在浏览器控制台中查看日志

### 渲染器进程日志
在浏览器开发者工具的Console标签中可以看到：
```
🤖📤 [2025-01-26 10:30:15.123] AI操作请求开始
⚙️ 操作: testAIProvider
🔍 请求ID: R1g6k8m3n9
🏢 提供商: OpenRouter
```

### 主进程日志
在Electron主进程控制台中可以看到：
```
🤖 AI日志系统已初始化 - 级别: DEBUG (3)
🤖 环境: development
🤖📤 [2025-01-26 10:30:15.123] AI API 请求开始
```

## 故障排查指南

### 常见问题

1. **看不到日志输出**
   ```bash
   # 检查日志级别
   export AI_LOG_LEVEL=3
   # 或在代码中
   AILogConfig.enableDevMode();
   ```

2. **只看到部分日志**
   ```bash
   # 启用详细模式
   export AI_LOG_LEVEL=4
   # 或
   AILogConfig.enableVerboseMode();
   ```

3. **DeepSeek R1模型响应为空**
   - 现在会自动检测推理模型并使用reasoning字段
   - 日志中会显示"检测到推理模型响应"

### 调试技巧

1. **启用详细日志进行故障排查**
   ```typescript
   AILogConfig.enableVerboseMode();
   ```

2. **检查完整的请求和响应**
   ```typescript
   // VERBOSE级别会显示完整的prompt和响应内容
   ```

3. **分析响应时间**
   ```typescript
   // 所有日志都包含响应时间，帮助分析性能问题
   ```

## 最佳实践

### 开发环境
- 使用DEBUG或VERBOSE级别获取完整信息
- 关注Token使用情况优化成本
- 检查响应时间优化性能

### 生产环境
- 使用INFO级别平衡信息量和性能
- 出现问题时临时启用DEBUG级别
- 定期检查ERROR和WARN日志

### 代码集成
```typescript
// 在AI服务方法中
const requestId = AILogger.generateRequestId();
const requestInfo: AIRequestInfo = {
  requestId,
  provider: 'OpenRouter',
  modelId: 'gpt-4',
  endpoint: url,
  prompt: prompt,
  context: '用户操作'
};
AILogger.logRequest(requestInfo);

// 在响应处理中
const responseInfo: AIResponseInfo = {
  requestId,
  success: true,
  responseTime: Date.now() - startTime,
  content: responseText
};
AILogger.logResponse(responseInfo);
```

## 更新历史

- **v1.0** - 初始版本，支持基本的AI API调用日志
- **v1.1** - 添加推理模型支持（DeepSeek R1等）
- **v1.2** - 增强错误处理和Token使用监控
- **v1.3** - 添加渲染器进程日志支持
- **v1.4** - 完善配置管理和环境变量支持

## 支持

如果遇到问题或需要帮助，请：
1. 首先启用VERBOSE日志级别进行故障排查
2. 检查控制台中的完整错误信息
3. 提交Issue时请附上相关日志信息