import { test, expect } from '@playwright/test';

test.describe('FocusOS 音频通知手动验证', () => {
  test('手动验证音频通知修复', async ({ page }) => {
    console.log('=== 开始手动音频通知验证 ===');
    
    const consoleMessages: string[] = [];
    
    // 监听控制台消息
    page.on('console', msg => {
      const text = msg.text();
      consoleMessages.push(text);
      
      // 记录重要的音频和通知消息
      if (text.includes('🔔') || text.includes('🎵') || text.includes('AudioService') || 
          text.includes('NotificationService') || text.includes('playNotificationSound') ||
          text.includes('Audio file loaded') || text.includes('initialized') ||
          text.includes('sendNotification') || text.includes('音频')) {
        console.log('重要消息:', text);
      }
    });

    // 启动应用
    await page.goto('http://localhost:5174'); // 使用新端口
    await page.waitForTimeout(5000); // 等待应用完全加载
    
    // 点击页面以激活音频上下文
    await page.click('body');
    await page.waitForTimeout(1000);

    console.log('✅ 应用已加载，开始测试');

    // 导航到番茄钟页面
    try {
      await page.click('text=番茄钟');
      await page.waitForTimeout(3000);
      console.log('✅ 已导航到番茄钟页面');
    } catch (error) {
      console.log('⚠️ 导航到番茄钟页面失败，尝试其他方式');
    }

    // 等待服务初始化
    await page.waitForTimeout(2000);

    // 在浏览器控制台中手动执行测试
    await page.evaluate(() => {
      console.log('🧪 === 开始手动音频测试 ===');
      
      // 添加一个全局测试函数
      (window as any).testAudio = async () => {
        console.log('🔧 开始音频系统测试...');
        
        try {
          // 测试Web Audio API
          const AudioContextClass = window.AudioContext || (window as any).webkitAudioContext;
          if (AudioContextClass) {
            const ctx = new AudioContextClass();
            console.log('✅ AudioContext创建成功，状态:', ctx.state);
            
            // 播放一个简单的测试音
            const oscillator = ctx.createOscillator();
            const gainNode = ctx.createGain();
            
            oscillator.connect(gainNode);
            gainNode.connect(ctx.destination);
            
            oscillator.frequency.setValueAtTime(440, ctx.currentTime); // A4音符
            gainNode.gain.setValueAtTime(0.1, ctx.currentTime);
            gainNode.gain.exponentialRampToValueAtTime(0.01, ctx.currentTime + 0.5);
            
            oscillator.start(ctx.currentTime);
            oscillator.stop(ctx.currentTime + 0.5);
            
            console.log('🔊 播放了测试音频（440Hz，0.5秒）');
            
            setTimeout(() => {
              ctx.close();
              console.log('✅ AudioContext已关闭');
            }, 1000);
          } else {
            console.log('❌ Web Audio API不支持');
          }
          
          // 测试HTML5 Audio
          if (window.Audio) {
            console.log('🔧 测试HTML5 Audio...');
            const audio = new Audio('/sounds/system-alert.mp3');
            audio.volume = 0.3;
            
            audio.addEventListener('loadeddata', () => {
              console.log('✅ 音频文件加载成功');
            });
            
            audio.addEventListener('canplay', () => {
              console.log('✅ 音频可以播放');
              audio.play().then(() => {
                console.log('🔊 HTML5 Audio播放成功');
              }).catch((error: any) => {
                console.log('❌ HTML5 Audio播放失败:', error);
              });
            });
            
            audio.addEventListener('error', (error) => {
              console.log('❌ 音频加载失败:', error);
            });
            
            audio.load();
          } else {
            console.log('❌ HTML5 Audio不支持');
          }
          
        } catch (error) {
          console.error('❌ 音频测试失败:', error);
        }
      };
      
      // 立即执行测试
      (window as any).testAudio();
      
      console.log('🧪 手动音频测试已启动');
      console.log('💡 提示：如果听到声音，说明音频系统工作正常');
      console.log('💡 如果没有声音，请检查浏览器音量设置和权限');
    });

    // 等待音频测试完成
    await page.waitForTimeout(5000);

    // 分析控制台消息
    const audioLogs = consoleMessages.filter(msg => 
      msg.includes('AudioContext') ||
      msg.includes('音频') ||
      msg.includes('Audio') ||
      msg.includes('🔊') ||
      msg.includes('🔧') ||
      msg.includes('播放')
    );

    console.log('=== 音频测试结果分析 ===');
    console.log('音频相关日志数量:', audioLogs.length);
    
    if (audioLogs.length > 0) {
      console.log('✅ 检测到音频活动:');
      audioLogs.forEach(log => console.log('  -', log));
    } else {
      console.log('⚠️ 未检测到音频活动');
    }

    // 检查系统状态
    const systemStatus = await page.evaluate(() => {
      return {
        webAudioSupported: !!(window.AudioContext || (window as any).webkitAudioContext),
        htmlAudioSupported: !!window.Audio,
        notificationPermission: 'Notification' in window ? Notification.permission : 'not-supported',
        userAgent: navigator.userAgent.substring(0, 100) + '...'
      };
    });

    console.log('=== 系统状态 ===');
    console.log('Web Audio API支持:', systemStatus.webAudioSupported);
    console.log('HTML5 Audio支持:', systemStatus.htmlAudioSupported);
    console.log('通知权限:', systemStatus.notificationPermission);

    // 验证基本音频支持
    expect(systemStatus.webAudioSupported || systemStatus.htmlAudioSupported).toBe(true);

    // 如果有音频活动，测试通过
    if (audioLogs.length > 0) {
      console.log('🎉 音频系统测试通过！');
    } else {
      console.log('⚠️ 音频系统可能需要用户交互才能激活');
    }

    console.log('=== 手动验证完成 ===');
    console.log('💡 请手动检查是否听到了测试音频');
    console.log('💡 如果听到声音，说明音频通知修复成功');
  });

  test('验证音频文件完整性', async ({ page }) => {
    console.log('=== 验证音频文件完整性 ===');
    
    const soundFiles = [
      '/sounds/task-complete.mp3',
      '/sounds/pomodoro-complete.mp3',
      '/sounds/break-complete.mp3',
      '/sounds/goal-achieved.mp3',
      '/sounds/system-alert.mp3',
      '/sounds/gentle-reminder.mp3'
    ];

    let accessibleFiles = 0;
    const fileStatus: { [key: string]: number } = {};

    for (const soundFile of soundFiles) {
      try {
        const response = await page.goto(`http://localhost:5174${soundFile}`);
        const status = response?.status() || 0;
        fileStatus[soundFile] = status;
        
        if (status === 200) {
          console.log(`✅ ${soundFile} 可访问`);
          accessibleFiles++;
        } else {
          console.log(`❌ ${soundFile} 无法访问 (${status})`);
        }
      } catch (error) {
        console.log(`❌ ${soundFile} 访问出错:`, error);
        fileStatus[soundFile] = 0;
      }
    }

    console.log(`音频文件可访问性: ${accessibleFiles}/${soundFiles.length}`);
    
    // 返回主页
    await page.goto('http://localhost:5174');
    await page.waitForTimeout(1000);

    // 验证至少有一些音频文件可访问
    expect(accessibleFiles).toBeGreaterThan(0);
    
    console.log('✅ 音频文件完整性验证完成');
  });
});
