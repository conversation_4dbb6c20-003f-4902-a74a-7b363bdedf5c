import { test, expect } from '@playwright/test';

test.describe('Focus Shield 黑名单管理功能测试', () => {
  test.beforeEach(async ({ page }) => {
    // 启动Electron应用
    await page.goto('http://localhost:5173');
    await page.waitForTimeout(2000); // 等待应用加载
  });

  test('应该能够打开Focus Shield黑名单管理', async ({ page }) => {
    // 等待页面加载完成
    await page.waitForLoadState('networkidle');

    // 导航到设置页面
    await page.click('text=设置');
    await page.waitForTimeout(1000);

    // 点击Focus Shield选项卡
    await page.click('text=Focus Shield');
    await page.waitForTimeout(1000);

    // 检查是否有黑名单管理相关内容
    const blacklistContent = page.locator('text=黑名单').or(page.locator('text=黑名单管理'));
    await expect(blacklistContent.first()).toBeVisible({ timeout: 10000 });
  });

  test('应该能够切换所有tab页面', async ({ page }) => {
    // 导航到设置页面
    await page.click('text=设置');
    await page.waitForTimeout(1000);

    // 点击Focus Shield选项卡
    await page.click('text=Focus Shield');
    await page.waitForTimeout(1000);

    // 测试黑名单tab
    const blacklistTab = page.locator('text=黑名单').first();
    if (await blacklistTab.count() > 0) {
      await blacklistTab.click();
      await page.waitForTimeout(500);
      console.log('黑名单tab点击成功');
    }

    // 测试白名单tab
    const whitelistTab = page.locator('text=白名单').first();
    if (await whitelistTab.count() > 0) {
      await whitelistTab.click();
      await page.waitForTimeout(500);
      console.log('白名单tab点击成功');
    }

    // 测试预设模板tab
    const templatesTab = page.locator('text=预设模板').first();
    if (await templatesTab.count() > 0) {
      await templatesTab.click();
      await page.waitForTimeout(500);
      console.log('预设模板tab点击成功');
    }

    // 测试违规记录tab
    const violationsTab = page.locator('text=违规记录').first();
    if (await violationsTab.count() > 0) {
      await violationsTab.click();
      await page.waitForTimeout(500);
      console.log('违规记录tab点击成功');
    }
  });

  test('应该能够添加黑名单规则', async ({ page }) => {
    // 导航到设置页面
    await page.click('text=设置');
    await page.waitForTimeout(1000);

    // 点击Focus Shield选项卡
    await page.click('text=Focus Shield');
    await page.waitForTimeout(1000);

    // 点击添加黑名单规则按钮
    const addButton = page.locator('text=添加黑名单规则').or(page.locator('button:has-text("添加")'));
    if (await addButton.count() > 0) {
      await addButton.first().click();
      await page.waitForTimeout(500);
      console.log('添加黑名单规则按钮点击成功');
    }
  });

  test('应该能够测试x.com访问功能', async ({ page }) => {
    // 导航到设置页面
    await page.click('text=设置');
    await page.waitForTimeout(1000);

    // 点击Focus Shield选项卡
    await page.click('text=Focus Shield');
    await page.waitForTimeout(1000);

    // 切换到违规记录tab
    const violationsTab = page.locator('text=违规记录').first();
    if (await violationsTab.count() > 0) {
      await violationsTab.click();
      await page.waitForTimeout(500);
    }

    // 点击测试x.com访问按钮
    const testButton = page.locator('text=测试 x.com 访问').or(page.locator('button:has-text("测试")'));
    if (await testButton.count() > 0) {
      await testButton.first().click();
      await page.waitForTimeout(2000);
      console.log('x.com访问测试按钮点击成功');
    }
  });

  test('应该能够编辑现有规则', async ({ page }) => {
    // 导航到设置页面
    await page.click('text=设置');
    await page.waitForTimeout(1000);

    // 点击Focus Shield选项卡
    await page.click('text=Focus Shield');
    await page.waitForTimeout(1000);

    // 查找编辑按钮
    const editButtons = page.locator('button:has-text("编辑")').or(page.locator('[aria-label="编辑"]'));
    if (await editButtons.count() > 0) {
      await editButtons.first().click();
      await page.waitForTimeout(500);
      console.log('编辑按钮点击成功');
    }
  });

  test('应该能够切换规则状态', async ({ page }) => {
    // 导航到设置页面
    await page.click('text=设置');
    await page.waitForTimeout(1000);

    // 点击Focus Shield选项卡
    await page.click('text=Focus Shield');
    await page.waitForTimeout(1000);

    // 查找状态切换开关
    const switches = page.locator('.ant-switch').or(page.locator('button[role="switch"]'));
    if (await switches.count() > 0) {
      await switches.first().click();
      await page.waitForTimeout(500);
      console.log('状态切换开关点击成功');
    }
  });

  test('检查控制台是否有React key重复错误', async ({ page }) => {
    const consoleMessages: string[] = [];
    
    // 监听控制台消息
    page.on('console', msg => {
      const text = msg.text();
      consoleMessages.push(text);
      if (text.includes('Warning') || text.includes('Error')) {
        console.log('控制台消息:', text);
      }
    });

    // 导航到设置页面并打开Focus Shield
    await page.click('text=设置');
    await page.waitForTimeout(1000);

    // 点击Focus Shield选项卡
    await page.click('text=Focus Shield');
    await page.waitForTimeout(1000);

    // 切换所有tab
    const tabs = ['黑名单', '白名单', '预设模板', '违规记录'];
    for (const tabName of tabs) {
      const tab = page.locator(`text=${tabName}`).first();
      if (await tab.count() > 0) {
        await tab.click();
        await page.waitForTimeout(500);
      }
    }

    // 检查是否有key重复错误
    const keyErrors = consoleMessages.filter(msg => 
      msg.includes('same key') || 
      msg.includes('Keys should be unique') ||
      msg.includes('duplicate key')
    );

    if (keyErrors.length > 0) {
      console.error('发现React key重复错误:', keyErrors);
      throw new Error(`发现 ${keyErrors.length} 个key重复错误`);
    } else {
      console.log('未发现React key重复错误');
    }
  });
});
