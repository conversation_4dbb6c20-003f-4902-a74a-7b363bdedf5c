import { test, expect } from '@playwright/test';

test.describe('FocusOS 音频通知系统测试', () => {
  test.beforeEach(async ({ page }) => {
    // 启动应用
    await page.goto('http://localhost:5173');
    await page.waitForTimeout(2000); // 等待应用加载
  });

  test('检查音频设置页面功能', async ({ page }) => {
    const consoleMessages: string[] = [];
    
    // 监听控制台消息
    page.on('console', msg => {
      const text = msg.text();
      consoleMessages.push(text);
      console.log('控制台消息:', text);
    });

    // 导航到设置页面
    await page.click('text=设置');
    await page.waitForTimeout(1000);

    // 查找音频设置部分
    const audioSection = page.locator('text=音频设置').or(page.locator('text=提示音设置'));
    if (await audioSection.count() > 0) {
      await audioSection.first().click();
      await page.waitForTimeout(1000);
    }

    // 检查音频设置开关
    const audioSwitch = page.locator('text=启用提示音').locator('..').locator('.ant-switch');
    if (await audioSwitch.count() > 0) {
      console.log('找到音频设置开关');
      
      // 确保音频已启用
      const isEnabled = await audioSwitch.getAttribute('aria-checked');
      if (isEnabled !== 'true') {
        await audioSwitch.click();
        await page.waitForTimeout(500);
        console.log('已启用音频设置');
      }
    }

    // 测试各种音效
    const soundTypes = [
      'task-complete',
      'pomodoro-complete', 
      'break-complete',
      'goal-achieved',
      'system-alert',
      'gentle-reminder'
    ];

    for (const soundType of soundTypes) {
      console.log(`测试音效: ${soundType}`);
      
      // 查找对应的测试按钮
      const testButton = page.locator(`button:has-text("测试")`).or(
        page.locator(`[data-testid="test-${soundType}"]`)
      );
      
      if (await testButton.count() > 0) {
        await testButton.first().click();
        await page.waitForTimeout(1000);
        console.log(`${soundType} 测试按钮点击成功`);
      }
    }

    // 检查是否有音频相关的错误
    const audioErrors = consoleMessages.filter(msg => 
      msg.includes('Audio') || 
      msg.includes('sound') || 
      msg.includes('音频') ||
      msg.includes('Failed to') ||
      msg.includes('Error')
    );

    console.log('音频相关消息:', audioErrors);
  });

  test('测试番茄钟完成通知音频', async ({ page }) => {
    const consoleMessages: string[] = [];
    
    // 监听控制台消息
    page.on('console', msg => {
      const text = msg.text();
      consoleMessages.push(text);
      if (text.includes('🍅') || text.includes('AudioService') || text.includes('NotificationService')) {
        console.log('番茄钟音频消息:', text);
      }
    });

    // 导航到番茄钟页面
    await page.click('text=番茄钟');
    await page.waitForTimeout(2000);

    // 检查是否有音频初始化消息
    const audioInitMessages = consoleMessages.filter(msg => 
      msg.includes('AudioService') || msg.includes('initialized')
    );
    console.log('音频初始化消息:', audioInitMessages);

    // 尝试手动触发音频测试
    await page.evaluate(() => {
      // 在浏览器中执行音频测试
      console.log('🧪 手动测试音频服务');
      
      // 尝试获取AudioService实例
      if (window.AudioService) {
        console.log('✅ AudioService 可用');
        window.AudioService.testSound('pomodoro-complete').then(() => {
          console.log('✅ 手动音频测试成功');
        }).catch((error: any) => {
          console.error('❌ 手动音频测试失败:', error);
        });
      } else {
        console.log('❌ AudioService 不可用');
      }
    });

    await page.waitForTimeout(2000);

    // 检查音频播放相关的日志
    const audioPlayMessages = consoleMessages.filter(msg => 
      msg.includes('playSound') || 
      msg.includes('playNotificationSound') ||
      msg.includes('🎵') ||
      msg.includes('🔊') ||
      msg.includes('🔇')
    );

    console.log('音频播放消息:', audioPlayMessages);
  });

  test('检查音频文件加载状态', async ({ page }) => {
    // 检查音频文件是否可以访问
    const soundFiles = [
      '/sounds/task-complete.mp3',
      '/sounds/pomodoro-complete.mp3',
      '/sounds/break-complete.mp3',
      '/sounds/goal-achieved.mp3',
      '/sounds/system-alert.mp3',
      '/sounds/gentle-reminder.mp3'
    ];

    for (const soundFile of soundFiles) {
      const response = await page.goto(`http://localhost:5173${soundFile}`);
      const status = response?.status();
      console.log(`音频文件 ${soundFile}: HTTP ${status}`);
      
      if (status === 200) {
        console.log(`✅ ${soundFile} 可访问`);
      } else {
        console.log(`❌ ${soundFile} 无法访问 (${status})`);
      }
    }

    // 返回主页
    await page.goto('http://localhost:5173');
    await page.waitForTimeout(1000);
  });

  test('检查Web Audio API支持状态', async ({ page }) => {
    const audioStatus = await page.evaluate(() => {
      const status = {
        webAudioSupported: !!(window.AudioContext || (window as any).webkitAudioContext),
        htmlAudioSupported: !!window.Audio,
        notificationSupported: 'Notification' in window,
        notificationPermission: 'Notification' in window ? Notification.permission : 'not-supported',
        userAgent: navigator.userAgent,
        audioContextState: null as string | null
      };

      // 尝试创建AudioContext
      try {
        const AudioContextClass = window.AudioContext || (window as any).webkitAudioContext;
        if (AudioContextClass) {
          const ctx = new AudioContextClass();
          status.audioContextState = ctx.state;
          ctx.close();
        }
      } catch (error) {
        console.error('AudioContext创建失败:', error);
      }

      return status;
    });

    console.log('浏览器音频支持状态:', audioStatus);

    // 验证基本音频支持
    expect(audioStatus.webAudioSupported || audioStatus.htmlAudioSupported).toBe(true);
  });
});
