import { test, expect } from '@playwright/test';

test.describe('Focus Shield React Key唯一性稳定性测试', () => {
  test.beforeEach(async ({ page }) => {
    // 启动应用并导航到Focus Shield
    await page.goto('http://localhost:5173');
    await page.waitForTimeout(3000);
    
    // 导航到设置页面
    await page.click('text=设置');
    await page.waitForTimeout(1000);

    // 点击Focus Shield选项卡
    await page.click('text=Focus Shield');
    await page.waitForTimeout(1000);
  });

  test('快速连续切换tab页面不应产生key重复错误', async ({ page }) => {
    const consoleMessages: string[] = [];
    
    // 监听控制台消息
    page.on('console', msg => {
      const text = msg.text();
      consoleMessages.push(text);
      if (text.includes('Warning') || text.includes('Error') || text.includes('duplicate') || text.includes('same key')) {
        console.log('控制台消息:', text);
      }
    });

    // 快速连续切换所有tab页面多次
    const tabs = ['黑名单', '白名单', '预设模板', '违规记录'];
    
    for (let round = 0; round < 5; round++) {
      console.log(`第 ${round + 1} 轮快速切换测试`);
      
      for (const tabName of tabs) {
        const tab = page.locator(`text=${tabName}`).first();
        if (await tab.count() > 0) {
          await tab.click();
          await page.waitForTimeout(100); // 快速切换，只等待100ms
        }
      }
    }

    // 检查是否有key重复错误
    const keyErrors = consoleMessages.filter(msg => 
      msg.includes('same key') || 
      msg.includes('Keys should be unique') ||
      msg.includes('duplicate key') ||
      msg.includes('重复key')
    );

    expect(keyErrors.length).toBe(0);
    console.log('快速切换测试完成，未发现key重复错误');
  });

  test('多次添加和删除规则不应产生key重复错误', async ({ page }) => {
    const consoleMessages: string[] = [];
    
    // 监听控制台消息
    page.on('console', msg => {
      const text = msg.text();
      consoleMessages.push(text);
      if (text.includes('Warning') || text.includes('Error') || text.includes('duplicate') || text.includes('same key')) {
        console.log('控制台消息:', text);
      }
    });

    // 确保在黑名单tab
    const blacklistTab = page.locator('text=黑名单').first();
    if (await blacklistTab.count() > 0) {
      await blacklistTab.click();
      await page.waitForTimeout(500);
    }

    // 多次尝试打开添加规则对话框
    for (let i = 0; i < 3; i++) {
      console.log(`第 ${i + 1} 次添加规则测试`);
      
      const addButton = page.locator('text=添加黑名单规则').first();
      if (await addButton.count() > 0) {
        await addButton.click();
        await page.waitForTimeout(500);
        
        // 关闭对话框
        const cancelButton = page.locator('text=取消').or(page.locator('.ant-modal-close'));
        if (await cancelButton.first().count() > 0) {
          await cancelButton.first().click();
          await page.waitForTimeout(300);
        }
      }
    }

    // 检查是否有key重复错误
    const keyErrors = consoleMessages.filter(msg => 
      msg.includes('same key') || 
      msg.includes('Keys should be unique') ||
      msg.includes('duplicate key') ||
      msg.includes('重复key')
    );

    expect(keyErrors.length).toBe(0);
    console.log('添加规则测试完成，未发现key重复错误');
  });

  test('频繁点击状态切换开关不应产生key重复错误', async ({ page }) => {
    const consoleMessages: string[] = [];
    
    // 监听控制台消息
    page.on('console', msg => {
      const text = msg.text();
      consoleMessages.push(text);
      if (text.includes('Warning') || text.includes('Error') || text.includes('duplicate') || text.includes('same key')) {
        console.log('控制台消息:', text);
      }
    });

    // 确保在黑名单tab
    const blacklistTab = page.locator('text=黑名单').first();
    if (await blacklistTab.count() > 0) {
      await blacklistTab.click();
      await page.waitForTimeout(500);
    }

    // 查找所有状态切换开关并频繁点击
    const switches = page.locator('.ant-switch');
    const switchCount = await switches.count();
    
    if (switchCount > 0) {
      console.log(`找到 ${switchCount} 个状态切换开关`);
      
      for (let i = 0; i < Math.min(switchCount, 3); i++) {
        console.log(`测试第 ${i + 1} 个开关`);
        
        // 快速点击多次
        for (let click = 0; click < 3; click++) {
          try {
            await switches.nth(i).click();
            await page.waitForTimeout(200);
          } catch (error) {
            console.log(`开关 ${i} 点击 ${click} 失败:`, error);
          }
        }
      }
    }

    // 检查是否有key重复错误
    const keyErrors = consoleMessages.filter(msg => 
      msg.includes('same key') || 
      msg.includes('Keys should be unique') ||
      msg.includes('duplicate key') ||
      msg.includes('重复key')
    );

    expect(keyErrors.length).toBe(0);
    console.log('状态切换测试完成，未发现key重复错误');
  });

  test('多次测试x.com访问功能不应产生key重复错误', async ({ page }) => {
    const consoleMessages: string[] = [];
    
    // 监听控制台消息
    page.on('console', msg => {
      const text = msg.text();
      consoleMessages.push(text);
      if (text.includes('Warning') || text.includes('Error') || text.includes('duplicate') || text.includes('same key')) {
        console.log('控制台消息:', text);
      }
    });

    // 切换到违规记录tab
    const violationsTab = page.locator('text=违规记录').first();
    if (await violationsTab.count() > 0) {
      await violationsTab.click();
      await page.waitForTimeout(500);
    }

    // 多次点击测试按钮
    for (let i = 0; i < 3; i++) {
      console.log(`第 ${i + 1} 次x.com访问测试`);
      
      const testButton = page.locator('text=测试 x.com 访问').first();
      if (await testButton.count() > 0 && await testButton.isVisible()) {
        try {
          await testButton.click();
          await page.waitForTimeout(2000); // 等待测试完成
        } catch (error) {
          console.log(`第 ${i + 1} 次测试失败:`, error);
        }
      }
    }

    // 检查是否有key重复错误
    const keyErrors = consoleMessages.filter(msg => 
      msg.includes('same key') || 
      msg.includes('Keys should be unique') ||
      msg.includes('duplicate key') ||
      msg.includes('重复key')
    );

    expect(keyErrors.length).toBe(0);
    console.log('x.com访问测试完成，未发现key重复错误');
  });

  test('应用预设模板不应产生key重复错误', async ({ page }) => {
    const consoleMessages: string[] = [];
    
    // 监听控制台消息
    page.on('console', msg => {
      const text = msg.text();
      consoleMessages.push(text);
      if (text.includes('Warning') || text.includes('Error') || text.includes('duplicate') || text.includes('same key')) {
        console.log('控制台消息:', text);
      }
    });

    // 切换到预设模板tab
    const templatesTab = page.locator('text=预设模板').first();
    if (await templatesTab.count() > 0) {
      await templatesTab.click();
      await page.waitForTimeout(500);
    }

    // 查找并点击应用按钮
    const applyButtons = page.locator('text=应用').or(page.locator('button:has-text("应用")'));
    const buttonCount = await applyButtons.count();
    
    if (buttonCount > 0) {
      console.log(`找到 ${buttonCount} 个应用按钮`);
      
      // 点击第一个应用按钮
      try {
        await applyButtons.first().click();
        await page.waitForTimeout(1000);
        console.log('应用模板成功');
      } catch (error) {
        console.log('应用模板失败:', error);
      }
    }

    // 检查是否有key重复错误
    const keyErrors = consoleMessages.filter(msg => 
      msg.includes('same key') || 
      msg.includes('Keys should be unique') ||
      msg.includes('duplicate key') ||
      msg.includes('重复key')
    );

    expect(keyErrors.length).toBe(0);
    console.log('模板应用测试完成，未发现key重复错误');
  });
});