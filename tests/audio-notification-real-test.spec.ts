import { test, expect } from '@playwright/test';

test.describe('FocusOS 音频通知真实场景测试', () => {
  test.beforeEach(async ({ page }) => {
    // 启动应用
    await page.goto('http://localhost:5173');
    await page.waitForTimeout(3000); // 等待应用完全加载
    
    // 点击页面以激活音频上下文
    await page.click('body');
    await page.waitForTimeout(500);
  });

  test('通过番茄钟真实操作测试音频通知', async ({ page }) => {
    const consoleMessages: string[] = [];
    
    // 监听控制台消息
    page.on('console', msg => {
      const text = msg.text();
      consoleMessages.push(text);
      
      // 只记录音频和通知相关的重要消息
      if (text.includes('🔔') || text.includes('🎵') || text.includes('AudioService') || 
          text.includes('NotificationService') || text.includes('playNotificationSound') ||
          text.includes('Audio file loaded') || text.includes('initialized')) {
        console.log('重要消息:', text);
      }
    });

    // 导航到番茄钟页面
    await page.click('text=番茄钟');
    await page.waitForTimeout(3000);

    // 查找并选择一个任务（如果需要）
    const taskSelectors = [
      '.ant-select-selector',
      'text=选择任务',
      '[placeholder*="任务"]',
      '.task-selector'
    ];

    for (const selector of taskSelectors) {
      if (await page.locator(selector).count() > 0) {
        try {
          await page.click(selector);
          await page.waitForTimeout(500);
          
          // 选择第一个任务选项
          const options = page.locator('.ant-select-item, .ant-select-option');
          if (await options.count() > 0) {
            await options.first().click();
            console.log('✅ 已选择任务');
            break;
          }
        } catch (error) {
          console.log('任务选择失败:', error);
        }
      }
    }

    await page.waitForTimeout(1000);

    // 查找开始按钮
    const startButtonSelectors = [
      'button:has-text("开始")',
      'button:has-text("Start")',
      '[data-testid="start-button"]',
      '.start-button',
      'button[title*="开始"]'
    ];

    let startButtonFound = false;
    for (const selector of startButtonSelectors) {
      if (await page.locator(selector).count() > 0) {
        try {
          console.log(`找到开始按钮: ${selector}`);
          await page.click(selector);
          startButtonFound = true;
          console.log('✅ 点击了开始按钮');
          break;
        } catch (error) {
          console.log(`开始按钮点击失败 ${selector}:`, error);
        }
      }
    }

    if (startButtonFound) {
      // 等待番茄钟开始
      await page.waitForTimeout(2000);

      // 查找暂停按钮（确认番茄钟已开始）
      const pauseButtonSelectors = [
        'button:has-text("暂停")',
        'button:has-text("Pause")',
        '[data-testid="pause-button"]',
        '.pause-button'
      ];

      for (const selector of pauseButtonSelectors) {
        if (await page.locator(selector).count() > 0) {
          try {
            console.log('✅ 番茄钟已开始（找到暂停按钮）');
            
            // 点击暂停来触发暂停通知
            await page.click(selector);
            console.log('✅ 点击了暂停按钮，应该触发暂停通知');
            await page.waitForTimeout(2000);
            break;
          } catch (error) {
            console.log('暂停按钮点击失败:', error);
          }
        }
      }
    }

    // 等待足够时间让音频系统处理
    await page.waitForTimeout(3000);

    // 分析控制台消息
    const audioInitLogs = consoleMessages.filter(msg => 
      msg.includes('AudioService initialized') ||
      msg.includes('Audio file loaded') ||
      msg.includes('Audio preload completed')
    );

    const audioPlayLogs = consoleMessages.filter(msg => 
      msg.includes('🔔 AudioService.playNotificationSound') ||
      msg.includes('🎵 AudioService.playSound') ||
      msg.includes('playNotificationSound called') ||
      msg.includes('About to play sound')
    );

    const notificationLogs = consoleMessages.filter(msg => 
      msg.includes('🔔 NotificationService') ||
      msg.includes('notifyPomodoro') ||
      msg.includes('sendNotification called')
    );

    console.log('=== 测试结果分析 ===');
    console.log('音频初始化日志数量:', audioInitLogs.length);
    console.log('音频播放日志数量:', audioPlayLogs.length);
    console.log('通知服务日志数量:', notificationLogs.length);

    if (audioInitLogs.length > 0) {
      console.log('✅ 音频系统已初始化');
      audioInitLogs.forEach(log => console.log('  -', log));
    }

    if (audioPlayLogs.length > 0) {
      console.log('✅ 检测到音频播放尝试');
      audioPlayLogs.forEach(log => console.log('  -', log));
    }

    if (notificationLogs.length > 0) {
      console.log('✅ 检测到通知服务调用');
      notificationLogs.forEach(log => console.log('  -', log));
    }

    // 验证至少有音频初始化
    expect(audioInitLogs.length).toBeGreaterThan(0);
  });

  test('检查音频文件可访问性', async ({ page }) => {
    console.log('=== 检查音频文件可访问性 ===');
    
    const soundFiles = [
      '/sounds/task-complete.mp3',
      '/sounds/pomodoro-complete.mp3',
      '/sounds/break-complete.mp3',
      '/sounds/goal-achieved.mp3',
      '/sounds/system-alert.mp3',
      '/sounds/gentle-reminder.mp3'
    ];

    let accessibleFiles = 0;
    for (const soundFile of soundFiles) {
      try {
        const response = await page.goto(`http://localhost:5173${soundFile}`);
        const status = response?.status();
        
        if (status === 200) {
          console.log(`✅ ${soundFile} 可访问`);
          accessibleFiles++;
        } else {
          console.log(`❌ ${soundFile} 无法访问 (${status})`);
        }
      } catch (error) {
        console.log(`❌ ${soundFile} 访问出错:`, error);
      }
    }

    console.log(`音频文件可访问性: ${accessibleFiles}/${soundFiles.length}`);
    
    // 返回主页
    await page.goto('http://localhost:5173');
    await page.waitForTimeout(1000);

    // 验证至少有一些音频文件可访问
    expect(accessibleFiles).toBeGreaterThan(0);
  });

  test('验证音频设置默认状态', async ({ page }) => {
    const audioSettings = await page.evaluate(() => {
      // 检查localStorage中的音频设置
      let settings = null;
      try {
        const saved = localStorage.getItem('focusOS_audio_settings');
        if (saved) {
          settings = JSON.parse(saved);
        }
      } catch (error) {
        console.error('读取音频设置失败:', error);
      }

      return {
        localStorageSettings: settings,
        webAudioSupported: !!(window.AudioContext || (window as any).webkitAudioContext),
        htmlAudioSupported: !!window.Audio,
        notificationPermission: 'Notification' in window ? Notification.permission : 'not-supported'
      };
    });

    console.log('=== 音频设置状态 ===');
    console.log('本地存储设置:', audioSettings.localStorageSettings);
    console.log('Web Audio API支持:', audioSettings.webAudioSupported);
    console.log('HTML5 Audio支持:', audioSettings.htmlAudioSupported);
    console.log('通知权限:', audioSettings.notificationPermission);

    // 验证基本音频支持
    expect(audioSettings.webAudioSupported || audioSettings.htmlAudioSupported).toBe(true);

    // 如果有本地设置，验证默认启用
    if (audioSettings.localStorageSettings) {
      expect(audioSettings.localStorageSettings.enabled).toBe(true);
    }
  });
});
