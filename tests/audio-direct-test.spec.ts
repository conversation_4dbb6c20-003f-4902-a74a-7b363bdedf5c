import { test, expect } from '@playwright/test';

test.describe('FocusOS 音频通知直接测试', () => {
  test.beforeEach(async ({ page }) => {
    // 启动应用
    await page.goto('http://localhost:5173');
    await page.waitForTimeout(3000); // 等待应用完全加载
    
    // 点击页面以激活音频上下文
    await page.click('body');
    await page.waitForTimeout(500);
  });

  test('直接测试音频通知修复效果', async ({ page }) => {
    const consoleMessages: string[] = [];
    
    // 监听控制台消息
    page.on('console', msg => {
      const text = msg.text();
      consoleMessages.push(text);
      console.log('控制台:', text);
    });

    // 导航到番茄钟页面以确保NotificationService被实例化
    await page.click('text=番茄钟');
    await page.waitForTimeout(2000);

    // 直接在浏览器中执行音频通知测试
    const testResult = await page.evaluate(async () => {
      const results = {
        notificationServiceAvailable: false,
        audioServiceAvailable: false,
        audioInitialized: false,
        testResults: [] as string[]
      };

      try {
        // 等待一下确保服务加载
        await new Promise(resolve => setTimeout(resolve, 1000));

        // 尝试获取NotificationService实例
        const { NotificationService } = await import('../services/NotificationService');
        const notificationService = NotificationService.getInstance();
        results.notificationServiceAvailable = true;
        results.testResults.push('✅ NotificationService 获取成功');

        // 获取AudioService实例
        const { AudioService } = await import('../services/AudioService');
        const audioService = AudioService.getInstance();
        results.audioServiceAvailable = true;
        results.testResults.push('✅ AudioService 获取成功');

        // 检查AudioService是否已初始化
        const audioSettings = audioService.getSettings();
        results.testResults.push(`🔧 音频设置: enabled=${audioSettings.enabled}, volume=${audioSettings.volume}`);

        // 强制初始化AudioService
        try {
          await audioService.initialize();
          results.audioInitialized = true;
          results.testResults.push('✅ AudioService 初始化成功');
        } catch (error) {
          results.testResults.push(`⚠️ AudioService 初始化警告: ${error}`);
        }

        // 测试直接播放音频
        try {
          console.log('🧪 开始测试音频播放');
          await audioService.playNotificationSound('system-alert', true); // 强制播放
          results.testResults.push('✅ 系统提示音播放测试完成');
        } catch (error) {
          results.testResults.push(`❌ 系统提示音播放失败: ${error}`);
        }

        // 等待一下
        await new Promise(resolve => setTimeout(resolve, 2000));

        // 测试任务完成通知
        try {
          console.log('🧪 开始测试任务完成通知');
          await notificationService.notifyTaskComplete('测试任务');
          results.testResults.push('✅ 任务完成通知测试完成');
        } catch (error) {
          results.testResults.push(`❌ 任务完成通知失败: ${error}`);
        }

        // 等待一下
        await new Promise(resolve => setTimeout(resolve, 2000));

        // 测试番茄钟完成通知
        try {
          console.log('🧪 开始测试番茄钟完成通知');
          await notificationService.notifyPomodoroComplete('work', 1);
          results.testResults.push('✅ 番茄钟完成通知测试完成');
        } catch (error) {
          results.testResults.push(`❌ 番茄钟完成通知失败: ${error}`);
        }

      } catch (error) {
        results.testResults.push(`❌ 测试执行失败: ${error}`);
      }

      return results;
    });

    // 等待足够时间让音频播放完成
    await page.waitForTimeout(5000);

    // 输出测试结果
    console.log('=== 直接测试结果 ===');
    console.log('NotificationService可用:', testResult.notificationServiceAvailable);
    console.log('AudioService可用:', testResult.audioServiceAvailable);
    console.log('AudioService已初始化:', testResult.audioInitialized);
    
    console.log('=== 测试步骤结果 ===');
    testResult.testResults.forEach(result => console.log(result));

    // 分析控制台消息中的音频相关日志
    const audioLogs = consoleMessages.filter(msg => 
      msg.includes('🔔 AudioService.playNotificationSound') ||
      msg.includes('🎵 AudioService.playSound') ||
      msg.includes('🔊 Notification audio played') ||
      msg.includes('AudioService initialized') ||
      msg.includes('Audio file loaded') ||
      msg.includes('About to play sound') ||
      msg.includes('🎵 Audio preload completed')
    );

    const notificationLogs = consoleMessages.filter(msg => 
      msg.includes('🔔 NotificationService') ||
      msg.includes('sendNotification called') ||
      msg.includes('notifyTaskComplete') ||
      msg.includes('notifyPomodoroComplete')
    );

    console.log('=== 控制台日志分析 ===');
    console.log('音频相关日志数量:', audioLogs.length);
    console.log('通知相关日志数量:', notificationLogs.length);

    if (audioLogs.length > 0) {
      console.log('✅ 检测到音频相关活动:');
      audioLogs.forEach(log => console.log('  -', log));
    }

    if (notificationLogs.length > 0) {
      console.log('✅ 检测到通知相关活动:');
      notificationLogs.forEach(log => console.log('  -', log));
    }

    // 验证测试结果
    expect(testResult.notificationServiceAvailable).toBe(true);
    expect(testResult.audioServiceAvailable).toBe(true);
    
    // 验证至少有一些音频或通知活动
    const hasAudioActivity = audioLogs.length > 0 || notificationLogs.length > 0;
    expect(hasAudioActivity).toBe(true);

    console.log('🎉 音频通知修复验证完成！');
  });

  test('验证音频权限和设置状态', async ({ page }) => {
    const systemStatus = await page.evaluate(() => {
      return {
        // 浏览器音频支持
        webAudioSupported: !!(window.AudioContext || (window as any).webkitAudioContext),
        htmlAudioSupported: !!window.Audio,
        
        // 通知权限
        notificationSupported: 'Notification' in window,
        notificationPermission: 'Notification' in window ? Notification.permission : 'not-supported',
        
        // 用户交互状态
        userActivated: true, // 我们已经点击过页面了
        
        // 本地存储
        hasLocalStorage: !!window.localStorage,
        audioSettings: (() => {
          try {
            const settings = localStorage.getItem('focusOS_audio_settings');
            return settings ? JSON.parse(settings) : null;
          } catch {
            return null;
          }
        })(),
        
        // AudioContext状态
        audioContextState: (() => {
          try {
            const AudioContextClass = window.AudioContext || (window as any).webkitAudioContext;
            if (AudioContextClass) {
              const ctx = new AudioContextClass();
              const state = ctx.state;
              ctx.close();
              return state;
            }
            return 'not-supported';
          } catch {
            return 'error';
          }
        })()
      };
    });

    console.log('=== 系统状态检查 ===');
    console.log('Web Audio API支持:', systemStatus.webAudioSupported);
    console.log('HTML5 Audio支持:', systemStatus.htmlAudioSupported);
    console.log('通知API支持:', systemStatus.notificationSupported);
    console.log('通知权限状态:', systemStatus.notificationPermission);
    console.log('用户已激活:', systemStatus.userActivated);
    console.log('LocalStorage可用:', systemStatus.hasLocalStorage);
    console.log('AudioContext状态:', systemStatus.audioContextState);
    console.log('本地音频设置:', systemStatus.audioSettings);

    // 验证基本要求
    expect(systemStatus.webAudioSupported || systemStatus.htmlAudioSupported).toBe(true);
    expect(systemStatus.hasLocalStorage).toBe(true);
    expect(systemStatus.audioContextState).not.toBe('error');

    // 如果通知权限被拒绝，这是正常的，我们的修复应该让音频独立于通知权限
    if (systemStatus.notificationPermission === 'denied') {
      console.log('✅ 通知权限被拒绝，但这不应该影响音频播放（已修复）');
    }

    console.log('✅ 系统状态检查完成');
  });
});
