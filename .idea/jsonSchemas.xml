<?xml version="1.0" encoding="UTF-8"?>
<project version="4">
  <component name="JsonSchemaMappingsProjectConfiguration">
    <state>
      <map>
        <entry key="GitHub Workflow">
          <value>
            <SchemaInfo>
              <option name="name" value="GitHub Workflow" />
              <option name="relativePathToSchema" value="https://json.schemastore.org/github-workflow.json" />
              <option name="applicationDefined" value="true" />
              <option name="patterns">
                <list>
                  <Item>
                    <option name="path" value=".github/workflows/release.yml" />
                  </Item>
                  <Item>
                    <option name="path" value=".github/workflows/ci.yml" />
                  </Item>
                </list>
              </option>
            </SchemaInfo>
          </value>
        </entry>
      </map>
    </state>
  </component>
</project>
