# Bash命令
- npm start: 启动项目
- npm run build: 构建项目
- npm run compile: 首先运行构建脚本，然后使用具有指定配置的 electron-builder 将项目编译为可执行文件
- npm run compile -- --dir -c.asar=false: 与 npm run compile 相同，但将额外的参数传递给 electron-builder 以禁用 asar 存档和安装程序创建。用于调试已编译的应用程序
- npm run test: 使用 Playwright 对编译的应用程序执行端到端测试
- npm run typecheck: 在所有工作区中运行 typecheck 命令（如果存在）
- npm run create-renderer: 初始化一个名为 renderer 的新 Vite 项目。与 npm create vite 基本相同
- npm run integrate-renderer: 使用 Vite Electron 构建器启动渲染器的集成过程
- npm run init: 通过创建新的渲染器、集成它并安装必要的包来设置初始环境

# 代码风格
- 使用ES模块 (import/export) 语法，而不是CommonJS (require)
- 尽可能使用解构导入 (例如 import { foo } from 'bar')

# 工作流程
- 在完成一系列代码更改后务必进行类型检查
- 为了性能考虑，优先运行单个测试，而不是整个测试套件

# 代码单元大小限制
- 每个函数不超过 40 行，每个文件不超过 500 行。
- 控制圈复杂度 ≤ 10，超过需重构或拆分。

# 模块化与组件化设计
- 所有功能模块应解耦为可单独测试与复用的组件。
- 渲染器、主进程逻辑、通用工具应物理分离，禁止交叉引用内部状态。

# 功能拆分
- 复杂流程应拆解为可组合的子任务函数。
- 所有子任务必须有独立的错误处理路径，避免级联异常。

# 控制耦合度
- 模块间依赖通过接口或事件通信（如 IPC）完成。
- 禁止直接访问外部模块的内部实现或状态。