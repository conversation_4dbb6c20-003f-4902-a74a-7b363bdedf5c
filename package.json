{"name": "root", "description": "Secure boilerplate for Electron app based on Vite", "version": "3.1.0", "private": true, "type": "module", "author": {"email": "<EMAIL>", "name": "<PERSON>", "url": "https://kozack.me"}, "main": "packages/entry-point.mjs", "workspaces": ["packages/*"], "engines": {"node": ">=16.0.0"}, "scripts": {"build": "npm run build -ws --if-present", "compile": "npm run build && electron-builder build --config electron-builder.mjs", "test": "npx playwright test ./tests/e2e.spec.ts", "start": "node packages/dev-mode.js", "typecheck": "npm run typecheck -ws --if-present", "create-renderer": "cd packages && npm create vite@latest renderer", "integrate-renderer": "npm start --workspace @app/integrate-renderer", "init": "npm run create-renderer && npm run integrate-renderer && npm install", "generate-audio": "node scripts/generate-test-audio.js", "setup-audio": "npm run generate-audio"}, "devDependencies": {"@npmcli/map-workspaces": "4.0.2", "@playwright/test": "1.53.1", "@types/node": "24.0.3", "electron": "36.5.0", "electron-builder": "26.0.12", "electron-rebuild": "^3.2.9", "glob": "11.0.3", "playwright": "^1.53.1", "terser": "^5.43.1"}, "dependencies": {"@app/main": "*", "@google/generative-ai": "^0.24.1", "@types/react-beautiful-dnd": "^13.1.8", "@types/react-window": "^1.8.8", "axios": "^1.10.0", "react-beautiful-dnd": "^13.1.1", "react-dnd": "^16.0.1", "react-dnd-html5-backend": "^16.0.1", "react-window": "^1.8.11"}}