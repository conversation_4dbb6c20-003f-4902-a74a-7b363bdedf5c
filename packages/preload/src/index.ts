import {sha256sum} from './nodeCrypto.js';
import {versions} from './versions.js';
import {ipc<PERSON><PERSON><PERSON>} from 'electron';

function send(channel: string, message: string) {
  return ipcRenderer.invoke(channel, message);
}

function invoke(channel: string, ...args: any[]) {
  return ipcRenderer.invoke(channel, ...args);
}

const electronAPI = {
  invoke,
};

export {sha256sum, versions, send, electronAPI};
