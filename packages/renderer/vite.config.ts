import { defineConfig } from 'vite'
import react from '@vitejs/plugin-react'
import { resolve } from 'path'

// https://vite.dev/config/
export default defineConfig({
  plugins: [
    react({
      // 启用React Fast Refresh
      fastRefresh: true,
      // 优化JSX运行时
      jsxRuntime: 'automatic',
    })
  ],

  // 构建优化
  build: {
    // 启用代码分割
    rollupOptions: {
      output: {
        // 手动分包策略
        manualChunks: {
          // 将React相关库打包到一个chunk
          'react-vendor': ['react', 'react-dom', 'react-redux'],
          // 将Ant Design打包到一个chunk
          'antd-vendor': ['antd'],
          // 将Redux相关库打包到一个chunk
          'redux-vendor': ['@reduxjs/toolkit'],
          // 将工具库打包到一个chunk
          'utils-vendor': ['dayjs', 'crypto-js', 'uuid'],
          // 将D3相关库打包到一个chunk
          'd3-vendor': ['d3'],
        },
        // 优化chunk文件名
        chunkFileNames: 'assets/js/[name]-[hash].js',
        entryFileNames: 'assets/js/[name]-[hash].js',
        assetFileNames: 'assets/[ext]/[name]-[hash].[ext]',
      },
    },

    // 启用压缩
    minify: 'terser',
    terserOptions: {
      compress: {
        // 移除console.log
        drop_console: true,
        // 移除debugger
        drop_debugger: true,
        // 移除未使用的代码
        dead_code: true,
      },
    },

    // 设置chunk大小警告阈值
    chunkSizeWarningLimit: 1000,

    // 启用CSS代码分割
    cssCodeSplit: true,

    // 生成source map用于调试
    sourcemap: false,

    // 设置目标浏览器
    target: 'esnext',
  },

  // 开发服务器优化
  server: {
    // 启用HMR
    hmr: true,
    // 预构建优化
    force: false,
  },

  // 依赖预构建优化
  optimizeDeps: {
    // 包含需要预构建的依赖
    include: [
      'react',
      'react-dom',
      'react-redux',
      '@reduxjs/toolkit',
      'antd',
      'dayjs',
      'crypto-js',
      'uuid',
      'd3',
    ],
    // 排除不需要预构建的依赖
    exclude: ['better-sqlite3'],
  },

  // 路径别名
  resolve: {
    alias: {
      '@': resolve(__dirname, 'src'),
      '@components': resolve(__dirname, 'src/components'),
      '@pages': resolve(__dirname, 'src/pages'),
      '@store': resolve(__dirname, 'src/store'),
      '@services': resolve(__dirname, 'src/services'),
      '@types': resolve(__dirname, 'src/types'),
      '@styles': resolve(__dirname, 'src/styles'),
    },
  },

  // CSS预处理器优化
  css: {
    // 启用CSS模块
    modules: {
      localsConvention: 'camelCase',
    },
    // PostCSS配置
    postcss: {
      plugins: [
        // 可以在这里添加PostCSS插件
      ],
    },
  },
})
