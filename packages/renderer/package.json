{"name": "@app/renderer", "private": true, "version": "0.0.0", "type": "module", "scripts": {"dev": "vite", "build": "tsc && vite build --base ./", "build:analyze": "npm run build && node analyze-bundle.js", "preview": "vite preview", "typecheck": "tsc --noEmit"}, "dependencies": {"@reduxjs/toolkit": "^2.2.7", "antd": "^5.22.0", "better-sqlite3": "^11.3.0", "crypto-js": "^4.2.0", "d3": "^7.9.0", "dayjs": "^1.11.13", "react": "^18.3.1", "react-dom": "^18.3.1", "react-redux": "^9.1.2", "uuid": "^10.0.0"}, "devDependencies": {"@types/better-sqlite3": "^7.6.11", "@types/crypto-js": "^4.2.2", "@types/d3": "^7.4.3", "@types/react": "^18.3.12", "@types/react-dom": "^18.3.1", "@types/uuid": "^10.0.0", "@vitejs/plugin-react": "^4.3.3", "typescript": "~5.8.3", "vite": "^6.3.5"}, "main": "./dist/index.html", "exports": {".": {"default": "./dist/index.html"}}}