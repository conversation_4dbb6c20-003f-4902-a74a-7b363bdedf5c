#!/usr/bin/env node

/**
 * Bundle分析脚本
 * 用于分析打包后的文件大小和依赖关系
 */

const fs = require('fs');
const path = require('path');
const { execSync } = require('child_process');

// 分析打包文件
function analyzeBundleSize() {
  const distPath = path.join(__dirname, 'dist');
  
  if (!fs.existsSync(distPath)) {
    console.error('❌ 打包文件不存在，请先运行 npm run build');
    process.exit(1);
  }

  console.log('📊 分析打包文件大小...\n');

  // 获取所有文件
  const files = getAllFiles(distPath);
  
  // 按类型分组
  const filesByType = {
    js: [],
    css: [],
    html: [],
    assets: [],
  };

  files.forEach(file => {
    const ext = path.extname(file).toLowerCase();
    const relativePath = path.relative(distPath, file);
    const size = fs.statSync(file).size;
    
    const fileInfo = {
      path: relativePath,
      size,
      sizeFormatted: formatBytes(size),
    };

    if (ext === '.js') {
      filesByType.js.push(fileInfo);
    } else if (ext === '.css') {
      filesByType.css.push(fileInfo);
    } else if (ext === '.html') {
      filesByType.html.push(fileInfo);
    } else {
      filesByType.assets.push(fileInfo);
    }
  });

  // 排序（按大小降序）
  Object.keys(filesByType).forEach(type => {
    filesByType[type].sort((a, b) => b.size - a.size);
  });

  // 输出分析结果
  console.log('📦 JavaScript 文件:');
  filesByType.js.forEach(file => {
    const sizeColor = file.size > 500000 ? '🔴' : file.size > 200000 ? '🟡' : '🟢';
    console.log(`  ${sizeColor} ${file.path} - ${file.sizeFormatted}`);
  });

  console.log('\n🎨 CSS 文件:');
  filesByType.css.forEach(file => {
    const sizeColor = file.size > 100000 ? '🔴' : file.size > 50000 ? '🟡' : '🟢';
    console.log(`  ${sizeColor} ${file.path} - ${file.sizeFormatted}`);
  });

  console.log('\n📄 HTML 文件:');
  filesByType.html.forEach(file => {
    console.log(`  📄 ${file.path} - ${file.sizeFormatted}`);
  });

  console.log('\n🖼️  资源文件:');
  filesByType.assets.forEach(file => {
    console.log(`  🖼️  ${file.path} - ${file.sizeFormatted}`);
  });

  // 总大小统计
  const totalSize = files.reduce((sum, file) => sum + fs.statSync(file).size, 0);
  const jsSize = filesByType.js.reduce((sum, file) => sum + file.size, 0);
  const cssSize = filesByType.css.reduce((sum, file) => sum + file.size, 0);

  console.log('\n📊 总体统计:');
  console.log(`  总大小: ${formatBytes(totalSize)}`);
  console.log(`  JavaScript: ${formatBytes(jsSize)} (${((jsSize / totalSize) * 100).toFixed(1)}%)`);
  console.log(`  CSS: ${formatBytes(cssSize)} (${((cssSize / totalSize) * 100).toFixed(1)}%)`);

  // 性能建议
  console.log('\n💡 性能建议:');
  
  const largeJsFiles = filesByType.js.filter(file => file.size > 500000);
  if (largeJsFiles.length > 0) {
    console.log('  🔴 发现大型JavaScript文件，建议:');
    console.log('     - 启用代码分割');
    console.log('     - 移除未使用的依赖');
    console.log('     - 使用动态导入');
  }

  const largeCssFiles = filesByType.css.filter(file => file.size > 100000);
  if (largeCssFiles.length > 0) {
    console.log('  🔴 发现大型CSS文件，建议:');
    console.log('     - 移除未使用的CSS');
    console.log('     - 启用CSS压缩');
    console.log('     - 考虑CSS-in-JS方案');
  }

  if (totalSize > 2000000) {
    console.log('  🔴 总包大小过大，建议:');
    console.log('     - 启用Gzip压缩');
    console.log('     - 使用CDN加载第三方库');
    console.log('     - 实现懒加载');
  }

  // 生成报告文件
  const report = {
    timestamp: new Date().toISOString(),
    totalSize,
    files: filesByType,
    recommendations: generateRecommendations(filesByType, totalSize),
  };

  fs.writeFileSync(
    path.join(__dirname, 'bundle-analysis.json'),
    JSON.stringify(report, null, 2)
  );

  console.log('\n📝 详细报告已保存到 bundle-analysis.json');
}

// 递归获取所有文件
function getAllFiles(dir) {
  const files = [];
  
  function traverse(currentDir) {
    const items = fs.readdirSync(currentDir);
    
    items.forEach(item => {
      const fullPath = path.join(currentDir, item);
      const stat = fs.statSync(fullPath);
      
      if (stat.isDirectory()) {
        traverse(fullPath);
      } else {
        files.push(fullPath);
      }
    });
  }
  
  traverse(dir);
  return files;
}

// 格式化字节大小
function formatBytes(bytes) {
  if (bytes === 0) return '0 Bytes';
  
  const k = 1024;
  const sizes = ['Bytes', 'KB', 'MB', 'GB'];
  const i = Math.floor(Math.log(bytes) / Math.log(k));
  
  return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
}

// 生成优化建议
function generateRecommendations(filesByType, totalSize) {
  const recommendations = [];

  // JavaScript优化建议
  const largeJsFiles = filesByType.js.filter(file => file.size > 500000);
  if (largeJsFiles.length > 0) {
    recommendations.push({
      type: 'javascript',
      priority: 'high',
      message: '发现大型JavaScript文件',
      files: largeJsFiles.map(f => f.path),
      suggestions: [
        '启用代码分割',
        '移除未使用的依赖',
        '使用动态导入',
        '启用Tree Shaking',
      ],
    });
  }

  // CSS优化建议
  const largeCssFiles = filesByType.css.filter(file => file.size > 100000);
  if (largeCssFiles.length > 0) {
    recommendations.push({
      type: 'css',
      priority: 'medium',
      message: '发现大型CSS文件',
      files: largeCssFiles.map(f => f.path),
      suggestions: [
        '移除未使用的CSS',
        '启用CSS压缩',
        '考虑CSS-in-JS方案',
        '使用CSS模块化',
      ],
    });
  }

  // 总体大小建议
  if (totalSize > 2000000) {
    recommendations.push({
      type: 'general',
      priority: 'high',
      message: '总包大小过大',
      suggestions: [
        '启用Gzip压缩',
        '使用CDN加载第三方库',
        '实现懒加载',
        '优化图片资源',
      ],
    });
  }

  return recommendations;
}

// 运行分析
if (require.main === module) {
  analyzeBundleSize();
}

module.exports = { analyzeBundleSize };
