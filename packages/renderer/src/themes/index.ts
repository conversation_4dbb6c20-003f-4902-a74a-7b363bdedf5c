export interface ThemeConfig {
  id: string;
  name: string;
  colors: {
    // 主色调
    primary: string;
    primaryHover: string;
    primaryActive: string;
    
    // 背景色
    background: string;
    backgroundSecondary: string;
    backgroundTertiary: string;
    
    // 表面色（毛玻璃效果）
    surface: string;
    surfaceHover: string;
    surfacePressed: string;
    
    // 文字色
    text: string;
    textSecondary: string;
    textTertiary: string;
    textInverse: string;
    
    // 边框色
    border: string;
    borderLight: string;
    
    // 阴影色
    shadow: string;
    shadowLight: string;
    
    // 状态色
    success: string;
    warning: string;
    error: string;
    info: string;
    
    // Glass效果
    glassBackground: string;
    glassBlur: string;
    glassBorder: string;
  };
  glassMorphism: {
    backdropFilter: string;
    background: string;
    border: string;
    borderRadius: string;
    boxShadow: string;
  };
}

export const themes: { [key: string]: ThemeConfig } = {
  // 1. 天空蓝 - 清新明亮
  skyBlue: {
    id: 'skyBlue',
    name: '天空蓝',
    colors: {
      primary: '#007AFF',
      primaryHover: '#0056CC',
      primaryActive: '#003D99',
      background: 'linear-gradient(135deg, #E3F2FD 0%, #BBDEFB 50%, #90CAF9 100%)',
      backgroundSecondary: 'rgba(227, 242, 253, 0.8)',
      backgroundTertiary: 'rgba(187, 222, 251, 0.6)',
      surface: 'rgba(255, 255, 255, 0.25)',
      surfaceHover: 'rgba(255, 255, 255, 0.35)',
      surfacePressed: 'rgba(255, 255, 255, 0.45)',
      text: '#1A1A1A',
      textSecondary: '#666666',
      textTertiary: '#999999',
      textInverse: '#FFFFFF',
      border: 'rgba(255, 255, 255, 0.3)',
      borderLight: 'rgba(255, 255, 255, 0.2)',
      shadow: 'rgba(0, 122, 255, 0.3)',
      shadowLight: 'rgba(0, 122, 255, 0.1)',
      success: '#34C759',
      warning: '#FF9500',
      error: '#FF3B30',
      info: '#5AC8FA',
      glassBackground: 'rgba(255, 255, 255, 0.15)',
      glassBlur: 'blur(20px)',
      glassBorder: '1px solid rgba(255, 255, 255, 0.25)',
    },
    glassMorphism: {
      backdropFilter: 'blur(20px) saturate(180%)',
      background: 'rgba(255, 255, 255, 0.15)',
      border: '1px solid rgba(255, 255, 255, 0.25)',
      borderRadius: '16px',
      boxShadow: '0 8px 32px rgba(0, 122, 255, 0.1), inset 0 1px 0 rgba(255, 255, 255, 0.4)',
    },
  },

  // 2. 薰衣草紫 - 优雅梦幻
  lavender: {
    id: 'lavender',
    name: '薰衣草紫',
    colors: {
      primary: '#AF52DE',
      primaryHover: '#8E3BB8',
      primaryActive: '#6D2992',
      background: 'linear-gradient(135deg, #F3E5F5 0%, #E1BEE7 50%, #CE93D8 100%)',
      backgroundSecondary: 'rgba(243, 229, 245, 0.8)',
      backgroundTertiary: 'rgba(225, 190, 231, 0.6)',
      surface: 'rgba(255, 255, 255, 0.25)',
      surfaceHover: 'rgba(255, 255, 255, 0.35)',
      surfacePressed: 'rgba(255, 255, 255, 0.45)',
      text: '#1A1A1A',
      textSecondary: '#666666',
      textTertiary: '#999999',
      textInverse: '#FFFFFF',
      border: 'rgba(255, 255, 255, 0.3)',
      borderLight: 'rgba(255, 255, 255, 0.2)',
      shadow: 'rgba(175, 82, 222, 0.3)',
      shadowLight: 'rgba(175, 82, 222, 0.1)',
      success: '#34C759',
      warning: '#FF9500',
      error: '#FF3B30',
      info: '#AF52DE',
      glassBackground: 'rgba(255, 255, 255, 0.15)',
      glassBlur: 'blur(20px)',
      glassBorder: '1px solid rgba(255, 255, 255, 0.25)',
    },
    glassMorphism: {
      backdropFilter: 'blur(20px) saturate(180%)',
      background: 'rgba(255, 255, 255, 0.15)',
      border: '1px solid rgba(255, 255, 255, 0.25)',
      borderRadius: '16px',
      boxShadow: '0 8px 32px rgba(175, 82, 222, 0.1), inset 0 1px 0 rgba(255, 255, 255, 0.4)',
    },
  },

  // 3. 森林绿 - 自然清新
  forestGreen: {
    id: 'forestGreen',
    name: '森林绿',
    colors: {
      primary: '#34C759',
      primaryHover: '#28A745',
      primaryActive: '#1E7E34',
      background: 'linear-gradient(135deg, #E8F5E8 0%, #C8E6C8 50%, #A5D6A5 100%)',
      backgroundSecondary: 'rgba(232, 245, 232, 0.8)',
      backgroundTertiary: 'rgba(200, 230, 200, 0.6)',
      surface: 'rgba(255, 255, 255, 0.25)',
      surfaceHover: 'rgba(255, 255, 255, 0.35)',
      surfacePressed: 'rgba(255, 255, 255, 0.45)',
      text: '#1A1A1A',
      textSecondary: '#666666',
      textTertiary: '#999999',
      textInverse: '#FFFFFF',
      border: 'rgba(255, 255, 255, 0.3)',
      borderLight: 'rgba(255, 255, 255, 0.2)',
      shadow: 'rgba(52, 199, 89, 0.3)',
      shadowLight: 'rgba(52, 199, 89, 0.1)',
      success: '#34C759',
      warning: '#FF9500',
      error: '#FF3B30',
      info: '#5AC8FA',
      glassBackground: 'rgba(255, 255, 255, 0.15)',
      glassBlur: 'blur(20px)',
      glassBorder: '1px solid rgba(255, 255, 255, 0.25)',
    },
    glassMorphism: {
      backdropFilter: 'blur(20px) saturate(180%)',
      background: 'rgba(255, 255, 255, 0.15)',
      border: '1px solid rgba(255, 255, 255, 0.25)',
      borderRadius: '16px',
      boxShadow: '0 8px 32px rgba(52, 199, 89, 0.1), inset 0 1px 0 rgba(255, 255, 255, 0.4)',
    },
  },

  // 4. 日落橙 - 温暖活力
  sunsetOrange: {
    id: 'sunsetOrange',
    name: '日落橙',
    colors: {
      primary: '#FF9500',
      primaryHover: '#E6850E',
      primaryActive: '#CC7A00',
      background: 'linear-gradient(135deg, #FFF3E0 0%, #FFE0B2 50%, #FFCC80 100%)',
      backgroundSecondary: 'rgba(255, 243, 224, 0.8)',
      backgroundTertiary: 'rgba(255, 224, 178, 0.6)',
      surface: 'rgba(255, 255, 255, 0.25)',
      surfaceHover: 'rgba(255, 255, 255, 0.35)',
      surfacePressed: 'rgba(255, 255, 255, 0.45)',
      text: '#1A1A1A',
      textSecondary: '#666666',
      textTertiary: '#999999',
      textInverse: '#FFFFFF',
      border: 'rgba(255, 255, 255, 0.3)',
      borderLight: 'rgba(255, 255, 255, 0.2)',
      shadow: 'rgba(255, 149, 0, 0.3)',
      shadowLight: 'rgba(255, 149, 0, 0.1)',
      success: '#34C759',
      warning: '#FF9500',
      error: '#FF3B30',
      info: '#5AC8FA',
      glassBackground: 'rgba(255, 255, 255, 0.15)',
      glassBlur: 'blur(20px)',
      glassBorder: '1px solid rgba(255, 255, 255, 0.25)',
    },
    glassMorphism: {
      backdropFilter: 'blur(20px) saturate(180%)',
      background: 'rgba(255, 255, 255, 0.15)',
      border: '1px solid rgba(255, 255, 255, 0.25)',
      borderRadius: '16px',
      boxShadow: '0 8px 32px rgba(255, 149, 0, 0.1), inset 0 1px 0 rgba(255, 255, 255, 0.4)',
    },
  },

  // 5. 樱花粉 - 温柔浪漫
  sakuraPink: {
    id: 'sakuraPink',
    name: '樱花粉',
    colors: {
      primary: '#FF2D92',
      primaryHover: '#E01E84',
      primaryActive: '#C11876',
      background: 'linear-gradient(135deg, #FCE4EC 0%, #F8BBD9 50%, #F48FB1 100%)',
      backgroundSecondary: 'rgba(252, 228, 236, 0.8)',
      backgroundTertiary: 'rgba(248, 187, 217, 0.6)',
      surface: 'rgba(255, 255, 255, 0.25)',
      surfaceHover: 'rgba(255, 255, 255, 0.35)',
      surfacePressed: 'rgba(255, 255, 255, 0.45)',
      text: '#1A1A1A',
      textSecondary: '#666666',
      textTertiary: '#999999',
      textInverse: '#FFFFFF',
      border: 'rgba(255, 255, 255, 0.3)',
      borderLight: 'rgba(255, 255, 255, 0.2)',
      shadow: 'rgba(255, 45, 146, 0.3)',
      shadowLight: 'rgba(255, 45, 146, 0.1)',
      success: '#34C759',
      warning: '#FF9500',
      error: '#FF3B30',
      info: '#FF2D92',
      glassBackground: 'rgba(255, 255, 255, 0.15)',
      glassBlur: 'blur(20px)',
      glassBorder: '1px solid rgba(255, 255, 255, 0.25)',
    },
    glassMorphism: {
      backdropFilter: 'blur(20px) saturate(180%)',
      background: 'rgba(255, 255, 255, 0.15)',
      border: '1px solid rgba(255, 255, 255, 0.25)',
      borderRadius: '16px',
      boxShadow: '0 8px 32px rgba(255, 45, 146, 0.1), inset 0 1px 0 rgba(255, 255, 255, 0.4)',
    },
  },

  // 6. 深海蓝 - 神秘冷静
  deepBlue: {
    id: 'deepBlue',
    name: '深海蓝',
    colors: {
      primary: '#5856D6',
      primaryHover: '#4B49C4',
      primaryActive: '#3E3CB2',
      background: 'linear-gradient(135deg, #E8EAF6 0%, #C5CAE9 50%, #9FA8DA 100%)',
      backgroundSecondary: 'rgba(232, 234, 246, 0.8)',
      backgroundTertiary: 'rgba(197, 202, 233, 0.6)',
      surface: 'rgba(255, 255, 255, 0.25)',
      surfaceHover: 'rgba(255, 255, 255, 0.35)',
      surfacePressed: 'rgba(255, 255, 255, 0.45)',
      text: '#1A1A1A',
      textSecondary: '#666666',
      textTertiary: '#999999',
      textInverse: '#FFFFFF',
      border: 'rgba(255, 255, 255, 0.3)',
      borderLight: 'rgba(255, 255, 255, 0.2)',
      shadow: 'rgba(88, 86, 214, 0.3)',
      shadowLight: 'rgba(88, 86, 214, 0.1)',
      success: '#34C759',
      warning: '#FF9500',
      error: '#FF3B30',
      info: '#5856D6',
      glassBackground: 'rgba(255, 255, 255, 0.15)',
      glassBlur: 'blur(20px)',
      glassBorder: '1px solid rgba(255, 255, 255, 0.25)',
    },
    glassMorphism: {
      backdropFilter: 'blur(20px) saturate(180%)',
      background: 'rgba(255, 255, 255, 0.15)',
      border: '1px solid rgba(255, 255, 255, 0.25)',
      borderRadius: '16px',
      boxShadow: '0 8px 32px rgba(88, 86, 214, 0.1), inset 0 1px 0 rgba(255, 255, 255, 0.4)',
    },
  },

  // 7. 青柠绿 - 活力清新
  limeGreen: {
    id: 'limeGreen',
    name: '青柠绿',
    colors: {
      primary: '#30D158',
      primaryHover: '#28B946',
      primaryActive: '#20A134',
      background: 'linear-gradient(135deg, #F1F8E9 0%, #DCEDC8 50%, #C5E1A5 100%)',
      backgroundSecondary: 'rgba(241, 248, 233, 0.8)',
      backgroundTertiary: 'rgba(220, 237, 200, 0.6)',
      surface: 'rgba(255, 255, 255, 0.25)',
      surfaceHover: 'rgba(255, 255, 255, 0.35)',
      surfacePressed: 'rgba(255, 255, 255, 0.45)',
      text: '#1A1A1A',
      textSecondary: '#666666',
      textTertiary: '#999999',
      textInverse: '#FFFFFF',
      border: 'rgba(255, 255, 255, 0.3)',
      borderLight: 'rgba(255, 255, 255, 0.2)',
      shadow: 'rgba(48, 209, 88, 0.3)',
      shadowLight: 'rgba(48, 209, 88, 0.1)',
      success: '#30D158',
      warning: '#FF9500',
      error: '#FF3B30',
      info: '#5AC8FA',
      glassBackground: 'rgba(255, 255, 255, 0.15)',
      glassBlur: 'blur(20px)',
      glassBorder: '1px solid rgba(255, 255, 255, 0.25)',
    },
    glassMorphism: {
      backdropFilter: 'blur(20px) saturate(180%)',
      background: 'rgba(255, 255, 255, 0.15)',
      border: '1px solid rgba(255, 255, 255, 0.25)',
      borderRadius: '16px',
      boxShadow: '0 8px 32px rgba(48, 209, 88, 0.1), inset 0 1px 0 rgba(255, 255, 255, 0.4)',
    },
  },

  // 8. 暮光金 - 奢华典雅
  twilightGold: {
    id: 'twilightGold',
    name: '暮光金',
    colors: {
      primary: '#FFCC02',
      primaryHover: '#E6B800',
      primaryActive: '#CCA400',
      background: 'linear-gradient(135deg, #FFFDE7 0%, #FFF9C4 50%, #FFF176 100%)',
      backgroundSecondary: 'rgba(255, 253, 231, 0.8)',
      backgroundTertiary: 'rgba(255, 249, 196, 0.6)',
      surface: 'rgba(255, 255, 255, 0.25)',
      surfaceHover: 'rgba(255, 255, 255, 0.35)',
      surfacePressed: 'rgba(255, 255, 255, 0.45)',
      text: '#1A1A1A',
      textSecondary: '#666666',
      textTertiary: '#999999',
      textInverse: '#FFFFFF',
      border: 'rgba(255, 255, 255, 0.3)',
      borderLight: 'rgba(255, 255, 255, 0.2)',
      shadow: 'rgba(255, 204, 2, 0.3)',
      shadowLight: 'rgba(255, 204, 2, 0.1)',
      success: '#34C759',
      warning: '#FFCC02',
      error: '#FF3B30',
      info: '#5AC8FA',
      glassBackground: 'rgba(255, 255, 255, 0.15)',
      glassBlur: 'blur(20px)',
      glassBorder: '1px solid rgba(255, 255, 255, 0.25)',
    },
    glassMorphism: {
      backdropFilter: 'blur(20px) saturate(180%)',
      background: 'rgba(255, 255, 255, 0.15)',
      border: '1px solid rgba(255, 255, 255, 0.25)',
      borderRadius: '16px',
      boxShadow: '0 8px 32px rgba(255, 204, 2, 0.1), inset 0 1px 0 rgba(255, 255, 255, 0.4)',
    },
  },

  // 9. 薄荷绿 - 清凉舒缓
  mintGreen: {
    id: 'mintGreen',
    name: '薄荷绿',
    colors: {
      primary: '#32D74B',
      primaryHover: '#28C840',
      primaryActive: '#1EBA35',
      background: 'linear-gradient(135deg, #E0F2F1 0%, #B2DFDB 50%, #80CBC4 100%)',
      backgroundSecondary: 'rgba(224, 242, 241, 0.8)',
      backgroundTertiary: 'rgba(178, 223, 219, 0.6)',
      surface: 'rgba(255, 255, 255, 0.25)',
      surfaceHover: 'rgba(255, 255, 255, 0.35)',
      surfacePressed: 'rgba(255, 255, 255, 0.45)',
      text: '#1A1A1A',
      textSecondary: '#666666',
      textTertiary: '#999999',
      textInverse: '#FFFFFF',
      border: 'rgba(255, 255, 255, 0.3)',
      borderLight: 'rgba(255, 255, 255, 0.2)',
      shadow: 'rgba(50, 215, 75, 0.3)',
      shadowLight: 'rgba(50, 215, 75, 0.1)',
      success: '#32D74B',
      warning: '#FF9500',
      error: '#FF3B30',
      info: '#5AC8FA',
      glassBackground: 'rgba(255, 255, 255, 0.15)',
      glassBlur: 'blur(20px)',
      glassBorder: '1px solid rgba(255, 255, 255, 0.25)',
    },
    glassMorphism: {
      backdropFilter: 'blur(20px) saturate(180%)',
      background: 'rgba(255, 255, 255, 0.15)',
      border: '1px solid rgba(255, 255, 255, 0.25)',
      borderRadius: '16px',
      boxShadow: '0 8px 32px rgba(50, 215, 75, 0.1), inset 0 1px 0 rgba(255, 255, 255, 0.4)',
    },
  },

  // 10. 星空灰 - 现代简约
  cosmicGray: {
    id: 'cosmicGray',
    name: '星空灰',
    colors: {
      primary: '#8E8E93',
      primaryHover: '#7C7C81',
      primaryActive: '#6A6A6F',
      background: 'linear-gradient(135deg, #F2F2F7 0%, #E5E5EA 50%, #D1D1D6 100%)',
      backgroundSecondary: 'rgba(242, 242, 247, 0.8)',
      backgroundTertiary: 'rgba(229, 229, 234, 0.6)',
      surface: 'rgba(255, 255, 255, 0.25)',
      surfaceHover: 'rgba(255, 255, 255, 0.35)',
      surfacePressed: 'rgba(255, 255, 255, 0.45)',
      text: '#1A1A1A',
      textSecondary: '#666666',
      textTertiary: '#999999',
      textInverse: '#FFFFFF',
      border: 'rgba(255, 255, 255, 0.3)',
      borderLight: 'rgba(255, 255, 255, 0.2)',
      shadow: 'rgba(142, 142, 147, 0.3)',
      shadowLight: 'rgba(142, 142, 147, 0.1)',
      success: '#34C759',
      warning: '#FF9500',
      error: '#FF3B30',
      info: '#5AC8FA',
      glassBackground: 'rgba(255, 255, 255, 0.15)',
      glassBlur: 'blur(20px)',
      glassBorder: '1px solid rgba(255, 255, 255, 0.25)',
    },
    glassMorphism: {
      backdropFilter: 'blur(20px) saturate(180%)',
      background: 'rgba(255, 255, 255, 0.15)',
      border: '1px solid rgba(255, 255, 255, 0.25)',
      borderRadius: '16px',
      boxShadow: '0 8px 32px rgba(142, 142, 147, 0.1), inset 0 1px 0 rgba(255, 255, 255, 0.4)',
    },
  },

  // 11. 夜间模式 - 深色主题
  darkNight: {
    id: 'darkNight',
    name: '夜间',
    colors: {
      primary: '#0A84FF',
      primaryHover: '#409CFF',
      primaryActive: '#0056CC',
      background: 'linear-gradient(135deg, #000000 0%, #1C1C1E 50%, #2C2C2E 100%)',
      backgroundSecondary: 'rgba(28, 28, 30, 0.95)',
      backgroundTertiary: 'rgba(44, 44, 46, 0.8)',
      surface: 'rgba(255, 255, 255, 0.05)',
      surfaceHover: 'rgba(255, 255, 255, 0.08)',
      surfacePressed: 'rgba(255, 255, 255, 0.12)',
      text: '#FFFFFF',
      textSecondary: '#98989D',
      textTertiary: '#636366',
      textInverse: '#1A1A1A',
      border: 'rgba(255, 255, 255, 0.1)',
      borderLight: 'rgba(255, 255, 255, 0.05)',
      shadow: 'rgba(0, 0, 0, 0.5)',
      shadowLight: 'rgba(0, 0, 0, 0.3)',
      success: '#30D158',
      warning: '#FF9F0A',
      error: '#FF453A',
      info: '#64D2FF',
      glassBackground: 'rgba(255, 255, 255, 0.05)',
      glassBlur: 'blur(20px)',
      glassBorder: '1px solid rgba(255, 255, 255, 0.1)',
    },
    glassMorphism: {
      backdropFilter: 'blur(20px) saturate(180%)',
      background: 'rgba(255, 255, 255, 0.05)',
      border: '1px solid rgba(255, 255, 255, 0.1)',
      borderRadius: '16px',
      boxShadow: '0 8px 32px rgba(0, 0, 0, 0.3), inset 0 1px 0 rgba(255, 255, 255, 0.1)',
    },
  },

  // 12. 深蓝夜空 - 深色变体
  darkBlue: {
    id: 'darkBlue',
    name: '深蓝夜空',
    colors: {
      primary: '#007AFF',
      primaryHover: '#409CFF',
      primaryActive: '#0056CC',
      background: 'linear-gradient(135deg, #0D1B2A 0%, #1B263B 50%, #2D3748 100%)',
      backgroundSecondary: 'rgba(27, 38, 59, 0.95)',
      backgroundTertiary: 'rgba(45, 55, 72, 0.8)',
      surface: 'rgba(255, 255, 255, 0.05)',
      surfaceHover: 'rgba(255, 255, 255, 0.08)',
      surfacePressed: 'rgba(255, 255, 255, 0.12)',
      text: '#E2E8F0',
      textSecondary: '#94A3B8',
      textTertiary: '#64748B',
      textInverse: '#1A1A1A',
      border: 'rgba(255, 255, 255, 0.1)',
      borderLight: 'rgba(255, 255, 255, 0.05)',
      shadow: 'rgba(0, 122, 255, 0.3)',
      shadowLight: 'rgba(0, 122, 255, 0.1)',
      success: '#10B981',
      warning: '#F59E0B',
      error: '#EF4444',
      info: '#3B82F6',
      glassBackground: 'rgba(255, 255, 255, 0.05)',
      glassBlur: 'blur(20px)',
      glassBorder: '1px solid rgba(255, 255, 255, 0.1)',
    },
    glassMorphism: {
      backdropFilter: 'blur(20px) saturate(180%)',
      background: 'rgba(255, 255, 255, 0.05)',
      border: '1px solid rgba(255, 255, 255, 0.1)',
      borderRadius: '16px',
      boxShadow: '0 8px 32px rgba(0, 122, 255, 0.2), inset 0 1px 0 rgba(255, 255, 255, 0.1)',
    },
  },
};

export const getTheme = (themeId: string): ThemeConfig => {
  return themes[themeId] || themes.skyBlue;
};