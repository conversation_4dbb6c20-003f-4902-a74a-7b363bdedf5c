import { Goal, Task } from '../types';
import { focusMonitorService } from './FocusMonitorService';

export interface TimeStatistics {
  totalFocusTime: number; // 总专注时间（分钟）
  totalWorkTime: number; // 总工作时间（分钟）
  averageDailyFocus: number; // 日均专注时间
  focusEfficiency: number; // 专注效率（0-100）
  mostProductiveHour: number; // 最高效时段
  weeklyTrend: number[]; // 周趋势数据
}

export interface CompletionAnalysis {
  totalGoals: number;
  completedGoals: number;
  inProgressGoals: number;
  totalTasks: number;
  completedTasks: number;
  completionRate: number; // 完成率（0-100）
  averageCompletionTime: number; // 平均完成时间（天）
  goalsByType: { [type: string]: number };
  tasksByPriority: { [priority: string]: number };
}

export interface FocusReport {
  averageFocusScore: number; // 平均专注分数
  focusSessionCount: number; // 专注会话数量
  totalDistractions: number; // 总分心次数
  commonDistractionTypes: string[]; // 常见分心类型
  focusImprovement: number; // 专注改善程度
  bestFocusPeriods: string[]; // 最佳专注时段
}

export interface ProductivityInsights {
  productivityScore: number; // 生产力评分
  timeAllocation: { [category: string]: number }; // 时间分配
  goalProgress: { [goalId: string]: number }; // 目标进度
  recommendations: string[]; // 改进建议
  achievements: string[]; // 成就列表
}

class AnalyticsService {
  // 获取时间统计
  public getTimeStatistics(days: number = 30): TimeStatistics {
    const focusInsights = focusMonitorService.getFocusInsights(days);
    
    const totalFocusTime = focusInsights.reduce((sum, insight) => sum + insight.totalFocusTime, 0);
    const averageDailyFocus = focusInsights.length > 0 ? totalFocusTime / focusInsights.length : 0;
    
    // 计算最高效时段
    const hourlyFocus = new Array(24).fill(0);
    focusInsights.forEach(insight => {
      hourlyFocus[insight.mostProductiveHour] += insight.totalFocusTime;
    });
    const mostProductiveHour = hourlyFocus.indexOf(Math.max(...hourlyFocus));
    
    // 计算周趋势（简化实现）
    const weeklyTrend = focusInsights.slice(-7).map(insight => insight.totalFocusTime);
    
    return {
      totalFocusTime,
      totalWorkTime: totalFocusTime * 1.2, // 估算总工作时间
      averageDailyFocus,
      focusEfficiency: focusInsights.length > 0 
        ? focusInsights.reduce((sum, i) => sum + i.averageFocusScore, 0) / focusInsights.length 
        : 0,
      mostProductiveHour,
      weeklyTrend: weeklyTrend.length === 7 ? weeklyTrend : new Array(7).fill(0)
    };
  }

  // 获取完成率分析
  public getCompletionAnalysis(goals: Goal[], tasks: Task[]): CompletionAnalysis {
    const completedGoals = goals.filter(goal => goal.status === 'completed').length;
    const inProgressGoals = goals.filter(goal => goal.status === 'in-progress').length;
    const completedTasks = tasks.filter(task => task.status === 'completed').length;
    
    const completionRate = goals.length > 0 ? (completedGoals / goals.length) * 100 : 0;
    
    // 按类型统计目标
    const goalsByType: { [type: string]: number } = {};
    goals.forEach(goal => {
      goalsByType[goal.type] = (goalsByType[goal.type] || 0) + 1;
    });
    
    // 按优先级统计任务
    const tasksByPriority: { [priority: string]: number } = {};
    tasks.forEach(task => {
      tasksByPriority[task.priority] = (tasksByPriority[task.priority] || 0) + 1;
    });
    
    // 计算平均完成时间（简化实现）
    const averageCompletionTime = 30; // 假设平均30天完成一个目标
    
    return {
      totalGoals: goals.length,
      completedGoals,
      inProgressGoals,
      totalTasks: tasks.length,
      completedTasks,
      completionRate,
      averageCompletionTime,
      goalsByType,
      tasksByPriority
    };
  }

  // 获取专注报告
  public getFocusReport(days: number = 30): FocusReport {
    const focusInsights = focusMonitorService.getFocusInsights(days);
    
    const averageFocusScore = focusInsights.length > 0 
      ? focusInsights.reduce((sum, i) => sum + i.averageFocusScore, 0) / focusInsights.length 
      : 0;
    
    const focusSessionCount = focusInsights.length;
    const totalDistractions = focusInsights.reduce((sum, i) => sum + i.distractionCount, 0);
    
    // 获取常见分心类型（简化实现）
    const commonDistractionTypes = ['社交媒体', '即时消息', '内心想法'];
    
    // 计算专注改善程度
    const recentScore = focusInsights.slice(0, Math.ceil(focusInsights.length / 2))
      .reduce((sum, i) => sum + i.averageFocusScore, 0) / Math.ceil(focusInsights.length / 2);
    const olderScore = focusInsights.slice(Math.ceil(focusInsights.length / 2))
      .reduce((sum, i) => sum + i.averageFocusScore, 0) / Math.floor(focusInsights.length / 2);
    const focusImprovement = recentScore - olderScore;
    
    // 最佳专注时段
    const bestFocusPeriods = ['上午 9-11点', '下午 2-4点'];
    
    return {
      averageFocusScore,
      focusSessionCount,
      totalDistractions,
      commonDistractionTypes,
      focusImprovement,
      bestFocusPeriods
    };
  }

  // 获取生产力洞察
  public getProductivityInsights(goals: Goal[], tasks: Task[], days: number = 30): ProductivityInsights {
    const timeStats = this.getTimeStatistics(days);
    const completionAnalysis = this.getCompletionAnalysis(goals, tasks);
    const focusReport = this.getFocusReport(days);
    
    // 计算生产力评分
    const productivityScore = Math.round(
      (completionAnalysis.completionRate * 0.4 + 
       focusReport.averageFocusScore * 0.4 + 
       timeStats.focusEfficiency * 0.2)
    );
    
    // 时间分配分析
    const timeAllocation = {
      '专注工作': timeStats.totalFocusTime,
      '任务执行': timeStats.totalWorkTime - timeStats.totalFocusTime,
      '休息时间': timeStats.totalWorkTime * 0.2
    };
    
    // 目标进度
    const goalProgress: { [goalId: string]: number } = {};
    goals.forEach(goal => {
      const goalTasks = tasks.filter(task => task.goalNodeId === goal.id);
      const completedTasks = goalTasks.filter(task => task.status === 'completed').length;
      goalProgress[goal.id] = goalTasks.length > 0 ? (completedTasks / goalTasks.length) * 100 : 0;
    });
    
    // 生成建议
    const recommendations = this.generateRecommendations(
      productivityScore, 
      completionAnalysis, 
      focusReport, 
      timeStats
    );
    
    // 生成成就
    const achievements = this.generateAchievements(completionAnalysis, focusReport, timeStats);
    
    return {
      productivityScore,
      timeAllocation,
      goalProgress,
      recommendations,
      achievements
    };
  }

  // 生成改进建议
  private generateRecommendations(
    productivityScore: number,
    completion: CompletionAnalysis,
    focus: FocusReport,
    time: TimeStatistics
  ): string[] {
    const recommendations: string[] = [];
    
    if (productivityScore < 60) {
      recommendations.push('建议制定更明确的日程安排，提高工作效率');
    }
    
    if (completion.completionRate < 50) {
      recommendations.push('考虑将大目标分解为更小的可执行任务');
    }
    
    if (focus.averageFocusScore < 70) {
      recommendations.push('尝试减少干扰源，创造更专注的工作环境');
    }
    
    if (time.averageDailyFocus < 120) {
      recommendations.push('建议增加每日专注时间，目标至少2小时');
    }
    
    if (focus.totalDistractions > 20) {
      recommendations.push('使用番茄工作法，将工作分解为更短的专注时段');
    }
    
    return recommendations;
  }

  // 生成成就
  private generateAchievements(
    completion: CompletionAnalysis,
    focus: FocusReport,
    time: TimeStatistics
  ): string[] {
    const achievements: string[] = [];
    
    if (completion.completionRate >= 80) {
      achievements.push('🏆 目标达成大师 - 完成率超过80%');
    }
    
    if (focus.averageFocusScore >= 85) {
      achievements.push('🎯 专注力专家 - 平均专注分数超过85分');
    }
    
    if (time.totalFocusTime >= 1000) {
      achievements.push('⏰ 时间管理达人 - 累计专注超过1000分钟');
    }
    
    if (focus.focusImprovement > 10) {
      achievements.push('📈 持续改进者 - 专注力显著提升');
    }
    
    if (completion.completedTasks >= 50) {
      achievements.push('✅ 任务完成者 - 完成50个以上任务');
    }
    
    return achievements;
  }

  // 导出报告数据
  public exportReport(goals: Goal[], tasks: Task[], days: number = 30) {
    const report = {
      generatedAt: new Date().toISOString(),
      period: `${days}天`,
      timeStatistics: this.getTimeStatistics(days),
      completionAnalysis: this.getCompletionAnalysis(goals, tasks),
      focusReport: this.getFocusReport(days),
      productivityInsights: this.getProductivityInsights(goals, tasks, days)
    };
    
    const dataStr = JSON.stringify(report, null, 2);
    const dataBlob = new Blob([dataStr], { type: 'application/json' });
    
    const link = document.createElement('a');
    link.href = URL.createObjectURL(dataBlob);
    link.download = `focusos-report-${new Date().toISOString().split('T')[0]}.json`;
    link.click();
  }
}

// 单例实例
export const analyticsService = new AnalyticsService();
export default AnalyticsService;
