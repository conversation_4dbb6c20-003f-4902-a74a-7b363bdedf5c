import { Task } from '../types';

export interface ReminderConfig {
  // 任务时间提醒
  taskDeadlineReminder: boolean;
  taskDeadlineAdvanceMinutes: number; // 提前多少分钟提醒

  // 间隔提醒 (Focus Pacer)
  focusPacerEnabled: boolean;
  focusPacerInterval: number; // 间隔分钟数
  focusPacerMessage: string;

  // 不活跃提醒
  inactivityReminderEnabled: boolean;
  inactivityThresholdMinutes: number; // 不活跃阈值（分钟）
  inactivityMessage: string;
}

export interface ReminderNotification {
  id: string;
  type: 'task-deadline' | 'focus-pacer' | 'inactivity';
  title: string;
  message: string;
  timestamp: Date;
  taskId?: string;
  actions?: Array<{
    label: string;
    action: string;
  }>;
}

class ReminderService {
  private config: ReminderConfig;
  private timers: Map<string, NodeJS.Timeout> = new Map();
  private lastActivityTime: Date = new Date();
  private inactivityTimer?: NodeJS.Timeout;
  private focusPacerTimer?: NodeJS.Timeout;
  private notificationCallbacks: Array<(notification: ReminderNotification) => void> = [];

  constructor() {
    this.config = this.getDefaultConfig();
    this.loadConfig();
    this.startInactivityMonitoring();
    this.startFocusPacer();
  }

  private getDefaultConfig(): ReminderConfig {
    return {
      taskDeadlineReminder: true,
      taskDeadlineAdvanceMinutes: 30,
      focusPacerEnabled: true,
      focusPacerInterval: 25, // 25分钟间隔
      focusPacerMessage: '🎯 专注检查：你现在在做什么？是否与目标一致？',
      inactivityReminderEnabled: true,
      inactivityThresholdMinutes: 15,
      inactivityMessage: '💤 你已经有一段时间没有活动了，要不要回到任务上来？'
    };
  }

  private loadConfig() {
    try {
      const saved = localStorage.getItem('focusOS_reminder_config');
      if (saved) {
        this.config = { ...this.config, ...JSON.parse(saved) };
      }
    } catch (error) {
      console.error('加载提醒配置失败:', error);
    }
  }

  public updateConfig(newConfig: Partial<ReminderConfig>) {
    this.config = { ...this.config, ...newConfig };
    localStorage.setItem('focusOS_reminder_config', JSON.stringify(this.config));

    // 重新启动相关服务
    this.restartServices();
  }

  public getConfig(): ReminderConfig {
    return { ...this.config };
  }

  private restartServices() {
    this.stopAllTimers();
    this.startInactivityMonitoring();
    this.startFocusPacer();
  }

  private stopAllTimers() {
    // 清除所有定时器
    this.timers.forEach(timer => clearTimeout(timer));
    this.timers.clear();

    if (this.inactivityTimer) {
      clearTimeout(this.inactivityTimer);
      this.inactivityTimer = undefined;
    }

    if (this.focusPacerTimer) {
      clearInterval(this.focusPacerTimer);
      this.focusPacerTimer = undefined;
    }
  }

  // 注册通知回调
  public onNotification(callback: (notification: ReminderNotification) => void) {
    this.notificationCallbacks.push(callback);
    return () => {
      const index = this.notificationCallbacks.indexOf(callback);
      if (index > -1) {
        this.notificationCallbacks.splice(index, 1);
      }
    };
  }

  private sendNotification(notification: ReminderNotification) {
    this.notificationCallbacks.forEach(callback => {
      try {
        callback(notification);
      } catch (error) {
        console.error('通知回调执行失败:', error);
      }
    });
  }

  // FR-IR-001: 任务时间提醒
  public scheduleTaskDeadlineReminder(task: Task) {
    if (!this.config.taskDeadlineReminder || !task.deadline) {
      return;
    }

    const deadlineTime = new Date(task.deadline);
    const reminderTime = new Date(deadlineTime.getTime() - this.config.taskDeadlineAdvanceMinutes * 60 * 1000);
    const now = new Date();

    // 如果提醒时间已过，不设置提醒
    if (reminderTime <= now) {
      return;
    }

    const timerId = `task-deadline-${task.id}`;
    const delay = reminderTime.getTime() - now.getTime();

    const timer = setTimeout(() => {
      const notification: ReminderNotification = {
        id: `deadline-${task.id}-${Date.now()}`,
        type: 'task-deadline',
        title: '⏰ 任务截止提醒',
        message: `任务"${task.title}"将在${this.config.taskDeadlineAdvanceMinutes}分钟后截止`,
        timestamp: new Date(),
        taskId: task.id,
        actions: [
          { label: '立即开始', action: 'start-task' },
          { label: '查看详情', action: 'view-task' },
          { label: '延期处理', action: 'postpone-task' }
        ]
      };

      this.sendNotification(notification);
      this.timers.delete(timerId);
    }, delay);

    // 清除旧的定时器
    const oldTimer = this.timers.get(timerId);
    if (oldTimer) {
      clearTimeout(oldTimer);
    }

    this.timers.set(timerId, timer);
  }

  public cancelTaskDeadlineReminder(taskId: string) {
    const timerId = `task-deadline-${taskId}`;
    const timer = this.timers.get(timerId);
    if (timer) {
      clearTimeout(timer);
      this.timers.delete(timerId);
    }
  }

  // FR-IR-002: 间隔提醒 (Focus Pacer)
  private startFocusPacer() {
    if (!this.config.focusPacerEnabled) {
      return;
    }

    this.focusPacerTimer = setInterval(() => {
      const notification: ReminderNotification = {
        id: `focus-pacer-${Date.now()}`,
        type: 'focus-pacer',
        title: '🎯 专注检查',
        message: this.config.focusPacerMessage,
        timestamp: new Date(),
        actions: [
          { label: '继续专注', action: 'continue-focus' },
          { label: '查看目标', action: 'view-goals' },
          { label: '休息一下', action: 'take-break' }
        ]
      };

      this.sendNotification(notification);
    }, this.config.focusPacerInterval * 60 * 1000);
  }

  // FR-IR-003: 不活跃提醒
  private startInactivityMonitoring() {
    if (!this.config.inactivityReminderEnabled) {
      return;
    }

    // 监听用户活动
    const activityEvents = ['mousedown', 'mousemove', 'keypress', 'scroll', 'touchstart'];

    const updateActivity = () => {
      this.lastActivityTime = new Date();
      this.resetInactivityTimer();
    };

    // 添加事件监听器
    activityEvents.forEach(event => {
      document.addEventListener(event, updateActivity, true);
    });

    this.resetInactivityTimer();
  }

  private resetInactivityTimer() {
    if (this.inactivityTimer) {
      clearTimeout(this.inactivityTimer);
    }

    this.inactivityTimer = setTimeout(() => {
      const notification: ReminderNotification = {
        id: `inactivity-${Date.now()}`,
        type: 'inactivity',
        title: '💤 活动提醒',
        message: this.config.inactivityMessage,
        timestamp: new Date(),
        actions: [
          { label: '回到任务', action: 'back-to-task' },
          { label: '查看进度', action: 'view-progress' },
          { label: '稍后提醒', action: 'snooze' }
        ]
      };

      this.sendNotification(notification);

      // 重新设置定时器
      this.resetInactivityTimer();
    }, this.config.inactivityThresholdMinutes * 60 * 1000);
  }

  public recordActivity() {
    this.lastActivityTime = new Date();
    this.resetInactivityTimer();
  }

  public getLastActivityTime(): Date {
    return new Date(this.lastActivityTime);
  }

  public getInactivityDuration(): number {
    return Date.now() - this.lastActivityTime.getTime();
  }

  // 暂停所有提醒
  public pauseAllReminders() {
    this.stopAllTimers();
  }

  // 恢复所有提醒
  public resumeAllReminders() {
    this.restartServices();
  }

  // 清理资源
  public destroy() {
    this.stopAllTimers();
    this.notificationCallbacks = [];
  }
}

// 单例实例
export const reminderService = new ReminderService();
export default ReminderService;