import { ApplicationInfo, WebsiteInfo } from './ApplicationMonitorService';
import { BlacklistRule } from './BlacklistManagerService';
import { focusMonitorService } from './FocusMonitorService';
import { goalBeaconService } from './GoalBeaconService';
import { AudioService } from './AudioService';

// 干预级别
export type InterventionLevel = 'gentle' | 'warning' | 'firm' | 'block';

// 干预类型
export type InterventionType = 'notification' | 'modal' | 'delay' | 'block' | 'redirect';

// 干预配置
export interface InterventionConfig {
  level: InterventionLevel;
  type: InterventionType;
  delaySeconds?: number; // 延迟时间
  allowSkip?: boolean; // 是否允许跳过
  maxSkips?: number; // 最大跳过次数
  resetSkipsAfter?: number; // 重置跳过次数的时间（小时）
  redirectUrl?: string; // 重定向URL
  customMessage?: string; // 自定义消息
}

// 干预事件
export interface InterventionEvent {
  id: string;
  timestamp: Date;
  target: ApplicationInfo | WebsiteInfo;
  rule: BlacklistRule;
  level: InterventionLevel;
  type: InterventionType;
  userResponse: 'blocked' | 'continued' | 'returned' | 'skipped' | 'pending';
  responseTime?: number; // 用户响应时间（毫秒）
  effectivenessScoredAt?: Date; // 效果评估时间
  effectivenessScore?: number; // 干预效果分数 (0-100)
}

// 干预统计
export interface InterventionStats {
  totalInterventions: number;
  successfulBlocks: number;
  userOverrides: number;
  averageResponseTime: number;
  effectivenessRate: number; // 成功率
  levelDistribution: Record<InterventionLevel, number>;
}

// 用户反馈
export interface UserFeedback {
  eventId: string;
  rating: number; // 1-5 评分
  comment?: string;
  suggestedLevel?: InterventionLevel;
  timestamp: Date;
}

class InterventionEngineService {
  private interventionHistory: InterventionEvent[] = [];
  private userSkipCounts: Map<string, { count: number; lastReset: Date }> = new Map();
  private config: Record<InterventionLevel, InterventionConfig> = {
    gentle: {
      level: 'gentle',
      type: 'notification',
      allowSkip: true,
      maxSkips: 5,
      resetSkipsAfter: 24
    },
    warning: {
      level: 'warning',
      type: 'modal',
      delaySeconds: 5,
      allowSkip: true,
      maxSkips: 3,
      resetSkipsAfter: 12
    },
    firm: {
      level: 'firm',
      type: 'delay',
      delaySeconds: 15,
      allowSkip: true,
      maxSkips: 1,
      resetSkipsAfter: 6
    },
    block: {
      level: 'block',
      type: 'block',
      allowSkip: false
    }
  };

  private listeners: {
    onIntervention?: (event: InterventionEvent) => void;
    onUserResponse?: (event: InterventionEvent) => void;
  } = {};

  constructor() {
    this.loadInterventionHistory();
    this.loadUserSkipCounts();
  }

  // 触发干预
  public async triggerIntervention(
    target: ApplicationInfo | WebsiteInfo,
    rule: BlacklistRule
  ): Promise<InterventionEvent> {
    // 根据规则严重程度和用户历史行为确定干预级别
    const level = this.determineInterventionLevel(rule, target);
    const config = this.config[level];
    
    const event: InterventionEvent = {
      id: `intervention-${Date.now()}`,
      timestamp: new Date(),
      target,
      rule,
      level,
      type: config.type,
      userResponse: 'pending'
    };

    // 检查是否还有跳过次数
    if (config.allowSkip && !this.canUserSkip(rule.id)) {
      // 如果没有跳过次数，升级干预级别
      const upgradedLevel = this.upgradeInterventionLevel(level);
      event.level = upgradedLevel;
      event.type = this.config[upgradedLevel].type;
    }

    this.interventionHistory.push(event);
    
    // 触发干预界面
    await this.executeIntervention(event);
    
    // 通知监听器
    if (this.listeners.onIntervention) {
      this.listeners.onIntervention(event);
    }

    return event;
  }

  // 确定干预级别
  private determineInterventionLevel(rule: BlacklistRule, target: ApplicationInfo | WebsiteInfo): InterventionLevel {
    // 基于规则严重程度
    let level: InterventionLevel = 'gentle';
    
    switch (rule.severity) {
      case 'low':
        level = 'gentle';
        break;
      case 'medium':
        level = 'warning';
        break;
      case 'high':
        level = 'firm';
        break;
    }

    // 基于最近的违规历史调整级别
    const recentViolations = this.getRecentViolations(rule.id, 1); // 最近1小时
    if (recentViolations.length >= 3) {
      level = this.upgradeInterventionLevel(level);
    }

    // 基于当前专注状态调整
    const currentSession = focusMonitorService.getCurrentSession();
    if (currentSession && currentSession.focusScore < 50) {
      level = this.upgradeInterventionLevel(level);
    }

    return level;
  }

  // 升级干预级别
  private upgradeInterventionLevel(currentLevel: InterventionLevel): InterventionLevel {
    const levels: InterventionLevel[] = ['gentle', 'warning', 'firm', 'block'];
    const currentIndex = levels.indexOf(currentLevel);
    return levels[Math.min(currentIndex + 1, levels.length - 1)];
  }

  // 检查用户是否还能跳过
  private canUserSkip(ruleId: string): boolean {
    const skipData = this.userSkipCounts.get(ruleId);
    if (!skipData) return true;

    const config = this.config.gentle; // 使用默认配置
    const hoursSinceReset = (Date.now() - skipData.lastReset.getTime()) / (1000 * 60 * 60);
    
    if (hoursSinceReset >= (config.resetSkipsAfter || 24)) {
      // 重置跳过次数
      this.userSkipCounts.set(ruleId, { count: 0, lastReset: new Date() });
      return true;
    }

    return skipData.count < (config.maxSkips || 5);
  }

  // 执行干预
  private async executeIntervention(event: InterventionEvent): Promise<void> {
    const config = this.config[event.level];
    
    switch (event.type) {
      case 'notification':
        await this.showNotificationIntervention(event, config);
        break;
      case 'modal':
        await this.showModalIntervention(event, config);
        break;
      case 'delay':
        await this.showDelayIntervention(event, config);
        break;
      case 'block':
        await this.showBlockIntervention(event, config);
        break;
      case 'redirect':
        await this.showRedirectIntervention(event, config);
        break;
    }
  }

  // 通知类型干预
  private async showNotificationIntervention(event: InterventionEvent, config: InterventionConfig): Promise<void> {
    const targetName = event.target.name || (event.target as WebsiteInfo).domain;
    
    // 发送桌面通知
    if ('Notification' in window && Notification.permission === 'granted') {
      new Notification('Focus Shield 提醒', {
        body: `您正在访问可能分心的${event.rule.type === 'app' ? '应用' : '网站'}: ${targetName}`,
        icon: '/icon.png'
      });
    }

    // 播放提醒音
    const audioService = AudioService.getInstance();
    audioService.playSystemAlert();

    // 自动标记为温和提醒
    setTimeout(() => {
      if (event.userResponse === 'pending') {
        this.handleUserResponse(event.id, 'continued');
      }
    }, 3000);
  }

  // 模态框类型干预
  private async showModalIntervention(event: InterventionEvent, config: InterventionConfig): Promise<void> {
    // 触发模态框显示事件
    const customEvent = new CustomEvent('show-intervention-modal', {
      detail: { event, config }
    });
    window.dispatchEvent(customEvent);
  }

  // 延迟类型干预
  private async showDelayIntervention(event: InterventionEvent, config: InterventionConfig): Promise<void> {
    const delaySeconds = config.delaySeconds || 10;
    
    // 触发延迟界面显示事件
    const customEvent = new CustomEvent('show-intervention-delay', {
      detail: { event, config, delaySeconds }
    });
    window.dispatchEvent(customEvent);

    // 延迟后自动处理
    setTimeout(() => {
      if (event.userResponse === 'pending') {
        this.handleUserResponse(event.id, 'continued');
      }
    }, delaySeconds * 1000);
  }

  // 阻止类型干预
  private async showBlockIntervention(event: InterventionEvent, config: InterventionConfig): Promise<void> {
    // 触发阻止界面显示事件
    const customEvent = new CustomEvent('show-intervention-block', {
      detail: { event, config }
    });
    window.dispatchEvent(customEvent);

    // 自动标记为已阻止
    this.handleUserResponse(event.id, 'blocked');
  }

  // 重定向类型干预
  private async showRedirectIntervention(event: InterventionEvent, config: InterventionConfig): Promise<void> {
    const redirectUrl = config.redirectUrl || '/dashboard';
    
    // 触发重定向事件
    const customEvent = new CustomEvent('intervention-redirect', {
      detail: { event, config, redirectUrl }
    });
    window.dispatchEvent(customEvent);
  }

  // 处理用户响应
  public handleUserResponse(
    eventId: string,
    response: InterventionEvent['userResponse'],
    responseTime?: number
  ): void {
    const event = this.interventionHistory.find(e => e.id === eventId);
    if (!event) return;

    event.userResponse = response;
    event.responseTime = responseTime || Date.now() - event.timestamp.getTime();

    // 更新跳过次数
    if (response === 'skipped' || response === 'continued') {
      const skipData = this.userSkipCounts.get(event.rule.id) || { count: 0, lastReset: new Date() };
      skipData.count++;
      this.userSkipCounts.set(event.rule.id, skipData);
    }

    // 记录分心行为
    if (response === 'continued' || response === 'skipped') {
      focusMonitorService.recordDistraction({
        type: 'manual',
        description: `用户选择继续访问黑名单${event.rule.type === 'app' ? '应用' : '网站'}: ${event.target.name || (event.target as WebsiteInfo).domain}`,
        severity: event.rule.severity === 'high' ? 'high' : 'medium',
        category: event.rule.category
      });
    }

    // 如果用户返回工作，触发正面强化
    if (response === 'returned') {
      goalBeaconService.triggerGoalBeacon('focus-restored', '很好！您选择了继续专注');
    }

    // 通知监听器
    if (this.listeners.onUserResponse) {
      this.listeners.onUserResponse(event);
    }

    this.saveInterventionHistory();
  }

  // 获取干预统计
  public getInterventionStats(days: number = 7): InterventionStats {
    const cutoff = new Date();
    cutoff.setDate(cutoff.getDate() - days);
    
    const recentEvents = this.interventionHistory.filter(e => e.timestamp >= cutoff);
    
    const totalInterventions = recentEvents.length;
    const successfulBlocks = recentEvents.filter(e => 
      e.userResponse === 'blocked' || e.userResponse === 'returned'
    ).length;
    const userOverrides = recentEvents.filter(e => 
      e.userResponse === 'continued' || e.userResponse === 'skipped'
    ).length;
    
    const responseTimes = recentEvents
      .filter(e => e.responseTime)
      .map(e => e.responseTime!);
    const averageResponseTime = responseTimes.length > 0 
      ? responseTimes.reduce((sum, time) => sum + time, 0) / responseTimes.length
      : 0;
    
    const effectivenessRate = totalInterventions > 0 
      ? (successfulBlocks / totalInterventions) * 100 
      : 0;
    
    const levelDistribution: Record<InterventionLevel, number> = {
      gentle: 0,
      warning: 0,
      firm: 0,
      block: 0
    };
    
    recentEvents.forEach(e => {
      levelDistribution[e.level]++;
    });

    return {
      totalInterventions,
      successfulBlocks,
      userOverrides,
      averageResponseTime,
      effectivenessRate,
      levelDistribution
    };
  }

  // 获取最近违规记录
  private getRecentViolations(ruleId: string, hours: number): InterventionEvent[] {
    const cutoff = new Date();
    cutoff.setHours(cutoff.getHours() - hours);
    
    return this.interventionHistory.filter(e => 
      e.rule.id === ruleId && e.timestamp >= cutoff
    );
  }

  // 更新干预配置
  public updateInterventionConfig(level: InterventionLevel, config: Partial<InterventionConfig>): void {
    this.config[level] = { ...this.config[level], ...config };
    this.saveConfig();
  }

  // 获取干预历史
  public getInterventionHistory(days: number = 7): InterventionEvent[] {
    const cutoff = new Date();
    cutoff.setDate(cutoff.getDate() - days);
    
    return this.interventionHistory.filter(e => e.timestamp >= cutoff);
  }

  // 评估干预效果
  public async evaluateInterventionEffectiveness(eventId: string): Promise<number> {
    const event = this.interventionHistory.find(e => e.id === eventId);
    if (!event) return 0;

    // 检查用户在干预后的行为
    const timeAfterIntervention = 15 * 60 * 1000; // 15分钟
    const endTime = new Date(event.timestamp.getTime() + timeAfterIntervention);
    
    const subsequentViolations = this.interventionHistory.filter(e => 
      e.timestamp > event.timestamp && 
      e.timestamp <= endTime &&
      e.rule.id === event.rule.id
    );

    // 基于后续行为计算效果分数
    let score = 100;
    score -= subsequentViolations.length * 20; // 每次后续违规减20分
    
    if (event.userResponse === 'blocked' || event.userResponse === 'returned') {
      score += 20; // 正面响应加分
    } else if (event.userResponse === 'continued' || event.userResponse === 'skipped') {
      score -= 30; // 负面响应减分
    }

    score = Math.max(0, Math.min(100, score));
    
    event.effectivenessScore = score;
    event.effectivenessScoredAt = new Date();
    
    return score;
  }

  // 事件监听器
  public on(event: 'intervention', callback: (event: InterventionEvent) => void): void;
  public on(event: 'userResponse', callback: (event: InterventionEvent) => void): void;
  public on(event: string, callback: any): void {
    this.listeners[`on${event.charAt(0).toUpperCase() + event.slice(1)}` as keyof typeof this.listeners] = callback;
  }

  // 数据持久化
  private saveInterventionHistory(): void {
    try {
      // 只保存最近30天的数据
      const thirtyDaysAgo = new Date();
      thirtyDaysAgo.setDate(thirtyDaysAgo.getDate() - 30);
      
      const recentHistory = this.interventionHistory.filter(e => e.timestamp >= thirtyDaysAgo);
      
      localStorage.setItem('focusOS_intervention_history', JSON.stringify(recentHistory));
    } catch (error) {
      console.error('保存干预历史失败:', error);
    }
  }

  private loadInterventionHistory(): void {
    try {
      const saved = localStorage.getItem('focusOS_intervention_history');
      if (saved) {
        this.interventionHistory = JSON.parse(saved).map((event: any) => ({
          ...event,
          timestamp: new Date(event.timestamp),
          effectivenessScoredAt: event.effectivenessScoredAt ? new Date(event.effectivenessScoredAt) : undefined
        }));
      }
    } catch (error) {
      console.error('加载干预历史失败:', error);
    }
  }

  private saveUserSkipCounts(): void {
    try {
      const data = Array.from(this.userSkipCounts.entries()).map(([key, value]) => [
        key,
        { ...value, lastReset: value.lastReset.toISOString() }
      ]);
      localStorage.setItem('focusOS_user_skip_counts', JSON.stringify(data));
    } catch (error) {
      console.error('保存跳过次数失败:', error);
    }
  }

  private loadUserSkipCounts(): void {
    try {
      const saved = localStorage.getItem('focusOS_user_skip_counts');
      if (saved) {
        const data = JSON.parse(saved);
        this.userSkipCounts = new Map(
          data.map(([key, value]: [string, any]) => [
            key,
            { ...value, lastReset: new Date(value.lastReset) }
          ])
        );
      }
    } catch (error) {
      console.error('加载跳过次数失败:', error);
    }
  }

  private saveConfig(): void {
    try {
      localStorage.setItem('focusOS_intervention_config', JSON.stringify(this.config));
    } catch (error) {
      console.error('保存干预配置失败:', error);
    }
  }

  private loadConfig(): void {
    try {
      const saved = localStorage.getItem('focusOS_intervention_config');
      if (saved) {
        this.config = { ...this.config, ...JSON.parse(saved) };
      }
    } catch (error) {
      console.error('加载干预配置失败:', error);
    }
  }

  // 清理资源
  public destroy(): void {
    this.saveInterventionHistory();
    this.saveUserSkipCounts();
    this.saveConfig();
  }
}

// 单例实例
export const interventionEngineService = new InterventionEngineService();
export default InterventionEngineService;