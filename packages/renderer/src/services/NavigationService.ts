/**
 * 导航服务
 * 处理应用内的页面导航和参数传递
 */

type NavigationListener = (page: string, params?: any) => void;

class NavigationService {
  private static instance: NavigationService;
  private listeners: NavigationListener[] = [];
  private currentPage: string = 'dashboard';
  private currentParams: any = null;

  private constructor() {}

  static getInstance(): NavigationService {
    if (!NavigationService.instance) {
      NavigationService.instance = new NavigationService();
    }
    return NavigationService.instance;
  }

  /**
   * 添加导航监听器
   */
  addListener(listener: NavigationListener): () => void {
    this.listeners.push(listener);
    return () => {
      const index = this.listeners.indexOf(listener);
      if (index > -1) {
        this.listeners.splice(index, 1);
      }
    };
  }

  /**
   * 导航到指定页面
   */
  navigateTo(page: string, params?: any): void {
    this.currentPage = page;
    this.currentParams = params;
    this.listeners.forEach(listener => listener(page, params));
  }

  /**
   * 获取当前页面
   */
  getCurrentPage(): string {
    return this.currentPage;
  }

  /**
   * 获取当前参数
   */
  getCurrentParams(): any {
    return this.currentParams;
  }

  /**
   * 导航到分解结果页面
   */
  navigateToDecompositionResult(goalId: string): void {
    this.navigateTo('decomposition', { goalId });
  }

  /**
   * 返回目标列表
   */
  navigateToGoals(): void {
    this.navigateTo('goals');
  }

  /**
   * 导航到仪表盘
   */
  navigateToDashboard(): void {
    this.navigateTo('dashboard');
  }
}

export default NavigationService;
