import { Goal } from '../types';

export interface GoalBeaconConfig {
  enabled: boolean;
  triggerInterval: number; // 触发间隔（分钟）
  showOnTaskStart: boolean; // 开始任务时显示
  showOnBreak: boolean; // 休息时显示
  showOnDistraction: boolean; // 分心时显示
  showOnLowFocus: boolean; // 专注度低时显示
  reminderStyle: 'subtle' | 'prominent' | 'immersive'; // 提醒样式
  includeWhyPower: boolean; // 是否包含核心驱动力
  customMessages: string[]; // 自定义激励消息
}

export interface GoalBeaconTrigger {
  id: string;
  type: 'interval' | 'task-start' | 'break' | 'distraction' | 'low-focus' | 'manual';
  timestamp: Date;
  goalId: string;
  context?: string; // 触发上下文
}

export interface GoalBeaconDisplay {
  id: string;
  goalId: string;
  goalName: string;
  whyPower: string;
  progress: number; // 0-100
  motivationalMessage: string;
  visualStyle: 'card' | 'overlay' | 'sidebar' | 'floating';
  duration: number; // 显示时长（秒）
  actions: Array<{
    label: string;
    action: string;
    primary?: boolean;
  }>;
}

class GoalBeaconService {
  private config: GoalBeaconConfig;
  private currentGoals: Goal[] = [];
  private intervalTimer?: NodeJS.Timeout;
  private lastTriggerTime: Date = new Date();
  private displayCallbacks: Array<(display: GoalBeaconDisplay) => void> = [];

  // 激励消息库
  private motivationalMessages = {
    general: [
      '🎯 记住你的目标，每一步都在接近成功！',
      '💪 你的努力正在积累，坚持就是胜利！',
      '🌟 伟大的成就来自持续的小步前进！',
      '🚀 你比昨天的自己更强大！',
      '⭐ 专注当下，成就未来！'
    ],
    taskStart: [
      '🎯 开始新任务！记住你为什么要实现这个目标',
      '💡 这个任务将带你更接近目标，全力以赴！',
      '🔥 点燃你的激情，专注完成这个任务！',
      '⚡ 每个任务都是通向成功的阶梯！'
    ],
    break: [
      '☕ 休息时也别忘记你的目标和初心',
      '🌱 短暂的休息是为了更好的前进',
      '💭 想想完成目标后的美好感受',
      '🎈 放松身心，但保持目标在心中'
    ],
    distraction: [
      '⚠️ 注意！你正在偏离目标轨道',
      '🎯 重新聚焦，回到你的目标上来',
      '💪 抵制诱惑，专注于真正重要的事',
      '🔄 每次重新专注都是意志力的胜利'
    ],
    lowFocus: [
      '🧠 专注度下降了，想想你的目标动力',
      '⚡ 重新点燃你的激情和动力',
      '🎯 回想你设定目标时的决心',
      '💎 专注是实现目标的关键武器'
    ]
  };

  constructor() {
    this.config = this.getDefaultConfig();
    this.loadConfig();
    this.startIntervalTrigger();
  }

  private getDefaultConfig(): GoalBeaconConfig {
    return {
      enabled: true,
      triggerInterval: 30, // 30分钟
      showOnTaskStart: true,
      showOnBreak: true,
      showOnDistraction: true,
      showOnLowFocus: true,
      reminderStyle: 'subtle',
      includeWhyPower: true,
      customMessages: []
    };
  }

  private loadConfig() {
    try {
      const saved = localStorage.getItem('focusOS_goal_beacon_config');
      if (saved) {
        this.config = { ...this.config, ...JSON.parse(saved) };
      }
    } catch (error) {
      console.error('加载目标提醒配置失败:', error);
    }
  }

  public updateConfig(newConfig: Partial<GoalBeaconConfig>) {
    this.config = { ...this.config, ...newConfig };
    localStorage.setItem('focusOS_goal_beacon_config', JSON.stringify(this.config));
    
    // 重启间隔触发器
    this.restartIntervalTrigger();
  }

  public getConfig(): GoalBeaconConfig {
    return { ...this.config };
  }

  // 设置当前目标
  public setCurrentGoals(goals: Goal[]) {
    this.currentGoals = goals.filter(goal => goal.status !== 'completed');
  }

  // 注册显示回调
  public onDisplay(callback: (display: GoalBeaconDisplay) => void) {
    this.displayCallbacks.push(callback);
    return () => {
      const index = this.displayCallbacks.indexOf(callback);
      if (index > -1) {
        this.displayCallbacks.splice(index, 1);
      }
    };
  }

  // 触发目标提醒
  public triggerGoalBeacon(type: GoalBeaconTrigger['type'], context?: string) {
    if (!this.config.enabled || this.currentGoals.length === 0) {
      return;
    }

    // 检查触发条件
    if (!this.shouldTrigger(type)) {
      return;
    }

    // 选择要显示的目标
    const goal = this.selectGoalToDisplay(type);
    if (!goal) return;

    // 创建显示对象
    const display = this.createGoalDisplay(goal, type, context);
    
    // 记录触发
    this.recordTrigger(type, goal.id, context);
    
    // 显示提醒
    this.showGoalBeacon(display);
  }

  private shouldTrigger(type: GoalBeaconTrigger['type']): boolean {
    switch (type) {
      case 'task-start':
        return this.config.showOnTaskStart;
      case 'break':
        return this.config.showOnBreak;
      case 'distraction':
        return this.config.showOnDistraction;
      case 'low-focus':
        return this.config.showOnLowFocus;
      case 'interval':
        // 检查间隔时间
        const timeSinceLastTrigger = Date.now() - this.lastTriggerTime.getTime();
        return timeSinceLastTrigger >= this.config.triggerInterval * 60 * 1000;
      case 'manual':
        return true;
      default:
        return false;
    }
  }

  private selectGoalToDisplay(type: GoalBeaconTrigger['type']): Goal | null {
    if (this.currentGoals.length === 0) return null;

    // 优先选择有强烈驱动力的目标
    const goalsWithWhyPower = this.currentGoals.filter(goal => 
      goal.whyPower && goal.whyPower.length > 50
    );

    if (goalsWithWhyPower.length > 0) {
      // 随机选择一个有强烈驱动力的目标
      return goalsWithWhyPower[Math.floor(Math.random() * goalsWithWhyPower.length)];
    }

    // 否则选择第一个目标
    return this.currentGoals[0];
  }

  private createGoalDisplay(goal: Goal, type: GoalBeaconTrigger['type'], context?: string): GoalBeaconDisplay {
    const motivationalMessage = this.getMotivationalMessage(type);
    const progress = this.calculateGoalProgress(goal);
    
    return {
      id: `beacon-${Date.now()}`,
      goalId: goal.id,
      goalName: goal.name,
      whyPower: goal.whyPower || '',
      progress,
      motivationalMessage,
      visualStyle: this.getVisualStyle(type),
      duration: this.getDisplayDuration(type),
      actions: this.getDisplayActions(type, goal)
    };
  }

  private getMotivationalMessage(type: GoalBeaconTrigger['type']): string {
    const messages = this.motivationalMessages[type] || this.motivationalMessages.general;
    const customMessages = this.config.customMessages;
    
    // 合并自定义消息
    const allMessages = [...messages, ...customMessages];
    
    return allMessages[Math.floor(Math.random() * allMessages.length)];
  }

  private calculateGoalProgress(goal: Goal): number {
    // 简化的进度计算
    if (goal.status === 'completed') return 100;
    if (goal.status === 'in-progress') return 50;
    return 0;
  }

  private getVisualStyle(type: GoalBeaconTrigger['type']): GoalBeaconDisplay['visualStyle'] {
    switch (this.config.reminderStyle) {
      case 'subtle':
        return 'floating';
      case 'prominent':
        return 'card';
      case 'immersive':
        return 'overlay';
      default:
        return 'floating';
    }
  }

  private getDisplayDuration(type: GoalBeaconTrigger['type']): number {
    switch (type) {
      case 'distraction':
      case 'low-focus':
        return 10; // 10秒
      case 'task-start':
        return 8; // 8秒
      case 'break':
        return 6; // 6秒
      default:
        return 5; // 5秒
    }
  }

  private getDisplayActions(type: GoalBeaconTrigger['type'], goal: Goal): GoalBeaconDisplay['actions'] {
    const baseActions = [
      { label: '继续专注', action: 'continue-focus', primary: true },
      { label: '查看目标', action: 'view-goal' },
      { label: '稍后提醒', action: 'snooze' }
    ];

    switch (type) {
      case 'distraction':
        return [
          { label: '重新专注', action: 'refocus', primary: true },
          { label: '记录分心', action: 'record-distraction' },
          { label: '查看目标', action: 'view-goal' }
        ];
      case 'low-focus':
        return [
          { label: '提升专注', action: 'boost-focus', primary: true },
          { label: '休息一下', action: 'take-break' },
          { label: '查看目标', action: 'view-goal' }
        ];
      case 'task-start':
        return [
          { label: '开始任务', action: 'start-task', primary: true },
          { label: '查看目标', action: 'view-goal' },
          { label: '设置提醒', action: 'set-reminder' }
        ];
      default:
        return baseActions;
    }
  }

  private showGoalBeacon(display: GoalBeaconDisplay) {
    this.displayCallbacks.forEach(callback => {
      try {
        callback(display);
      } catch (error) {
        console.error('目标提醒显示回调执行失败:', error);
      }
    });
  }

  private recordTrigger(type: GoalBeaconTrigger['type'], goalId: string, context?: string) {
    this.lastTriggerTime = new Date();
    
    // 记录触发历史（可选）
    try {
      const triggers = this.getTriggerHistory();
      triggers.push({
        id: `trigger-${Date.now()}`,
        type,
        timestamp: new Date(),
        goalId,
        context
      });
      
      // 只保留最近50条记录
      const recentTriggers = triggers.slice(-50);
      localStorage.setItem('focusOS_goal_beacon_triggers', JSON.stringify(recentTriggers));
    } catch (error) {
      console.error('记录目标提醒触发失败:', error);
    }
  }

  private getTriggerHistory(): GoalBeaconTrigger[] {
    try {
      const saved = localStorage.getItem('focusOS_goal_beacon_triggers');
      if (!saved) return [];
      
      return JSON.parse(saved).map((t: any) => ({
        ...t,
        timestamp: new Date(t.timestamp)
      }));
    } catch (error) {
      console.error('加载目标提醒触发历史失败:', error);
      return [];
    }
  }

  // 间隔触发器
  private startIntervalTrigger() {
    this.stopIntervalTrigger();
    
    if (this.config.enabled && this.config.triggerInterval > 0) {
      this.intervalTimer = setInterval(() => {
        this.triggerGoalBeacon('interval');
      }, this.config.triggerInterval * 60 * 1000);
    }
  }

  private stopIntervalTrigger() {
    if (this.intervalTimer) {
      clearInterval(this.intervalTimer);
      this.intervalTimer = undefined;
    }
  }

  private restartIntervalTrigger() {
    this.stopIntervalTrigger();
    this.startIntervalTrigger();
  }

  // 手动触发
  public manualTrigger(goalId?: string) {
    if (goalId) {
      const goal = this.currentGoals.find(g => g.id === goalId);
      if (goal) {
        const display = this.createGoalDisplay(goal, 'manual');
        this.showGoalBeacon(display);
      }
    } else {
      this.triggerGoalBeacon('manual');
    }
  }

  // 暂停和恢复
  public pause() {
    this.stopIntervalTrigger();
  }

  public resume() {
    this.startIntervalTrigger();
  }

  // 清理资源
  public destroy() {
    this.stopIntervalTrigger();
    this.displayCallbacks = [];
  }
}

// 单例实例
export const goalBeaconService = new GoalBeaconService();
export default GoalBeaconService;
