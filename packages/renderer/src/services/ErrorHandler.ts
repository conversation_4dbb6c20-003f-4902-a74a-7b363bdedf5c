/**
 * 全局错误处理系统
 * 提供统一的错误分类、处理和用户反馈机制
 */

import { message, notification } from 'antd';

// 错误类型枚举
export enum ErrorType {
  NETWORK = 'network',
  AI_SERVICE = 'ai_service',
  VALIDATION = 'validation',
  DATABASE = 'database',
  PERMISSION = 'permission',
  TIMEOUT = 'timeout',
  UNKNOWN = 'unknown'
}

// 错误严重级别
export enum ErrorSeverity {
  LOW = 'low',
  MEDIUM = 'medium',
  HIGH = 'high',
  CRITICAL = 'critical'
}

// 错误信息接口
export interface ErrorInfo {
  type: ErrorType;
  severity: ErrorSeverity;
  code?: string;
  message: string;
  details?: any;
  timestamp: number;
  context?: {
    component?: string;
    action?: string;
    userId?: string;
    sessionId?: string;
  };
}

// 错误处理配置
export interface ErrorHandlerConfig {
  enableLogging: boolean;
  enableNotification: boolean;
  enableReporting: boolean;
  maxRetries: number;
  retryDelay: number;
}

// 错误处理器接口
export interface ErrorHandler {
  handle(error: ErrorInfo): void;
}

// 控制台日志处理器
class ConsoleErrorHandler implements ErrorHandler {
  handle(error: ErrorInfo): void {
    const logLevel = this.getLogLevel(error.severity);
    const logMessage = `[${error.type.toUpperCase()}] ${error.message}`;
    
    console[logLevel](logMessage, {
      code: error.code,
      details: error.details,
      context: error.context,
      timestamp: new Date(error.timestamp).toISOString()
    });
  }

  private getLogLevel(severity: ErrorSeverity): 'log' | 'warn' | 'error' {
    switch (severity) {
      case ErrorSeverity.LOW:
        return 'log';
      case ErrorSeverity.MEDIUM:
        return 'warn';
      case ErrorSeverity.HIGH:
      case ErrorSeverity.CRITICAL:
        return 'error';
      default:
        return 'log';
    }
  }
}

// 用户通知处理器
class NotificationErrorHandler implements ErrorHandler {
  handle(error: ErrorInfo): void {
    const userMessage = this.getUserFriendlyMessage(error);
    
    switch (error.severity) {
      case ErrorSeverity.LOW:
        message.info(userMessage);
        break;
      case ErrorSeverity.MEDIUM:
        message.warning(userMessage);
        break;
      case ErrorSeverity.HIGH:
        message.error(userMessage);
        break;
      case ErrorSeverity.CRITICAL:
        notification.error({
          message: '严重错误',
          description: userMessage,
          duration: 0, // 不自动关闭
        });
        break;
    }
  }

  private getUserFriendlyMessage(error: ErrorInfo): string {
    switch (error.type) {
      case ErrorType.NETWORK:
        return '网络连接异常，请检查网络设置后重试';
      case ErrorType.AI_SERVICE:
        return 'AI服务暂时不可用，请稍后重试';
      case ErrorType.VALIDATION:
        return error.message || '输入数据格式不正确，请检查后重试';
      case ErrorType.DATABASE:
        return '数据保存失败，请重试';
      case ErrorType.PERMISSION:
        return '权限不足，无法执行此操作';
      case ErrorType.TIMEOUT:
        return '操作超时，请重试';
      default:
        return error.message || '操作失败，请重试';
    }
  }
}

// 错误报告处理器
class ReportingErrorHandler implements ErrorHandler {
  private errorQueue: ErrorInfo[] = [];
  private maxQueueSize = 100;

  handle(error: ErrorInfo): void {
    // 添加到错误队列
    this.errorQueue.push(error);
    
    // 限制队列大小
    if (this.errorQueue.length > this.maxQueueSize) {
      this.errorQueue.shift();
    }

    // 对于严重错误，立即报告
    if (error.severity === ErrorSeverity.CRITICAL) {
      this.reportError(error);
    }
  }

  private async reportError(error: ErrorInfo): Promise<void> {
    try {
      // TODO: 实现错误报告逻辑
      console.log('报告错误:', error);
    } catch (reportError) {
      console.error('错误报告失败:', reportError);
    }
  }

  getErrorHistory(): ErrorInfo[] {
    return [...this.errorQueue];
  }

  clearHistory(): void {
    this.errorQueue = [];
  }
}

// 主错误管理器
export class ErrorManager {
  private static instance: ErrorManager;
  private handlers: ErrorHandler[] = [];
  private config: ErrorHandlerConfig;

  private constructor(config: Partial<ErrorHandlerConfig> = {}) {
    this.config = {
      enableLogging: true,
      enableNotification: true,
      enableReporting: false,
      maxRetries: 3,
      retryDelay: 1000,
      ...config
    };

    this.initializeHandlers();
  }

  static getInstance(config?: Partial<ErrorHandlerConfig>): ErrorManager {
    if (!ErrorManager.instance) {
      ErrorManager.instance = new ErrorManager(config);
    }
    return ErrorManager.instance;
  }

  private initializeHandlers(): void {
    if (this.config.enableLogging) {
      this.handlers.push(new ConsoleErrorHandler());
    }
    
    if (this.config.enableNotification) {
      this.handlers.push(new NotificationErrorHandler());
    }
    
    if (this.config.enableReporting) {
      this.handlers.push(new ReportingErrorHandler());
    }
  }

  /**
   * 处理错误
   */
  handleError(error: Error | ErrorInfo, context?: Partial<ErrorInfo['context']>): void {
    let errorInfo: ErrorInfo;

    if (error instanceof Error) {
      errorInfo = this.parseError(error, context);
    } else {
      errorInfo = {
        ...error,
        context: { ...error.context, ...context }
      };
    }

    // 分发给所有处理器
    this.handlers.forEach(handler => {
      try {
        handler.handle(errorInfo);
      } catch (handlerError) {
        console.error('错误处理器失败:', handlerError);
      }
    });
  }

  /**
   * 解析原生错误对象
   */
  private parseError(error: Error, context?: Partial<ErrorInfo['context']>): ErrorInfo {
    let type = ErrorType.UNKNOWN;
    let severity = ErrorSeverity.MEDIUM;
    let code: string | undefined;

    // 根据错误信息判断类型
    const message = error.message.toLowerCase();
    
    if (message.includes('network') || message.includes('fetch')) {
      type = ErrorType.NETWORK;
      severity = ErrorSeverity.HIGH;
    } else if (message.includes('timeout')) {
      type = ErrorType.TIMEOUT;
      severity = ErrorSeverity.MEDIUM;
    } else if (message.includes('validation') || message.includes('invalid')) {
      type = ErrorType.VALIDATION;
      severity = ErrorSeverity.LOW;
    } else if (message.includes('permission') || message.includes('unauthorized')) {
      type = ErrorType.PERMISSION;
      severity = ErrorSeverity.HIGH;
    } else if (message.includes('ai') || message.includes('model')) {
      type = ErrorType.AI_SERVICE;
      severity = ErrorSeverity.HIGH;
    }

    // 提取错误代码
    const codeMatch = error.message.match(/code:\s*(\w+)/i);
    if (codeMatch) {
      code = codeMatch[1];
    }

    return {
      type,
      severity,
      code,
      message: error.message,
      details: {
        stack: error.stack,
        name: error.name
      },
      timestamp: Date.now(),
      context
    };
  }

  /**
   * 带重试的操作执行
   */
  async executeWithRetry<T>(
    operation: () => Promise<T>,
    context?: Partial<ErrorInfo['context']>,
    maxRetries?: number
  ): Promise<T> {
    const retries = maxRetries ?? this.config.maxRetries;
    let lastError: Error;

    for (let attempt = 0; attempt <= retries; attempt++) {
      try {
        return await operation();
      } catch (error) {
        lastError = error as Error;
        
        if (attempt === retries) {
          // 最后一次尝试失败，记录错误
          this.handleError(lastError, {
            ...context,
            action: 'retry_failed',
            details: { attempts: attempt + 1 }
          });
          break;
        }

        // 记录重试
        this.handleError(lastError, {
          ...context,
          action: 'retry_attempt',
          details: { attempt: attempt + 1, maxRetries: retries }
        });

        // 等待后重试
        await new Promise(resolve => setTimeout(resolve, this.config.retryDelay));
      }
    }

    throw lastError!;
  }

  /**
   * 创建错误边界包装器
   */
  createErrorBoundary<T extends (...args: any[]) => any>(
    func: T,
    context?: Partial<ErrorInfo['context']>
  ): T {
    return ((...args: Parameters<T>) => {
      try {
        const result = func(...args);
        
        // 如果返回Promise，处理异步错误
        if (result instanceof Promise) {
          return result.catch(error => {
            this.handleError(error, context);
            throw error;
          });
        }
        
        return result;
      } catch (error) {
        this.handleError(error as Error, context);
        throw error;
      }
    }) as T;
  }

  /**
   * 获取错误历史
   */
  getErrorHistory(): ErrorInfo[] {
    const reportingHandler = this.handlers.find(h => h instanceof ReportingErrorHandler) as ReportingErrorHandler;
    return reportingHandler?.getErrorHistory() || [];
  }

  /**
   * 清理错误历史
   */
  clearErrorHistory(): void {
    const reportingHandler = this.handlers.find(h => h instanceof ReportingErrorHandler) as ReportingErrorHandler;
    reportingHandler?.clearHistory();
  }
}

// 导出单例实例
export const errorManager = ErrorManager.getInstance();

// 便捷函数
export const handleError = (error: Error | ErrorInfo, context?: Partial<ErrorInfo['context']>) => {
  errorManager.handleError(error, context);
};

export const executeWithRetry = <T>(
  operation: () => Promise<T>,
  context?: Partial<ErrorInfo['context']>,
  maxRetries?: number
) => {
  return errorManager.executeWithRetry(operation, context, maxRetries);
};

export const createErrorBoundary = <T extends (...args: any[]) => any>(
  func: T,
  context?: Partial<ErrorInfo['context']>
) => {
  return errorManager.createErrorBoundary(func, context);
};
