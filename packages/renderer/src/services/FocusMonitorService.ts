import { reminderService } from './ReminderService';
import { goalBeaconService } from './GoalBeaconService';

export interface FocusSession {
  id: string;
  startTime: Date;
  endTime?: Date;
  taskId?: string;
  taskName?: string;
  focusScore: number; // 0-100
  distractions: DistractionRecord[];
  totalDuration: number; // 分钟
  effectiveDuration: number; // 实际专注时间
}

export interface DistractionRecord {
  id: string;
  timestamp: Date;
  type: 'manual' | 'auto-detected' | 'app-switch' | 'notification';
  description: string;
  duration?: number; // 分心持续时间（秒）
  severity: 'low' | 'medium' | 'high';
  category: string; // 分心类别：社交、娱乐、工作相关等
}

export interface FocusInsight {
  date: Date;
  totalFocusTime: number; // 总专注时间（分钟）
  averageFocusScore: number; // 平均专注分数
  distractionCount: number; // 分心次数
  mostProductiveHour: number; // 最高效的小时
  focusTrend: 'improving' | 'declining' | 'stable';
  suggestions: string[];
}

export interface FocusCheckQuestion {
  id: string;
  question: string;
  type: 'rating' | 'choice' | 'text';
  options?: string[];
  weight: number; // 权重，用于计算专注分数
}

class FocusMonitorService {
  private currentSession: FocusSession | null = null;
  private focusCheckTimer?: NodeJS.Timeout;
  private activityMonitorTimer?: NodeJS.Timeout;
  private lastActivityTime: Date = new Date();
  private focusCheckInterval: number = 15; // 分钟
  private isMonitoring: boolean = false;

  // FR-FM-002: 定时专注检查
  private focusCheckQuestions: FocusCheckQuestion[] = [
    {
      id: 'focus-level',
      question: '你现在的专注程度如何？',
      type: 'rating',
      weight: 0.4
    },
    {
      id: 'task-alignment',
      question: '你现在在做的事情与计划的任务一致吗？',
      type: 'choice',
      options: ['完全一致', '基本一致', '有些偏离', '完全不一致'],
      weight: 0.3
    },
    {
      id: 'distraction-level',
      question: '在过去的时间里，你被什么干扰了？',
      type: 'choice',
      options: ['没有干扰', '轻微干扰', '中等干扰', '严重干扰'],
      weight: 0.3
    }
  ];

  constructor() {
    this.loadSettings();
    this.startActivityMonitoring();
  }

  private loadSettings() {
    try {
      const saved = localStorage.getItem('focusOS_focus_settings');
      if (saved) {
        const settings = JSON.parse(saved);
        this.focusCheckInterval = settings.focusCheckInterval || 15;
      }
    } catch (error) {
      console.error('加载专注设置失败:', error);
    }
  }

  public updateSettings(settings: { focusCheckInterval: number }) {
    this.focusCheckInterval = settings.focusCheckInterval;
    localStorage.setItem('focusOS_focus_settings', JSON.stringify(settings));
    
    if (this.isMonitoring) {
      this.stopFocusChecks();
      this.startFocusChecks();
    }
  }

  // 开始专注会话
  public startFocusSession(taskId?: string, taskName?: string): FocusSession {
    this.currentSession = {
      id: `focus-${Date.now()}`,
      startTime: new Date(),
      taskId,
      taskName,
      focusScore: 100,
      distractions: [],
      totalDuration: 0,
      effectiveDuration: 0
    };

    this.isMonitoring = true;
    this.startFocusChecks();
    this.recordActivity();

    return this.currentSession;
  }

  // 结束专注会话
  public endFocusSession(): FocusSession | null {
    if (!this.currentSession) return null;

    this.currentSession.endTime = new Date();
    this.currentSession.totalDuration = Math.round(
      (this.currentSession.endTime.getTime() - this.currentSession.startTime.getTime()) / (1000 * 60)
    );

    // 计算有效专注时间（总时间 - 分心时间）
    const totalDistractionTime = this.currentSession.distractions.reduce(
      (sum, d) => sum + (d.duration || 0), 0
    ) / 60; // 转换为分钟

    this.currentSession.effectiveDuration = Math.max(
      0, 
      this.currentSession.totalDuration - totalDistractionTime
    );

    // 保存会话记录
    this.saveFocusSession(this.currentSession);

    this.stopFocusChecks();
    this.isMonitoring = false;

    const session = this.currentSession;
    this.currentSession = null;
    return session;
  }

  // 开始定时专注检查
  private startFocusChecks() {
    this.stopFocusChecks();
    
    this.focusCheckTimer = setInterval(() => {
      this.performFocusCheck();
    }, this.focusCheckInterval * 60 * 1000);
  }

  private stopFocusChecks() {
    if (this.focusCheckTimer) {
      clearInterval(this.focusCheckTimer);
      this.focusCheckTimer = undefined;
    }
  }

  // 执行专注检查
  private performFocusCheck() {
    if (!this.currentSession) return;

    // 通过提醒服务发送专注检查通知
    reminderService.onNotification((notification) => {
      if (notification.type === 'focus-pacer') {
        // 这里可以触发专注检查UI
        this.showFocusCheckDialog();
      }
    });
  }

  private showFocusCheckDialog() {
    // 这个方法会被UI组件调用来显示专注检查对话框
    // 实际的UI显示逻辑在组件中实现
    const event = new CustomEvent('focus-check-required', {
      detail: {
        questions: this.focusCheckQuestions,
        sessionId: this.currentSession?.id
      }
    });
    window.dispatchEvent(event);
  }

  // 处理专注检查结果
  public handleFocusCheckResult(answers: { [questionId: string]: any }) {
    if (!this.currentSession) return;

    let totalScore = 0;
    let totalWeight = 0;

    this.focusCheckQuestions.forEach(question => {
      const answer = answers[question.id];
      let score = 0;

      switch (question.type) {
        case 'rating':
          score = (answer || 0) * 20; // 1-5 转换为 20-100
          break;
        case 'choice':
          if (question.options) {
            const index = question.options.indexOf(answer);
            score = index >= 0 ? (question.options.length - index) * (100 / question.options.length) : 0;
          }
          break;
      }

      totalScore += score * question.weight;
      totalWeight += question.weight;
    });

    const focusScore = totalWeight > 0 ? totalScore / totalWeight : 100;
    this.currentSession.focusScore = Math.round(
      (this.currentSession.focusScore + focusScore) / 2
    );

    // 如果专注分数较低，记录为分心并触发目标提醒
    if (focusScore < 60) {
      this.recordDistraction({
        type: 'manual',
        description: '专注检查显示注意力分散',
        severity: focusScore < 40 ? 'high' : 'medium',
        category: '注意力分散'
      });

      // 触发目标提醒
      if (focusScore < 40) {
        goalBeaconService.triggerGoalBeacon('low-focus', '专注检查显示专注度较低');
      }
    }
  }

  // FR-FM-004: 记录分心
  public recordDistraction(distraction: Omit<DistractionRecord, 'id' | 'timestamp'>) {
    if (!this.currentSession) return;

    const record: DistractionRecord = {
      id: `distraction-${Date.now()}`,
      timestamp: new Date(),
      ...distraction
    };

    this.currentSession.distractions.push(record);

    // 根据分心严重程度调整专注分数
    const penalty = {
      low: 2,
      medium: 5,
      high: 10
    }[distraction.severity];

    this.currentSession.focusScore = Math.max(0, this.currentSession.focusScore - penalty);

    // 如果是严重分心，触发目标提醒
    if (distraction.severity === 'high') {
      goalBeaconService.triggerGoalBeacon('distraction', distraction.description);
    }
  }

  // 活动监控
  private startActivityMonitoring() {
    // 监听用户活动
    const events = ['mousedown', 'mousemove', 'keypress', 'scroll', 'touchstart'];
    
    events.forEach(event => {
      document.addEventListener(event, () => {
        this.recordActivity();
      }, true);
    });

    // 定期检查不活跃状态
    this.activityMonitorTimer = setInterval(() => {
      this.checkInactivity();
    }, 30000); // 每30秒检查一次
  }

  private recordActivity() {
    this.lastActivityTime = new Date();
  }

  private checkInactivity() {
    if (!this.isMonitoring) return;

    const inactiveTime = Date.now() - this.lastActivityTime.getTime();
    const inactiveMinutes = inactiveTime / (1000 * 60);

    // 如果超过5分钟不活跃，记录为分心
    if (inactiveMinutes > 5) {
      this.recordDistraction({
        type: 'auto-detected',
        description: `检测到${Math.round(inactiveMinutes)}分钟无活动`,
        duration: inactiveTime / 1000,
        severity: inactiveMinutes > 15 ? 'high' : inactiveMinutes > 10 ? 'medium' : 'low',
        category: '长时间无活动'
      });
    }
  }

  // FR-FM-003: 时间与进度洞察
  public getFocusInsights(days: number = 7): FocusInsight[] {
    const sessions = this.getFocusSessions(days);
    const insights: FocusInsight[] = [];

    // 按日期分组
    const sessionsByDate = new Map<string, FocusSession[]>();
    sessions.forEach(session => {
      const dateKey = session.startTime.toISOString().split('T')[0];
      if (!sessionsByDate.has(dateKey)) {
        sessionsByDate.set(dateKey, []);
      }
      sessionsByDate.get(dateKey)!.push(session);
    });

    sessionsByDate.forEach((daySessions, dateKey) => {
      const date = new Date(dateKey);
      const totalFocusTime = daySessions.reduce((sum, s) => sum + s.effectiveDuration, 0);
      const averageFocusScore = daySessions.reduce((sum, s) => sum + s.focusScore, 0) / daySessions.length;
      const distractionCount = daySessions.reduce((sum, s) => sum + s.distractions.length, 0);

      // 计算最高效的小时
      const hourlyFocus = new Array(24).fill(0);
      daySessions.forEach(session => {
        const hour = session.startTime.getHours();
        hourlyFocus[hour] += session.effectiveDuration;
      });
      const mostProductiveHour = hourlyFocus.indexOf(Math.max(...hourlyFocus));

      // 生成建议
      const suggestions = this.generateFocusSuggestions(daySessions);

      insights.push({
        date,
        totalFocusTime,
        averageFocusScore,
        distractionCount,
        mostProductiveHour,
        focusTrend: 'stable', // 简化实现
        suggestions
      });
    });

    return insights.sort((a, b) => b.date.getTime() - a.date.getTime());
  }

  private generateFocusSuggestions(sessions: FocusSession[]): string[] {
    const suggestions: string[] = [];
    
    const avgScore = sessions.reduce((sum, s) => sum + s.focusScore, 0) / sessions.length;
    const totalDistractions = sessions.reduce((sum, s) => sum + s.distractions.length, 0);

    if (avgScore < 70) {
      suggestions.push('尝试减少干扰源，创造更安静的工作环境');
    }

    if (totalDistractions > 10) {
      suggestions.push('考虑使用番茄工作法，将工作分解为更短的专注时段');
    }

    const commonDistractionTypes = this.getCommonDistractionTypes(sessions);
    if (commonDistractionTypes.length > 0) {
      suggestions.push(`主要干扰来源：${commonDistractionTypes.join('、')}，建议针对性改善`);
    }

    return suggestions;
  }

  private getCommonDistractionTypes(sessions: FocusSession[]): string[] {
    const typeCount = new Map<string, number>();
    
    sessions.forEach(session => {
      session.distractions.forEach(d => {
        typeCount.set(d.category, (typeCount.get(d.category) || 0) + 1);
      });
    });

    return Array.from(typeCount.entries())
      .sort((a, b) => b[1] - a[1])
      .slice(0, 3)
      .map(([type]) => type);
  }

  // 数据持久化
  private saveFocusSession(session: FocusSession) {
    try {
      const sessions = this.getFocusSessions(30); // 保留30天的数据
      sessions.push(session);
      
      // 只保留最近30天的数据
      const thirtyDaysAgo = new Date();
      thirtyDaysAgo.setDate(thirtyDaysAgo.getDate() - 30);
      
      const filteredSessions = sessions.filter(s => s.startTime >= thirtyDaysAgo);
      
      localStorage.setItem('focusOS_focus_sessions', JSON.stringify(filteredSessions));
    } catch (error) {
      console.error('保存专注会话失败:', error);
    }
  }

  private getFocusSessions(days: number): FocusSession[] {
    try {
      const saved = localStorage.getItem('focusOS_focus_sessions');
      if (!saved) return [];

      const sessions = JSON.parse(saved).map((s: any) => ({
        ...s,
        startTime: new Date(s.startTime),
        endTime: s.endTime ? new Date(s.endTime) : undefined,
        distractions: s.distractions.map((d: any) => ({
          ...d,
          timestamp: new Date(d.timestamp)
        }))
      }));

      const cutoffDate = new Date();
      cutoffDate.setDate(cutoffDate.getDate() - days);

      return sessions.filter((s: FocusSession) => s.startTime >= cutoffDate);
    } catch (error) {
      console.error('加载专注会话失败:', error);
      return [];
    }
  }

  public getCurrentSession(): FocusSession | null {
    return this.currentSession;
  }

  public isCurrentlyMonitoring(): boolean {
    return this.isMonitoring;
  }

  // 清理资源
  public destroy() {
    this.stopFocusChecks();
    if (this.activityMonitorTimer) {
      clearInterval(this.activityMonitorTimer);
    }
  }
}

// 单例实例
export const focusMonitorService = new FocusMonitorService();
export default FocusMonitorService;
