export interface DeleteAnalysis {
  goalId: string;
  goalName: string;
  subGoalsCount: number;
  milestonesCount: number;
  tasksCount: number;
  pomodoroSessionsCount: number;
  decompositionSessionsCount: number;
  userModificationsCount: number;
  totalItemsCount: number;
  subGoals: Array<{
    id: string;
    name: string;
    milestonesCount: number;
    tasksCount: number;
  }>;
  milestones: Array<{
    id: string;
    name: string;
    tasksCount: number;
  }>;
  tasks: Array<{
    id: string;
    title: string;
    parentType: string;
    parentId: string;
  }>;
}

export interface DeleteResult {
  success: boolean;
  deletedItems: {
    goals: number;
    subGoals: number;
    milestones: number;
    tasks: number;
    pomodoroSessions: number;
    decompositionSessions: number;
    userModifications: number;
  };
  error?: string;
}

export class CascadeDeleteService {
  private static instance: CascadeDeleteService;

  public static getInstance(): CascadeDeleteService {
    if (!CascadeDeleteService.instance) {
      CascadeDeleteService.instance = new CascadeDeleteService();
    }
    return CascadeDeleteService.instance;
  }

  /**
   * 分析删除目标的影响
   */
  public async analyzeGoalDeletion(goalId: string): Promise<DeleteAnalysis> {
    try {
      const { DatabaseAPI } = await import('./api');
      const result = await DatabaseAPI.analyzeGoalDeletion(goalId);
      return result;
    } catch (error) {
      console.error('分析目标删除影响失败:', error);
      throw new Error('分析删除影响失败，请重试');
    }
  }

  /**
   * 执行级联删除
   */
  public async cascadeDeleteGoal(goalId: string): Promise<DeleteResult> {
    try {
      const { DatabaseAPI } = await import('./api');
      const result = await DatabaseAPI.cascadeDeleteGoal(goalId);
      return result;
    } catch (error) {
      console.error('级联删除目标失败:', error);
      throw new Error('删除操作失败，请重试');
    }
  }

  /**
   * 软删除目标（标记为已删除，但不实际删除数据）
   */
  public async softDeleteGoal(goalId: string): Promise<boolean> {
    try {
      const result = await window.electronAPI.softDeleteGoal(goalId);
      return result;
    } catch (error) {
      console.error('软删除目标失败:', error);
      throw new Error('软删除操作失败，请重试');
    }
  }

  /**
   * 恢复软删除的目标
   */
  public async restoreGoal(goalId: string): Promise<boolean> {
    try {
      const result = await window.electronAPI.restoreGoal(goalId);
      return result;
    } catch (error) {
      console.error('恢复目标失败:', error);
      throw new Error('恢复操作失败，请重试');
    }
  }

  /**
   * 获取已软删除的目标列表
   */
  public async getSoftDeletedGoals(): Promise<any[]> {
    try {
      const result = await window.electronAPI.getSoftDeletedGoals();
      return result;
    } catch (error) {
      console.error('获取已删除目标列表失败:', error);
      return [];
    }
  }

  /**
   * 永久删除软删除的目标
   */
  public async permanentlyDeleteGoal(goalId: string): Promise<DeleteResult> {
    try {
      const result = await window.electronAPI.permanentlyDeleteGoal(goalId);
      return result;
    } catch (error) {
      console.error('永久删除目标失败:', error);
      throw new Error('永久删除操作失败，请重试');
    }
  }

  /**
   * 批量删除多个目标
   */
  public async batchDeleteGoals(goalIds: string[]): Promise<{
    successful: string[];
    failed: Array<{ goalId: string; error: string }>;
    totalDeleted: number;
  }> {
    const successful: string[] = [];
    const failed: Array<{ goalId: string; error: string }> = [];
    let totalDeleted = 0;

    for (const goalId of goalIds) {
      try {
        const result = await this.cascadeDeleteGoal(goalId);
        if (result.success) {
          successful.push(goalId);
          totalDeleted += Object.values(result.deletedItems).reduce((sum, count) => sum + count, 0);
        } else {
          failed.push({ goalId, error: result.error || '删除失败' });
        }
      } catch (error) {
        failed.push({ 
          goalId, 
          error: error instanceof Error ? error.message : '未知错误' 
        });
      }
    }

    return {
      successful,
      failed,
      totalDeleted
    };
  }

  /**
   * 验证删除权限
   */
  public async validateDeletePermission(goalId: string): Promise<{
    canDelete: boolean;
    reason?: string;
    warnings?: string[];
  }> {
    try {
      // 检查目标是否存在
      const goal = await window.electronAPI.getGoalById(goalId);
      if (!goal) {
        return {
          canDelete: false,
          reason: '目标不存在'
        };
      }

      // 检查是否有正在进行的番茄钟会话
      const activeSessions = await window.electronAPI.getActivePomodoroSessions(goalId);
      const warnings: string[] = [];

      if (activeSessions.length > 0) {
        warnings.push('存在正在进行的番茄钟会话，删除后会话将被终止');
      }

      // 检查是否有未完成的AI分解会话
      const activeDecomposition = await window.electronAPI.getActiveDecompositionSession(goalId);
      if (activeDecomposition) {
        warnings.push('存在未完成的AI分解会话，删除后会话将丢失');
      }

      // 检查是否有重要的历史数据
      const analysis = await this.analyzeGoalDeletion(goalId);
      if (analysis.pomodoroSessionsCount > 10) {
        warnings.push(`将删除 ${analysis.pomodoroSessionsCount} 条番茄钟记录`);
      }

      return {
        canDelete: true,
        warnings: warnings.length > 0 ? warnings : undefined
      };
    } catch (error) {
      console.error('验证删除权限失败:', error);
      return {
        canDelete: false,
        reason: '验证权限时发生错误'
      };
    }
  }

  /**
   * 获取删除统计信息
   */
  public async getDeleteStatistics(): Promise<{
    totalDeleted: number;
    deletedToday: number;
    deletedThisWeek: number;
    deletedThisMonth: number;
    recentDeletions: Array<{
      goalName: string;
      deletedAt: Date;
      itemsCount: number;
    }>;
  }> {
    try {
      const result = await window.electronAPI.getDeleteStatistics();
      return {
        ...result,
        recentDeletions: result.recentDeletions.map((deletion: any) => ({
          ...deletion,
          deletedAt: new Date(deletion.deletedAt)
        }))
      };
    } catch (error) {
      console.error('获取删除统计信息失败:', error);
      return {
        totalDeleted: 0,
        deletedToday: 0,
        deletedThisWeek: 0,
        deletedThisMonth: 0,
        recentDeletions: []
      };
    }
  }
}

// 导出单例实例
export const cascadeDeleteService = CascadeDeleteService.getInstance();
