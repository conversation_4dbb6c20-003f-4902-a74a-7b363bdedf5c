import { applicationMonitorService, ApplicationInfo, WebsiteInfo } from './ApplicationMonitorService';
import { blacklistManagerService, BlacklistRule } from './BlacklistManagerService';
import { interventionEngineService, InterventionEvent } from './InterventionEngineService';
import { focusMonitorService } from './FocusMonitorService';
import { goalBeaconService } from './GoalBeaconService';

// Focus Shield 配置
export interface FocusShieldConfig {
  isEnabled: boolean;
  enabledInPomodoroMode: boolean;
  enabledInDeepFocusMode: boolean;
  enabledInBreakTime: boolean;
  autoStartWithFocusSession: boolean;
  interventionLevel: 'gentle' | 'warning' | 'firm' | 'block';
  monitorApps: boolean;
  monitorWebsites: boolean;
  enableSmartDetection: boolean; // 智能检测新的分心源
  notificationSettings: {
    showDesktopNotifications: boolean;
    playAudioAlerts: boolean;
    vibrationEnabled: boolean;
  };
}

// Focus Shield 状态
export interface FocusShieldStatus {
  isActive: boolean;
  isMonitoring: boolean;
  currentMode: 'off' | 'pomodoro' | 'deep-focus' | 'break' | 'manual';
  sessionStartTime?: Date;
  totalInterventionsToday: number;
  successfulBlocksToday: number;
  lastActivity?: {
    type: 'app' | 'website';
    name: string;
    isBlacklisted: boolean;
    timestamp: Date;
  };
}

// Focus Shield 报告
export interface FocusShieldReport {
  date: Date;
  totalMonitoringTime: number; // 总监控时间（分钟）
  interventions: {
    total: number;
    byLevel: Record<string, number>;
    successRate: number;
  };
  topDistractions: {
    name: string;
    type: 'app' | 'website';
    count: number;
    totalTime: number;
  }[];
  focusScore: number; // 综合专注分数
  recommendations: string[];
}

class FocusShieldService {
  private config: FocusShieldConfig = {
    isEnabled: true,
    enabledInPomodoroMode: true,
    enabledInDeepFocusMode: true,
    enabledInBreakTime: false,
    autoStartWithFocusSession: true,
    interventionLevel: 'warning',
    monitorApps: true,
    monitorWebsites: true,
    enableSmartDetection: true,
    notificationSettings: {
      showDesktopNotifications: true,
      playAudioAlerts: true,
      vibrationEnabled: false
    }
  };

  private status: FocusShieldStatus = {
    isActive: false,
    isMonitoring: false,
    currentMode: 'off',
    totalInterventionsToday: 0,
    successfulBlocksToday: 0
  };

  private listeners: {
    onStatusChange?: (status: FocusShieldStatus) => void;
    onIntervention?: (event: InterventionEvent) => void;
    onBlocklistViolation?: (target: ApplicationInfo | WebsiteInfo, rule: BlacklistRule) => void;
  } = {};

  constructor() {
    this.loadConfig();
    this.loadStatus();
    this.setupEventListeners();
    this.initializeServices();
  }

  // 启动 Focus Shield
  public async start(mode: 'pomodoro' | 'deep-focus' | 'manual' = 'manual'): Promise<void> {
    if (!this.config.isEnabled) {
      console.log('Focus Shield is disabled');
      return;
    }

    // 检查模式兼容性
    if (!this.isModeEnabled(mode)) {
      console.log(`Focus Shield is disabled for ${mode} mode`);
      return;
    }

    this.status.isActive = true;
    this.status.isMonitoring = true;
    this.status.currentMode = mode;
    this.status.sessionStartTime = new Date();

    // 启动应用监控
    if (this.config.monitorApps || this.config.monitorWebsites) {
      applicationMonitorService.startMonitoring();
    }

    // 启动专注监控
    if (focusMonitorService.isCurrentlyMonitoring()) {
      // 如果已经在监控中，继续使用当前会话
    } else {
      focusMonitorService.startFocusSession();
    }

    this.saveStatus();
    this.notifyStatusChange();
    console.log(`Focus Shield started in ${mode} mode`);
  }

  // 停止 Focus Shield
  public async stop(): Promise<void> {
    this.status.isActive = false;
    this.status.isMonitoring = false;
    this.status.currentMode = 'off';
    this.status.sessionStartTime = undefined;

    // 停止应用监控
    applicationMonitorService.stopMonitoring();

    this.saveStatus();
    this.notifyStatusChange();
    console.log('Focus Shield stopped');
  }

  // 暂停监控
  public pause(): void {
    this.status.isMonitoring = false;
    applicationMonitorService.stopMonitoring();
    this.saveStatus();
    this.notifyStatusChange();
  }

  // 恢复监控
  public resume(): void {
    if (this.status.isActive) {
      this.status.isMonitoring = true;
      if (this.config.monitorApps) {
        applicationMonitorService.startMonitoring();
      }
      this.saveStatus();
      this.notifyStatusChange();
    }
  }

  // 检查模式是否启用
  private isModeEnabled(mode: string): boolean {
    switch (mode) {
      case 'pomodoro':
        return this.config.enabledInPomodoroMode;
      case 'deep-focus':
        return this.config.enabledInDeepFocusMode;
      case 'break':
        return this.config.enabledInBreakTime;
      default:
        return true;
    }
  }

  // 设置事件监听器
  private setupEventListeners(): void {
    // 监听应用切换
    applicationMonitorService.on('appChanged', (app: ApplicationInfo) => {
      this.handleActivityChange('app', app);
    });

    // 监听网站切换
    applicationMonitorService.on('websiteChanged', (website: WebsiteInfo) => {
      this.handleActivityChange('website', website);
    });

    // 监听黑名单访问
    applicationMonitorService.on('blacklistAccess', (target: ApplicationInfo | WebsiteInfo) => {
      this.handleBlacklistAccess(target);
    });

    // 监听干预响应
    interventionEngineService.on('userResponse', (event: InterventionEvent) => {
      this.handleInterventionResponse(event);
    });
  }

  // 初始化相关服务
  private initializeServices(): void {
    // 确保所有服务都已初始化
    // 服务会在导入时自动初始化，这里只是确保它们已加载
  }

  // 处理活动变化
  private async handleActivityChange(type: 'app' | 'website', target: ApplicationInfo | WebsiteInfo): Promise<void> {
    if (!this.status.isMonitoring) return;

    // 更新最后活动
    this.status.lastActivity = {
      type,
      name: target.name || (target as WebsiteInfo).domain,
      isBlacklisted: await blacklistManagerService.isBlacklisted(type, target),
      timestamp: new Date()
    };

    this.saveStatus();
    this.notifyStatusChange();

    // 检查是否需要干预
    if (this.status.lastActivity.isBlacklisted) {
      await this.handleBlacklistAccess(target);
    }
  }

  // 处理黑名单访问
  private async handleBlacklistAccess(target: ApplicationInfo | WebsiteInfo): Promise<void> {
    try {
      // 获取匹配的黑名单规则
      const blacklistRules = blacklistManagerService.getBlacklistRules();
      const matchingRule = blacklistRules.find(rule => {
        const type = 'bundleId' in target || 'executable' in target ? 'app' : 'website';
        return rule.isActive && rule.type === type && this.matchesPattern(rule.pattern, target);
      });

      if (!matchingRule) return;

      // 触发干预
      const event = await interventionEngineService.triggerIntervention(target, matchingRule);
      
      this.status.totalInterventionsToday++;
      this.saveStatus();
      this.notifyStatusChange();

      // 通知监听器
      if (this.listeners.onBlocklistViolation) {
        this.listeners.onBlocklistViolation(target, matchingRule);
      }

      if (this.listeners.onIntervention) {
        this.listeners.onIntervention(event);
      }

    } catch (error) {
      console.error('处理黑名单访问失败:', error);
    }
  }

  // 简单的模式匹配逻辑
  private matchesPattern(pattern: string, target: ApplicationInfo | WebsiteInfo): boolean {
    const targetString = target.name || (target as WebsiteInfo).domain || '';
    
    if (pattern.includes('|')) {
      const patterns = pattern.split('|');
      return patterns.some(p => targetString.toLowerCase().includes(p.toLowerCase()));
    } else {
      return targetString.toLowerCase().includes(pattern.toLowerCase());
    }
  }

  // 处理干预响应
  private handleInterventionResponse(event: InterventionEvent): void {
    if (event.userResponse === 'blocked' || event.userResponse === 'returned') {
      this.status.successfulBlocksToday++;
      this.saveStatus();
      this.notifyStatusChange();

      // 如果用户选择返回工作，触发正面强化
      if (event.userResponse === 'returned') {
        goalBeaconService.triggerGoalBeacon('intervention-success', '很好！您选择了专注');
      }
    }
  }

  // 获取今日报告
  public getTodayReport(): FocusShieldReport {
    const today = new Date();
    const interventionStats = interventionEngineService.getInterventionStats(1);
    const focusInsights = focusMonitorService.getFocusInsights(1);
    const todayInsight = focusInsights[0];

    // 计算综合专注分数
    const focusScore = this.calculateFocusScore(interventionStats, todayInsight);

    // 获取top分心源
    const topDistractions = this.getTopDistractions();

    // 生成建议
    const recommendations = this.generateRecommendations(interventionStats, todayInsight);

    return {
      date: today,
      totalMonitoringTime: this.getMonitoringTimeToday(),
      interventions: {
        total: interventionStats.totalInterventions,
        byLevel: interventionStats.levelDistribution,
        successRate: interventionStats.effectivenessRate
      },
      topDistractions,
      focusScore,
      recommendations
    };
  }

  // 计算综合专注分数
  private calculateFocusScore(interventionStats: any, focusInsight: any): number {
    let score = 100;

    // 基于干预次数扣分
    if (interventionStats.totalInterventions > 0) {
      score -= Math.min(interventionStats.totalInterventions * 5, 30);
    }

    // 基于专注分数
    if (focusInsight) {
      score = (score + focusInsight.averageFocusScore) / 2;
    }

    // 基于干预成功率加分
    if (interventionStats.effectivenessRate > 70) {
      score += 10;
    }

    return Math.max(0, Math.min(100, Math.round(score)));
  }

  // 获取今日监控时间
  private getMonitoringTimeToday(): number {
    if (!this.status.sessionStartTime) return 0;
    
    const now = new Date();
    const sessionStart = this.status.sessionStartTime;
    
    // 检查是否是今天开始的会话
    if (sessionStart.toDateString() !== now.toDateString()) {
      return 0;
    }

    return Math.round((now.getTime() - sessionStart.getTime()) / (1000 * 60));
  }

  // 获取top分心源
  private getTopDistractions(): FocusShieldReport['topDistractions'] {
    const interventionHistory = interventionEngineService.getInterventionHistory(1);
    const distractionMap = new Map<string, { count: number; totalTime: number; type: 'app' | 'website' }>();

    interventionHistory.forEach(event => {
      const name = event.target.name || (event.target as WebsiteInfo).domain;
      const type = 'bundleId' in event.target || 'executable' in event.target ? 'app' : 'website';
      
      if (distractionMap.has(name)) {
        const data = distractionMap.get(name)!;
        data.count++;
        data.totalTime += event.responseTime || 0;
      } else {
        distractionMap.set(name, {
          count: 1,
          totalTime: event.responseTime || 0,
          type
        });
      }
    });

    return Array.from(distractionMap.entries())
      .map(([name, data]) => ({
        name,
        type: data.type,
        count: data.count,
        totalTime: Math.round(data.totalTime / 1000 / 60) // 转换为分钟
      }))
      .sort((a, b) => b.count - a.count)
      .slice(0, 5);
  }

  // 生成建议
  private generateRecommendations(interventionStats: any, focusInsight: any): string[] {
    const recommendations: string[] = [];

    if (interventionStats.totalInterventions > 10) {
      recommendations.push('今日干预次数较多，建议检查和调整黑名单规则');
    }

    if (interventionStats.effectivenessRate < 50) {
      recommendations.push('干预效果不佳，建议提高干预级别或调整策略');
    }

    if (focusInsight && focusInsight.averageFocusScore < 70) {
      recommendations.push('专注分数偏低，建议减少干扰源或使用番茄工作法');
    }

    if (this.status.totalInterventionsToday === 0) {
      recommendations.push('今日表现优秀！保持这种专注状态');
    }

    return recommendations;
  }

  // 更新配置
  public updateConfig(newConfig: Partial<FocusShieldConfig>): void {
    this.config = { ...this.config, ...newConfig };
    this.saveConfig();

    // 如果配置发生重大变化，重启服务
    if ('isEnabled' in newConfig || 'monitorApps' in newConfig || 'monitorWebsites' in newConfig) {
      if (this.status.isActive) {
        this.stop().then(() => {
          if (newConfig.isEnabled !== false) {
            this.start(this.status.currentMode as any);
          }
        });
      }
    }
  }

  // 获取当前配置
  public getConfig(): FocusShieldConfig {
    return { ...this.config };
  }

  // 获取当前状态
  public getStatus(): FocusShieldStatus {
    return { ...this.status };
  }

  // 事件监听器
  public on(event: 'statusChange', callback: (status: FocusShieldStatus) => void): void;
  public on(event: 'intervention', callback: (event: InterventionEvent) => void): void;
  public on(event: 'blacklistViolation', callback: (target: ApplicationInfo | WebsiteInfo, rule: BlacklistRule) => void): void;
  public on(event: string, callback: any): void {
    this.listeners[`on${event.charAt(0).toUpperCase() + event.slice(1)}` as keyof typeof this.listeners] = callback;
  }

  // 通知状态变化
  private notifyStatusChange(): void {
    if (this.listeners.onStatusChange) {
      this.listeners.onStatusChange(this.status);
    }
  }

  // 手动触发干预测试
  public async testIntervention(level: 'gentle' | 'warning' | 'firm' | 'block' = 'warning'): Promise<void> {
    const mockTarget: ApplicationInfo = {
      id: 'test-app',
      name: '测试应用',
      isActive: true,
      lastActiveTime: new Date(),
      category: 'entertainment'
    };

    const mockRule: BlacklistRule = {
      id: 'test-rule',
      name: '测试规则',
      type: 'app',
      pattern: '测试应用',
      category: '测试',
      isActive: true,
      severity: level === 'block' ? 'high' : level === 'firm' ? 'medium' : 'low',
      createdAt: new Date(),
      updatedAt: new Date()
    };

    await interventionEngineService.triggerIntervention(mockTarget, mockRule);
  }

  // 数据持久化
  private saveConfig(): void {
    try {
      localStorage.setItem('focusOS_focus_shield_config', JSON.stringify(this.config));
    } catch (error) {
      console.error('保存Focus Shield配置失败:', error);
    }
  }

  private loadConfig(): void {
    try {
      const saved = localStorage.getItem('focusOS_focus_shield_config');
      if (saved) {
        this.config = { ...this.config, ...JSON.parse(saved) };
      }
    } catch (error) {
      console.error('加载Focus Shield配置失败:', error);
    }
  }

  private saveStatus(): void {
    try {
      // 保存状态时，将Date对象转换为ISO字符串
      const statusToSave = {
        ...this.status,
        sessionStartTime: this.status.sessionStartTime?.toISOString(),
        lastActivity: this.status.lastActivity ? {
          ...this.status.lastActivity,
          timestamp: this.status.lastActivity.timestamp.toISOString()
        } : undefined
      };
      localStorage.setItem('focusOS_focus_shield_status', JSON.stringify(statusToSave));
    } catch (error) {
      console.error('保存Focus Shield状态失败:', error);
    }
  }

  private loadStatus(): void {
    try {
      const saved = localStorage.getItem('focusOS_focus_shield_status');
      if (saved) {
        const savedStatus = JSON.parse(saved);
        
        // 检查状态是否是今天的，如果不是今天的则重置部分状态
        const today = new Date().toDateString();
        const isToday = savedStatus.sessionStartTime ? 
          new Date(savedStatus.sessionStartTime).toDateString() === today : false;
        
        this.status = {
          ...this.status,
          ...savedStatus,
          // 恢复Date对象
          sessionStartTime: savedStatus.sessionStartTime ? new Date(savedStatus.sessionStartTime) : undefined,
          lastActivity: savedStatus.lastActivity ? {
            ...savedStatus.lastActivity,
            timestamp: new Date(savedStatus.lastActivity.timestamp)
          } : undefined,
          // 如果不是今天的会话，重置今日统计
          totalInterventionsToday: isToday ? savedStatus.totalInterventionsToday || 0 : 0,
          successfulBlocksToday: isToday ? savedStatus.successfulBlocksToday || 0 : 0
        };

        // 如果Focus Shield之前是启动状态且还是今天的会话，自动恢复监控
        if (this.status.isActive && this.config.isEnabled && isToday) {
          // 延迟一点时间让服务完全初始化
          setTimeout(() => {
            this.resumeMonitoringAfterReload();
          }, 1000);
        }
      }
    } catch (error) {
      console.error('加载Focus Shield状态失败:', error);
    }
  }

  // 页面重载后恢复监控状态
  private resumeMonitoringAfterReload(): void {
    if (this.status.isActive && this.status.isMonitoring) {
      // 重新启动应用监控
      if (this.config.monitorApps || this.config.monitorWebsites) {
        applicationMonitorService.startMonitoring();
      }
      
      // 重新启动专注监控（如果之前在监控中）
      if (focusMonitorService.isCurrentlyMonitoring()) {
        // 继续使用当前会话
      } else {
        focusMonitorService.startFocusSession();
      }
      
      console.log(`Focus Shield resumed from saved state in ${this.status.currentMode} mode`);
    }
  }

  // 清理资源
  public destroy(): void {
    this.stop();
    this.saveConfig();
    
    // 清理相关服务
    applicationMonitorService.destroy();
    interventionEngineService.destroy();
    blacklistManagerService.destroy();
  }
}

// 单例实例
export const focusShieldService = new FocusShieldService();
export default FocusShieldService;