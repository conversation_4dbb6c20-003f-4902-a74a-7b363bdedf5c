import { AudioService, SoundType } from './AudioService';

// 通知服务 - 处理桌面通知和音频提醒
export class NotificationService {
  private static instance: NotificationService;
  private isNotificationGranted = false;
  private audioService: AudioService;

  private constructor() {
    this.checkNotificationPermission();
    this.audioService = AudioService.getInstance();
    this.initializeAudioService();
  }

  // 初始化音频服务
  private async initializeAudioService() {
    try {
      await this.audioService.initialize();
    } catch (error) {
      console.warn('Failed to initialize audio service:', error);
    }
  }

  public static getInstance(): NotificationService {
    if (!NotificationService.instance) {
      NotificationService.instance = new NotificationService();
    }
    return NotificationService.instance;
  }

  // 检查通知权限
  private async checkNotificationPermission() {
    if ('Notification' in window) {
      if (Notification.permission === 'default') {
        const permission = await Notification.requestPermission();
        this.isNotificationGranted = permission === 'granted';
      } else {
        this.isNotificationGranted = Notification.permission === 'granted';
      }
    }
  }

  // 发送桌面通知
  public async sendNotification(
    title: string,
    options: {
      body?: string;
      icon?: string;
      sound?: boolean;
      soundType?: SoundType;
      duration?: number;
    } = {}
  ) {
    // 确保有通知权限
    if (!this.isNotificationGranted) {
      await this.checkNotificationPermission();
    }

    if (this.isNotificationGranted && 'Notification' in window) {
      const notification = new Notification(title, {
        body: options.body,
        icon: options.icon || '/icon.png',
        silent: !options.sound,
        requireInteraction: true, // 需要用户交互才关闭
      });

      // 播放提示音（在显示通知之前）
      if (options.sound && options.soundType) {
        try {
          console.log(`🔔 NotificationService: Playing notification sound: ${options.soundType}`);
          await this.audioService.playNotificationSound(options.soundType, false);
        } catch (error) {
          console.warn(`Failed to play notification sound: ${options.soundType}`, error);
          // 音频播放失败不应该阻止通知显示
        }
      }

      // 自动关闭通知
      if (options.duration) {
        setTimeout(() => {
          notification.close();
        }, options.duration);
      }

      return notification;
    }

    // 如果不支持桌面通知，则使用浏览器内提醒
    console.warn('桌面通知不可用，使用替代方案');
    return null;
  }

  // 番茄钟完成通知
  public async notifyPomodoroComplete(type: 'work' | 'break', sessionCount: number) {
    const isWorkSession = type === 'work';

    const title = isWorkSession
      ? '🍅 番茄钟完成！'
      : '⏰ 休息时间到！';

    const body = isWorkSession
      ? `恭喜完成第 ${sessionCount} 个番茄钟！是时候休息一下了。`
      : '休息结束，准备开始下一个专注时段吧！';

    // 发送带音频的通知
    await this.sendNotification(title, {
      body,
      sound: true,
      soundType: isWorkSession ? 'pomodoro-complete' : 'break-complete',
      duration: 5000,
    });

    // 同时发送系统通知（Electron环境）
    await this.sendSystemNotification(title, body);
  }

  // 番茄钟开始通知
  public async notifyPomodoroStart(taskTitle: string) {
    await this.sendNotification('🚀 开始专注！', {
      body: `专注任务：${taskTitle}`,
      sound: true,
      soundType: 'gentle-reminder',
      duration: 3000,
    });

    await this.sendSystemNotification('🚀 开始专注！', `专注任务：${taskTitle}`);
  }

  // 番茄钟暂停通知
  public async notifyPomodoroPause() {
    await this.sendNotification('⏸️ 番茄钟已暂停', {
      body: '记得稍后继续你的专注时间！',
      sound: true,
      soundType: 'gentle-reminder',
      duration: 2000,
    });
  }

  // 任务完成通知
  public async notifyTaskComplete(taskTitle: string) {
    await this.sendNotification('✅ 任务完成！', {
      body: `已完成任务：${taskTitle}`,
      sound: true,
      soundType: 'task-complete',
      duration: 4000,
    });
  }

  // 目标达成通知
  public async notifyGoalAchieved(goalTitle: string) {
    await this.sendNotification('🎯 目标达成！', {
      body: `恭喜达成目标：${goalTitle}`,
      sound: true,
      soundType: 'goal-achieved',
      duration: 6000,
    });
  }

  // 系统重要提示
  public async notifySystemAlert(title: string, message: string) {
    await this.sendNotification(title, {
      body: message,
      sound: true,
      soundType: 'system-alert',
      duration: 0, // 不自动关闭
    });
  }

  // 系统级通知（通过 Electron）
  public async sendSystemNotification(title: string, body: string) {
    try {
      // 如果在 Electron 环境中，使用系统通知
      if (window.electronAPI && window.electronAPI.invoke) {
        const result = await window.electronAPI.invoke('notification:show', title, body);
        if (!result.success) {
          throw new Error(result.error);
        }
      } else {
        // 降级到 Web 通知
        await this.sendNotification(title, { body });
      }
    } catch (error) {
      console.error('发送系统通知失败:', error);
      // 如果系统通知失败，尝试 Web 通知
      await this.sendNotification(title, { body });
    }
  }

  // 检查是否支持通知
  public isNotificationSupported(): boolean {
    return 'Notification' in window && this.isNotificationGranted;
  }

  // 每日统计通知
  public async notifyDailyStats(stats: {
    completedSessions: number;
    totalFocusTime: number;
    streakDays: number;
  }) {
    const hours = Math.floor(stats.totalFocusTime / 60);
    const minutes = stats.totalFocusTime % 60;

    const timeText = hours > 0
      ? `${hours}小时${minutes}分钟`
      : `${minutes}分钟`;

    await this.sendNotification('📊 今日专注总结', {
      body: `完成 ${stats.completedSessions} 个番茄钟，专注时长 ${timeText}。连续专注 ${stats.streakDays} 天！`,
      sound: true,
      soundType: 'gentle-reminder',
      duration: 6000,
    });
  }

  // 长时间未使用提醒
  public async notifyInactivity() {
    await this.sendNotification('💭 该专注了！', {
      body: '您已经有一段时间没有使用番茄钟了，是时候开始专注工作了！',
      sound: true,
      soundType: 'gentle-reminder',
      duration: 4000,
    });
  }

  // 获取音频服务实例（供外部使用）
  public getAudioService(): AudioService {
    return this.audioService;
  }
}