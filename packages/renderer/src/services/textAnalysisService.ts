// 文本分析服务 - 用于目标关键词提取和时间属性识别

export interface KeywordExtractionResult {
  keywords: string[];
  actionVerbs: string[];
  entities: string[];
  metrics: string[];
}

export interface TimeExtractionResult {
  timeReferences: string[];
  suggestedDeadline?: Date;
  timeKeywords: string[];
}

export interface GoalAnalysisResult {
  keywords: KeywordExtractionResult;
  timeInfo: TimeExtractionResult;
  smartAnalysis: {
    hasSpecificGoal: boolean;
    hasMeasurableMetrics: boolean;
    hasTimeFrame: boolean;
    confidence: number;
  };
}

class TextAnalysisService {
  // 常见的动作动词
  private actionVerbs = [
    '学习', '掌握', '提高', '改善', '完成', '实现', '达到', '获得', '建立', '创建',
    '开发', '设计', '写作', '阅读', '练习', '训练', '减少', '增加', '优化', '改进',
    '通过', '考试', '认证', '毕业', '找到', '换', '转行', '升职', '加薪', '减肥',
    '健身', '跑步', '游泳', '旅行', '搬家', '买', '卖', '投资', '储蓄', '还清'
  ];

  // 时间相关关键词
  private timeKeywords = [
    '天', '周', '月', '年', '季度', '小时', '分钟', '秒',
    '今天', '明天', '后天', '昨天', '今年', '明年', '去年',
    '本周', '下周', '上周', '本月', '下月', '上月',
    '春天', '夏天', '秋天', '冬天', '春季', '夏季', '秋季', '冬季',
    '早上', '上午', '中午', '下午', '晚上', '深夜',
    '工作日', '周末', '假期', '节日'
  ];

  // 数量和度量词
  private metricKeywords = [
    '个', '次', '遍', '本', '页', '章', '节', '课', '小时', '分钟',
    '公斤', '斤', '米', '公里', '元', '万', '千', '百',
    '分', '级', '段', '层', '步', '阶段', '期', '轮'
  ];

  // 提取关键词
  extractKeywords(text: string): KeywordExtractionResult {
    const cleanText = text.replace(/[^\u4e00-\u9fa5a-zA-Z0-9\s]/g, ' ');
    const words = cleanText.split(/\s+/).filter(word => word.length > 1);
    
    // 提取动作动词
    const actionVerbs = words.filter(word => 
      this.actionVerbs.some(verb => word.includes(verb))
    );

    // 提取实体（简单的名词识别）
    const entities = words.filter(word => 
      word.length >= 2 && 
      !this.actionVerbs.includes(word) &&
      !this.timeKeywords.includes(word) &&
      !this.metricKeywords.includes(word)
    );

    // 提取度量相关词汇
    const metrics = words.filter(word =>
      this.metricKeywords.some(metric => word.includes(metric)) ||
      /\d+/.test(word)
    );

    // 简单的关键词提取（去重并按长度排序）
    const keywords = [...new Set([...actionVerbs, ...entities])]
      .filter(word => word.length >= 2)
      .sort((a, b) => b.length - a.length)
      .slice(0, 10);

    return {
      keywords,
      actionVerbs: [...new Set(actionVerbs)],
      entities: [...new Set(entities)].slice(0, 8),
      metrics: [...new Set(metrics)]
    };
  }

  // 提取时间信息
  extractTimeInfo(text: string): TimeExtractionResult {
    const timeReferences: string[] = [];
    const timeKeywords: string[] = [];
    let suggestedDeadline: Date | undefined;

    // 查找时间关键词
    this.timeKeywords.forEach(keyword => {
      if (text.includes(keyword)) {
        timeKeywords.push(keyword);
      }
    });

    // 查找数字+时间单位的模式
    const timePatterns = [
      /(\d+)\s*天/g,
      /(\d+)\s*周/g,
      /(\d+)\s*月/g,
      /(\d+)\s*年/g,
      /(\d+)\s*小时/g
    ];

    timePatterns.forEach(pattern => {
      const matches = text.matchAll(pattern);
      for (const match of matches) {
        timeReferences.push(match[0]);
      }
    });

    // 简单的截止日期建议逻辑
    if (timeReferences.length > 0) {
      const now = new Date();
      
      // 查找最长的时间期限作为建议
      for (const ref of timeReferences) {
        if (ref.includes('年')) {
          const years = parseInt(ref.match(/\d+/)?.[0] || '1');
          suggestedDeadline = new Date(now.getFullYear() + years, now.getMonth(), now.getDate());
          break;
        } else if (ref.includes('月')) {
          const months = parseInt(ref.match(/\d+/)?.[0] || '1');
          suggestedDeadline = new Date(now.getFullYear(), now.getMonth() + months, now.getDate());
          break;
        } else if (ref.includes('周')) {
          const weeks = parseInt(ref.match(/\d+/)?.[0] || '1');
          suggestedDeadline = new Date(now.getTime() + weeks * 7 * 24 * 60 * 60 * 1000);
          break;
        } else if (ref.includes('天')) {
          const days = parseInt(ref.match(/\d+/)?.[0] || '1');
          suggestedDeadline = new Date(now.getTime() + days * 24 * 60 * 60 * 1000);
          break;
        }
      }
    }

    return {
      timeReferences: [...new Set(timeReferences)],
      suggestedDeadline,
      timeKeywords: [...new Set(timeKeywords)]
    };
  }

  // SMART原则分析
  analyzeSmart(text: string, keywords: KeywordExtractionResult, timeInfo: TimeExtractionResult): any {
    let confidence = 0;
    
    // 检查是否具体 (Specific)
    const hasSpecificGoal = keywords.actionVerbs.length > 0 && keywords.entities.length > 0;
    if (hasSpecificGoal) confidence += 20;

    // 检查是否可衡量 (Measurable)
    const hasMeasurableMetrics = keywords.metrics.length > 0 || /\d+/.test(text);
    if (hasMeasurableMetrics) confidence += 20;

    // 检查是否有时间框架 (Time-bound)
    const hasTimeFrame = timeInfo.timeReferences.length > 0 || timeInfo.timeKeywords.length > 0;
    if (hasTimeFrame) confidence += 20;

    // 检查文本长度和详细程度
    if (text.length > 50) confidence += 20;
    if (text.length > 100) confidence += 20;

    return {
      hasSpecificGoal,
      hasMeasurableMetrics,
      hasTimeFrame,
      confidence: Math.min(confidence, 100)
    };
  }

  // 综合分析目标文本
  analyzeGoal(description: string, whyPower?: string): GoalAnalysisResult {
    const fullText = `${description} ${whyPower || ''}`;
    
    const keywords = this.extractKeywords(fullText);
    const timeInfo = this.extractTimeInfo(fullText);
    const smartAnalysis = this.analyzeSmart(fullText, keywords, timeInfo);

    return {
      keywords,
      timeInfo,
      smartAnalysis
    };
  }
}

export const textAnalysisService = new TextAnalysisService();
