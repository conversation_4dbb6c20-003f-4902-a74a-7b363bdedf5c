import { Goal, Task, PomodoroSession, CreateGoalDto, CreateTaskDto, GoalAnalysis, AIProvider, CreateAIProviderDto } from '../types';
import { textAnalysisService } from './textAnalysisService';
import { AILogger, AIRequestInfo, AIResponseInfo } from './AILogger';

declare global {
  interface Window {
    [key: string]: any;
  }
}

// 获取preload暴露的electronAPI
const getElectronAPI = () => {
  const encodedKey = btoa('electronAPI');
  return window[encodedKey];
};

// 检查是否在Electron环境中
const isElectronEnvironment = () => {
  return getElectronAPI() !== undefined;
};

// 浏览器环境的localStorage fallback
const browserStorageAPI = {
  async getSetting(key: string): Promise<string | null> {
    try {
      return localStorage.getItem(`focusOS_setting_${key}`);
    } catch (error) {
      console.warn('Failed to get setting from localStorage:', error);
      return null;
    }
  },

  async setSetting(key: string, value: string): Promise<void> {
    try {
      localStorage.setItem(`focusOS_setting_${key}`, value);
    } catch (error) {
      console.warn('Failed to set setting to localStorage:', error);
      throw new Error('保存设置失败');
    }
  },

  async getAllSettings(): Promise<{ [key: string]: string }> {
    try {
      const settings: { [key: string]: string } = {};
      for (let i = 0; i < localStorage.length; i++) {
        const key = localStorage.key(i);
        if (key && key.startsWith('focusOS_setting_')) {
          const settingKey = key.replace('focusOS_setting_', '');
          const value = localStorage.getItem(key);
          if (value !== null) {
            settings[settingKey] = value;
          }
        }
      }
      return settings;
    } catch (error) {
      console.warn('Failed to get all settings from localStorage:', error);
      return {};
    }
  }
};

export class DatabaseAPI {
  // 目标相关API
  static async createGoal(goalData: CreateGoalDto): Promise<Goal> {
    // 分析目标文本
    const analysisResult = textAnalysisService.analyzeGoal(goalData.description, goalData.whyPower);

    const analysis: GoalAnalysis = {
      keywords: analysisResult.keywords.keywords,
      actionVerbs: analysisResult.keywords.actionVerbs,
      entities: analysisResult.keywords.entities,
      metrics: analysisResult.keywords.metrics,
      timeReferences: analysisResult.timeInfo.timeReferences,
      suggestedDeadline: analysisResult.timeInfo.suggestedDeadline,
      smartScore: analysisResult.smartAnalysis.confidence,
      analyzedAt: new Date()
    };

    const goal = {
      id: crypto.randomUUID(),
      userId: 'current-user',
      ...goalData,
      analysis,
      status: 'active' as const,
      createdAt: new Date().toISOString(),
      updatedAt: new Date().toISOString(),
    };

    const electronAPI = getElectronAPI();
    const result = await electronAPI.invoke('db:goals:create', goal);
    if (!result.success) {
      throw new Error(result.error || '创建目标失败');
    }
    return goal;
  }

  static async updateGoal(id: string, updates: Partial<Goal>): Promise<void> {
    if (isElectronEnvironment()) {
      const updateData = {
        ...updates,
        updatedAt: new Date().toISOString(),
      };

      const result = await getElectronAPI().invoke('db:goals:update', id, updateData);
      if (!result.success) {
        throw new Error(result.error || '更新目标失败');
      }
    } else {
      // 浏览器环境fallback - 模拟成功
      console.log('Browser environment: updateGoal simulated');
    }
  }

  static async deleteGoal(id: string): Promise<void> {
    if (isElectronEnvironment()) {
      const result = await getElectronAPI().invoke('db:goals:delete', id);
      if (!result.success) {
        throw new Error(result.error || '删除目标失败');
      }
    } else {
      // 浏览器环境fallback - 模拟成功
      console.log('Browser environment: deleteGoal simulated');
    }
  }

  static async analyzeGoalDeletion(goalId: string): Promise<any> {
    if (isElectronEnvironment()) {
      const result = await getElectronAPI().invoke('db:goals:analyze-deletion', goalId);
      if (!result.success) {
        throw new Error(result.error || '分析删除影响失败');
      }
      return result;
    } else {
      // 浏览器环境fallback - 返回模拟数据
      return { success: true, data: { affectedTasks: 0, affectedSubGoals: 0 } };
    }
  }

  static async cascadeDeleteGoal(goalId: string): Promise<any> {
    if (isElectronEnvironment()) {
      const result = await getElectronAPI().invoke('db:goals:cascade-delete', goalId);
      if (!result.success) {
        throw new Error(result.error || '级联删除失败');
      }
      return result;
    } else {
      // 浏览器环境fallback - 返回模拟数据
      return { success: true, data: { deletedGoals: 0, deletedTasks: 0 } };
    }
  }

  static async getGoals(): Promise<Goal[]> {
    if (isElectronEnvironment()) {
      const result = await getElectronAPI().invoke('db:goals:getAll');
      if (!result.success) {
        throw new Error(result.error || '获取目标列表失败');
      }
      return result.data || [];
    } else {
      // 浏览器环境fallback - 返回空数组
      return [];
    }
  }

  static async getGoalById(id: string): Promise<Goal | null> {
    if (isElectronEnvironment()) {
      const result = await getElectronAPI().invoke('db:goals:getById', id);
      if (!result.success) {
        throw new Error(result.error || '获取目标失败');
      }
      return result.data;
    } else {
      // 浏览器环境fallback - 返回null
      return null;
    }
  }

  // 任务相关API
  static async createTask(taskData: CreateTaskDto): Promise<Task> {
    const task = {
      id: crypto.randomUUID(),
      ...taskData,
      actualTime: 0,
      completionPercentage: 0,
      status: 'todo' as const,
      createdAt: new Date().toISOString(),
      updatedAt: new Date().toISOString(),
    };

    if (isElectronEnvironment()) {
      const result = await getElectronAPI().invoke('db:tasks:create', task);
      if (!result.success) {
        throw new Error(result.error || '创建任务失败');
      }
    } else {
      // 浏览器环境fallback - 模拟成功
      console.log('Browser environment: createTask simulated');
    }
    return task;
  }

  static async updateTask(id: string, updates: Partial<Task>): Promise<void> {
    if (isElectronEnvironment()) {
      const updateData = {
        ...updates,
        updatedAt: new Date().toISOString(),
      };

      const result = await getElectronAPI().invoke('db:tasks:update', id, updateData);
      if (!result.success) {
        throw new Error(result.error || '更新任务失败');
      }
    } else {
      // 浏览器环境fallback - 模拟成功
      console.log('Browser environment: updateTask simulated');
    }
  }

  static async deleteTask(id: string): Promise<void> {
    if (isElectronEnvironment()) {
      const result = await getElectronAPI().invoke('db:tasks:delete', id);
      if (!result.success) {
        throw new Error(result.error || '删除任务失败');
      }
    } else {
      // 浏览器环境fallback - 模拟成功
      console.log('Browser environment: deleteTask simulated');
    }
  }

  static async getTasks(): Promise<Task[]> {
    if (isElectronEnvironment()) {
      const result = await getElectronAPI().invoke('db:tasks:getAll');
      if (!result.success) {
        throw new Error(result.error || '获取任务列表失败');
      }
      return result.data || [];
    } else {
      // 浏览器环境fallback - 返回空数组
      return [];
    }
  }

  // 番茄钟会话相关API
  static async createPomodoroSession(sessionData: Omit<PomodoroSession, 'id'>): Promise<PomodoroSession> {
    const session = {
      id: crypto.randomUUID(),
      ...sessionData,
      // 确保所有必需字段都有默认值
      endTime: sessionData.endTime || undefined,
      isCompleted: sessionData.isCompleted ?? false,
      wasInterrupted: sessionData.wasInterrupted ?? false,
    };

    if (isElectronEnvironment()) {
      const result = await getElectronAPI().invoke('db:pomodoro:createSession', session);
      if (!result.success) {
        throw new Error(result.error || '创建番茄钟会话失败');
      }
    } else {
      // 浏览器环境fallback - 模拟成功
      console.log('Browser environment: createPomodoroSession simulated');
    }
    return session;
  }

  static async getPomodoroSessions(): Promise<PomodoroSession[]> {
    if (isElectronEnvironment()) {
      const result = await getElectronAPI().invoke('db:pomodoro:getSessions');
      if (!result.success) {
        throw new Error(result.error || '获取番茄钟会话失败');
      }
      return result.data || [];
    } else {
      // 浏览器环境fallback - 返回空数组
      return [];
    }
  }

  static async updatePomodoroSession(id: string, updates: Partial<PomodoroSession>): Promise<PomodoroSession> {
    if (isElectronEnvironment()) {
      const result = await getElectronAPI().invoke('db:pomodoro:updateSession', id, updates);
      if (!result.success) {
        throw new Error(result.error || '更新番茄钟会话失败');
      }
      return result.data;
    } else {
      // 浏览器环境fallback - 返回模拟数据
      return { ...updates, id } as PomodoroSession;
    }
  }

  // 设置相关API
  static async getSetting(key: string): Promise<string | null> {
    if (isElectronEnvironment()) {
      const result = await getElectronAPI().invoke('db:settings:get', key);
      if (!result.success) {
        throw new Error(result.error || '获取设置失败');
      }
      return result.data;
    } else {
      // 浏览器环境fallback
      return await browserStorageAPI.getSetting(key);
    }
  }

  static async setSetting(key: string, value: string): Promise<void> {
    if (isElectronEnvironment()) {
      const result = await getElectronAPI().invoke('db:settings:set', key, value);
      if (!result.success) {
        throw new Error(result.error || '保存设置失败');
      }
    } else {
      // 浏览器环境fallback
      await browserStorageAPI.setSetting(key, value);
    }
  }

  static async getAllSettings(): Promise<{ [key: string]: string }> {
    if (isElectronEnvironment()) {
      const result = await getElectronAPI().invoke('db:settings:getAll');
      if (!result.success) {
        throw new Error(result.error || '获取所有设置失败');
      }
      return result.data || {};
    } else {
      // 浏览器环境fallback
      return await browserStorageAPI.getAllSettings();
    }
  }

  // AI Provider 相关API
  static async createAIProvider(providerData: CreateAIProviderDto): Promise<AIProvider> {
    const provider: AIProvider = {
      id: crypto.randomUUID(),
      ...providerData,
      enabled: providerData.enabled ?? true,
      createdAt: new Date(),
      updatedAt: new Date(),
    };

    if (isElectronEnvironment()) {
      const result = await getElectronAPI().invoke('db:ai-providers:create', provider);
      if (!result.success) {
        throw new Error(result.error || '创建AI提供商失败');
      }
    } else {
      // 浏览器环境fallback - 模拟成功
      console.log('Browser environment: createAIProvider simulated');
    }
    return provider;
  }

  static async updateAIProvider(id: string, updates: Partial<AIProvider>): Promise<void> {
    if (isElectronEnvironment()) {
      const updateData = {
        ...updates,
        updatedAt: new Date(),
      };

      const result = await getElectronAPI().invoke('db:ai-providers:update', id, updateData);
      if (!result.success) {
        throw new Error(result.error || '更新AI提供商失败');
      }
    } else {
      // 浏览器环境fallback - 模拟成功
      console.log('Browser environment: updateAIProvider simulated');
    }
  }

  static async deleteAIProvider(id: string): Promise<void> {
    if (isElectronEnvironment()) {
      const result = await getElectronAPI().invoke('db:ai-providers:delete', id);
      if (!result.success) {
        throw new Error(result.error || '删除AI提供商失败');
      }
    } else {
      // 浏览器环境fallback - 模拟成功
      console.log('Browser environment: deleteAIProvider simulated');
    }
  }

  static async getAIProviders(): Promise<AIProvider[]> {
    if (isElectronEnvironment()) {
      const result = await getElectronAPI().invoke('db:ai-providers:getAll');
      if (!result.success) {
        throw new Error(result.error || '获取AI提供商列表失败');
      }
      return result.data || [];
    } else {
      // 浏览器环境fallback - 返回空数组
      return [];
    }
  }

  static async getAIProviderById(id: string): Promise<AIProvider | null> {
    if (isElectronEnvironment()) {
      const result = await getElectronAPI().invoke('db:ai-providers:getById', id);
      if (!result.success) {
        throw new Error(result.error || '获取AI提供商失败');
      }
      return result.data;
    } else {
      // 浏览器环境fallback - 返回null
      return null;
    }
  }

  static async testAIProvider(id: string): Promise<{
    success: boolean;
    message: string;
    responseTime?: number;
    error?: string;
  }> {
    const requestId = AILogger.generateRequestId();
    const startTime = Date.now();

    // 记录请求
    const requestInfo: AIRequestInfo = {
      requestId,
      operation: 'testAIProvider',
      context: 'AI提供商连接测试',
      requestData: { providerId: id }
    };
    AILogger.logRequest(requestInfo);

    try {
      let result;
      if (isElectronEnvironment()) {
        result = await getElectronAPI().invoke('db:ai-providers:test', id);
      } else {
        // 浏览器环境fallback - 返回模拟测试结果
        AILogger.logDebug('浏览器环境：模拟AI提供商测试');
        result = {
          success: true,
          message: '浏览器环境模拟测试成功',
          responseTime: 100
        };
      }

      const responseTime = Date.now() - startTime;
      
      // 记录响应
      const responseInfo: AIResponseInfo = {
        requestId,
        success: result.success,
        responseTime,
        error: result.error,
        resultData: result
      };
      AILogger.logResponse(responseInfo);
      AILogger.logApiTest('Unknown Provider', 'Unknown Model', result.success, responseTime, result.error);

      return result;
    } catch (error) {
      const responseTime = Date.now() - startTime;
      const errorMsg = error instanceof Error ? error.message : String(error);
      
      // 记录错误响应
      const responseInfo: AIResponseInfo = {
        requestId,
        success: false,
        responseTime,
        error: errorMsg
      };
      AILogger.logResponse(responseInfo);
      AILogger.logError('AI提供商测试失败', error, { providerId: id });

      throw error;
    }
  }

  // AI分解相关API
  static async startAIDecomposition(request: any): Promise<{
    success: boolean;
    sessionId?: string;
    error?: string;
  }> {
    const requestId = AILogger.generateRequestId();
    const startTime = Date.now();

    // 记录请求
    const requestInfo: AIRequestInfo = {
      requestId,
      operation: 'startAIDecomposition',
      provider: request.aiProvider,
      context: `目标分解启动: ${request.goalName}`,
      requestData: { 
        goalId: request.goalId, 
        goalName: request.goalName,
        aiProvider: request.aiProvider 
      }
    };
    AILogger.logRequest(requestInfo);

    try {
      let result;
      if (isElectronEnvironment()) {
        result = await getElectronAPI().invoke('ai-decomposition:start', request);
      } else {
        // 浏览器环境fallback - 返回模拟结果
        AILogger.logDebug('浏览器环境：模拟AI分解启动');
        result = {
          success: true,
          sessionId: crypto.randomUUID(),
        };
      }

      const responseTime = Date.now() - startTime;
      
      // 记录响应
      const responseInfo: AIResponseInfo = {
        requestId,
        success: result.success,
        responseTime,
        error: result.error,
        resultData: result
      };
      AILogger.logResponse(responseInfo);
      AILogger.logOperationSummary(requestId, 'AI分解启动', result.success, responseTime, result.error);

      return result;
    } catch (error) {
      const responseTime = Date.now() - startTime;
      const errorMsg = error instanceof Error ? error.message : String(error);
      
      // 记录错误响应
      const responseInfo: AIResponseInfo = {
        requestId,
        success: false,
        responseTime,
        error: errorMsg
      };
      AILogger.logResponse(responseInfo);
      AILogger.logError('AI分解启动失败', error, { goalName: request.goalName });

      throw error;
    }
  }

  static async performAIDecomposition(sessionId: string): Promise<{
    success: boolean;
    result?: any;
    error?: string;
  }> {
    if (isElectronEnvironment()) {
      const result = await getElectronAPI().invoke('ai-decomposition:perform', sessionId);
      return result;
    } else {
      // 浏览器环境fallback - 返回模拟结果
      return {
        success: true,
        result: { message: '浏览器环境模拟AI分解结果' }
      };
    }
  }

  static async getAIDecompositionResult(sessionId: string): Promise<{
    success: boolean;
    result?: any;
    error?: string;
  }> {
    if (isElectronEnvironment()) {
      const result = await getElectronAPI().invoke('ai-decomposition:get-result', sessionId);
      return result;
    } else {
      // 浏览器环境fallback - 返回模拟结果
      return {
        success: true,
        result: { message: '浏览器环境模拟分解结果' }
      };
    }
  }

  static async getAIDecompositionTemplates(): Promise<{
    success: boolean;
    templates?: any[];
    error?: string;
  }> {
    if (isElectronEnvironment()) {
      const result = await getElectronAPI().invoke('ai-decomposition:get-templates');
      return result;
    } else {
      // 浏览器环境fallback - 返回空模板列表
      return {
        success: true,
        templates: []
      };
    }
  }

  static async performAIDecompositionWithTemplate(sessionId: string, templateType: string): Promise<{
    success: boolean;
    result?: any;
    error?: string;
  }> {
    if (isElectronEnvironment()) {
      const result = await getElectronAPI().invoke('ai-decomposition:perform-with-template', sessionId, templateType);
      return result;
    } else {
      return { success: true, result: { message: '浏览器环境模拟模板分解结果' } };
    }
  }

  static async getAvailableAIProviders(): Promise<{
    success: boolean;
    providers?: any[];
    error?: string;
  }> {
    if (isElectronEnvironment()) {
      const result = await getElectronAPI().invoke('ai-decomposition:get-available-providers');
      return result;
    } else {
      return { success: true, providers: [] };
    }
  }

  // 分解会话管理API
  static async getGoalDecompositionSessions(goalId: string): Promise<{
    success: boolean;
    sessions?: any[];
    error?: string;
  }> {
    if (isElectronEnvironment()) {
      const result = await getElectronAPI().invoke('ai-decomposition:get-goal-sessions', goalId);
      return result;
    } else {
      return { success: true, sessions: [] };
    }
  }

  static async getDecompositionSession(sessionId: string): Promise<{
    success: boolean;
    session?: any;
    error?: string;
  }> {
    if (isElectronEnvironment()) {
      const result = await getElectronAPI().invoke('ai-decomposition:get-session', sessionId);
      return result;
    } else {
      return { success: true, session: null };
    }
  }

  static async cancelDecompositionSession(sessionId: string): Promise<{
    success: boolean;
    error?: string;
  }> {
    if (isElectronEnvironment()) {
      const result = await getElectronAPI().invoke('ai-decomposition:cancel-session', sessionId);
      return result;
    } else {
      return { success: true };
    }
  }

  static async deleteDecompositionSession(sessionId: string): Promise<{
    success: boolean;
    error?: string;
  }> {
    if (isElectronEnvironment()) {
      const result = await getElectronAPI().invoke('ai-decomposition:delete-session', sessionId);
      return result;
    } else {
      return { success: true };
    }
  }

  static async restartDecompositionSession(sessionId: string): Promise<{
    success: boolean;
    sessionId?: string;
    error?: string;
  }> {
    if (isElectronEnvironment()) {
      const result = await getElectronAPI().invoke('ai-decomposition:restart-session', sessionId);
      return result;
    } else {
      return { success: true, sessionId: crypto.randomUUID() };
    }
  }

  static async getDecompositionStats(): Promise<{
    success: boolean;
    stats?: any;
    error?: string;
  }> {
    if (isElectronEnvironment()) {
      const result = await getElectronAPI().invoke('ai-decomposition:get-stats');
      return result;
    } else {
      return { success: true, stats: { totalSessions: 0, completedSessions: 0 } };
    }
  }

  static async saveDecompositionResult(sessionId: string, confirmed: boolean = true): Promise<{
    success: boolean;
    error?: string;
  }> {
    if (isElectronEnvironment()) {
      const result = await getElectronAPI().invoke('ai-decomposition:save-result', sessionId, confirmed);
      return result;
    } else {
      return { success: true };
    }
  }

  static async getGoalDecompositionStructure(goalId: string): Promise<{
    success: boolean;
    structure?: any;
    error?: string;
  }> {
    if (isElectronEnvironment()) {
      const result = await getElectronAPI().invoke('ai-decomposition:get-goal-structure', goalId);
      return result;
    } else {
      return { success: true, structure: null };
    }
  }

  // 重新分解相关API
  static async redecomposeGoal(request: any): Promise<{
    success: boolean;
    sessionId?: string;
    error?: string;
  }> {
    if (isElectronEnvironment()) {
      const result = await getElectronAPI().invoke('ai-decomposition:redecompose-goal', request);
      return result;
    } else {
      return { success: true, sessionId: crypto.randomUUID() };
    }
  }

  static async getDecompositionHistory(goalId: string): Promise<{
    success: boolean;
    history?: any;
    error?: string;
  }> {
    if (isElectronEnvironment()) {
      const result = await getElectronAPI().invoke('ai-decomposition:get-history', goalId);
      return result;
    } else {
      return { success: true, history: [] };
    }
  }

  static async rollbackToVersion(goalId: string, sessionId: string): Promise<{
    success: boolean;
    error?: string;
  }> {
    if (isElectronEnvironment()) {
      const result = await getElectronAPI().invoke('ai-decomposition:rollback-version', goalId, sessionId);
      return result;
    } else {
      return { success: true };
    }
  }
}