import { DatabaseAPI } from './api';

export interface DeleteAnalysis {
  goalId: string;
  goalName: string;
  subGoalsCount: number;
  milestonesCount: number;
  tasksCount: number;
  pomodoroSessionsCount: number;
  decompositionSessionsCount: number;
  userModificationsCount: number;
  totalItemsCount: number;
  subGoals: Array<{
    id: string;
    name: string;
    milestonesCount: number;
    tasksCount: number;
  }>;
  milestones: Array<{
    id: string;
    name: string;
    tasksCount: number;
  }>;
  tasks: Array<{
    id: string;
    title: string;
    parentType: string;
    parentId: string;
  }>;
}

export interface DeleteResult {
  success: boolean;
  deletedItems: {
    goals: number;
    subGoals: number;
    milestones: number;
    tasks: number;
    pomodoroSessions: number;
    decompositionSessions: number;
    userModifications: number;
  };
  error?: string;
  rollbackInfo?: any;
}

export interface SoftDeleteInfo {
  id: string;
  type: 'goal' | 'subgoal' | 'milestone' | 'task';
  originalData: any;
  deletedAt: Date;
  canRestore: boolean;
  expiresAt: Date;
}

export class GoalCascadeDeleteService {
  // 软删除恢复期限（天）
  private static readonly RECOVERY_PERIOD_DAYS = 30;

  /**
   * 分析删除目标将影响的所有数据
   */
  public static async analyzeGoalDeletion(goalId: string): Promise<DeleteAnalysis> {
    try {
      const result = await DatabaseAPI.analyzeGoalDeletion(goalId);
      
      if (!result.success) {
        throw new Error(result.error || '分析删除影响失败');
      }

      return result.data;
    } catch (error) {
      console.error('分析删除影响失败:', error);
      throw error;
    }
  }

  /**
   * 执行级联删除操作
   */
  public static async cascadeDeleteGoal(goalId: string): Promise<DeleteResult> {
    try {
      const result = await DatabaseAPI.cascadeDeleteGoal(goalId);
      
      if (!result.success) {
        throw new Error(result.error || '删除操作失败');
      }

      // 触发相关事件通知其他组件
      this.notifyDeletionComplete(goalId, result.data);

      return result.data;
    } catch (error) {
      console.error('级联删除失败:', error);
      throw error;
    }
  }

  /**
   * 软删除目标（标记为已删除，但不实际删除）
   */
  public static async softDeleteGoal(goalId: string): Promise<SoftDeleteInfo> {
    try {
      const goal = await DatabaseAPI.getGoalById(goalId);
      if (!goal) {
        throw new Error('目标不存在');
      }

      const deletedAt = new Date();
      const expiresAt = new Date(deletedAt.getTime() + this.RECOVERY_PERIOD_DAYS * 24 * 60 * 60 * 1000);

      // 更新目标状态为已删除
      await DatabaseAPI.updateGoal(goalId, {
        status: 'cancelled',
        deletedAt: deletedAt.toISOString(),
        expiresAt: expiresAt.toISOString()
      });

      const softDeleteInfo: SoftDeleteInfo = {
        id: goalId,
        type: 'goal',
        originalData: goal,
        deletedAt,
        canRestore: true,
        expiresAt
      };

      // 存储软删除信息
      this.storeSoftDeleteInfo(softDeleteInfo);

      return softDeleteInfo;
    } catch (error) {
      console.error('软删除失败:', error);
      throw error;
    }
  }

  /**
   * 恢复软删除的目标
   */
  public static async restoreSoftDeletedGoal(goalId: string): Promise<boolean> {
    try {
      const softDeleteInfo = this.getSoftDeleteInfo(goalId);
      if (!softDeleteInfo || !softDeleteInfo.canRestore) {
        throw new Error('无法恢复该目标');
      }

      if (new Date() > softDeleteInfo.expiresAt) {
        throw new Error('恢复期限已过，无法恢复');
      }

      // 恢复目标状态
      await DatabaseAPI.updateGoal(goalId, {
        status: 'active',
        deletedAt: null,
        expiresAt: null
      });

      // 清理软删除信息
      this.removeSoftDeleteInfo(goalId);

      return true;
    } catch (error) {
      console.error('恢复软删除目标失败:', error);
      throw error;
    }
  }

  /**
   * 获取可恢复的软删除项目列表
   */
  public static getSoftDeletedItems(): SoftDeleteInfo[] {
    try {
      const stored = localStorage.getItem('focusOS_soft_deleted_items');
      if (!stored) return [];

      const items: SoftDeleteInfo[] = JSON.parse(stored);
      const now = new Date();

      // 过滤掉已过期的项目
      const validItems = items.filter(item => {
        const expiresAt = new Date(item.expiresAt);
        return now <= expiresAt;
      });

      // 如果有过期项目，更新存储
      if (validItems.length !== items.length) {
        localStorage.setItem('focusOS_soft_deleted_items', JSON.stringify(validItems));
      }

      return validItems;
    } catch (error) {
      console.error('获取软删除项目失败:', error);
      return [];
    }
  }

  /**
   * 清理过期的软删除项目
   */
  public static async cleanupExpiredSoftDeletes(): Promise<number> {
    const softDeletedItems = this.getSoftDeletedItems();
    const now = new Date();
    let cleanedCount = 0;

    for (const item of softDeletedItems) {
      if (now > item.expiresAt) {
        try {
          // 执行真正的删除
          await this.cascadeDeleteGoal(item.id);
          this.removeSoftDeleteInfo(item.id);
          cleanedCount++;
        } catch (error) {
          console.error(`清理过期软删除项目失败: ${item.id}`, error);
        }
      }
    }

    return cleanedCount;
  }

  /**
   * 检查目标是否有依赖关系
   */
  public static async checkGoalDependencies(goalId: string): Promise<{
    hasParent: boolean;
    hasChildren: boolean;
    parentGoal?: any;
    childGoals: any[];
  }> {
    try {
      const goal = await DatabaseAPI.getGoalById(goalId);
      if (!goal) {
        throw new Error('目标不存在');
      }

      let parentGoal = null;
      if (goal.parentId) {
        parentGoal = await DatabaseAPI.getGoalById(goal.parentId);
      }

      // 获取子目标（这里需要扩展API来支持目标层级）
      const childGoals: any[] = []; // 暂时为空，需要API支持

      return {
        hasParent: !!parentGoal,
        hasChildren: childGoals.length > 0,
        parentGoal,
        childGoals
      };
    } catch (error) {
      console.error('检查目标依赖关系失败:', error);
      throw error;
    }
  }

  /**
   * 批量删除多个目标
   */
  public static async batchDeleteGoals(goalIds: string[]): Promise<{
    successful: string[];
    failed: Array<{ goalId: string; error: string }>;
  }> {
    const successful: string[] = [];
    const failed: Array<{ goalId: string; error: string }> = [];

    for (const goalId of goalIds) {
      try {
        await this.cascadeDeleteGoal(goalId);
        successful.push(goalId);
      } catch (error) {
        failed.push({
          goalId,
          error: error instanceof Error ? error.message : '未知错误'
        });
      }
    }

    return { successful, failed };
  }

  /**
   * 存储软删除信息
   */
  private static storeSoftDeleteInfo(info: SoftDeleteInfo): void {
    try {
      const existing = this.getSoftDeletedItems();
      const updated = existing.filter(item => item.id !== info.id);
      updated.push(info);
      
      localStorage.setItem('focusOS_soft_deleted_items', JSON.stringify(updated));
    } catch (error) {
      console.error('存储软删除信息失败:', error);
    }
  }

  /**
   * 获取软删除信息
   */
  private static getSoftDeleteInfo(goalId: string): SoftDeleteInfo | null {
    const items = this.getSoftDeletedItems();
    return items.find(item => item.id === goalId) || null;
  }

  /**
   * 移除软删除信息
   */
  private static removeSoftDeleteInfo(goalId: string): void {
    try {
      const existing = this.getSoftDeletedItems();
      const updated = existing.filter(item => item.id !== goalId);
      localStorage.setItem('focusOS_soft_deleted_items', JSON.stringify(updated));
    } catch (error) {
      console.error('移除软删除信息失败:', error);
    }
  }

  /**
   * 通知删除完成事件
   */
  private static notifyDeletionComplete(goalId: string, result: DeleteResult): void {
    // 触发自定义事件
    const event = new CustomEvent('goal-deleted', {
      detail: { goalId, result }
    });
    window.dispatchEvent(event);

    // 清理相关缓存
    this.clearRelatedCaches(goalId);
  }

  /**
   * 清理相关缓存
   */
  private static clearRelatedCaches(goalId: string): void {
    // 清理localStorage中的相关数据
    const keysToCheck = [
      'focusOS_goals_cache',
      'focusOS_tasks_cache',
      'focusOS_recent_goals',
      `focusOS_goal_${goalId}`
    ];

    keysToCheck.forEach(key => {
      try {
        localStorage.removeItem(key);
      } catch (error) {
        console.warn(`清理缓存失败: ${key}`, error);
      }
    });
  }

  /**
   * 获取删除操作的影响评估
   */
  public static async getDeletionImpactAssessment(goalId: string): Promise<{
    riskLevel: 'low' | 'medium' | 'high';
    warnings: string[];
    recommendations: string[];
  }> {
    try {
      const analysis = await this.analyzeGoalDeletion(goalId);
      const dependencies = await this.checkGoalDependencies(goalId);

      let riskLevel: 'low' | 'medium' | 'high' = 'low';
      const warnings: string[] = [];
      const recommendations: string[] = [];

      // 评估风险级别
      if (analysis.totalItemsCount > 50) {
        riskLevel = 'high';
        warnings.push('该目标包含大量关联数据（超过50项），删除将影响重大');
      } else if (analysis.totalItemsCount > 20) {
        riskLevel = 'medium';
        warnings.push('该目标包含较多关联数据，请谨慎考虑');
      }

      if (dependencies.hasChildren) {
        riskLevel = 'high';
        warnings.push('该目标包含子目标，删除将影响目标层级结构');
      }

      if (analysis.pomodoroSessionsCount > 10) {
        warnings.push(`将丢失 ${analysis.pomodoroSessionsCount} 个番茄钟记录`);
      }

      if (analysis.decompositionSessionsCount > 0) {
        warnings.push('将丢失AI分解的历史记录和优化建议');
      }

      // 生成建议
      if (riskLevel === 'high') {
        recommendations.push('建议先导出相关数据进行备份');
        recommendations.push('考虑使用"暂停"功能而不是删除');
      }

      if (analysis.tasksCount > 0) {
        recommendations.push('检查是否有未完成的重要任务需要转移');
      }

      if (dependencies.hasParent) {
        recommendations.push('删除后可能需要调整父目标的计划');
      }

      return {
        riskLevel,
        warnings,
        recommendations
      };
    } catch (error) {
      console.error('获取删除影响评估失败:', error);
      return {
        riskLevel: 'medium',
        warnings: ['无法评估删除影响，请谨慎操作'],
        recommendations: ['建议联系技术支持']
      };
    }
  }
}

export default GoalCascadeDeleteService;