/**
 * 渲染器进程AI日志级别
 */
export enum AILogLevel {
  ERROR = 0,   // 只记录错误
  WARN = 1,    // 记录警告和错误
  INFO = 2,    // 记录基本信息、警告和错误
  DEBUG = 3,   // 记录所有调试信息
  VERBOSE = 4  // 记录最详细的信息
}

/**
 * AI API调用请求信息
 */
export interface AIRequestInfo {
  requestId: string;
  operation: string;
  provider?: string;
  modelId?: string;
  context?: string;
  requestData?: any;
}

/**
 * AI API调用响应信息
 */
export interface AIResponseInfo {
  requestId: string;
  success: boolean;
  responseTime: number;
  error?: string;
  resultData?: any;
}

/**
 * 渲染器进程AI调试日志工具类
 * 提供统一的AI操作日志记录功能
 */
export class AILogger {
  private static logLevel: AILogLevel = AILogLevel.INFO;
  private static isDevelopment = process.env.NODE_ENV === 'development';
  
  // 表情符号常量
  private static readonly ICONS = {
    REQUEST: '🤖📤',
    RESPONSE: '🤖📥',
    SUCCESS: '✅',
    ERROR: '❌',
    WARNING: '⚠️',
    DEBUG: '🔍',
    TIMING: '⏱️',
    PROVIDER: '🏢',
    MODEL: '🧠',
    OPERATION: '⚙️',
    DATA: '📊'
  };

  /**
   * 设置日志级别
   */
  static setLogLevel(level: AILogLevel): void {
    this.logLevel = level;
  }

  /**
   * 获取当前日志级别
   */
  static getLogLevel(): AILogLevel {
    // 开发环境默认DEBUG，生产环境默认INFO
    return this.isDevelopment ? AILogLevel.DEBUG : AILogLevel.INFO;
  }

  /**
   * 判断是否应该记录指定级别的日志
   */
  private static shouldLog(level: AILogLevel): boolean {
    return level <= this.getLogLevel();
  }

  /**
   * 生成唯一的请求ID
   */
  static generateRequestId(): string {
    return `R${Date.now().toString(36)}-${Math.random().toString(36).substr(2, 5)}`;
  }

  /**
   * 截断长内容
   */
  private static truncateContent(content: any, maxLength: number = 300): string {
    if (!content) return '';
    const str = typeof content === 'string' ? content : JSON.stringify(content);
    if (str.length <= maxLength) return str;
    return str.substring(0, maxLength) + '...[截断]';
  }

  /**
   * 格式化时间戳
   */
  private static formatTimestamp(): string {
    return new Date().toISOString().replace('T', ' ').substring(0, 23);
  }

  /**
   * 记录AI操作请求
   */
  static logRequest(requestInfo: AIRequestInfo): void {
    if (!this.shouldLog(AILogLevel.DEBUG)) return;

    const timestamp = this.formatTimestamp();
    const { requestId, operation, provider, modelId, context, requestData } = requestInfo;

    console.log(`\n${this.ICONS.REQUEST} [${timestamp}] AI操作请求开始`);
    console.log(`${this.ICONS.DEBUG} 请求ID: ${requestId}`);
    console.log(`${this.ICONS.OPERATION} 操作: ${operation}`);
    
    if (provider) {
      console.log(`${this.ICONS.PROVIDER} 提供商: ${provider}`);
    }
    
    if (modelId) {
      console.log(`${this.ICONS.MODEL} 模型: ${modelId}`);
    }
    
    if (context) {
      console.log(`${this.ICONS.DEBUG} 上下文: ${context}`);
    }

    if (requestData && this.shouldLog(AILogLevel.VERBOSE)) {
      console.log(`${this.ICONS.DATA} 请求数据:`);
      console.log(`────────────────────────────────────────`);
      console.log(this.truncateContent(requestData, 1000));
      console.log(`────────────────────────────────────────`);
    } else if (requestData) {
      console.log(`${this.ICONS.DATA} 请求数据预览: ${this.truncateContent(requestData, 200)}`);
    }
  }

  /**
   * 记录AI操作响应
   */
  static logResponse(responseInfo: AIResponseInfo): void {
    if (!this.shouldLog(AILogLevel.DEBUG)) return;

    const timestamp = this.formatTimestamp();
    const { requestId, success, responseTime, error, resultData } = responseInfo;

    const statusIcon = success ? this.ICONS.SUCCESS : this.ICONS.ERROR;
    const statusText = success ? '成功' : '失败';

    console.log(`\n${this.ICONS.RESPONSE} [${timestamp}] AI操作响应 ${statusIcon}`);
    console.log(`${this.ICONS.DEBUG} 请求ID: ${requestId}`);
    console.log(`${this.ICONS.DEBUG} 状态: ${statusText}`);
    console.log(`${this.ICONS.TIMING} 响应时间: ${responseTime}ms`);

    if (success && resultData) {
      if (this.shouldLog(AILogLevel.VERBOSE)) {
        console.log(`${this.ICONS.DATA} 完整响应数据:`);
        console.log(`────────────────────────────────────────`);
        console.log(this.truncateContent(resultData, 2000));
        console.log(`────────────────────────────────────────`);
      } else {
        console.log(`${this.ICONS.DATA} 响应数据预览: ${this.truncateContent(resultData, 300)}`);
      }
    } else if (error) {
      console.log(`${this.ICONS.ERROR} 错误信息: ${error}`);
    }
  }

  /**
   * 记录操作总结
   */
  static logOperationSummary(
    requestId: string, 
    operation: string, 
    success: boolean, 
    responseTime: number,
    error?: string
  ): void {
    if (!this.shouldLog(AILogLevel.INFO)) return;

    const timestamp = this.formatTimestamp();
    const statusIcon = success ? this.ICONS.SUCCESS : this.ICONS.ERROR;
    const statusText = success ? '成功' : '失败';

    console.log(`${statusIcon} [${timestamp}] ${operation} ${statusText} (${requestId}) - ${responseTime}ms`);
    
    if (!success && error && this.shouldLog(AILogLevel.WARN)) {
      console.log(`${this.ICONS.ERROR} 错误: ${error}`);
    }
  }

  /**
   * 记录警告信息
   */
  static logWarning(message: string, context?: any): void {
    if (!this.shouldLog(AILogLevel.WARN)) return;

    const timestamp = this.formatTimestamp();
    console.log(`${this.ICONS.WARNING} [${timestamp}] AI警告: ${message}`);
    
    if (context && this.shouldLog(AILogLevel.DEBUG)) {
      console.log(`${this.ICONS.DEBUG} 上下文:`, context);
    }
  }

  /**
   * 记录错误信息
   */
  static logError(message: string, error?: any, context?: any): void {
    if (!this.shouldLog(AILogLevel.ERROR)) return;

    const timestamp = this.formatTimestamp();
    console.error(`${this.ICONS.ERROR} [${timestamp}] AI错误: ${message}`);
    
    if (error) {
      console.error(`${this.ICONS.ERROR} 详细错误:`, error);
    }
    
    if (context && this.shouldLog(AILogLevel.DEBUG)) {
      console.error(`${this.ICONS.DEBUG} 上下文:`, context);
    }
  }

  /**
   * 记录调试信息
   */
  static logDebug(message: string, data?: any): void {
    if (!this.shouldLog(AILogLevel.DEBUG)) return;

    const timestamp = this.formatTimestamp();
    console.log(`${this.ICONS.DEBUG} [${timestamp}] AI调试: ${message}`);
    
    if (data) {
      console.log(`${this.ICONS.DEBUG} 数据:`, data);
    }
  }

  /**
   * 记录API测试信息
   */
  static logApiTest(provider: string, modelId: string, success: boolean, responseTime: number, error?: string): void {
    if (!this.shouldLog(AILogLevel.INFO)) return;

    const timestamp = this.formatTimestamp();
    const statusIcon = success ? this.ICONS.SUCCESS : this.ICONS.ERROR;
    const statusText = success ? '通过' : '失败';

    console.log(`${statusIcon} [${timestamp}] ${provider}/${modelId} 连接测试${statusText} - ${responseTime}ms`);
    
    if (!success && error) {
      console.log(`${this.ICONS.ERROR} 测试错误: ${error}`);
    }
  }

  /**
   * 记录分解操作信息
   */
  static logDecomposition(goalName: string, provider: string, success: boolean, responseTime: number, error?: string): void {
    if (!this.shouldLog(AILogLevel.INFO)) return;

    const timestamp = this.formatTimestamp();
    const statusIcon = success ? this.ICONS.SUCCESS : this.ICONS.ERROR;
    const statusText = success ? '完成' : '失败';

    console.log(`${statusIcon} [${timestamp}] 目标分解${statusText}: "${goalName}" (${provider}) - ${responseTime}ms`);
    
    if (!success && error) {
      console.log(`${this.ICONS.ERROR} 分解错误: ${error}`);
    }
  }

  /**
   * 启用开发模式调试
   */
  static enableDevMode(): void {
    this.setLogLevel(AILogLevel.DEBUG);
    console.log(`${this.ICONS.DEBUG} 渲染器AI日志：已启用开发模式调试`);
  }

  /**
   * 启用详细模式
   */
  static enableVerboseMode(): void {
    this.setLogLevel(AILogLevel.VERBOSE);
    console.log(`${this.ICONS.DEBUG} 渲染器AI日志：已启用详细模式`);
  }
}