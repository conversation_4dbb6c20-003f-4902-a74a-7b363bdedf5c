import { ApplicationInfo, WebsiteInfo } from './ApplicationMonitorService';

// 黑名单规则接口
export interface BlacklistRule {
  id: string;
  name: string;
  type: 'app' | 'website' | 'keyword';
  pattern: string; // 匹配模式：应用名、域名或关键词
  category: string; // 分类：社交、娱乐、购物等
  isActive: boolean;
  severity: 'low' | 'medium' | 'high'; // 违规严重程度
  // 时间限制规则
  timeRules?: {
    allowedHours?: { start: string; end: string }[]; // 允许访问的时间段
    allowedDays?: number[]; // 允许访问的星期（0=周日）
    maxDailyMinutes?: number; // 每日最大访问分钟数
  };
  // 上下文规则
  contextRules?: {
    allowInBreakTime?: boolean; // 休息时间是否允许
    allowWithWhitelistApp?: boolean; // 与白名单应用同时使用时是否允许
  };
  createdAt: Date;
  updatedAt: Date;
}

// 白名单规则接口
export interface WhitelistRule {
  id: string;
  name: string;
  type: 'app' | 'website' | 'keyword';
  pattern: string;
  category: string;
  isActive: boolean;
  priority: 'high' | 'medium' | 'low'; // 优先级，用于冲突解决
  description?: string;
  createdAt: Date;
  updatedAt: Date;
}

// 预设模板接口
export interface RuleTemplate {
  id: string;
  name: string;
  description: string;
  category: 'developer' | 'designer' | 'student' | 'writer' | 'manager' | 'custom';
  blacklistRules: Omit<BlacklistRule, 'id' | 'createdAt' | 'updatedAt'>[];
  whitelistRules: Omit<WhitelistRule, 'id' | 'createdAt' | 'updatedAt'>[];
}

// 违规记录接口
export interface ViolationRecord {
  id: string;
  ruleId: string;
  ruleName: string;
  type: 'app' | 'website';
  target: string; // 应用名或网站域名
  timestamp: Date;
  duration?: number; // 访问持续时间（毫秒）
  action: 'blocked' | 'warned' | 'allowed'; // 采取的行动
  userResponse?: 'dismissed' | 'continued' | 'returned'; // 用户响应
}

class BlacklistManagerService {
  private blacklistRules: BlacklistRule[] = [];
  private whitelistRules: WhitelistRule[] = [];
  private violationHistory: ViolationRecord[] = [];
  private dailyUsageStats: Map<string, number> = new Map(); // 每日使用统计
  private listeners: Set<() => void> = new Set(); // 事件监听器
  private static idCounter: number = 0; // 全局计数器确保ID唯一性
  private usedIds: Set<string> = new Set(); // 已使用的ID集合

  // 预设模板
  private readonly RULE_TEMPLATES: RuleTemplate[] = [
    {
      id: 'developer',
      name: '开发者模式',
      description: '适合软件开发者的规则集，允许开发工具，限制社交娱乐',
      category: 'developer',
      blacklistRules: [
        {
          name: '社交媒体',
          type: 'website',
          pattern: 'facebook.com|twitter.com|x.com|instagram.com|weibo.com|douyin.com',
          category: '社交',
          isActive: true,
          severity: 'high',
          timeRules: {
            allowedHours: [{ start: '12:00', end: '13:00' }, { start: '18:00', end: '19:00' }],
            maxDailyMinutes: 30
          }
        },
        {
          name: '娱乐视频',
          type: 'website',
          pattern: 'youtube.com|bilibili.com|netflix.com',
          category: '娱乐',
          isActive: true,
          severity: 'medium',
          contextRules: {
            allowInBreakTime: true
          }
        },
        {
          name: '游戏应用',
          type: 'app',
          pattern: 'Steam|Game|游戏',
          category: '游戏',
          isActive: true,
          severity: 'high'
        }
      ],
      whitelistRules: [
        {
          name: '开发工具',
          type: 'app',
          pattern: 'Code|IntelliJ|Studio|Terminal|iTerm',
          category: '开发',
          isActive: true,
          priority: 'high'
        },
        {
          name: '技术文档',
          type: 'website',
          pattern: 'github.com|stackoverflow.com|docs.microsoft.com|developer.mozilla.org',
          category: '学习',
          isActive: true,
          priority: 'high'
        }
      ]
    },
    {
      id: 'student',
      name: '学生模式',
      description: '适合学生使用，限制娱乐和社交，允许学习相关网站',
      category: 'student',
      blacklistRules: [
        {
          name: '游戏网站',
          type: 'website',
          pattern: 'game|游戏|steam|epic|4399',
          category: '游戏',
          isActive: true,
          severity: 'high'
        },
        {
          name: '购物网站',
          type: 'website',
          pattern: 'taobao.com|tmall.com|jd.com|amazon.com',
          category: '购物',
          isActive: true,
          severity: 'medium',
          timeRules: {
            allowedHours: [{ start: '20:00', end: '22:00' }],
            maxDailyMinutes: 60
          }
        }
      ],
      whitelistRules: [
        {
          name: '学习平台',
          type: 'website',
          pattern: 'coursera.org|edx.org|khan|学堂在线|慕课',
          category: '学习',
          isActive: true,
          priority: 'high'
        },
        {
          name: '学术搜索',
          type: 'website',
          pattern: 'scholar.google.com|cnki.net|arxiv.org',
          category: '学术',
          isActive: true,
          priority: 'high'
        }
      ]
    }
  ];

  constructor() {
    this.loadRules();
    this.loadViolationHistory();
    this.loadDailyStats();
    this.initializeUsedIds(); // 初始化已使用ID集合
    this.ensureDefaultRules();
  }

  // 初始化已使用ID集合
  private initializeUsedIds(): void {
    this.usedIds.clear();

    // 收集所有现有ID
    this.blacklistRules.forEach(rule => {
      if (rule.id) this.usedIds.add(rule.id);
    });

    this.whitelistRules.forEach(rule => {
      if (rule.id) this.usedIds.add(rule.id);
    });

    this.violationHistory.forEach(record => {
      if (record.id) this.usedIds.add(record.id);
    });

    console.log(`初始化已使用ID集合，共 ${this.usedIds.size} 个ID`);
  }

  // 确保有默认的测试规则
  private ensureDefaultRules(): void {
    // 检查是否已经有x.com的规则
    const hasXcomRule = this.blacklistRules.some(rule => 
      rule.pattern.includes('x.com') && rule.type === 'website'
    );

    if (!hasXcomRule) {
      // 自动添加x.com规则用于测试
      const xcomRule: BlacklistRule = {
        id: this.generateUniqueId('blacklist-xcom'),
        name: '社交媒体 - X(Twitter)',
        type: 'website',
        pattern: 'x.com',
        category: '社交',
        isActive: true,
        severity: 'medium',
        createdAt: new Date(),
        updatedAt: new Date()
      };

      this.blacklistRules.push(xcomRule);
      this.saveRules();
      console.log('自动添加 x.com 黑名单规则');
    }
  }

  // 生成唯一ID - 改进版本，确保绝对唯一性
  private generateUniqueId(prefix: string): string {
    // 增加全局计数器确保唯一性
    BlacklistManagerService.idCounter++;

    const timestamp = Date.now();
    const random = Math.random().toString(36).substring(2, 11);
    const performanceNow = performance.now().toString().replace('.', '');
    const counter = BlacklistManagerService.idCounter;

    // 使用更强的唯一性保证策略
    let id = `${prefix}-${timestamp}-${performanceNow}-${random}-${counter}`;

    // 如果ID已存在，继续生成直到找到唯一ID
    let attempts = 0;
    while (this.usedIds.has(id) && attempts < 100) {
      attempts++;
      BlacklistManagerService.idCounter++;
      const extraRandom = Math.random().toString(36).substring(2, 15);
      id = `${prefix}-${timestamp}-${performanceNow}-${extraRandom}-${BlacklistManagerService.idCounter}`;
    }

    if (attempts >= 100) {
      // 极端情况下使用UUID风格的ID
      id = `${prefix}-${crypto.randomUUID ? crypto.randomUUID() : this.generateFallbackUUID()}`;
    }

    // 记录已使用的ID
    this.usedIds.add(id);

    console.log(`生成唯一ID: ${id} (尝试次数: ${attempts})`);
    return id;
  }

  // 备用UUID生成器（当crypto.randomUUID不可用时）
  private generateFallbackUUID(): string {
    return 'xxxxxxxx-xxxx-4xxx-yxxx-xxxxxxxxxxxx'.replace(/[xy]/g, function(c) {
      const r = Math.random() * 16 | 0;
      const v = c === 'x' ? r : (r & 0x3 | 0x8);
      return v.toString(16);
    });
  }

  // 添加黑名单规则
  public async addBlacklistRule(rule: Omit<BlacklistRule, 'id' | 'createdAt' | 'updatedAt'>): Promise<BlacklistRule> {
    const newRule: BlacklistRule = {
      ...rule,
      id: this.generateUniqueId('blacklist'),
      createdAt: new Date(),
      updatedAt: new Date()
    };

    this.blacklistRules.push(newRule);
    await this.saveRules();
    return newRule;
  }

  // 添加白名单规则
  public async addWhitelistRule(rule: Omit<WhitelistRule, 'id' | 'createdAt' | 'updatedAt'>): Promise<WhitelistRule> {
    const newRule: WhitelistRule = {
      ...rule,
      id: this.generateUniqueId('whitelist'),
      createdAt: new Date(),
      updatedAt: new Date()
    };

    this.whitelistRules.push(newRule);
    await this.saveRules();
    return newRule;
  }

  // 更新黑名单规则
  public async updateBlacklistRule(id: string, updates: Partial<BlacklistRule>): Promise<boolean> {
    const index = this.blacklistRules.findIndex(rule => rule.id === id);
    if (index === -1) return false;

    this.blacklistRules[index] = {
      ...this.blacklistRules[index],
      ...updates,
      updatedAt: new Date()
    };

    await this.saveRules();
    return true;
  }

  // 更新白名单规则
  public async updateWhitelistRule(id: string, updates: Partial<WhitelistRule>): Promise<boolean> {
    const index = this.whitelistRules.findIndex(rule => rule.id === id);
    if (index === -1) return false;

    this.whitelistRules[index] = {
      ...this.whitelistRules[index],
      ...updates,
      updatedAt: new Date()
    };

    await this.saveRules();
    return true;
  }

  // 删除规则
  public async deleteBlacklistRule(id: string): Promise<boolean> {
    const index = this.blacklistRules.findIndex(rule => rule.id === id);
    if (index === -1) return false;

    this.blacklistRules.splice(index, 1);
    this.usedIds.delete(id); // 从已使用ID集合中移除
    await this.saveRules();
    return true;
  }

  public async deleteWhitelistRule(id: string): Promise<boolean> {
    const index = this.whitelistRules.findIndex(rule => rule.id === id);
    if (index === -1) return false;

    this.whitelistRules.splice(index, 1);
    this.usedIds.delete(id); // 从已使用ID集合中移除
    await this.saveRules();
    return true;
  }

  // 检查是否在黑名单中
  public async isBlacklisted(type: 'app' | 'website', target: ApplicationInfo | WebsiteInfo): Promise<boolean> {
    const activeRules = this.blacklistRules.filter(rule => rule.isActive && rule.type === type);
    
    for (const rule of activeRules) {
      if (this.matchesPattern(rule.pattern, target)) {
        // 检查时间规则
        if (rule.timeRules && !this.isWithinTimeRules(rule.timeRules)) {
          continue;
        }

        // 检查上下文规则
        if (rule.contextRules && !this.isWithinContextRules(rule.contextRules)) {
          continue;
        }

        // 检查是否在白名单中（白名单优先级更高）
        if (await this.isWhitelisted(type, target)) {
          continue;
        }

        // 记录违规
        console.log(`记录违规: ${target.name || (target as WebsiteInfo).domain} 匹配规则: ${rule.name}`);
        await this.recordViolation(rule, type, target, 'blocked');
        return true;
      }
    }

    return false;
  }

  // 检查是否在白名单中
  public async isWhitelisted(type: 'app' | 'website', target: ApplicationInfo | WebsiteInfo): Promise<boolean> {
    const activeRules = this.whitelistRules.filter(rule => rule.isActive && rule.type === type);
    
    // 按优先级排序
    activeRules.sort((a, b) => {
      const priorityWeight = { high: 3, medium: 2, low: 1 };
      return priorityWeight[b.priority] - priorityWeight[a.priority];
    });

    for (const rule of activeRules) {
      if (this.matchesPattern(rule.pattern, target)) {
        return true;
      }
    }

    return false;
  }

  // 模式匹配
  private matchesPattern(pattern: string, target: ApplicationInfo | WebsiteInfo): boolean {
    const targetString = target.name || (target as WebsiteInfo).domain || '';
    const isWebsite = 'domain' in target;
    
    try {
      if (pattern.includes('|')) {
        // 多个模式用 | 分隔
        const patterns = pattern.split('|');
        return patterns.some(p => this.matchSinglePattern(p.trim(), targetString, isWebsite));
      } else {
        // 单个模式
        return this.matchSinglePattern(pattern, targetString, isWebsite);
      }
    } catch (error) {
      console.error('模式匹配失败:', error);
      return false;
    }
  }

  // 单个模式匹配
  private matchSinglePattern(pattern: string, targetString: string, isWebsite: boolean): boolean {
    const patternLower = pattern.toLowerCase();
    const targetLower = targetString.toLowerCase();
    
    if (isWebsite) {
      // 对于网站，支持精确域名匹配和包含匹配
      if (patternLower.includes('.')) {
        // 如果模式包含点，进行精确域名匹配
        return targetLower === patternLower || 
               targetLower.endsWith('.' + patternLower) ||
               targetLower.includes(patternLower);
      } else {
        // 如果模式不包含点，进行包含匹配
        return targetLower.includes(patternLower);
      }
    } else {
      // 对于应用，进行包含匹配
      return targetLower.includes(patternLower);
    }
  }

  // 检查时间规则
  private isWithinTimeRules(timeRules: BlacklistRule['timeRules']): boolean {
    if (!timeRules) return true;

    const now = new Date();
    const currentTime = `${now.getHours().toString().padStart(2, '0')}:${now.getMinutes().toString().padStart(2, '0')}`;
    const currentDay = now.getDay();

    // 检查允许的星期
    if (timeRules.allowedDays && !timeRules.allowedDays.includes(currentDay)) {
      return false;
    }

    // 检查允许的时间段
    if (timeRules.allowedHours) {
      const isInAllowedHour = timeRules.allowedHours.some(({ start, end }) => {
        return currentTime >= start && currentTime <= end;
      });
      if (!isInAllowedHour) return false;
    }

    // 检查每日使用限制
    if (timeRules.maxDailyMinutes) {
      const today = now.toISOString().split('T')[0];
      const dailyUsage = this.dailyUsageStats.get(today) || 0;
      if (dailyUsage >= timeRules.maxDailyMinutes) {
        return false;
      }
    }

    return true;
  }

  // 检查上下文规则
  private isWithinContextRules(contextRules: BlacklistRule['contextRules']): boolean {
    if (!contextRules) return true;

    // 这里可以扩展更多上下文检查逻辑
    // 例如检查当前是否在休息时间、是否有白名单应用在运行等

    return true;
  }

  // 记录违规 - 优化重复检查和错误处理
  private async recordViolation(
    rule: BlacklistRule,
    type: 'app' | 'website',
    target: ApplicationInfo | WebsiteInfo,
    action: ViolationRecord['action']
  ): Promise<void> {
    try {
      const targetName = target.name || (target as WebsiteInfo).domain;
      if (!targetName || !rule.id) {
        console.warn('无效的违规记录数据:', { targetName, ruleId: rule.id });
        return;
      }
      
      // 使用改进的ID生成策略
      const uniqueId = this.generateUniqueId(`violation-${type}`);

      const violation: ViolationRecord = {
        id: uniqueId,
        ruleId: rule.id,
        ruleName: rule.name,
        type,
        target: targetName,
        timestamp: new Date(),
        action
      };

      console.log('记录违规行为:', violation);

      // 优化重复检查：增加时间间隔至10秒
      const isDuplicate = this.violationHistory.some(existing =>
        existing.ruleId === violation.ruleId &&
        existing.target === violation.target &&
        existing.type === violation.type &&
        Math.abs(existing.timestamp.getTime() - violation.timestamp.getTime()) < 10000 // 10秒内的重复记录
      );

      if (!isDuplicate) {
        this.violationHistory.unshift(violation);

        // 保持历史记录在合理范围内
        if (this.violationHistory.length > 500) { // 减少保存数量
          this.violationHistory = this.violationHistory.slice(0, 500);
        }

        await this.saveViolationHistory();
        this.notifyListeners();

        console.log('当前违规记录总数:', this.violationHistory.length);
      } else {
        console.log('跳过重复的违规记录:', violation.target);
      }
    } catch (error) {
      console.error('记录违规失败:', error);
      // 不抛出错误，避免影响上层调用
    }
  }

  // 应用预设模板
  public async applyTemplate(templateId: string): Promise<boolean> {
    const template = this.RULE_TEMPLATES.find(t => t.id === templateId);
    if (!template) return false;

    // 添加黑名单规则
    for (const rule of template.blacklistRules) {
      await this.addBlacklistRule(rule);
    }

    // 添加白名单规则
    for (const rule of template.whitelistRules) {
      await this.addWhitelistRule(rule);
    }

    return true;
  }

  // 智能建议
  public getSuggestions(activityHistory: any[]): {
    suggestedBlacklist: string[];
    suggestedWhitelist: string[];
    insights: string[];
  } {
    const suggestions = {
      suggestedBlacklist: [],
      suggestedWhitelist: [],
      insights: []
    };

    // 分析活动历史，提供智能建议
    const frequentApps = this.analyzeFrequentItems(activityHistory, 'app');
    const frequentSites = this.analyzeFrequentItems(activityHistory, 'website');

    // 根据使用频率和时长提供建议
    // 这里可以实现更复杂的机器学习算法

    return suggestions;
  }

  private analyzeFrequentItems(history: any[], type: 'app' | 'website'): string[] {
    // 简化的分析逻辑
    return [];
  }

  // 获取规则列表
  public getBlacklistRules(): BlacklistRule[] {
    return this.blacklistRules;
  }

  public getWhitelistRules(): WhitelistRule[] {
    return this.whitelistRules;
  }

  public getViolationHistory(days: number = 7): ViolationRecord[] {
    const cutoff = new Date();
    cutoff.setDate(cutoff.getDate() - days);
    
    return this.violationHistory.filter(record => record.timestamp >= cutoff);
  }

  public getRuleTemplates(): RuleTemplate[] {
    return this.RULE_TEMPLATES;
  }

  // 数据持久化
  private async saveRules(): Promise<void> {
    try {
      const data = {
        blacklistRules: this.blacklistRules,
        whitelistRules: this.whitelistRules
      };
      localStorage.setItem('focusOS_blacklist_rules', JSON.stringify(data));
    } catch (error) {
      console.error('保存规则失败:', error);
    }
  }

  private loadRules(): void {
    try {
      const saved = localStorage.getItem('focusOS_blacklist_rules');
      if (saved) {
        const data = JSON.parse(saved);
        
        this.blacklistRules = (data.blacklistRules || []).map((rule: any) => ({
          ...rule,
          createdAt: new Date(rule.createdAt),
          updatedAt: new Date(rule.updatedAt)
        }));
        
        this.whitelistRules = (data.whitelistRules || []).map((rule: any) => ({
          ...rule,
          createdAt: new Date(rule.createdAt),
          updatedAt: new Date(rule.updatedAt)
        }));
      }
    } catch (error) {
      console.error('加载规则失败:', error);
    }
  }

  private async saveViolationHistory(): Promise<void> {
    try {
      // 只保存最近30天的记录
      const thirtyDaysAgo = new Date();
      thirtyDaysAgo.setDate(thirtyDaysAgo.getDate() - 30);
      
      const recentHistory = this.violationHistory.filter(record => record.timestamp >= thirtyDaysAgo);
      
      localStorage.setItem('focusOS_violation_history', JSON.stringify(recentHistory));
    } catch (error) {
      console.error('保存违规历史失败:', error);
    }
  }

  private loadViolationHistory(): void {
    try {
      const saved = localStorage.getItem('focusOS_violation_history');
      if (saved) {
        this.violationHistory = JSON.parse(saved).map((record: any) => ({
          ...record,
          timestamp: new Date(record.timestamp)
        }));
      }
    } catch (error) {
      console.error('加载违规历史失败:', error);
    }
  }

  private loadDailyStats(): void {
    try {
      const saved = localStorage.getItem('focusOS_daily_usage_stats');
      if (saved) {
        this.dailyUsageStats = new Map(JSON.parse(saved));
      }
    } catch (error) {
      console.error('加载每日统计失败:', error);
    }
  }

  private async saveDailyStats(): Promise<void> {
    try {
      const data = Array.from(this.dailyUsageStats.entries());
      localStorage.setItem('focusOS_daily_usage_stats', JSON.stringify(data));
    } catch (error) {
      console.error('保存每日统计失败:', error);
    }
  }

  // 事件监听器管理
  public addListener(listener: () => void): void {
    this.listeners.add(listener);
  }

  public removeListener(listener: () => void): void {
    this.listeners.delete(listener);
  }

  private notifyListeners(): void {
    // 使用异步通知避免阻塞
    setTimeout(() => {
      this.listeners.forEach(listener => {
        try {
          listener();
        } catch (error) {
          console.error('监听器执行失败:', error);
        }
      });
    }, 0);
  }

  // 清理资源
  public async destroy(): Promise<void> {
    await this.saveRules();
    await this.saveViolationHistory();
    await this.saveDailyStats();
    this.listeners.clear();
  }
}

// 单例实例
export const blacklistManagerService = new BlacklistManagerService();
export default BlacklistManagerService;