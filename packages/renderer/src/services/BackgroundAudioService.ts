export type BackgroundSoundType = 
  | 'rain' 
  | 'forest' 
  | 'ocean' 
  | 'cafe' 
  | 'white-noise' 
  | 'brown-noise' 
  | 'pink-noise'
  | 'fireplace'
  | 'thunderstorm'
  | 'birds'
  | 'wind'
  | 'none';

export interface BackgroundAudioSettings {
  enabled: boolean;
  soundType: BackgroundSoundType;
  volume: number; // 0-1
  fadeInDuration: number; // 秒
  fadeOutDuration: number; // 秒
  workSessionSound: BackgroundSoundType;
  breakSessionSound: BackgroundSoundType;
  autoSwitchSounds: boolean; // 是否在工作/休息时自动切换音效
}

interface SoundConfig {
  name: string;
  description: string;
  category: 'nature' | 'ambient' | 'noise' | 'urban';
  icon: string;
  color: string;
  // 音频文件路径（如果有预置音频）
  audioFile?: string;
  // 合成音频配置（用于程序生成的音效）
  synthConfig?: {
    type: 'noise' | 'oscillator' | 'complex';
    parameters: any;
  };
}

export class BackgroundAudioService {
  private static instance: BackgroundAudioService;
  private audioContext: AudioContext | null = null;
  private currentSource: AudioBufferSourceNode | null = null;
  private gainNode: GainNode | null = null;
  private settings: BackgroundAudioSettings;
  private isPlaying = false;
  private currentSoundType: BackgroundSoundType = 'none';
  private fadeTimeout: NodeJS.Timeout | null = null;

  // 音效配置
  private soundConfigs: Record<BackgroundSoundType, SoundConfig> = {
    'none': {
      name: '无背景音',
      description: '静音模式',
      category: 'ambient',
      icon: '🔇',
      color: '#8c8c8c'
    },
    'rain': {
      name: '雨声',
      description: '轻柔的雨滴声，有助于集中注意力',
      category: 'nature',
      icon: '🌧️',
      color: '#1890ff',
      audioFile: '/sounds/background/rain.mp3',
      synthConfig: {
        type: 'noise',
        parameters: { 
          type: 'white',
          filterFreq: 2000,
          filterQ: 0.5,
          modulation: { rate: 0.1, depth: 0.3 }
        }
      }
    },
    'forest': {
      name: '森林',
      description: '鸟鸣和树叶沙沙声',
      category: 'nature',
      icon: '🌲',
      color: '#52c41a',
      audioFile: '/sounds/background/forest.mp3',
      synthConfig: {
        type: 'complex',
        parameters: {
          layers: [
            { type: 'noise', freq: 1000, gain: 0.3 },
            { type: 'oscillator', freq: 200, gain: 0.2, modRate: 0.05 }
          ]
        }
      }
    },
    'ocean': {
      name: '海浪',
      description: '舒缓的海浪声',
      category: 'nature',
      icon: '🌊',
      color: '#13c2c2',
      audioFile: '/sounds/background/ocean.mp3',
      synthConfig: {
        type: 'noise',
        parameters: {
          type: 'brown',
          filterFreq: 500,
          filterQ: 1.0,
          modulation: { rate: 0.05, depth: 0.5 }
        }
      }
    },
    'cafe': {
      name: '咖啡厅',
      description: '咖啡厅的环境音',
      category: 'urban',
      icon: '☕',
      color: '#d4b106',
      audioFile: '/sounds/background/cafe.mp3',
      synthConfig: {
        type: 'complex',
        parameters: {
          layers: [
            { type: 'noise', freq: 800, gain: 0.4 },
            { type: 'noise', freq: 200, gain: 0.2 }
          ]
        }
      }
    },
    'white-noise': {
      name: '白噪音',
      description: '均匀的白噪音，屏蔽外界干扰',
      category: 'noise',
      icon: '⚪',
      color: '#f0f0f0',
      synthConfig: {
        type: 'noise',
        parameters: { type: 'white', filterFreq: 20000, filterQ: 0.1 }
      }
    },
    'brown-noise': {
      name: '棕噪音',
      description: '低频噪音，更加温和',
      category: 'noise',
      icon: '🟤',
      color: '#8b4513',
      synthConfig: {
        type: 'noise',
        parameters: { type: 'brown', filterFreq: 1000, filterQ: 0.3 }
      }
    },
    'pink-noise': {
      name: '粉噪音',
      description: '平衡的频率分布',
      category: 'noise',
      icon: '🩷',
      color: '#ff69b4',
      synthConfig: {
        type: 'noise',
        parameters: { type: 'pink', filterFreq: 5000, filterQ: 0.2 }
      }
    },
    'fireplace': {
      name: '壁炉',
      description: '温暖的壁炉燃烧声',
      category: 'ambient',
      icon: '🔥',
      color: '#ff4500',
      audioFile: '/sounds/background/fireplace.mp3',
      synthConfig: {
        type: 'noise',
        parameters: {
          type: 'brown',
          filterFreq: 300,
          filterQ: 2.0,
          modulation: { rate: 0.2, depth: 0.4 }
        }
      }
    },
    'thunderstorm': {
      name: '雷雨',
      description: '远处的雷声和雨声',
      category: 'nature',
      icon: '⛈️',
      color: '#4b0082',
      audioFile: '/sounds/background/thunderstorm.mp3',
      synthConfig: {
        type: 'complex',
        parameters: {
          layers: [
            { type: 'noise', freq: 1500, gain: 0.4 },
            { type: 'oscillator', freq: 60, gain: 0.1, modRate: 0.01 }
          ]
        }
      }
    },
    'birds': {
      name: '鸟鸣',
      description: '清晨的鸟儿歌声',
      category: 'nature',
      icon: '🐦',
      color: '#87ceeb',
      audioFile: '/sounds/background/birds.mp3',
      synthConfig: {
        type: 'complex',
        parameters: {
          layers: [
            { type: 'oscillator', freq: 1000, gain: 0.2, modRate: 0.3 },
            { type: 'oscillator', freq: 1500, gain: 0.15, modRate: 0.5 }
          ]
        }
      }
    },
    'wind': {
      name: '微风',
      description: '轻柔的风声',
      category: 'nature',
      icon: '💨',
      color: '#b0e0e6',
      audioFile: '/sounds/background/wind.mp3',
      synthConfig: {
        type: 'noise',
        parameters: {
          type: 'white',
          filterFreq: 800,
          filterQ: 0.8,
          modulation: { rate: 0.08, depth: 0.6 }
        }
      }
    }
  };

  private constructor() {
    this.settings = this.loadSettings();
  }

  public static getInstance(): BackgroundAudioService {
    if (!BackgroundAudioService.instance) {
      BackgroundAudioService.instance = new BackgroundAudioService();
    }
    return BackgroundAudioService.instance;
  }

  private loadSettings(): BackgroundAudioSettings {
    const saved = localStorage.getItem('backgroundAudioSettings');
    if (saved) {
      try {
        return JSON.parse(saved);
      } catch (error) {
        console.error('Failed to parse background audio settings:', error);
      }
    }

    return {
      enabled: false,
      soundType: 'rain',
      volume: 0.3,
      fadeInDuration: 3,
      fadeOutDuration: 2,
      workSessionSound: 'rain',
      breakSessionSound: 'forest',
      autoSwitchSounds: true
    };
  }

  public saveSettings(settings: Partial<BackgroundAudioSettings>): void {
    this.settings = { ...this.settings, ...settings };
    localStorage.setItem('backgroundAudioSettings', JSON.stringify(this.settings));
  }

  public getSettings(): BackgroundAudioSettings {
    return { ...this.settings };
  }

  public getSoundConfigs(): Record<BackgroundSoundType, SoundConfig> {
    return this.soundConfigs;
  }

  public getSoundConfig(soundType: BackgroundSoundType): SoundConfig {
    return this.soundConfigs[soundType];
  }

  private async initializeAudioContext(): Promise<void> {
    if (!this.audioContext) {
      this.audioContext = new (window.AudioContext || (window as any).webkitAudioContext)();
      this.gainNode = this.audioContext.createGain();
      this.gainNode.connect(this.audioContext.destination);
      this.gainNode.gain.value = 0;
    }

    if (this.audioContext.state === 'suspended') {
      await this.audioContext.resume();
    }
  }

  public async startBackgroundSound(soundType: BackgroundSoundType = this.settings.soundType): Promise<void> {
    if (soundType === 'none' || !this.settings.enabled) {
      await this.stopBackgroundSound();
      return;
    }

    try {
      await this.initializeAudioContext();
      
      // 如果已经在播放相同的音效，不需要重新开始
      if (this.isPlaying && this.currentSoundType === soundType) {
        return;
      }

      // 停止当前播放的音效
      if (this.isPlaying) {
        await this.stopBackgroundSound();
      }

      const config = this.soundConfigs[soundType];
      
      // 尝试加载预置音频文件
      if (config.audioFile) {
        const success = await this.tryLoadAudioFile(config.audioFile, soundType);
        if (success) return;
      }

      // 降级到合成音频
      if (config.synthConfig) {
        await this.generateSynthAudio(config.synthConfig, soundType);
      }

    } catch (error) {
      console.error('Failed to start background sound:', error);
      throw error;
    }
  }

  private async tryLoadAudioFile(audioFile: string, soundType: BackgroundSoundType): Promise<boolean> {
    try {
      const response = await fetch(audioFile);
      if (!response.ok) {
        console.warn(`Audio file not found: ${audioFile}`);
        return false;
      }

      const arrayBuffer = await response.arrayBuffer();
      const audioBuffer = await this.audioContext!.decodeAudioData(arrayBuffer);
      
      await this.playAudioBuffer(audioBuffer, soundType, true); // loop = true
      return true;
    } catch (error) {
      console.warn(`Failed to load audio file ${audioFile}:`, error);
      return false;
    }
  }

  private async generateSynthAudio(synthConfig: any, soundType: BackgroundSoundType): Promise<void> {
    if (!this.audioContext || !this.gainNode) return;

    switch (synthConfig.type) {
      case 'noise':
        this.currentSource = this.createNoiseSource(synthConfig.parameters);
        break;
      case 'oscillator':
        this.currentSource = this.createOscillatorSource(synthConfig.parameters);
        break;
      case 'complex':
        // 复杂音效暂时使用噪音替代
        this.currentSource = this.createNoiseSource({ type: 'white' });
        break;
      default:
        this.currentSource = this.createNoiseSource({ type: 'white' });
    }

    if (this.currentSource) {
      this.currentSource.connect(this.gainNode);
      this.currentSource.start();
      this.isPlaying = true;
      this.currentSoundType = soundType;
      
      // 淡入效果
      await this.fadeIn();
    }
  }

  private async playAudioBuffer(audioBuffer: AudioBuffer, soundType: BackgroundSoundType, loop: boolean = true): Promise<void> {
    if (!this.audioContext || !this.gainNode) return;

    this.currentSource = this.audioContext.createBufferSource();
    this.currentSource.buffer = audioBuffer;
    this.currentSource.loop = loop;
    this.currentSource.connect(this.gainNode);
    this.currentSource.start();
    
    this.isPlaying = true;
    this.currentSoundType = soundType;
    
    // 淡入效果
    await this.fadeIn();
  }

  private createNoiseSource(params: any): AudioBufferSourceNode {
    if (!this.audioContext) throw new Error('Audio context not initialized');

    // 创建噪音缓冲区
    const bufferSize = this.audioContext.sampleRate * 2; // 2秒的缓冲区
    const buffer = this.audioContext.createBuffer(1, bufferSize, this.audioContext.sampleRate);
    const output = buffer.getChannelData(0);

    // 生成噪音
    for (let i = 0; i < bufferSize; i++) {
      switch (params.type) {
        case 'white':
          output[i] = Math.random() * 2 - 1;
          break;
        case 'brown':
          // 简化的棕噪音
          output[i] = (Math.random() * 2 - 1) * 0.5;
          break;
        case 'pink':
          // 简化的粉噪音
          output[i] = (Math.random() * 2 - 1) * 0.7;
          break;
        default:
          output[i] = Math.random() * 2 - 1;
      }
    }

    const source = this.audioContext.createBufferSource();
    source.buffer = buffer;
    source.loop = true;

    return source;
  }

  private createOscillatorSource(params: any): OscillatorNode {
    if (!this.audioContext) throw new Error('Audio context not initialized');

    const oscillator = this.audioContext.createOscillator();
    oscillator.type = 'sine';
    oscillator.frequency.setValueAtTime(params.freq || 440, this.audioContext.currentTime);

    return oscillator as any; // 类型转换，因为OscillatorNode也有start方法
  }

  private async fadeIn(): Promise<void> {
    if (!this.gainNode) return;

    const duration = this.settings.fadeInDuration;
    const targetVolume = this.settings.volume;
    
    this.gainNode.gain.setValueAtTime(0, this.audioContext!.currentTime);
    this.gainNode.gain.linearRampToValueAtTime(targetVolume, this.audioContext!.currentTime + duration);
  }

  private async fadeOut(): Promise<void> {
    if (!this.gainNode) return;

    const duration = this.settings.fadeOutDuration;
    const currentTime = this.audioContext!.currentTime;
    
    this.gainNode.gain.setValueAtTime(this.gainNode.gain.value, currentTime);
    this.gainNode.gain.linearRampToValueAtTime(0, currentTime + duration);

    // 等待淡出完成
    return new Promise(resolve => {
      this.fadeTimeout = setTimeout(resolve, duration * 1000);
    });
  }

  public async stopBackgroundSound(): Promise<void> {
    if (!this.isPlaying) return;

    try {
      // 淡出效果
      await this.fadeOut();

      // 停止音频源
      if (this.currentSource) {
        this.currentSource.stop();
        this.currentSource.disconnect();
        this.currentSource = null;
      }

      this.isPlaying = false;
      this.currentSoundType = 'none';
    } catch (error) {
      console.error('Failed to stop background sound:', error);
    }
  }

  public async setVolume(volume: number): Promise<void> {
    this.settings.volume = Math.max(0, Math.min(1, volume));
    this.saveSettings({ volume: this.settings.volume });

    if (this.gainNode && this.isPlaying) {
      this.gainNode.gain.setValueAtTime(this.settings.volume, this.audioContext!.currentTime);
    }
  }

  public getVolume(): number {
    return this.settings.volume;
  }

  public isCurrentlyPlaying(): boolean {
    return this.isPlaying;
  }

  public getCurrentSoundType(): BackgroundSoundType {
    return this.currentSoundType;
  }

  // 番茄钟集成方法
  public async startWorkSession(): Promise<void> {
    if (!this.settings.enabled) return;

    const soundType = this.settings.autoSwitchSounds 
      ? this.settings.workSessionSound 
      : this.settings.soundType;
    
    await this.startBackgroundSound(soundType);
  }

  public async startBreakSession(): Promise<void> {
    if (!this.settings.enabled) return;

    const soundType = this.settings.autoSwitchSounds 
      ? this.settings.breakSessionSound 
      : this.settings.soundType;
    
    await this.startBackgroundSound(soundType);
  }

  public async pauseSession(): Promise<void> {
    await this.stopBackgroundSound();
  }

  public async resumeSession(): Promise<void> {
    if (!this.settings.enabled) return;
    await this.startBackgroundSound(this.currentSoundType);
  }

  // 清理资源
  public dispose(): void {
    this.stopBackgroundSound();
    
    if (this.fadeTimeout) {
      clearTimeout(this.fadeTimeout);
      this.fadeTimeout = null;
    }

    if (this.audioContext) {
      this.audioContext.close();
      this.audioContext = null;
    }
  }
}

// 导出单例实例
export const backgroundAudioService = BackgroundAudioService.getInstance();
