import { AudioContextManager } from '../utils/AudioContextManager';

// 音频播放服务 - 处理通知提示音播放
export type SoundType =
  | 'task-complete'
  | 'pomodoro-complete'
  | 'break-complete'
  | 'goal-achieved'
  | 'system-alert'
  | 'gentle-reminder';

export interface AudioSettings {
  enabled: boolean;
  volume: number; // 0-1
  soundTheme: 'default' | 'gentle' | 'energetic';
}

export class AudioService {
  private static instance: AudioService;
  private audioContextManager: AudioContextManager;
  private audioBuffers: Map<SoundType, AudioBuffer> = new Map();
  private htmlAudioElements: Map<SoundType, HTMLAudioElement> = new Map();
  private settings: AudioSettings;
  private isInitialized = false;

  // 音频文件映射
  private soundFiles: Record<SoundType, string> = {
    'task-complete': '/sounds/task-complete.mp3',
    'pomodoro-complete': '/sounds/pomodoro-complete.mp3',
    'break-complete': '/sounds/break-complete.mp3',
    'goal-achieved': '/sounds/goal-achieved.mp3',
    'system-alert': '/sounds/system-alert.mp3',
    'gentle-reminder': '/sounds/gentle-reminder.mp3',
  };

  // 合成音频配置（作为备用方案）
  private synthSoundConfig: Record<SoundType, { frequencies: number[], durations: number[] }> = {
    'task-complete': { frequencies: [800, 1000, 1200], durations: [0.2, 0.2, 0.3] },
    'pomodoro-complete': { frequencies: [1000, 800, 1000], durations: [0.3, 0.2, 0.4] },
    'break-complete': { frequencies: [600, 800], durations: [0.4, 0.4] },
    'goal-achieved': { frequencies: [800, 1000, 1200, 1400], durations: [0.2, 0.2, 0.2, 0.4] },
    'system-alert': { frequencies: [1200], durations: [0.3] },
    'gentle-reminder': { frequencies: [600, 800], durations: [0.3, 0.2] },
  };

  private constructor() {
    this.settings = this.loadSettings();
    this.audioContextManager = AudioContextManager.getInstance();
  }

  public static getInstance(): AudioService {
    if (!AudioService.instance) {
      AudioService.instance = new AudioService();
    }
    return AudioService.instance;
  }

  // 初始化音频服务
  public async initialize(): Promise<void> {
    if (this.isInitialized) return;

    try {
      // 尝试初始化Web Audio API
      await this.initializeWebAudio();
      
      // 预加载音频文件
      await this.preloadAudioFiles();
      
      this.isInitialized = true;
      console.log('AudioService initialized successfully');
    } catch (error) {
      console.warn('AudioService initialization failed, using fallback:', error);
      // 即使初始化失败，也标记为已初始化，使用合成音频作为备用
      this.isInitialized = true;
    }
  }

  // 初始化Web Audio API
  private async initializeWebAudio(): Promise<void> {
    try {
      // 使用AudioContextManager来管理音频上下文
      const audioContext = await this.audioContextManager.getAudioContext();
      if (!audioContext) {
        throw new Error('Failed to get audio context');
      }
      console.log('Web Audio API initialized successfully');
    } catch (error) {
      console.warn('Failed to initialize Web Audio API:', error);
      throw error;
    }
  }

  // 预加载音频文件
  private async preloadAudioFiles(): Promise<void> {
    const loadPromises: Promise<void>[] = [];

    for (const [soundType, filePath] of Object.entries(this.soundFiles)) {
      loadPromises.push(this.loadAudioFile(soundType as SoundType, filePath));
    }

    try {
      const results = await Promise.allSettled(loadPromises);
      const successCount = results.filter(r => r.status === 'fulfilled').length;
      const failureCount = results.filter(r => r.status === 'rejected').length;
      
      console.log(`🎵 Audio preload completed: ${successCount} succeeded, ${failureCount} failed`);
      if (failureCount > 0) {
        console.log('🔊 Failed audio files will use synthetic audio fallback');
      }
    } catch (error) {
      console.warn('Unexpected error during audio preload:', error);
    }
  }

  // 加载单个音频文件
  private async loadAudioFile(soundType: SoundType, filePath: string): Promise<void> {
    try {
      // 首先尝试HTML5 Audio (更兼容)
      const audio = new Audio();
      audio.preload = 'metadata'; // 只加载元数据，减少资源占用
      audio.volume = this.settings.volume;
      
      // 设置音频源
      audio.src = filePath;
      
      // 等待音频可以播放
      await new Promise<void>((resolve, reject) => {
        const handleCanPlay = () => {
          cleanup();
          this.htmlAudioElements.set(soundType, audio);
          resolve();
        };
        
        const handleError = (error: any) => {
          cleanup();
          reject(error);
        };
        
        const handleTimeout = () => {
          cleanup();
          reject(new Error('Audio load timeout'));
        };
        
        const cleanup = () => {
          audio.removeEventListener('canplay', handleCanPlay);
          audio.removeEventListener('error', handleError);
          clearTimeout(timeoutId);
        };
        
        audio.addEventListener('canplay', handleCanPlay, { once: true });
        audio.addEventListener('error', handleError, { once: true });
        
        // 设置较短的超时时间
        const timeoutId = setTimeout(handleTimeout, 2000);
        
        // 开始加载
        audio.load();
      });
      
      console.log(`✅ Audio file loaded successfully: ${soundType}`);
    } catch (error) {
      console.warn(`⚠️ Failed to load audio file ${filePath}, will use synthetic audio:`, error);
      // 不需要做任何事，playSound方法会自动降级到合成音频
    }
  }

  // 播放提示音
  public async playSound(soundType: SoundType): Promise<void> {
    console.log(`🎵 AudioService.playSound called: ${soundType}`);
    console.log(`🎵 Audio settings:`, {
      enabled: this.settings.enabled,
      volume: this.settings.volume,
      initialized: this.isInitialized
    });

    if (!this.settings.enabled) {
      console.log(`🔇 Audio disabled, skipping playback: ${soundType}`);
      return;
    }

    try {
      // 确保已初始化
      if (!this.isInitialized) {
        console.log(`🎵 Initializing audio service...`);
        await this.initialize();
      }

      console.log(`🎵 Attempting to play: ${soundType}`);

      // 尝试播放预加载的音频
      if (await this.playPreloadedAudio(soundType)) {
        console.log(`✅ Successfully played preloaded audio: ${soundType}`);
        return;
      }

      console.log(`⚠️ Preloaded audio failed, falling back to synthetic: ${soundType}`);
      // 降级到合成音频
      await this.playSynthAudio(soundType);
      console.log(`✅ Successfully played synthetic audio: ${soundType}`);
    } catch (error) {
      console.error(`❌ Failed to play sound ${soundType}:`, error);
      throw error;
    }
  }

  // 专门用于通知的音频播放方法
  public async playNotificationSound(soundType: SoundType, forcePlay: boolean = false): Promise<void> {
    console.log(`🔔 AudioService.playNotificationSound called: ${soundType}, forcePlay: ${forcePlay}`);

    // 如果是强制播放（测试）或者音频已启用
    if (forcePlay || this.settings.enabled) {
      try {
        // 确保音频上下文已激活
        await this.ensureAudioContextActive();

        // 确保已初始化
        if (!this.isInitialized) {
          console.log(`🎵 Initializing audio service for notification...`);
          await this.initialize();
        }

        // 播放音频
        await this.playSound(soundType);

        // 记录播放日志
        console.log(`🔊 Notification audio played: ${soundType}, enabled: ${this.settings.enabled}, forced: ${forcePlay}`);
      } catch (error) {
        console.error(`🔇 Notification audio playback failed: ${soundType}`, error);
        throw error;
      }
    } else {
      console.log(`🔇 Audio disabled, skipping notification sound: ${soundType}`);
    }
  }

  // 确保音频上下文处于活跃状态
  private async ensureAudioContextActive(): Promise<void> {
    const context = await this.audioContextManager.getAudioContext();
    if (context && context.state === 'suspended') {
      try {
        await context.resume();
        console.log('🎵 Audio context resumed for notification');
      } catch (error) {
        console.warn('Failed to resume audio context:', error);
      }
    }
  }

  // 播放预加载的音频
  private async playPreloadedAudio(soundType: SoundType): Promise<boolean> {
    // 尝试Web Audio API
    if (this.audioBuffers.has(soundType)) {
      try {
        const buffer = this.audioBuffers.get(soundType)!;
        const source = await this.audioContextManager.createBufferSource(buffer);
        const gainNode = await this.audioContextManager.createGainNode();
        const destination = await this.audioContextManager.getDestination();

        if (source && gainNode && destination) {
          source.buffer = buffer;
          source.connect(gainNode);
          gainNode.connect(destination);
          gainNode.gain.value = this.settings.volume;

          source.start();
          return true;
        }
      } catch (error) {
        console.warn('Web Audio playback failed:', error);
      }
    }

    // 尝试HTML5 Audio
    if (this.htmlAudioElements.has(soundType)) {
      try {
        const audio = this.htmlAudioElements.get(soundType)!;
        audio.volume = this.settings.volume;
        
        // 检查音频是否可以播放
        if (audio.readyState >= 2) { // HAVE_CURRENT_DATA
          audio.currentTime = 0; // 重置播放位置
          const playPromise = audio.play();
          
          // 处理play()返回的Promise
          if (playPromise !== undefined) {
            await playPromise;
          }
          return true;
        } else {
          console.warn(`Audio not ready for playback: ${soundType}, readyState: ${audio.readyState}`);
        }
      } catch (error) {
        console.warn(`HTML5 Audio playback failed for ${soundType}:`, error);
      }
    }

    return false;
  }

  // 播放合成音频（备用方案）
  private async playSynthAudio(soundType: SoundType): Promise<void> {
    const config = this.synthSoundConfig[soundType];
    const { frequencies, durations } = config;

    let currentTime = this.audioContextManager.getCurrentTime();

    for (let i = 0; i < frequencies.length; i++) {
      const oscillator = await this.audioContextManager.createOscillator();
      const gainNode = await this.audioContextManager.createGainNode();
      const destination = await this.audioContextManager.getDestination();

      if (oscillator && gainNode && destination) {
        oscillator.connect(gainNode);
        gainNode.connect(destination);

        oscillator.frequency.value = frequencies[i];
        oscillator.type = 'sine';

        // 音量包络
        gainNode.gain.setValueAtTime(0, currentTime);
        gainNode.gain.linearRampToValueAtTime(this.settings.volume * 0.3, currentTime + 0.01);
        gainNode.gain.exponentialRampToValueAtTime(0.001, currentTime + durations[i]);

        oscillator.start(currentTime);
        oscillator.stop(currentTime + durations[i]);
      }

      currentTime += durations[i] + 0.1; // 添加间隔
    }
  }



  // 更新设置
  public updateSettings(newSettings: Partial<AudioSettings>): void {
    this.settings = { ...this.settings, ...newSettings };
    this.saveSettings();

    // 更新HTML5 Audio元素的音量
    for (const audio of this.htmlAudioElements.values()) {
      audio.volume = this.settings.volume;
    }
  }

  // 获取当前设置
  public getSettings(): AudioSettings {
    return { ...this.settings };
  }

  // 播放系统警告音
  public async playSystemAlert(): Promise<void> {
    console.log(`🚨 AudioService.playSystemAlert called`);
    await this.playNotificationSound('system-alert');
  }

  // 播放温和提醒音
  public async playGentleReminder(): Promise<void> {
    console.log(`🔔 AudioService.playGentleReminder called`);
    await this.playNotificationSound('gentle-reminder');
  }

  // 播放任务完成音
  public async playTaskComplete(): Promise<void> {
    console.log(`✅ AudioService.playTaskComplete called`);
    await this.playNotificationSound('task-complete');
  }

  // 播放番茄钟完成音
  public async playPomodoroComplete(): Promise<void> {
    console.log(`🍅 AudioService.playPomodoroComplete called`);
    await this.playNotificationSound('pomodoro-complete');
  }

  // 播放目标达成音
  public async playGoalAchieved(): Promise<void> {
    console.log(`🎯 AudioService.playGoalAchieved called`);
    await this.playNotificationSound('goal-achieved');
  }

  // 测试播放（用于设置页面预览）
  public async testSound(soundType: SoundType): Promise<void> {
    console.log(`🧪 AudioService.testSound called: ${soundType}`);
    // 使用强制播放模式，绕过设置检查
    await this.playNotificationSound(soundType, true);
  }

  // 加载设置
  private loadSettings(): AudioSettings {
    try {
      const saved = localStorage.getItem('focusOS_audio_settings');
      if (saved) {
        return { ...this.getDefaultSettings(), ...JSON.parse(saved) };
      }
    } catch (error) {
      console.warn('Failed to load audio settings:', error);
    }
    return this.getDefaultSettings();
  }

  // 保存设置
  private saveSettings(): void {
    try {
      localStorage.setItem('focusOS_audio_settings', JSON.stringify(this.settings));
    } catch (error) {
      console.warn('Failed to save audio settings:', error);
    }
  }

  // 获取默认设置
  private getDefaultSettings(): AudioSettings {
    return {
      enabled: true,
      volume: 0.7,
      soundTheme: 'default',
    };
  }

  // 检查音频支持
  public isAudioSupported(): boolean {
    return this.audioContextManager.isWebAudioSupported() || !!window.Audio;
  }

  // 获取音频状态信息
  public getAudioStatus(): {
    initialized: boolean;
    webAudioSupported: boolean;
    htmlAudioSupported: boolean;
    userInteracted: boolean;
    loadedSounds: SoundType[];
    contextState: AudioContextState | null;
  } {
    const contextStatus = this.audioContextManager.getDetailedStatus();

    return {
      initialized: this.isInitialized,
      webAudioSupported: contextStatus.supported,
      htmlAudioSupported: !!window.Audio,
      userInteracted: contextStatus.userInteracted,
      loadedSounds: Array.from(this.audioBuffers.keys()).concat(Array.from(this.htmlAudioElements.keys())),
      contextState: contextStatus.contextState,
    };
  }
}
