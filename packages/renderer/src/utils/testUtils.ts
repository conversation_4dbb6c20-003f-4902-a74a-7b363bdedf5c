/**
 * 测试工具
 * 提供功能测试、边界情况测试、用户场景测试等工具
 */

import { DatabaseAPI } from '../services/api';

// 测试结果接口
export interface TestResult {
  name: string;
  status: 'pass' | 'fail' | 'skip';
  message?: string;
  duration: number;
  details?: any;
}

// 测试套件接口
export interface TestSuite {
  name: string;
  tests: TestResult[];
  duration: number;
  passed: number;
  failed: number;
  skipped: number;
}

// 测试运行器
export class TestRunner {
  private results: TestSuite[] = [];

  async runTest(
    name: string,
    testFn: () => Promise<void> | void,
    timeout: number = 5000
  ): Promise<TestResult> {
    const startTime = Date.now();
    
    try {
      const timeoutPromise = new Promise((_, reject) => {
        setTimeout(() => reject(new Error('Test timeout')), timeout);
      });

      const testPromise = Promise.resolve(testFn());
      await Promise.race([testPromise, timeoutPromise]);

      return {
        name,
        status: 'pass',
        duration: Date.now() - startTime
      };
    } catch (error) {
      return {
        name,
        status: 'fail',
        message: (error as Error).message,
        duration: Date.now() - startTime,
        details: error
      };
    }
  }

  async runSuite(suiteName: string, tests: Array<{
    name: string;
    fn: () => Promise<void> | void;
    timeout?: number;
  }>): Promise<TestSuite> {
    const startTime = Date.now();
    const results: TestResult[] = [];

    for (const test of tests) {
      const result = await this.runTest(test.name, test.fn, test.timeout);
      results.push(result);
    }

    const suite: TestSuite = {
      name: suiteName,
      tests: results,
      duration: Date.now() - startTime,
      passed: results.filter(r => r.status === 'pass').length,
      failed: results.filter(r => r.status === 'fail').length,
      skipped: results.filter(r => r.status === 'skip').length
    };

    this.results.push(suite);
    return suite;
  }

  getResults(): TestSuite[] {
    return this.results;
  }

  generateReport(): string {
    let report = '# 测试报告\n\n';
    
    const totalTests = this.results.reduce((sum, suite) => sum + suite.tests.length, 0);
    const totalPassed = this.results.reduce((sum, suite) => sum + suite.passed, 0);
    const totalFailed = this.results.reduce((sum, suite) => sum + suite.failed, 0);
    const totalSkipped = this.results.reduce((sum, suite) => sum + suite.skipped, 0);

    report += `## 总览\n`;
    report += `- 总测试数: ${totalTests}\n`;
    report += `- 通过: ${totalPassed}\n`;
    report += `- 失败: ${totalFailed}\n`;
    report += `- 跳过: ${totalSkipped}\n`;
    report += `- 成功率: ${((totalPassed / totalTests) * 100).toFixed(2)}%\n\n`;

    for (const suite of this.results) {
      report += `## ${suite.name}\n`;
      report += `- 耗时: ${suite.duration}ms\n`;
      report += `- 通过: ${suite.passed}/${suite.tests.length}\n\n`;

      for (const test of suite.tests) {
        const status = test.status === 'pass' ? '✅' : test.status === 'fail' ? '❌' : '⏭️';
        report += `${status} ${test.name} (${test.duration}ms)\n`;
        if (test.message) {
          report += `   ${test.message}\n`;
        }
      }
      report += '\n';
    }

    return report;
  }
}

// AI分解功能测试
export class AIDecompositionTester {
  private testRunner = new TestRunner();

  async runBasicFunctionalityTests(): Promise<TestSuite> {
    return this.testRunner.runSuite('AI分解基础功能测试', [
      {
        name: '获取AI Provider列表',
        fn: async () => {
          const result = await DatabaseAPI.getAIProviders();
          if (!result.success) {
            throw new Error('获取AI Provider失败');
          }
          if (!Array.isArray(result.providers)) {
            throw new Error('返回的providers不是数组');
          }
        }
      },
      {
        name: '创建分解会话',
        fn: async () => {
          const providers = await DatabaseAPI.getAIProviders();
          if (!providers.success || providers.providers.length === 0) {
            throw new Error('没有可用的AI Provider');
          }

          const request = {
            goalId: 'test-goal-id',
            goalName: '测试目标',
            goalDescription: '这是一个测试目标的描述',
            whyPower: '测试核心驱动力',
            aiProvider: providers.providers[0].id,
            preferences: {
              maxDepth: 3,
              timeHorizon: 'medium',
              complexity: 'medium'
            }
          };

          const result = await DatabaseAPI.startAIDecomposition(request);
          if (!result.success) {
            throw new Error(`创建分解会话失败: ${result.error}`);
          }
          if (!result.sessionId) {
            throw new Error('返回的sessionId为空');
          }
        }
      },
      {
        name: '获取分解统计信息',
        fn: async () => {
          const result = await DatabaseAPI.getDecompositionStats();
          if (!result.success) {
            throw new Error('获取分解统计失败');
          }
          if (typeof result.stats !== 'object') {
            throw new Error('返回的stats不是对象');
          }
        }
      }
    ]);
  }

  async runBoundaryTests(): Promise<TestSuite> {
    return this.testRunner.runSuite('边界情况测试', [
      {
        name: '空目标名称',
        fn: async () => {
          const providers = await DatabaseAPI.getAIProviders();
          if (!providers.success || providers.providers.length === 0) {
            return; // 跳过测试
          }

          const request = {
            goalId: 'test-goal-id',
            goalName: '',
            goalDescription: '测试描述',
            whyPower: '测试驱动力',
            aiProvider: providers.providers[0].id,
            preferences: {}
          };

          try {
            await DatabaseAPI.startAIDecomposition(request);
            throw new Error('应该抛出验证错误');
          } catch (error) {
            // 期望的错误
          }
        }
      },
      {
        name: '无效的AI Provider ID',
        fn: async () => {
          const request = {
            goalId: 'test-goal-id',
            goalName: '测试目标',
            goalDescription: '测试描述',
            whyPower: '测试驱动力',
            aiProvider: 'invalid-provider-id',
            preferences: {}
          };

          try {
            await DatabaseAPI.startAIDecomposition(request);
            throw new Error('应该抛出Provider不存在错误');
          } catch (error) {
            // 期望的错误
          }
        }
      },
      {
        name: '超长目标描述',
        fn: async () => {
          const providers = await DatabaseAPI.getAIProviders();
          if (!providers.success || providers.providers.length === 0) {
            return;
          }

          const longDescription = 'a'.repeat(10000); // 10000字符
          const request = {
            goalId: 'test-goal-id',
            goalName: '测试目标',
            goalDescription: longDescription,
            whyPower: '测试驱动力',
            aiProvider: providers.providers[0].id,
            preferences: {}
          };

          // 应该能够处理长描述
          const result = await DatabaseAPI.startAIDecomposition(request);
          if (!result.success) {
            throw new Error('处理长描述失败');
          }
        }
      }
    ]);
  }

  async runUserScenarioTests(): Promise<TestSuite> {
    return this.testRunner.runSuite('用户场景测试', [
      {
        name: '完整的目标分解流程',
        fn: async () => {
          // 1. 获取AI Provider
          const providersResult = await DatabaseAPI.getAIProviders();
          if (!providersResult.success || providersResult.providers.length === 0) {
            throw new Error('没有可用的AI Provider');
          }

          // 2. 创建分解会话
          const request = {
            goalId: 'scenario-test-goal',
            goalName: '学习React开发',
            goalDescription: '我想在3个月内掌握React前端开发技能，能够独立开发中等复杂度的Web应用',
            whyPower: '为了提升职业技能，获得更好的工作机会',
            aiProvider: providersResult.providers[0].id,
            preferences: {
              maxDepth: 3,
              timeHorizon: 'medium',
              complexity: 'medium'
            }
          };

          const sessionResult = await DatabaseAPI.startAIDecomposition(request);
          if (!sessionResult.success) {
            throw new Error('创建分解会话失败');
          }

          // 3. 执行分解
          const decompositionResult = await DatabaseAPI.performAIDecomposition(sessionResult.sessionId!);
          if (!decompositionResult.success) {
            throw new Error('执行AI分解失败');
          }

          // 4. 获取分解结果
          const resultData = await DatabaseAPI.getDecompositionResult(sessionResult.sessionId!);
          if (!resultData.success) {
            throw new Error('获取分解结果失败');
          }

          // 验证结果结构
          if (!resultData.result || !resultData.result.subGoals) {
            throw new Error('分解结果结构不正确');
          }
        },
        timeout: 30000 // 30秒超时
      },
      {
        name: '多个并发分解会话',
        fn: async () => {
          const providers = await DatabaseAPI.getAIProviders();
          if (!providers.success || providers.providers.length === 0) {
            return;
          }

          const requests = Array.from({ length: 3 }, (_, i) => ({
            goalId: `concurrent-test-goal-${i}`,
            goalName: `并发测试目标${i}`,
            goalDescription: `这是第${i}个并发测试目标`,
            whyPower: '测试并发处理',
            aiProvider: providers.providers[0].id,
            preferences: {}
          }));

          // 并发创建会话
          const sessionPromises = requests.map(req => 
            DatabaseAPI.startAIDecomposition(req)
          );

          const sessionResults = await Promise.all(sessionPromises);
          
          // 验证所有会话都创建成功
          for (const result of sessionResults) {
            if (!result.success) {
              throw new Error('并发会话创建失败');
            }
          }
        }
      }
    ]);
  }

  async runPerformanceTests(): Promise<TestSuite> {
    return this.testRunner.runSuite('性能测试', [
      {
        name: 'AI Provider列表加载性能',
        fn: async () => {
          const startTime = Date.now();
          await DatabaseAPI.getAIProviders();
          const duration = Date.now() - startTime;
          
          if (duration > 1000) { // 1秒
            throw new Error(`加载时间过长: ${duration}ms`);
          }
        }
      },
      {
        name: '分解会话创建性能',
        fn: async () => {
          const providers = await DatabaseAPI.getAIProviders();
          if (!providers.success || providers.providers.length === 0) {
            return;
          }

          const request = {
            goalId: 'perf-test-goal',
            goalName: '性能测试目标',
            goalDescription: '测试分解会话创建性能',
            whyPower: '性能测试',
            aiProvider: providers.providers[0].id,
            preferences: {}
          };

          const startTime = Date.now();
          await DatabaseAPI.startAIDecomposition(request);
          const duration = Date.now() - startTime;
          
          if (duration > 2000) { // 2秒
            throw new Error(`创建时间过长: ${duration}ms`);
          }
        }
      }
    ]);
  }

  async runAllTests(): Promise<TestSuite[]> {
    console.log('开始运行AI分解功能测试...');
    
    const suites = await Promise.all([
      this.runBasicFunctionalityTests(),
      this.runBoundaryTests(),
      this.runUserScenarioTests(),
      this.runPerformanceTests()
    ]);

    console.log('测试完成，生成报告...');
    console.log(this.testRunner.generateReport());

    return suites;
  }
}

// 导出测试工具
export const testUtils = {
  TestRunner,
  AIDecompositionTester
};
