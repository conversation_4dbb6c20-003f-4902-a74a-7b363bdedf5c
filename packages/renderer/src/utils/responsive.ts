/**
 * 响应式设计工具
 * 提供断点检测、响应式样式、自适应布局等功能
 */

import { useState, useEffect, useMemo } from 'react';

// 断点定义
export const breakpoints = {
  xs: 480,
  sm: 576,
  md: 768,
  lg: 992,
  xl: 1200,
  xxl: 1600
} as const;

export type Breakpoint = keyof typeof breakpoints;

// 屏幕尺寸类型
export interface ScreenSize {
  width: number;
  height: number;
  breakpoint: Breakpoint;
  isMobile: boolean;
  isTablet: boolean;
  isDesktop: boolean;
}

/**
 * 获取当前断点
 */
export function getCurrentBreakpoint(width: number): Breakpoint {
  if (width < breakpoints.xs) return 'xs';
  if (width < breakpoints.sm) return 'xs';
  if (width < breakpoints.md) return 'sm';
  if (width < breakpoints.lg) return 'md';
  if (width < breakpoints.xl) return 'lg';
  if (width < breakpoints.xxl) return 'xl';
  return 'xxl';
}

/**
 * 检查是否为移动设备
 */
export function isMobileDevice(width: number): boolean {
  return width < breakpoints.md;
}

/**
 * 检查是否为平板设备
 */
export function isTabletDevice(width: number): boolean {
  return width >= breakpoints.md && width < breakpoints.lg;
}

/**
 * 检查是否为桌面设备
 */
export function isDesktopDevice(width: number): boolean {
  return width >= breakpoints.lg;
}

/**
 * 响应式Hook - 监听屏幕尺寸变化
 */
export function useResponsive(): ScreenSize {
  const [screenSize, setScreenSize] = useState<ScreenSize>(() => {
    const width = window.innerWidth;
    const height = window.innerHeight;
    return {
      width,
      height,
      breakpoint: getCurrentBreakpoint(width),
      isMobile: isMobileDevice(width),
      isTablet: isTabletDevice(width),
      isDesktop: isDesktopDevice(width)
    };
  });

  useEffect(() => {
    const handleResize = () => {
      const width = window.innerWidth;
      const height = window.innerHeight;
      setScreenSize({
        width,
        height,
        breakpoint: getCurrentBreakpoint(width),
        isMobile: isMobileDevice(width),
        isTablet: isTabletDevice(width),
        isDesktop: isDesktopDevice(width)
      });
    };

    window.addEventListener('resize', handleResize);
    return () => window.removeEventListener('resize', handleResize);
  }, []);

  return screenSize;
}

/**
 * 断点匹配Hook
 */
export function useBreakpoint(): Record<Breakpoint, boolean> {
  const { width } = useResponsive();

  return useMemo(() => ({
    xs: width >= breakpoints.xs,
    sm: width >= breakpoints.sm,
    md: width >= breakpoints.md,
    lg: width >= breakpoints.lg,
    xl: width >= breakpoints.xl,
    xxl: width >= breakpoints.xxl
  }), [width]);
}

/**
 * 媒体查询Hook
 */
export function useMediaQuery(query: string): boolean {
  const [matches, setMatches] = useState(() => {
    if (typeof window !== 'undefined') {
      return window.matchMedia(query).matches;
    }
    return false;
  });

  useEffect(() => {
    if (typeof window === 'undefined') return;

    const mediaQuery = window.matchMedia(query);
    const handleChange = (e: MediaQueryListEvent) => setMatches(e.matches);

    mediaQuery.addEventListener('change', handleChange);
    return () => mediaQuery.removeEventListener('change', handleChange);
  }, [query]);

  return matches;
}

/**
 * 响应式值Hook - 根据断点返回不同的值
 */
export function useResponsiveValue<T>(values: Partial<Record<Breakpoint, T>>): T | undefined {
  const { breakpoint } = useResponsive();

  return useMemo(() => {
    // 按优先级查找匹配的值
    const orderedBreakpoints: Breakpoint[] = ['xxl', 'xl', 'lg', 'md', 'sm', 'xs'];
    const currentIndex = orderedBreakpoints.indexOf(breakpoint);

    // 从当前断点开始向下查找
    for (let i = currentIndex; i < orderedBreakpoints.length; i++) {
      const bp = orderedBreakpoints[i];
      if (values[bp] !== undefined) {
        return values[bp];
      }
    }

    // 如果没找到，从当前断点向上查找
    for (let i = currentIndex - 1; i >= 0; i--) {
      const bp = orderedBreakpoints[i];
      if (values[bp] !== undefined) {
        return values[bp];
      }
    }

    return undefined;
  }, [breakpoint, values]);
}

/**
 * 响应式样式生成器
 */
export interface ResponsiveStyleConfig {
  base?: React.CSSProperties;
  xs?: React.CSSProperties;
  sm?: React.CSSProperties;
  md?: React.CSSProperties;
  lg?: React.CSSProperties;
  xl?: React.CSSProperties;
  xxl?: React.CSSProperties;
}

export function useResponsiveStyle(config: ResponsiveStyleConfig): React.CSSProperties {
  const { breakpoint } = useResponsive();

  return useMemo(() => {
    const orderedBreakpoints: Breakpoint[] = ['xs', 'sm', 'md', 'lg', 'xl', 'xxl'];
    const currentIndex = orderedBreakpoints.indexOf(breakpoint);

    let style: React.CSSProperties = { ...config.base };

    // 应用从小到大的断点样式
    for (let i = 0; i <= currentIndex; i++) {
      const bp = orderedBreakpoints[i];
      if (config[bp]) {
        style = { ...style, ...config[bp] };
      }
    }

    return style;
  }, [breakpoint, config]);
}

/**
 * 响应式网格系统
 */
export interface GridConfig {
  columns?: Partial<Record<Breakpoint, number>>;
  gap?: Partial<Record<Breakpoint, number>>;
  padding?: Partial<Record<Breakpoint, number>>;
}

export function useResponsiveGrid(config: GridConfig) {
  const columns = useResponsiveValue(config.columns || { xs: 1, sm: 2, md: 3, lg: 4 });
  const gap = useResponsiveValue(config.gap || { xs: 8, sm: 12, md: 16, lg: 20 });
  const padding = useResponsiveValue(config.padding || { xs: 8, sm: 12, md: 16, lg: 20 });

  const gridStyle: React.CSSProperties = {
    display: 'grid',
    gridTemplateColumns: `repeat(${columns}, 1fr)`,
    gap: `${gap}px`,
    padding: `${padding}px`
  };

  return { gridStyle, columns, gap, padding };
}

/**
 * 响应式容器Hook
 */
export interface ContainerConfig {
  maxWidth?: Partial<Record<Breakpoint, number>>;
  padding?: Partial<Record<Breakpoint, number>>;
  margin?: Partial<Record<Breakpoint, string>>;
}

export function useResponsiveContainer(config: ContainerConfig = {}) {
  const maxWidth = useResponsiveValue(config.maxWidth || {
    xs: '100%',
    sm: 540,
    md: 720,
    lg: 960,
    xl: 1140,
    xxl: 1320
  });

  const padding = useResponsiveValue(config.padding || {
    xs: 16,
    sm: 20,
    md: 24,
    lg: 32
  });

  const margin = useResponsiveValue(config.margin || {
    xs: '0 auto',
    sm: '0 auto',
    md: '0 auto',
    lg: '0 auto',
    xl: '0 auto',
    xxl: '0 auto'
  });

  const containerStyle: React.CSSProperties = {
    maxWidth: typeof maxWidth === 'number' ? `${maxWidth}px` : maxWidth,
    padding: typeof padding === 'number' ? `0 ${padding}px` : padding,
    margin,
    width: '100%'
  };

  return { containerStyle, maxWidth, padding, margin };
}

/**
 * 响应式字体大小
 */
export function useResponsiveFontSize(config: Partial<Record<Breakpoint, number>>) {
  return useResponsiveValue(config);
}

/**
 * 响应式间距
 */
export function useResponsiveSpacing(config: Partial<Record<Breakpoint, number>>) {
  return useResponsiveValue(config);
}

/**
 * 设备检测工具
 */
export function useDeviceDetection() {
  const { isMobile, isTablet, isDesktop } = useResponsive();
  
  const isTouchDevice = useMemo(() => {
    return 'ontouchstart' in window || navigator.maxTouchPoints > 0;
  }, []);

  const isPortrait = useMediaQuery('(orientation: portrait)');
  const isLandscape = useMediaQuery('(orientation: landscape)');

  const prefersReducedMotion = useMediaQuery('(prefers-reduced-motion: reduce)');
  const prefersDarkMode = useMediaQuery('(prefers-color-scheme: dark)');

  return {
    isMobile,
    isTablet,
    isDesktop,
    isTouchDevice,
    isPortrait,
    isLandscape,
    prefersReducedMotion,
    prefersDarkMode
  };
}

/**
 * 响应式表格配置
 */
export function useResponsiveTable() {
  const { isMobile, isTablet } = useResponsive();

  const tableConfig = useMemo(() => {
    if (isMobile) {
      return {
        size: 'small' as const,
        scroll: { x: 800 },
        pagination: { pageSize: 5, simple: true }
      };
    }

    if (isTablet) {
      return {
        size: 'middle' as const,
        scroll: { x: 1000 },
        pagination: { pageSize: 10 }
      };
    }

    return {
      size: 'middle' as const,
      pagination: { pageSize: 20 }
    };
  }, [isMobile, isTablet]);

  return tableConfig;
}
