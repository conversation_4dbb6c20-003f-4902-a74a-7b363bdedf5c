// 性能测试工具
export class PerformanceTest {
  private static instance: PerformanceTest;
  private testResults: Map<string, any[]> = new Map();

  static getInstance(): PerformanceTest {
    if (!PerformanceTest.instance) {
      PerformanceTest.instance = new PerformanceTest();
    }
    return PerformanceTest.instance;
  }

  // 测试组件渲染性能
  testComponentRender(componentName: string, renderCount: number = 100): Promise<any> {
    return new Promise((resolve) => {
      const results: number[] = [];
      let completed = 0;

      const runTest = () => {
        const startTime = performance.now();
        
        // 模拟组件渲染
        requestAnimationFrame(() => {
          const endTime = performance.now();
          results.push(endTime - startTime);
          completed++;

          if (completed < renderCount) {
            setTimeout(runTest, 0);
          } else {
            const avg = results.reduce((sum, val) => sum + val, 0) / results.length;
            const min = Math.min(...results);
            const max = Math.max(...results);
            
            const testResult = {
              componentName,
              renderCount,
              averageTime: avg,
              minTime: min,
              maxTime: max,
              results: results.slice(0, 10), // 只保留前10个结果
              timestamp: Date.now()
            };

            this.testResults.set(componentName, [
              ...(this.testResults.get(componentName) || []),
              testResult
            ]);

            resolve(testResult);
          }
        });
      };

      runTest();
    });
  }

  // 测试内存使用
  testMemoryUsage(): any {
    if ('memory' in performance) {
      const memory = (performance as any).memory;
      return {
        used: Math.round(memory.usedJSHeapSize / 1024 / 1024 * 100) / 100, // MB
        total: Math.round(memory.totalJSHeapSize / 1024 / 1024 * 100) / 100, // MB
        limit: Math.round(memory.jsHeapSizeLimit / 1024 / 1024 * 100) / 100, // MB
        timestamp: Date.now()
      };
    }
    return null;
  }

  // 测试FPS
  testFPS(duration: number = 5000): Promise<number> {
    return new Promise((resolve) => {
      let frameCount = 0;
      const startTime = performance.now();

      const countFrame = () => {
        frameCount++;
        const currentTime = performance.now();
        
        if (currentTime - startTime < duration) {
          requestAnimationFrame(countFrame);
        } else {
          const fps = Math.round((frameCount * 1000) / (currentTime - startTime));
          resolve(fps);
        }
      };

      requestAnimationFrame(countFrame);
    });
  }

  // 测试网络延迟
  async testNetworkLatency(url: string = '/api/health'): Promise<number> {
    const startTime = performance.now();
    
    try {
      await fetch(url, { method: 'HEAD' });
      const endTime = performance.now();
      return endTime - startTime;
    } catch (error) {
      console.warn('Network latency test failed:', error);
      return -1;
    }
  }

  // 运行完整的性能测试套件
  async runFullTest(): Promise<any> {
    console.log('🚀 开始性能测试...');
    
    const results = {
      timestamp: Date.now(),
      memory: this.testMemoryUsage(),
      fps: await this.testFPS(3000),
      components: {},
      network: await this.testNetworkLatency(),
    };

    // 测试主要组件
    const componentsToTest = ['Goals', 'Table', 'Modal', 'Form'];
    
    for (const component of componentsToTest) {
      try {
        const componentResult = await this.testComponentRender(component, 50);
        (results.components as any)[component] = componentResult;
      } catch (error) {
        console.warn(`Component test failed for ${component}:`, error);
      }
    }

    console.log('📊 性能测试完成:', results);
    return results;
  }

  // 获取测试历史
  getTestHistory(componentName?: string): any {
    if (componentName) {
      return this.testResults.get(componentName) || [];
    }
    
    const history: any = {};
    for (const [name, results] of this.testResults) {
      history[name] = results;
    }
    return history;
  }

  // 清除测试历史
  clearHistory(): void {
    this.testResults.clear();
  }

  // 生成性能报告
  generateReport(): string {
    const history = this.getTestHistory();
    let report = '📈 性能测试报告\n';
    report += '=' * 50 + '\n\n';

    for (const [componentName, tests] of Object.entries(history)) {
      if (Array.isArray(tests) && tests.length > 0) {
        const latestTest = tests[tests.length - 1];
        report += `🔧 ${componentName} 组件:\n`;
        report += `   平均渲染时间: ${latestTest.averageTime.toFixed(2)}ms\n`;
        report += `   最快渲染时间: ${latestTest.minTime.toFixed(2)}ms\n`;
        report += `   最慢渲染时间: ${latestTest.maxTime.toFixed(2)}ms\n`;
        report += `   测试次数: ${latestTest.renderCount}\n\n`;
      }
    }

    const memory = this.testMemoryUsage();
    if (memory) {
      report += `💾 内存使用:\n`;
      report += `   已使用: ${memory.used}MB\n`;
      report += `   总计: ${memory.total}MB\n`;
      report += `   限制: ${memory.limit}MB\n\n`;
    }

    return report;
  }
}

// 导出单例实例
export const performanceTest = PerformanceTest.getInstance();

// 全局性能测试函数
(window as any).runPerformanceTest = () => {
  return performanceTest.runFullTest();
};

(window as any).getPerformanceReport = () => {
  console.log(performanceTest.generateReport());
  return performanceTest.generateReport();
};

// 自动在开发环境中启用性能监控
if (process.env.NODE_ENV === 'development') {
  console.log('🔍 性能监控已启用');
  console.log('使用 runPerformanceTest() 运行性能测试');
  console.log('使用 getPerformanceReport() 查看性能报告');
}
