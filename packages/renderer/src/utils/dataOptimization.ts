/**
 * 数据处理优化工具
 * 提供数据分页、虚拟化、缓存等优化功能
 */

// 分页配置
export interface PaginationConfig {
  page: number;
  pageSize: number;
  total: number;
}

// 虚拟化配置
export interface VirtualizationConfig {
  itemHeight: number;
  containerHeight: number;
  overscan: number;
}

// 缓存配置
export interface CacheConfig {
  maxSize: number;
  ttl: number; // 毫秒
}

/**
 * 数据分页器
 */
export class DataPaginator<T> {
  private data: T[];
  private pageSize: number;

  constructor(data: T[], pageSize: number = 20) {
    this.data = data;
    this.pageSize = pageSize;
  }

  /**
   * 获取指定页的数据
   */
  getPage(page: number): {
    data: T[];
    pagination: PaginationConfig;
  } {
    const startIndex = (page - 1) * this.pageSize;
    const endIndex = Math.min(startIndex + this.pageSize, this.data.length);
    
    return {
      data: this.data.slice(startIndex, endIndex),
      pagination: {
        page,
        pageSize: this.pageSize,
        total: this.data.length
      }
    };
  }

  /**
   * 搜索并分页
   */
  searchAndPaginate(
    searchTerm: string,
    searchFields: (keyof T)[],
    page: number = 1
  ): {
    data: T[];
    pagination: PaginationConfig;
  } {
    const filteredData = this.data.filter(item => {
      return searchFields.some(field => {
        const value = item[field];
        if (typeof value === 'string') {
          return value.toLowerCase().includes(searchTerm.toLowerCase());
        }
        return false;
      });
    });

    const startIndex = (page - 1) * this.pageSize;
    const endIndex = Math.min(startIndex + this.pageSize, filteredData.length);

    return {
      data: filteredData.slice(startIndex, endIndex),
      pagination: {
        page,
        pageSize: this.pageSize,
        total: filteredData.length
      }
    };
  }
}

/**
 * 虚拟化计算器
 */
export class VirtualizationCalculator {
  private config: VirtualizationConfig;

  constructor(config: VirtualizationConfig) {
    this.config = config;
  }

  /**
   * 计算可见范围
   */
  calculateVisibleRange(scrollTop: number, totalItems: number): {
    startIndex: number;
    endIndex: number;
    visibleItems: number;
  } {
    const { itemHeight, containerHeight, overscan } = this.config;
    
    const visibleItems = Math.ceil(containerHeight / itemHeight);
    const startIndex = Math.max(0, Math.floor(scrollTop / itemHeight) - overscan);
    const endIndex = Math.min(totalItems - 1, startIndex + visibleItems + overscan * 2);

    return {
      startIndex,
      endIndex,
      visibleItems
    };
  }

  /**
   * 计算总高度
   */
  calculateTotalHeight(totalItems: number): number {
    return totalItems * this.config.itemHeight;
  }

  /**
   * 计算偏移量
   */
  calculateOffset(startIndex: number): number {
    return startIndex * this.config.itemHeight;
  }
}

/**
 * LRU缓存
 */
export class LRUCache<K, V> {
  private maxSize: number;
  private ttl: number;
  private cache: Map<K, { value: V; timestamp: number; accessTime: number }>;

  constructor(config: CacheConfig) {
    this.maxSize = config.maxSize;
    this.ttl = config.ttl;
    this.cache = new Map();
  }

  /**
   * 获取缓存值
   */
  get(key: K): V | null {
    const entry = this.cache.get(key);
    
    if (!entry) {
      return null;
    }

    // 检查是否过期
    if (Date.now() - entry.timestamp > this.ttl) {
      this.cache.delete(key);
      return null;
    }

    // 更新访问时间
    entry.accessTime = Date.now();
    
    return entry.value;
  }

  /**
   * 设置缓存值
   */
  set(key: K, value: V): void {
    const now = Date.now();

    // 如果缓存已满，删除最久未使用的项
    if (this.cache.size >= this.maxSize && !this.cache.has(key)) {
      this.evictLRU();
    }

    this.cache.set(key, {
      value,
      timestamp: now,
      accessTime: now
    });
  }

  /**
   * 删除最久未使用的项
   */
  private evictLRU(): void {
    let lruKey: K | null = null;
    let lruTime = Infinity;

    for (const [key, entry] of this.cache.entries()) {
      if (entry.accessTime < lruTime) {
        lruTime = entry.accessTime;
        lruKey = key;
      }
    }

    if (lruKey !== null) {
      this.cache.delete(lruKey);
    }
  }

  /**
   * 清理过期项
   */
  cleanup(): void {
    const now = Date.now();
    for (const [key, entry] of this.cache.entries()) {
      if (now - entry.timestamp > this.ttl) {
        this.cache.delete(key);
      }
    }
  }

  /**
   * 清空缓存
   */
  clear(): void {
    this.cache.clear();
  }

  /**
   * 获取缓存统计
   */
  getStats(): {
    size: number;
    maxSize: number;
    hitRate?: number;
  } {
    return {
      size: this.cache.size,
      maxSize: this.maxSize
    };
  }
}

/**
 * 防抖函数
 */
export function debounce<T extends (...args: any[]) => any>(
  func: T,
  delay: number
): (...args: Parameters<T>) => void {
  let timeoutId: NodeJS.Timeout;
  
  return (...args: Parameters<T>) => {
    clearTimeout(timeoutId);
    timeoutId = setTimeout(() => func(...args), delay);
  };
}

/**
 * 节流函数
 */
export function throttle<T extends (...args: any[]) => any>(
  func: T,
  delay: number
): (...args: Parameters<T>) => void {
  let lastCall = 0;
  
  return (...args: Parameters<T>) => {
    const now = Date.now();
    if (now - lastCall >= delay) {
      lastCall = now;
      func(...args);
    }
  };
}

/**
 * 批处理器
 */
export class BatchProcessor<T> {
  private batch: T[] = [];
  private batchSize: number;
  private delay: number;
  private processor: (batch: T[]) => void;
  private timeoutId: NodeJS.Timeout | null = null;

  constructor(
    processor: (batch: T[]) => void,
    batchSize: number = 10,
    delay: number = 100
  ) {
    this.processor = processor;
    this.batchSize = batchSize;
    this.delay = delay;
  }

  /**
   * 添加项到批处理
   */
  add(item: T): void {
    this.batch.push(item);

    // 如果达到批处理大小，立即处理
    if (this.batch.length >= this.batchSize) {
      this.flush();
    } else {
      // 否则设置延迟处理
      this.scheduleFlush();
    }
  }

  /**
   * 立即处理当前批次
   */
  flush(): void {
    if (this.batch.length === 0) return;

    const currentBatch = [...this.batch];
    this.batch = [];
    
    if (this.timeoutId) {
      clearTimeout(this.timeoutId);
      this.timeoutId = null;
    }

    this.processor(currentBatch);
  }

  /**
   * 安排延迟处理
   */
  private scheduleFlush(): void {
    if (this.timeoutId) return;

    this.timeoutId = setTimeout(() => {
      this.flush();
    }, this.delay);
  }
}

/**
 * 数据转换优化器
 */
export class DataTransformer<T, R> {
  private cache: LRUCache<string, R>;
  private transformer: (data: T) => R;

  constructor(
    transformer: (data: T) => R,
    cacheConfig: CacheConfig = { maxSize: 1000, ttl: 5 * 60 * 1000 }
  ) {
    this.transformer = transformer;
    this.cache = new LRUCache(cacheConfig);
  }

  /**
   * 转换数据（带缓存）
   */
  transform(data: T, cacheKey?: string): R {
    const key = cacheKey || JSON.stringify(data);
    
    // 尝试从缓存获取
    const cached = this.cache.get(key);
    if (cached !== null) {
      return cached;
    }

    // 执行转换
    const result = this.transformer(data);
    
    // 缓存结果
    this.cache.set(key, result);
    
    return result;
  }

  /**
   * 批量转换
   */
  transformBatch(dataList: T[], generateKey?: (data: T, index: number) => string): R[] {
    return dataList.map((data, index) => {
      const key = generateKey ? generateKey(data, index) : undefined;
      return this.transform(data, key);
    });
  }

  /**
   * 清理缓存
   */
  clearCache(): void {
    this.cache.clear();
  }
}
