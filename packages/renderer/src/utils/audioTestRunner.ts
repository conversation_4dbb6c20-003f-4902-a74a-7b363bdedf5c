import { AudioService, SoundType } from '../services/AudioService';
import { NotificationService } from '../services/NotificationService';

export interface TestResult {
  platform: string;
  userAgent: string;
  audioSupport: {
    webAudio: boolean;
    htmlAudio: boolean;
  };
  tests: Array<{
    name: string;
    status: 'pass' | 'fail' | 'skip';
    details: string;
    duration?: number;
  }>;
  summary: {
    total: number;
    passed: number;
    failed: number;
    skipped: number;
  };
}

export class AudioTestRunner {
  private audioService: AudioService;
  private notificationService: NotificationService;

  constructor() {
    this.audioService = AudioService.getInstance();
    this.notificationService = NotificationService.getInstance();
  }

  async runComprehensiveTest(): Promise<TestResult> {
    const startTime = Date.now();
    
    const results: TestResult = {
      platform: navigator.platform,
      userAgent: navigator.userAgent,
      audioSupport: {
        webAudio: !!window.AudioContext,
        htmlAudio: !!window.Audio,
      },
      tests: [],
      summary: {
        total: 0,
        passed: 0,
        failed: 0,
        skipped: 0
      }
    };

    console.log('🧪 开始音频系统综合测试...');

    // 测试1：音频服务初始化
    await this.runTest(results, 'Audio Service Initialization', async () => {
      await this.audioService.initialize();
      const status = this.audioService.getAudioStatus();
      if (!status.initialized) {
        throw new Error('Audio service failed to initialize');
      }
      return 'Audio service initialized successfully';
    });

    // 测试2：音频设置检查
    await this.runTest(results, 'Audio Settings Check', async () => {
      const settings = this.audioService.getSettings();
      return `Audio enabled: ${settings.enabled}, Volume: ${settings.volume}`;
    });

    // 测试3：音频上下文状态
    await this.runTest(results, 'Audio Context State', async () => {
      const status = this.audioService.getAudioStatus();
      if (status.contextState === 'suspended') {
        throw new Error('Audio context is suspended - user interaction required');
      }
      return `Audio context state: ${status.contextState}`;
    });

    // 测试4：直接音频播放
    const soundTypes: SoundType[] = ['task-complete', 'pomodoro-complete', 'break-complete'];
    
    for (const soundType of soundTypes) {
      await this.runTest(results, `Direct Audio Play: ${soundType}`, async () => {
        const testStart = Date.now();
        await this.audioService.playSound(soundType);
        const duration = Date.now() - testStart;
        return `Audio played successfully in ${duration}ms`;
      });
    }

    // 测试5：强制音频播放
    for (const soundType of soundTypes) {
      await this.runTest(results, `Force Audio Play: ${soundType}`, async () => {
        const testStart = Date.now();
        await this.audioService.playNotificationSound(soundType, true);
        const duration = Date.now() - testStart;
        return `Force audio played successfully in ${duration}ms`;
      });
    }

    // 测试6：通知音频播放
    await this.runTest(results, 'Notification Audio: Task Complete', async () => {
      const testStart = Date.now();
      await this.notificationService.sendNotification('Test Task Complete', {
        body: 'Testing task completion notification',
        sound: true,
        soundType: 'task-complete',
        duration: 2000
      });
      const duration = Date.now() - testStart;
      return `Notification with audio sent successfully in ${duration}ms`;
    });

    await this.runTest(results, 'Notification Audio: Pomodoro Complete', async () => {
      const testStart = Date.now();
      await this.notificationService.notifyPomodoroComplete('work', 1);
      const duration = Date.now() - testStart;
      return `Pomodoro notification sent successfully in ${duration}ms`;
    });

    // 测试7：音频设置切换测试
    await this.runTest(results, 'Audio Settings Toggle Test', async () => {
      const originalSettings = this.audioService.getSettings();
      
      // 禁用音频
      this.audioService.updateSettings({ enabled: false });
      
      try {
        await this.audioService.playSound('gentle-reminder');
        // 如果到这里说明音频被播放了，这是不应该的
        throw new Error('Audio played when disabled');
      } catch (error) {
        // 这是期望的行为
      }
      
      // 恢复设置
      this.audioService.updateSettings(originalSettings);
      
      return 'Audio settings toggle works correctly';
    });

    // 测试8：音频文件加载状态
    await this.runTest(results, 'Audio Files Loading Status', async () => {
      const status = this.audioService.getAudioStatus();
      const loadedCount = status.loadedSounds.length;
      const totalSounds = Object.keys(soundTypes).length;
      
      if (loadedCount === 0) {
        throw new Error('No audio files loaded');
      }
      
      return `${loadedCount} audio files loaded successfully`;
    });

    // 计算总结
    results.summary.total = results.tests.length;
    results.summary.passed = results.tests.filter(t => t.status === 'pass').length;
    results.summary.failed = results.tests.filter(t => t.status === 'fail').length;
    results.summary.skipped = results.tests.filter(t => t.status === 'skip').length;

    const totalTime = Date.now() - startTime;
    console.log(`🧪 测试完成，耗时 ${totalTime}ms`);
    console.log(`📊 结果: ${results.summary.passed}/${results.summary.total} 通过`);

    return results;
  }

  private async runTest(
    results: TestResult, 
    testName: string, 
    testFn: () => Promise<string>
  ): Promise<void> {
    const startTime = Date.now();
    
    try {
      console.log(`🧪 运行测试: ${testName}`);
      const details = await testFn();
      const duration = Date.now() - startTime;
      
      results.tests.push({
        name: testName,
        status: 'pass',
        details,
        duration
      });
      
      console.log(`✅ ${testName}: ${details} (${duration}ms)`);
    } catch (error) {
      const duration = Date.now() - startTime;
      const errorMessage = error instanceof Error ? error.message : String(error);
      
      results.tests.push({
        name: testName,
        status: 'fail',
        details: `Failed: ${errorMessage}`,
        duration
      });
      
      console.error(`❌ ${testName}: ${errorMessage} (${duration}ms)`);
    }
  }

  // 快速诊断方法
  async quickDiagnosis(): Promise<{
    audioEnabled: boolean;
    contextState: string | null;
    userInteracted: boolean;
    loadedSounds: number;
    recommendations: string[];
  }> {
    const status = this.audioService.getAudioStatus();
    const settings = this.audioService.getSettings();
    
    const recommendations: string[] = [];
    
    if (!settings.enabled) {
      recommendations.push('音频已被禁用，请在设置中启用音频');
    }
    
    if (status.contextState === 'suspended') {
      recommendations.push('音频上下文被暂停，请点击页面任意位置激活');
    }
    
    if (!status.userInteracted) {
      recommendations.push('需要用户交互才能播放音频，请点击页面任意位置');
    }
    
    if (status.loadedSounds.length === 0) {
      recommendations.push('音频文件未加载，请检查音频文件路径');
    }
    
    if (recommendations.length === 0) {
      recommendations.push('音频系统状态正常');
    }
    
    return {
      audioEnabled: settings.enabled,
      contextState: status.contextState,
      userInteracted: status.userInteracted,
      loadedSounds: status.loadedSounds.length,
      recommendations
    };
  }

  // 模拟实际通知场景
  async simulateRealNotificationScenarios(): Promise<void> {
    console.log('🎬 模拟实际通知场景...');
    
    // 场景1：番茄钟完成
    console.log('📅 场景1: 番茄钟完成');
    await new Promise(resolve => setTimeout(resolve, 1000));
    await this.notificationService.notifyPomodoroComplete('work', 1);
    
    // 场景2：任务完成
    console.log('✅ 场景2: 任务完成');
    await new Promise(resolve => setTimeout(resolve, 2000));
    await this.notificationService.notifyTaskComplete('重要任务');
    
    // 场景3：目标达成
    console.log('🎯 场景3: 目标达成');
    await new Promise(resolve => setTimeout(resolve, 2000));
    await this.notificationService.notifyGoalAchieved('月度目标');
    
    // 场景4：系统提醒
    console.log('🔔 场景4: 系统提醒');
    await new Promise(resolve => setTimeout(resolve, 2000));
    await this.notificationService.notifySystemAlert('重要提醒', '这是一个系统提醒测试');
    
    console.log('🎬 实际场景模拟完成');
  }
}

// 导出便捷方法
export const audioTestRunner = new AudioTestRunner();

// 全局调试方法
(window as any).audioDebug = {
  runTest: () => audioTestRunner.runComprehensiveTest(),
  quickCheck: () => audioTestRunner.quickDiagnosis(),
  simulate: () => audioTestRunner.simulateRealNotificationScenarios(),
  
  // 直接访问服务
  audioService: AudioService.getInstance(),
  notificationService: NotificationService.getInstance(),
  
  // 快速测试方法
  testDirect: (soundType: SoundType) => AudioService.getInstance().playSound(soundType),
  testForce: (soundType: SoundType) => AudioService.getInstance().playNotificationSound(soundType, true),
  testNotification: (soundType: SoundType) => NotificationService.getInstance().sendNotification('Test', {
    body: 'Test notification',
    sound: true,
    soundType,
    duration: 3000
  })
};

console.log('🔧 音频调试工具已加载，使用 window.audioDebug 访问调试方法');
