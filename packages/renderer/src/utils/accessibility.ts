/**
 * 无障碍访问工具
 * 提供键盘导航、屏幕阅读器支持、焦点管理等功能
 */

import { useEffect, useRef, useCallback, useState } from 'react';

// 键盘事件常量
export const KEYBOARD_KEYS = {
  ENTER: 'Enter',
  SPACE: ' ',
  ESCAPE: 'Escape',
  TAB: 'Tab',
  ARROW_UP: 'ArrowUp',
  ARROW_DOWN: 'ArrowDown',
  ARROW_LEFT: 'ArrowLeft',
  ARROW_RIGHT: 'ArrowRight',
  HOME: 'Home',
  END: 'End',
  PAGE_UP: 'PageUp',
  PAGE_DOWN: 'PageDown'
} as const;

/**
 * 焦点管理Hook
 */
export function useFocusManagement() {
  const focusableElementsSelector = [
    'a[href]',
    'button:not([disabled])',
    'input:not([disabled])',
    'select:not([disabled])',
    'textarea:not([disabled])',
    '[tabindex]:not([tabindex="-1"])',
    '[contenteditable="true"]'
  ].join(', ');

  const getFocusableElements = useCallback((container: HTMLElement) => {
    return Array.from(container.querySelectorAll(focusableElementsSelector)) as HTMLElement[];
  }, [focusableElementsSelector]);

  const trapFocus = useCallback((container: HTMLElement) => {
    const focusableElements = getFocusableElements(container);
    const firstElement = focusableElements[0];
    const lastElement = focusableElements[focusableElements.length - 1];

    const handleKeyDown = (e: KeyboardEvent) => {
      if (e.key === KEYBOARD_KEYS.TAB) {
        if (e.shiftKey) {
          // Shift + Tab
          if (document.activeElement === firstElement) {
            e.preventDefault();
            lastElement?.focus();
          }
        } else {
          // Tab
          if (document.activeElement === lastElement) {
            e.preventDefault();
            firstElement?.focus();
          }
        }
      }
    };

    container.addEventListener('keydown', handleKeyDown);
    
    // 初始焦点
    firstElement?.focus();

    return () => {
      container.removeEventListener('keydown', handleKeyDown);
    };
  }, [getFocusableElements]);

  const restoreFocus = useCallback((previousActiveElement: Element | null) => {
    if (previousActiveElement && 'focus' in previousActiveElement) {
      (previousActiveElement as HTMLElement).focus();
    }
  }, []);

  return {
    getFocusableElements,
    trapFocus,
    restoreFocus
  };
}

/**
 * 键盘导航Hook
 */
export function useKeyboardNavigation(
  items: any[],
  onSelect?: (item: any, index: number) => void,
  options: {
    loop?: boolean;
    orientation?: 'horizontal' | 'vertical';
    disabled?: boolean;
  } = {}
) {
  const { loop = true, orientation = 'vertical', disabled = false } = options;
  const [activeIndex, setActiveIndex] = useState(-1);

  const handleKeyDown = useCallback((e: KeyboardEvent) => {
    if (disabled || items.length === 0) return;

    const isVertical = orientation === 'vertical';
    const nextKey = isVertical ? KEYBOARD_KEYS.ARROW_DOWN : KEYBOARD_KEYS.ARROW_RIGHT;
    const prevKey = isVertical ? KEYBOARD_KEYS.ARROW_UP : KEYBOARD_KEYS.ARROW_LEFT;

    switch (e.key) {
      case nextKey:
        e.preventDefault();
        setActiveIndex(prev => {
          const next = prev + 1;
          return next >= items.length ? (loop ? 0 : prev) : next;
        });
        break;

      case prevKey:
        e.preventDefault();
        setActiveIndex(prev => {
          const next = prev - 1;
          return next < 0 ? (loop ? items.length - 1 : prev) : next;
        });
        break;

      case KEYBOARD_KEYS.HOME:
        e.preventDefault();
        setActiveIndex(0);
        break;

      case KEYBOARD_KEYS.END:
        e.preventDefault();
        setActiveIndex(items.length - 1);
        break;

      case KEYBOARD_KEYS.ENTER:
      case KEYBOARD_KEYS.SPACE:
        e.preventDefault();
        if (activeIndex >= 0 && activeIndex < items.length && onSelect) {
          onSelect(items[activeIndex], activeIndex);
        }
        break;

      case KEYBOARD_KEYS.ESCAPE:
        setActiveIndex(-1);
        break;
    }
  }, [items, activeIndex, onSelect, loop, orientation, disabled]);

  useEffect(() => {
    document.addEventListener('keydown', handleKeyDown);
    return () => document.removeEventListener('keydown', handleKeyDown);
  }, [handleKeyDown]);

  return {
    activeIndex,
    setActiveIndex,
    handleKeyDown
  };
}

/**
 * 屏幕阅读器公告Hook
 */
export function useScreenReader() {
  const announceRef = useRef<HTMLDivElement>(null);

  const announce = useCallback((message: string, priority: 'polite' | 'assertive' = 'polite') => {
    if (!announceRef.current) {
      // 创建屏幕阅读器公告元素
      const announcer = document.createElement('div');
      announcer.setAttribute('aria-live', priority);
      announcer.setAttribute('aria-atomic', 'true');
      announcer.style.position = 'absolute';
      announcer.style.left = '-10000px';
      announcer.style.width = '1px';
      announcer.style.height = '1px';
      announcer.style.overflow = 'hidden';
      document.body.appendChild(announcer);
      announceRef.current = announcer;
    }

    // 清空后设置新消息，确保屏幕阅读器能够读取
    announceRef.current.textContent = '';
    setTimeout(() => {
      if (announceRef.current) {
        announceRef.current.textContent = message;
      }
    }, 100);
  }, []);

  useEffect(() => {
    return () => {
      if (announceRef.current && announceRef.current.parentNode) {
        announceRef.current.parentNode.removeChild(announceRef.current);
      }
    };
  }, []);

  return { announce };
}

/**
 * 跳过链接Hook
 */
export function useSkipLink() {
  const skipLinkRef = useRef<HTMLAnchorElement>(null);

  const createSkipLink = useCallback((targetId: string, text: string = '跳到主要内容') => {
    const skipLink = document.createElement('a');
    skipLink.href = `#${targetId}`;
    skipLink.textContent = text;
    skipLink.className = 'skip-link';
    
    // 样式
    Object.assign(skipLink.style, {
      position: 'absolute',
      top: '-40px',
      left: '6px',
      background: '#000',
      color: '#fff',
      padding: '8px',
      textDecoration: 'none',
      zIndex: '9999',
      borderRadius: '4px'
    });

    // 焦点时显示
    skipLink.addEventListener('focus', () => {
      skipLink.style.top = '6px';
    });

    skipLink.addEventListener('blur', () => {
      skipLink.style.top = '-40px';
    });

    document.body.insertBefore(skipLink, document.body.firstChild);
    skipLinkRef.current = skipLink;

    return () => {
      if (skipLink.parentNode) {
        skipLink.parentNode.removeChild(skipLink);
      }
    };
  }, []);

  return { createSkipLink };
}

/**
 * ARIA属性管理Hook
 */
export function useAriaAttributes() {
  const setAriaLabel = useCallback((element: HTMLElement, label: string) => {
    element.setAttribute('aria-label', label);
  }, []);

  const setAriaDescribedBy = useCallback((element: HTMLElement, describedById: string) => {
    element.setAttribute('aria-describedby', describedById);
  }, []);

  const setAriaExpanded = useCallback((element: HTMLElement, expanded: boolean) => {
    element.setAttribute('aria-expanded', expanded.toString());
  }, []);

  const setAriaSelected = useCallback((element: HTMLElement, selected: boolean) => {
    element.setAttribute('aria-selected', selected.toString());
  }, []);

  const setAriaDisabled = useCallback((element: HTMLElement, disabled: boolean) => {
    element.setAttribute('aria-disabled', disabled.toString());
  }, []);

  const setAriaHidden = useCallback((element: HTMLElement, hidden: boolean) => {
    element.setAttribute('aria-hidden', hidden.toString());
  }, []);

  const setRole = useCallback((element: HTMLElement, role: string) => {
    element.setAttribute('role', role);
  }, []);

  return {
    setAriaLabel,
    setAriaDescribedBy,
    setAriaExpanded,
    setAriaSelected,
    setAriaDisabled,
    setAriaHidden,
    setRole
  };
}

/**
 * 颜色对比度检查
 */
export function checkColorContrast(foreground: string, background: string): {
  ratio: number;
  wcagAA: boolean;
  wcagAAA: boolean;
} {
  // 简化的对比度计算（实际应用中应使用更精确的算法）
  const getLuminance = (color: string): number => {
    // 这里应该实现完整的亮度计算
    // 为了简化，返回一个模拟值
    return 0.5;
  };

  const foregroundLuminance = getLuminance(foreground);
  const backgroundLuminance = getLuminance(background);
  
  const ratio = (Math.max(foregroundLuminance, backgroundLuminance) + 0.05) / 
                (Math.min(foregroundLuminance, backgroundLuminance) + 0.05);

  return {
    ratio,
    wcagAA: ratio >= 4.5,
    wcagAAA: ratio >= 7
  };
}

/**
 * 焦点可见性管理
 */
export function useFocusVisible() {
  const [focusVisible, setFocusVisible] = useState(false);
  const [usingKeyboard, setUsingKeyboard] = useState(false);

  useEffect(() => {
    const handleKeyDown = () => setUsingKeyboard(true);
    const handleMouseDown = () => setUsingKeyboard(false);
    const handleFocus = () => setFocusVisible(usingKeyboard);
    const handleBlur = () => setFocusVisible(false);

    document.addEventListener('keydown', handleKeyDown);
    document.addEventListener('mousedown', handleMouseDown);
    document.addEventListener('focusin', handleFocus);
    document.addEventListener('focusout', handleBlur);

    return () => {
      document.removeEventListener('keydown', handleKeyDown);
      document.removeEventListener('mousedown', handleMouseDown);
      document.removeEventListener('focusin', handleFocus);
      document.removeEventListener('focusout', handleBlur);
    };
  }, [usingKeyboard]);

  return { focusVisible, usingKeyboard };
}

/**
 * 减少动画偏好检测
 */
export function usePrefersReducedMotion(): boolean {
  const [prefersReducedMotion, setPrefersReducedMotion] = useState(false);

  useEffect(() => {
    const mediaQuery = window.matchMedia('(prefers-reduced-motion: reduce)');
    setPrefersReducedMotion(mediaQuery.matches);

    const handleChange = (e: MediaQueryListEvent) => {
      setPrefersReducedMotion(e.matches);
    };

    mediaQuery.addEventListener('change', handleChange);
    return () => mediaQuery.removeEventListener('change', handleChange);
  }, []);

  return prefersReducedMotion;
}

/**
 * 语义化HTML工具
 */
export const semanticElements = {
  // 页面结构
  header: 'header',
  nav: 'nav',
  main: 'main',
  section: 'section',
  article: 'article',
  aside: 'aside',
  footer: 'footer',
  
  // 内容分组
  h1: 'h1',
  h2: 'h2',
  h3: 'h3',
  h4: 'h4',
  h5: 'h5',
  h6: 'h6',
  
  // 列表
  ul: 'ul',
  ol: 'ol',
  li: 'li',
  dl: 'dl',
  dt: 'dt',
  dd: 'dd'
} as const;

/**
 * 无障碍表单工具
 */
export function useAccessibleForm() {
  const generateId = useCallback(() => {
    return `form-field-${Math.random().toString(36).substr(2, 9)}`;
  }, []);

  const createFieldProps = useCallback((
    label: string,
    required: boolean = false,
    error?: string,
    description?: string
  ) => {
    const id = generateId();
    const labelId = `${id}-label`;
    const errorId = error ? `${id}-error` : undefined;
    const descriptionId = description ? `${id}-description` : undefined;

    const describedBy = [errorId, descriptionId].filter(Boolean).join(' ');

    return {
      field: {
        id,
        'aria-labelledby': labelId,
        'aria-describedby': describedBy || undefined,
        'aria-required': required,
        'aria-invalid': !!error
      },
      label: {
        id: labelId,
        htmlFor: id
      },
      error: errorId ? {
        id: errorId,
        role: 'alert',
        'aria-live': 'polite'
      } : undefined,
      description: descriptionId ? {
        id: descriptionId
      } : undefined
    };
  }, [generateId]);

  return { createFieldProps };
}
