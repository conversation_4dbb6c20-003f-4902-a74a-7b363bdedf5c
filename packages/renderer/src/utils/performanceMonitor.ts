// 性能监控工具
export class PerformanceMonitor {
  private static instance: PerformanceMonitor;
  private metrics: Map<string, number[]> = new Map();
  private observers: PerformanceObserver[] = [];

  static getInstance(): PerformanceMonitor {
    if (!PerformanceMonitor.instance) {
      PerformanceMonitor.instance = new PerformanceMonitor();
    }
    return PerformanceMonitor.instance;
  }

  // 监控组件渲染时间
  measureRender(componentName: string, renderFn: () => void): void {
    const startTime = performance.now();
    renderFn();
    const endTime = performance.now();
    const duration = endTime - startTime;
    
    this.addMetric(`render_${componentName}`, duration);
    
    if (duration > 16) { // 超过一帧的时间
      console.warn(`⚠️ 组件 ${componentName} 渲染时间过长: ${duration.toFixed(2)}ms`);
    }
  }

  // 监控函数执行时间
  measureFunction<T>(name: string, fn: () => T): T {
    const startTime = performance.now();
    const result = fn();
    const endTime = performance.now();
    const duration = endTime - startTime;
    
    this.addMetric(`function_${name}`, duration);
    
    if (duration > 5) {
      console.warn(`⚠️ 函数 ${name} 执行时间过长: ${duration.toFixed(2)}ms`);
    }
    
    return result;
  }

  // 监控异步函数执行时间
  async measureAsync<T>(name: string, fn: () => Promise<T>): Promise<T> {
    const startTime = performance.now();
    const result = await fn();
    const endTime = performance.now();
    const duration = endTime - startTime;
    
    this.addMetric(`async_${name}`, duration);
    
    if (duration > 100) {
      console.warn(`⚠️ 异步函数 ${name} 执行时间过长: ${duration.toFixed(2)}ms`);
    }
    
    return result;
  }

  // 添加性能指标
  private addMetric(name: string, value: number): void {
    if (!this.metrics.has(name)) {
      this.metrics.set(name, []);
    }
    const values = this.metrics.get(name)!;
    values.push(value);
    
    // 只保留最近100次的记录
    if (values.length > 100) {
      values.shift();
    }
  }

  // 获取性能统计
  getStats(name: string): { avg: number; min: number; max: number; count: number } | null {
    const values = this.metrics.get(name);
    if (!values || values.length === 0) {
      return null;
    }
    
    const avg = values.reduce((sum, val) => sum + val, 0) / values.length;
    const min = Math.min(...values);
    const max = Math.max(...values);
    
    return { avg, min, max, count: values.length };
  }

  // 获取所有性能统计
  getAllStats(): Record<string, { avg: number; min: number; max: number; count: number }> {
    const stats: Record<string, any> = {};
    
    for (const [name] of this.metrics) {
      const stat = this.getStats(name);
      if (stat) {
        stats[name] = stat;
      }
    }
    
    return stats;
  }

  // 开始监控FPS
  startFPSMonitoring(): void {
    let lastTime = performance.now();
    let frameCount = 0;
    
    const measureFPS = () => {
      frameCount++;
      const currentTime = performance.now();
      
      if (currentTime - lastTime >= 1000) {
        const fps = Math.round((frameCount * 1000) / (currentTime - lastTime));
        this.addMetric('fps', fps);
        
        if (fps < 30) {
          console.warn(`⚠️ FPS过低: ${fps}`);
        }
        
        frameCount = 0;
        lastTime = currentTime;
      }
      
      requestAnimationFrame(measureFPS);
    };
    
    requestAnimationFrame(measureFPS);
  }

  // 监控内存使用
  measureMemory(): void {
    if ('memory' in performance) {
      const memory = (performance as any).memory;
      this.addMetric('memory_used', memory.usedJSHeapSize / 1024 / 1024); // MB
      this.addMetric('memory_total', memory.totalJSHeapSize / 1024 / 1024); // MB
      this.addMetric('memory_limit', memory.jsHeapSizeLimit / 1024 / 1024); // MB
    }
  }

  // 清除所有指标
  clear(): void {
    this.metrics.clear();
  }

  // 输出性能报告
  report(): void {
    console.group('📊 性能监控报告');
    
    const stats = this.getAllStats();
    
    // FPS报告
    if (stats.fps) {
      console.log(`🎯 FPS: 平均 ${stats.fps.avg.toFixed(1)}, 最低 ${stats.fps.min}, 最高 ${stats.fps.max}`);
    }
    
    // 内存报告
    if (stats.memory_used) {
      console.log(`💾 内存使用: ${stats.memory_used.avg.toFixed(1)}MB`);
    }
    
    // 渲染时间报告
    const renderStats = Object.entries(stats).filter(([name]) => name.startsWith('render_'));
    if (renderStats.length > 0) {
      console.group('🎨 组件渲染时间');
      renderStats.forEach(([name, stat]) => {
        const componentName = name.replace('render_', '');
        console.log(`${componentName}: 平均 ${stat.avg.toFixed(2)}ms, 最大 ${stat.max.toFixed(2)}ms`);
      });
      console.groupEnd();
    }
    
    // 函数执行时间报告
    const functionStats = Object.entries(stats).filter(([name]) => name.startsWith('function_'));
    if (functionStats.length > 0) {
      console.group('⚡ 函数执行时间');
      functionStats.forEach(([name, stat]) => {
        const functionName = name.replace('function_', '');
        console.log(`${functionName}: 平均 ${stat.avg.toFixed(2)}ms, 最大 ${stat.max.toFixed(2)}ms`);
      });
      console.groupEnd();
    }
    
    console.groupEnd();
  }
}

// 导出单例实例
export const performanceMonitor = PerformanceMonitor.getInstance();

// React Hook for performance monitoring
export function usePerformanceMonitor() {
  return {
    measureRender: performanceMonitor.measureRender.bind(performanceMonitor),
    measureFunction: performanceMonitor.measureFunction.bind(performanceMonitor),
    measureAsync: performanceMonitor.measureAsync.bind(performanceMonitor),
    getStats: performanceMonitor.getStats.bind(performanceMonitor),
    report: performanceMonitor.report.bind(performanceMonitor),
  };
}
