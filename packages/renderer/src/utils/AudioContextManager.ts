// 音频上下文管理器 - 处理浏览器自动播放策略
export class AudioContextManager {
  private static instance: AudioContextManager;
  private audioContext: AudioContext | null = null;
  private isUserInteracted = false;
  private interactionPromise: Promise<void> | null = null;
  private interactionResolve: (() => void) | null = null;
  private eventListeners: (() => void)[] = [];

  private constructor() {
    this.setupInteractionDetection();
  }

  public static getInstance(): AudioContextManager {
    if (!AudioContextManager.instance) {
      AudioContextManager.instance = new AudioContextManager();
    }
    return AudioContextManager.instance;
  }

  // 获取或创建音频上下文
  public async getAudioContext(): Promise<AudioContext | null> {
    try {
      // 如果已经有音频上下文，直接返回
      if (this.audioContext) {
        await this.ensureContextActive();
        return this.audioContext;
      }

      // 创建新的音频上下文
      const AudioContextClass = window.AudioContext || (window as any).webkitAudioContext;
      if (!AudioContextClass) {
        console.warn('Web Audio API not supported');
        return null;
      }

      this.audioContext = new AudioContextClass();
      
      // 确保上下文处于活跃状态
      await this.ensureContextActive();
      
      return this.audioContext;
    } catch (error) {
      console.warn('Failed to create or activate audio context:', error);
      return null;
    }
  }

  // 确保音频上下文处于活跃状态
  private async ensureContextActive(): Promise<void> {
    if (!this.audioContext) return;

    // 如果上下文被暂停，尝试恢复
    if (this.audioContext.state === 'suspended') {
      // 等待用户交互
      await this.waitForUserInteraction();
      
      try {
        await this.audioContext.resume();
        console.log('Audio context resumed successfully');
      } catch (error) {
        console.warn('Failed to resume audio context:', error);
        throw error;
      }
    }
  }

  // 等待用户交互
  private async waitForUserInteraction(): Promise<void> {
    if (this.isUserInteracted) {
      return Promise.resolve();
    }

    // 如果已经有等待中的Promise，返回它
    if (this.interactionPromise) {
      return this.interactionPromise;
    }

    // 创建新的Promise
    this.interactionPromise = new Promise<void>((resolve) => {
      this.interactionResolve = resolve;
    });

    return this.interactionPromise;
  }

  // 设置用户交互检测
  private setupInteractionDetection(): void {
    const handleUserInteraction = () => {
      if (this.isUserInteracted) return;

      this.isUserInteracted = true;
      console.log('User interaction detected, audio context can be activated');

      // 解决等待中的Promise
      if (this.interactionResolve) {
        this.interactionResolve();
        this.interactionResolve = null;
        this.interactionPromise = null;
      }

      // 尝试激活音频上下文
      if (this.audioContext && this.audioContext.state === 'suspended') {
        this.audioContext.resume().catch(error => {
          console.warn('Failed to resume audio context after user interaction:', error);
        });
      }

      // 移除事件监听器
      this.removeEventListeners();
    };

    // 添加多种用户交互事件监听
    const events = ['click', 'keydown', 'touchstart', 'touchend', 'mousedown'];
    
    events.forEach(eventType => {
      const listener = () => handleUserInteraction();
      document.addEventListener(eventType, listener, { once: true, passive: true });
      this.eventListeners.push(() => {
        document.removeEventListener(eventType, listener);
      });
    });

    // 特殊处理：监听页面可见性变化
    const visibilityListener = () => {
      if (!document.hidden && this.isUserInteracted && this.audioContext) {
        // 页面重新可见时，尝试恢复音频上下文
        if (this.audioContext.state === 'suspended') {
          this.audioContext.resume().catch(error => {
            console.warn('Failed to resume audio context on visibility change:', error);
          });
        }
      }
    };

    document.addEventListener('visibilitychange', visibilityListener);
    this.eventListeners.push(() => {
      document.removeEventListener('visibilitychange', visibilityListener);
    });
  }

  // 移除事件监听器
  private removeEventListeners(): void {
    this.eventListeners.forEach(removeListener => removeListener());
    this.eventListeners = [];
  }

  // 检查是否支持Web Audio API
  public isWebAudioSupported(): boolean {
    return !!(window.AudioContext || (window as any).webkitAudioContext);
  }

  // 检查用户是否已交互
  public hasUserInteracted(): boolean {
    return this.isUserInteracted;
  }

  // 获取音频上下文状态
  public getContextState(): AudioContextState | null {
    return this.audioContext?.state || null;
  }

  // 强制用户交互（用于测试）
  public forceUserInteraction(): void {
    if (!this.isUserInteracted) {
      this.isUserInteracted = true;
      if (this.interactionResolve) {
        this.interactionResolve();
        this.interactionResolve = null;
        this.interactionPromise = null;
      }
    }
  }

  // 创建音频缓冲区源
  public async createBufferSource(buffer: AudioBuffer): Promise<AudioBufferSourceNode | null> {
    const context = await this.getAudioContext();
    if (!context) return null;

    try {
      const source = context.createBufferSource();
      source.buffer = buffer;
      return source;
    } catch (error) {
      console.warn('Failed to create buffer source:', error);
      return null;
    }
  }

  // 创建增益节点
  public async createGainNode(): Promise<GainNode | null> {
    const context = await this.getAudioContext();
    if (!context) return null;

    try {
      return context.createGain();
    } catch (error) {
      console.warn('Failed to create gain node:', error);
      return null;
    }
  }

  // 创建振荡器
  public async createOscillator(): Promise<OscillatorNode | null> {
    const context = await this.getAudioContext();
    if (!context) return null;

    try {
      return context.createOscillator();
    } catch (error) {
      console.warn('Failed to create oscillator:', error);
      return null;
    }
  }

  // 获取当前时间
  public getCurrentTime(): number {
    return this.audioContext?.currentTime || 0;
  }

  // 获取目标节点
  public async getDestination(): Promise<AudioDestinationNode | null> {
    const context = await this.getAudioContext();
    return context?.destination || null;
  }

  // 解码音频数据
  public async decodeAudioData(arrayBuffer: ArrayBuffer): Promise<AudioBuffer | null> {
    const context = await this.getAudioContext();
    if (!context) return null;

    try {
      return await context.decodeAudioData(arrayBuffer);
    } catch (error) {
      console.warn('Failed to decode audio data:', error);
      return null;
    }
  }

  // 检查音频上下文是否可用
  public async isContextAvailable(): Promise<boolean> {
    try {
      const context = await this.getAudioContext();
      return context !== null && context.state !== 'closed';
    } catch (error) {
      return false;
    }
  }

  // 获取详细状态信息
  public getDetailedStatus(): {
    supported: boolean;
    userInteracted: boolean;
    contextState: AudioContextState | null;
    contextAvailable: boolean;
  } {
    return {
      supported: this.isWebAudioSupported(),
      userInteracted: this.isUserInteracted,
      contextState: this.getContextState(),
      contextAvailable: this.audioContext !== null && this.audioContext.state !== 'closed',
    };
  }

  // 清理资源
  public dispose(): void {
    this.removeEventListeners();
    
    if (this.audioContext && this.audioContext.state !== 'closed') {
      this.audioContext.close().catch(error => {
        console.warn('Failed to close audio context:', error);
      });
    }
    
    this.audioContext = null;
    this.isUserInteracted = false;
    this.interactionPromise = null;
    this.interactionResolve = null;
  }
}
