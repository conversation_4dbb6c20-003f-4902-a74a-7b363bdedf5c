import React, { useState, useEffect, useMemo, useCallback } from 'react';
import { Card, Button, Table, Tag, Space, Modal, Form, Input, Select, DatePicker, InputNumber, Tabs, Radio } from 'antd';
import {
  PlusOutlined,
  EditOutlined,
  DeleteOutlined,
  PlayCircleOutlined,
  RobotOutlined,
  UnorderedListOutlined,
  AppstoreOutlined,
  CalendarOutlined,
  ApartmentOutlined,
  TableOutlined
} from '@ant-design/icons';
import { useSelector, useDispatch } from 'react-redux';
import { RootState } from '../store/store';
import { Task, CreateTaskDto } from '../types';
import { loadTasks, createTaskAsync, updateTaskAsync, deleteTaskAsync } from '../store/thunks/tasksThunks';
import { loadGoals } from '../store/thunks/goalsThunks';
import { createPomodoroSessionAsync } from '../store/thunks/pomodoroThunks';
import { setCurrentSession, resetTimer } from '../store/slices/pomodoroSlice';
import { VirtualTable } from '../components/performance/VirtualList';
import { PerformanceCard, PerformanceContainer } from '../components/performance/MemoizedComponents';
import { useOptimizedData, useDebounce } from '../hooks/useOptimizedData';
import AITasksView from '../components/AITasksView';
import TaskKanbanView from '../components/TaskKanbanView';
import TaskCalendarView from '../components/TaskCalendarView';
import TaskHierarchyView from '../components/TaskHierarchyView';
import { reminderService } from '../services/ReminderService';
import { goalBeaconService } from '../services/GoalBeaconService';
import type { ColumnsType } from 'antd/es/table';
import dayjs from 'dayjs';

const { TextArea } = Input;
const { Option } = Select;
const { TabPane } = Tabs;

const Tasks: React.FC = () => {
  const dispatch = useDispatch();
  const { tasks, loading } = useSelector((state: RootState) => state.tasks);
  const { goals } = useSelector((state: RootState) => state.goals);
  const [isModalVisible, setIsModalVisible] = useState(false);
  const [editingTask, setEditingTask] = useState<Task | null>(null);
  const [searchTerm, setSearchTerm] = useState('');
  const [statusFilter, setStatusFilter] = useState<string>('all');
  const [activeTab, setActiveTab] = useState('manual');
  const [selectedGoalForAI, setSelectedGoalForAI] = useState<string>('');
  const [viewMode, setViewMode] = useState<'table' | 'kanban' | 'calendar' | 'hierarchy'>('table');
  const [form] = Form.useForm();

  // 防抖搜索
  const debouncedSearchTerm = useDebounce(searchTerm, 300);

  // 组件挂载时加载数据
  useEffect(() => {
    dispatch(loadTasks());
    dispatch(loadGoals());
  }, [dispatch]);

  // 使用useCallback缓存事件处理函数
  const handleAddTask = useCallback(() => {
    setEditingTask(null);
    form.resetFields();
    setIsModalVisible(true);
  }, [form]);

  const handleEditTask = useCallback((task: Task) => {
    setEditingTask(task);
    form.setFieldsValue({
      ...task,
      deadline: task.deadline ? dayjs(task.deadline) : null,
    });
    setIsModalVisible(true);
  }, [form]);

  const handleDeleteTask = useCallback((taskId: string) => {
    Modal.confirm({
      title: '确认删除',
      content: '确定要删除这个任务吗？',
      onOk: () => {
        dispatch(deleteTaskAsync(taskId));
      },
    });
  }, [dispatch]);

  const handleStartPomodoro = useCallback(async (task: Task) => {
    try {
      // 创建新的番茄钟会话
      const sessionData = {
        taskId: task.id,
        type: 'work' as const,
        startTime: new Date(),
        duration: 25, // 默认25分钟
      };

      const session = await dispatch(createPomodoroSessionAsync(sessionData)).unwrap();

      // 设置当前会话
      dispatch(setCurrentSession(session));

      // 重置计时器
      dispatch(resetTimer());

      // 触发目标提醒
      goalBeaconService.triggerGoalBeacon('task-start', `开始任务：${task.title}`);

      // 显示成功消息并提供跳转选项
      Modal.success({
        title: '番茄钟已准备就绪',
        content: `已为任务"${task.title}"创建番茄钟会话，是否跳转到番茄钟页面开始计时？`,
        okText: '跳转到番茄钟',
        cancelText: '稍后开始',
        onOk: () => {
          // 这里可以添加页面跳转逻辑
          // 由于我们使用的是单页应用，可以通过修改selectedKey来切换页面
          // 或者使用路由跳转
          console.log('Navigating to pomodoro page...');

          // 如果有路由系统，可以这样跳转：
          // navigate('/pomodoro');

          // 或者通过父组件的状态管理来切换页面
          // 这里暂时使用console.log作为占位符
        },
      });
    } catch (error) {
      console.error('创建番茄钟会话失败:', error);
      Modal.error({
        title: '创建失败',
        content: '创建番茄钟会话失败，请稍后重试',
      });
    }
  }, [dispatch]);

  // 过滤和搜索任务
  const filteredTasks = useMemo(() => {
    let filtered = tasks;

    // 搜索过滤
    if (debouncedSearchTerm) {
      filtered = filtered.filter(task =>
        task.title.toLowerCase().includes(debouncedSearchTerm.toLowerCase()) ||
        task.description?.toLowerCase().includes(debouncedSearchTerm.toLowerCase())
      );
    }

    // 状态过滤
    if (statusFilter !== 'all') {
      filtered = filtered.filter(task => task.status === statusFilter);
    }

    return filtered;
  }, [tasks, debouncedSearchTerm, statusFilter]);

  // 获取有AI分解的目标
  const aiGoals = useMemo(() => {
    return goals.filter(goal => goal.hasAIDecomposition);
  }, [goals]);

  const handleSubmit = async (values: any) => {
    try {
      const taskData = {
        ...values,
        tags: values.tags || [],
        deadline: values.deadline ? values.deadline.toISOString() : undefined,
      };

      if (editingTask) {
        const updatedTask = await dispatch(updateTaskAsync({
          id: editingTask.id,
          updates: taskData
        })).unwrap();

        // 更新提醒
        if (updatedTask.deadline) {
          reminderService.scheduleTaskDeadlineReminder(updatedTask);
        } else {
          reminderService.cancelTaskDeadlineReminder(updatedTask.id);
        }
      } else {
        const newTask = await dispatch(createTaskAsync(taskData)).unwrap();

        // 设置提醒
        if (newTask.deadline) {
          reminderService.scheduleTaskDeadlineReminder(newTask);
        }
      }

      setIsModalVisible(false);
      form.resetFields();
    } catch (error) {
      console.error('保存任务失败:', error);
    }
  };

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'todo':
        return 'default';
      case 'in-progress':
        return 'processing';
      case 'completed':
        return 'success';
      case 'paused':
        return 'warning';
      case 'cancelled':
        return 'error';
      default:
        return 'default';
    }
  };

  const getStatusText = (status: string) => {
    switch (status) {
      case 'todo':
        return '待办';
      case 'in-progress':
        return '进行中';
      case 'completed':
        return '已完成';
      case 'paused':
        return '已暂停';
      case 'cancelled':
        return '已取消';
      default:
        return status;
    }
  };

  const getPriorityColor = (priority: string) => {
    switch (priority) {
      case 'high':
        return 'red';
      case 'medium':
        return 'orange';
      case 'low':
        return 'green';
      default:
        return 'default';
    }
  };

  const getPriorityText = (priority: string) => {
    switch (priority) {
      case 'high':
        return '高';
      case 'medium':
        return '中';
      case 'low':
        return '低';
      default:
        return priority;
    }
  };

  const columns: ColumnsType<Task> = [
    {
      title: '任务名称',
      dataIndex: 'title',
      key: 'title',
      width: 200,
    },
    {
      title: '优先级',
      dataIndex: 'priority',
      key: 'priority',
      width: 80,
      render: (priority: string) => (
        <Tag color={getPriorityColor(priority)}>{getPriorityText(priority)}</Tag>
      ),
    },
    {
      title: '状态',
      dataIndex: 'status',
      key: 'status',
      width: 100,
      render: (status: string) => (
        <Tag color={getStatusColor(status)}>{getStatusText(status)}</Tag>
      ),
    },
    {
      title: '预估时间',
      dataIndex: 'estimatedTime',
      key: 'estimatedTime',
      width: 100,
      render: (time?: number) => time ? `${time}分钟` : '-',
    },
    {
      title: '实际时间',
      dataIndex: 'actualTime',
      key: 'actualTime',
      width: 100,
      render: (time: number) => `${time}分钟`,
    },
    {
      title: '截止日期',
      dataIndex: 'deadline',
      key: 'deadline',
      width: 120,
      render: (deadline?: Date) => 
        deadline ? new Date(deadline).toLocaleDateString() : '-',
    },
    {
      title: '操作',
      key: 'action',
      width: 150,
      render: (_, record) => (
        <Space size="small">
          <Button
            className="fast-button"
            size="small"
            icon={<PlayCircleOutlined />}
            title="开始番茄钟"
            onClick={() => handleStartPomodoro(record)}
          />
          <Button
            className="fast-button"
            size="small"
            icon={<EditOutlined />}
            onClick={() => handleEditTask(record)}
          />
          <Button
            className="fast-button"
            size="small"
            icon={<DeleteOutlined />}
            onClick={() => handleDeleteTask(record.id)}
            danger
          />
        </Space>
      ),
    },
  ];

  return (
    <PerformanceContainer>
      <div className="fast-grid" style={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', marginBottom: 24 }}>
        <h1 className="fast-text">任务管理</h1>
        <Button className="fast-button" type="primary" icon={<PlusOutlined />} onClick={handleAddTask}>
          新建任务
        </Button>
      </div>

      <PerformanceCard>
        <Tabs
          activeKey={activeTab}
          onChange={setActiveTab}
          items={[
            {
              key: 'manual',
              label: (
                <Space>
                  <UnorderedListOutlined />
                  <span>手动任务</span>
                </Space>
              ),
              children: (
                <div>
                  {/* 搜索、过滤和视图切换区域 */}
                  <div style={{ marginBottom: 16 }}>
                    <div style={{ display: 'flex', gap: 16, alignItems: 'center', justifyContent: 'space-between' }}>
                      <div style={{ display: 'flex', gap: 16, alignItems: 'center' }}>
                        <Input.Search
                          placeholder="搜索任务..."
                          value={searchTerm}
                          onChange={(e) => setSearchTerm(e.target.value)}
                          style={{ width: 300 }}
                          allowClear
                        />
                        <Select
                          value={statusFilter}
                          onChange={setStatusFilter}
                          style={{ width: 120 }}
                        >
                          <Select.Option value="all">全部状态</Select.Option>
                          <Select.Option value="todo">待办</Select.Option>
                          <Select.Option value="in-progress">进行中</Select.Option>
                          <Select.Option value="completed">已完成</Select.Option>
                          <Select.Option value="paused">已暂停</Select.Option>
                        </Select>
                      </div>

                      {/* 视图切换 */}
                      <Radio.Group
                        value={viewMode}
                        onChange={(e) => setViewMode(e.target.value)}
                        buttonStyle="solid"
                        size="small"
                      >
                        <Radio.Button value="table">
                          <TableOutlined /> 表格
                        </Radio.Button>
                        <Radio.Button value="kanban">
                          <AppstoreOutlined /> 看板
                        </Radio.Button>
                        <Radio.Button value="calendar">
                          <CalendarOutlined /> 日历
                        </Radio.Button>
                        <Radio.Button value="hierarchy">
                          <ApartmentOutlined /> 层级
                        </Radio.Button>
                      </Radio.Group>
                    </div>
                  </div>

                  {/* 根据视图模式渲染不同组件 */}
                  {viewMode === 'table' && (
                    <Table
                      className="fast-grid"
                      columns={columns}
                      dataSource={filteredTasks}
                      loading={loading}
                      rowKey="id"
                      pagination={{
                        pageSize: 20,
                        showSizeChanger: true,
                        showQuickJumper: true,
                        showTotal: (total) => `共 ${total} 条记录`,
                      }}
                      scroll={{ x: 800 }}
                    />
                  )}

                  {viewMode === 'kanban' && (
                    <TaskKanbanView
                      tasks={filteredTasks}
                      onTaskUpdate={(taskId, updates) => {
                        dispatch(updateTaskAsync({ id: taskId, updates }));
                      }}
                      onTaskEdit={handleEditTask}
                      onTaskDelete={handleDeleteTask}
                      onStartPomodoro={handleStartPomodoro}
                      loading={loading}
                    />
                  )}

                  {viewMode === 'calendar' && (
                    <TaskCalendarView
                      tasks={filteredTasks}
                      onTaskEdit={handleEditTask}
                      onTaskDelete={handleDeleteTask}
                      onStartPomodoro={handleStartPomodoro}
                      loading={loading}
                    />
                  )}

                  {viewMode === 'hierarchy' && (
                    <TaskHierarchyView
                      tasks={filteredTasks}
                      goals={goals}
                      onTaskEdit={handleEditTask}
                      onTaskDelete={handleDeleteTask}
                      onStartPomodoro={handleStartPomodoro}
                      loading={loading}
                    />
                  )}
                </div>
              )
            },
            {
              key: 'ai',
              label: (
                <Space>
                  <RobotOutlined />
                  <span>AI任务</span>
                  {aiGoals.length > 0 && (
                    <Tag color="blue" size="small">{aiGoals.length}</Tag>
                  )}
                </Space>
              ),
              children: (
                <div>
                  {aiGoals.length === 0 ? (
                    <div style={{ textAlign: 'center', padding: '40px 20px' }}>
                      <RobotOutlined style={{ fontSize: 48, color: '#d9d9d9', marginBottom: 16 }} />
                      <div>
                        <span style={{ color: '#999' }}>暂无AI分解任务</span>
                      </div>
                      <div style={{ marginTop: 8 }}>
                        <span style={{ color: '#999', fontSize: '12px' }}>
                          请在创建目标时启用AI分解功能
                        </span>
                      </div>
                    </div>
                  ) : (
                    <div>
                      {/* AI目标选择器 */}
                      <div style={{ marginBottom: 16 }}>
                        <Select
                          placeholder="选择要查看的AI分解目标"
                          value={selectedGoalForAI}
                          onChange={setSelectedGoalForAI}
                          style={{ width: 300 }}
                          allowClear
                        >
                          {aiGoals.map(goal => (
                            <Option key={goal.id} value={goal.id}>
                              <Space>
                                <RobotOutlined style={{ color: '#1890ff' }} />
                                {goal.name}
                              </Space>
                            </Option>
                          ))}
                        </Select>
                      </div>

                      {/* AI任务视图 */}
                      {selectedGoalForAI && (
                        <AITasksView
                          goalId={selectedGoalForAI}
                          goalName={aiGoals.find(g => g.id === selectedGoalForAI)?.name || ''}
                        />
                      )}
                    </div>
                  )}
                </div>
              )
            }
          ]}
        />
      </PerformanceCard>

      <Modal
        title={editingTask ? '编辑任务' : '新建任务'}
        open={isModalVisible}
        onCancel={() => setIsModalVisible(false)}
        onOk={() => form.submit()}
        width={600}
      >
        <Form
          form={form}
          layout="vertical"
          onFinish={handleSubmit}
        >
          <Form.Item
            name="goalNodeId"
            label="关联目标"
            rules={[{ required: true, message: '请选择关联目标' }]}
          >
            <Select placeholder="选择关联目标">
              {goals.map((goal) => (
                <Option key={goal.id} value={goal.id}>
                  {goal.name}
                </Option>
              ))}
            </Select>
          </Form.Item>

          <Form.Item
            name="title"
            label="任务名称"
            rules={[{ required: true, message: '请输入任务名称' }]}
          >
            <Input className="fast-input" placeholder="请输入任务名称" />
          </Form.Item>

          <Form.Item
            name="description"
            label="任务描述"
          >
            <TextArea className="fast-input" rows={3} placeholder="详细描述任务内容" />
          </Form.Item>

          <Form.Item
            name="priority"
            label="优先级"
            initialValue="medium"
          >
            <Select>
              <Option value="high">高优先级</Option>
              <Option value="medium">中优先级</Option>
              <Option value="low">低优先级</Option>
            </Select>
          </Form.Item>

          <Form.Item
            name="estimatedTime"
            label="预估时间（分钟）"
          >
            <InputNumber className="fast-input" min={1} max={480} placeholder="预估完成时间" style={{ width: '100%' }} />
          </Form.Item>

          <Form.Item
            name="deadline"
            label="截止日期"
          >
            <DatePicker style={{ width: '100%' }} />
          </Form.Item>

          <Form.Item
            name="tags"
            label="标签"
          >
            <Select mode="tags" placeholder="输入任务标签">
              <Option value="重要">重要</Option>
              <Option value="紧急">紧急</Option>
              <Option value="学习">学习</Option>
              <Option value="工作">工作</Option>
            </Select>
          </Form.Item>
        </Form>
      </Modal>
    </PerformanceContainer>
  );
};

export default Tasks;