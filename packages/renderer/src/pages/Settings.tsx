import React, { useState, useEffect } from 'react';
import { Form, Select, Switch, Space, message, Row, Col, Card, Button, Tabs } from 'antd';
import { SettingOutlined, RobotOutlined, BgColorsOutlined, BellOutlined, DashboardOutlined, SoundOutlined, SafetyOutlined } from '@ant-design/icons';
import { useTheme } from '../contexts/ThemeContext';
import { themes } from '../themes';
import { DatabaseAPI } from '../services/api';
import AIProviders from './AIProviders';
import { ReminderSettings } from '../components/ReminderNotification';
import { GoalBeaconSettings } from '../components/GoalBeaconDisplay';
import PerformanceMonitor from '../components/PerformanceMonitor';
import AudioSettings from '../components/AudioSettings';
import AudioTestPanel from '../components/AudioTestPanel';
import AudioDebugPanel from '../components/AudioDebugPanel';
import DropdownStyleTest from '../components/DropdownStyleTest';
import FocusShieldControl from '../components/FocusShieldControl';

const { Option } = Select;

const Settings: React.FC = () => {
  const [form] = Form.useForm();
  const { theme: currentTheme, themeId, setTheme } = useTheme();
  const [loading, setLoading] = useState(false);

  // 从数据库加载设置
  useEffect(() => {
    const loadSettings = async () => {
      try {
        const settings = await DatabaseAPI.getAllSettings();
        form.setFieldsValue({
          theme: themeId,
          language: settings.language || 'zh-CN',
          notifications: settings.notifications_enabled === 'true',
          autoStart: settings.auto_start === 'true',
        });
      } catch (error) {
        console.error('加载设置失败:', error);
      }
    };
    loadSettings();
  }, [form, themeId]);

  const handleSave = async (values: any) => {
    setLoading(true);
    try {
      // 保存主题
      if (values.theme !== themeId) {
        setTheme(values.theme);
      }
      
      // 保存其他设置
      await DatabaseAPI.setSetting('language', values.language);
      await DatabaseAPI.setSetting('notifications_enabled', values.notifications.toString());
      await DatabaseAPI.setSetting('auto_start', values.autoStart.toString());
      
      message.success('设置保存成功');
    } catch (error) {
      console.error('保存设置失败:', error);
      message.error('保存设置失败');
    } finally {
      setLoading(false);
    }
  };

  const handleReset = () => {
    form.setFieldsValue({
      theme: 'skyBlue',
      language: 'zh-CN',
      notifications: true,
      autoStart: false,
    });
  };

  const tabItems = [
    {
      key: 'appearance',
      label: (
        <span style={{ display: 'flex', alignItems: 'center', gap: '8px' }}>
          <BgColorsOutlined />
          主题外观
        </span>
      ),
      children: (
        <Space direction="vertical" size="large" style={{ width: '100%' }}>
          {/* 主题设置 */}
          <Card className="ultra-fast-card">
            <h3 className="fast-text" style={{
              margin: '0 0 24px',
              color: currentTheme.colors.text,
              fontSize: '18px',
              fontWeight: 600
            }}>
              🎨 主题配色
            </h3>

            <Form form={form} onFinish={handleSave} layout="vertical">
              <Form.Item name="theme" label="主题配色">
                <Select
                  className="fast-input"
                  style={{ width: '100%' }}
                  onChange={(value) => {
                    setTheme(value);
                    form.setFieldValue('theme', value);
                  }}
                >
                  {Object.values(themes).map((theme) => (
                    <Option key={theme.id} value={theme.id}>
                      <div style={{ display: 'flex', alignItems: 'center', gap: '12px' }}>
                        <div
                          style={{
                            width: '24px',
                            height: '24px',
                            borderRadius: '50%',
                            background: theme.colors.primary,
                            border: '2px solid rgba(255,255,255,0.3)',
                            boxShadow: `0 2px 8px ${theme.colors.shadow}`,
                          }}
                        />
                        <span>{theme.name}</span>
                      </div>
                    </Option>
                  ))}
                </Select>
              </Form.Item>

              {/* 颜色主题预览 */}
              <div style={{ marginBottom: '24px' }}>
                <div style={{
                  marginBottom: '16px',
                  fontSize: '16px',
                  fontWeight: 600,
                  color: currentTheme.colors.text
                }}>
                  颜色主题
                </div>

                {/* 浅色主题 */}
                <div style={{ marginBottom: '20px' }}>
                  <Row gutter={[12, 12]}>
                    {Object.values(themes).filter(theme => !['darkNight', 'darkBlue'].includes(theme.id)).map((theme) => (
                      <Col key={theme.id} span={3}>
                        <div
                          style={{
                            position: 'relative',
                            cursor: 'pointer',
                            textAlign: 'center',
                          }}
                          onClick={() => {
                            setTheme(theme.id);
                            form.setFieldValue('theme', theme.id);
                          }}
                        >
                          <div
                            style={{
                              width: '80px',
                              height: '80px',
                              borderRadius: '12px',
                              background: theme.colors.background.includes('gradient')
                                ? theme.colors.background
                                : theme.colors.primary,
                              margin: '0 auto 8px',
                              border: themeId === theme.id ? `3px solid ${currentTheme.colors.primary}` : '2px solid rgba(0,0,0,0.1)',
                              position: 'relative',
                              overflow: 'hidden',
                              boxShadow: themeId === theme.id ? `0 4px 12px ${theme.colors.shadow}` : '0 2px 8px rgba(0,0,0,0.1)',
                            }}
                          >
                            {/* 显示主色调小圆点 */}
                            <div
                              style={{
                                position: 'absolute',
                                bottom: '8px',
                                left: '8px',
                                width: '16px',
                                height: '16px',
                                borderRadius: '50%',
                                background: theme.colors.primary,
                                border: '2px solid rgba(255,255,255,0.8)',
                              }}
                            />
                            {themeId === theme.id && (
                              <div
                                style={{
                                  position: 'absolute',
                                  bottom: '6px',
                                  right: '6px',
                                  width: '16px',
                                  height: '16px',
                                  borderRadius: '50%',
                                  background: '#ffffff',
                                  display: 'flex',
                                  alignItems: 'center',
                                  justifyContent: 'center',
                                  fontSize: '10px',
                                  color: theme.colors.primary,
                                  fontWeight: 'bold',
                                }}
                              >
                                ✓
                              </div>
                            )}
                          </div>
                          <div style={{
                            fontSize: '12px',
                            color: currentTheme.colors.textSecondary,
                            fontWeight: 500,
                          }}>
                            {theme.name}
                          </div>
                        </div>
                      </Col>
                    ))}
                  </Row>
                </div>

                {/* 深色主题 */}
                <div>
                  <div style={{
                    marginBottom: '12px',
                    fontSize: '14px',
                    color: currentTheme.colors.textSecondary,
                    fontWeight: 500,
                  }}>
                    深色主题
                  </div>
                  <Row gutter={[12, 12]}>
                    {Object.values(themes).filter(theme => ['darkNight', 'darkBlue'].includes(theme.id)).map((theme) => (
                      <Col key={theme.id} span={3}>
                        <div
                          style={{
                            position: 'relative',
                            cursor: 'pointer',
                            textAlign: 'center',
                          }}
                          onClick={() => {
                            setTheme(theme.id);
                            form.setFieldValue('theme', theme.id);
                          }}
                        >
                          <div
                            style={{
                              width: '80px',
                              height: '80px',
                              borderRadius: '12px',
                              background: theme.colors.background.includes('gradient')
                                ? theme.colors.background
                                : '#1C1C1E',
                              margin: '0 auto 8px',
                              border: themeId === theme.id ? `3px solid ${currentTheme.colors.primary}` : '2px solid rgba(255,255,255,0.2)',
                              position: 'relative',
                              overflow: 'hidden',
                              boxShadow: themeId === theme.id ? `0 4px 12px ${theme.colors.shadow}` : '0 2px 8px rgba(0,0,0,0.3)',
                            }}
                          >
                            {/* 显示主色调小圆点 */}
                            <div
                              style={{
                                position: 'absolute',
                                bottom: '8px',
                                left: '8px',
                                width: '16px',
                                height: '16px',
                                borderRadius: '50%',
                                background: theme.colors.primary,
                                border: '2px solid rgba(255,255,255,0.3)',
                              }}
                            />
                            {themeId === theme.id && (
                              <div
                                style={{
                                  position: 'absolute',
                                  bottom: '6px',
                                  right: '6px',
                                  width: '16px',
                                  height: '16px',
                                  borderRadius: '50%',
                                  background: '#ffffff',
                                  display: 'flex',
                                  alignItems: 'center',
                                  justifyContent: 'center',
                                  fontSize: '10px',
                                  color: theme.colors.primary,
                                  fontWeight: 'bold',
                                }}
                              >
                                ✓
                              </div>
                            )}
                          </div>
                          <div style={{
                            fontSize: '12px',
                            color: currentTheme.colors.textSecondary,
                            fontWeight: 500,
                          }}>
                            {theme.name}
                          </div>
                        </div>
                      </Col>
                    ))}
                  </Row>
                </div>
              </div>

              <Form.Item name="language" label="语言">
                <Select className="fast-input" style={{ width: '200px' }}>
                  <Option value="zh-CN">🇨🇳 中文</Option>
                  <Option value="en-US">🇺🇸 English</Option>
                </Select>
              </Form.Item>
            </Form>
          </Card>
        </Space>
      )
    },
    {
      key: 'notifications',
      label: (
        <span style={{ display: 'flex', alignItems: 'center', gap: '8px' }}>
          <BellOutlined />
          通知设置
        </span>
      ),
      children: (
        <Space direction="vertical" size="large" style={{ width: '100%' }}>
          {/* 通知设置 */}
          <Card className="ultra-fast-card">
            <h3 className="fast-text" style={{
              margin: '0 0 24px',
              color: currentTheme.colors.text,
              fontSize: '18px',
              fontWeight: 600
            }}>
              🔔 通知设置
            </h3>

            <Form form={form} onFinish={handleSave} layout="vertical">
              <Form.Item name="notifications" label="桌面通知" valuePropName="checked">
                <Switch />
              </Form.Item>

              <Form.Item name="autoStart" label="开机自启动" valuePropName="checked">
                <Switch />
              </Form.Item>
            </Form>
          </Card>

          {/* 音频设置 */}
          <AudioSettings />

          {/* 音频测试面板 */}
          <AudioTestPanel />

          {/* 音频调试面板 */}
          <AudioDebugPanel />

          {/* 下拉列表样式测试 */}
          <DropdownStyleTest />

          {/* 提醒设置 */}
          <ReminderSettings />

          {/* 目标可视化提醒设置 */}
          <GoalBeaconSettings />

          {/* 操作按钮 */}
          <Card className="ultra-fast-card">
            <div className="fast-grid" style={{ display: 'flex', gap: '12px', justifyContent: 'flex-end' }}>
              <Button className="fast-button" type="ghost" onClick={handleReset}>
                重置设置
              </Button>
              <Button
                className="fast-button"
                type="primary"
                loading={loading}
                onClick={() => form.submit()}
              >
                保存设置
              </Button>
            </div>
          </Card>
        </Space>
      )
    },
    {
      key: 'performance',
      label: (
        <span style={{ display: 'flex', alignItems: 'center', gap: '8px' }}>
          <DashboardOutlined />
          性能监控
        </span>
      ),
      children: <PerformanceMonitor enabled={true} showDetails={true} />
    },
    {
      key: 'focus-shield',
      label: (
        <span style={{ display: 'flex', alignItems: 'center', gap: '8px' }}>
          <SafetyOutlined />
          Focus Shield
        </span>
      ),
      children: <FocusShieldControl embedded={false} showFullControls={true} />
    },
    {
      key: 'ai-providers',
      label: (
        <span style={{ display: 'flex', alignItems: 'center', gap: '8px' }}>
          <RobotOutlined />
          AI API 管理
        </span>
      ),
      children: <AIProviders />
    }
  ];

  return (
    <div
      className="lazy-content stable-layout"
      style={{
        maxWidth: '1200px',
        margin: '0 auto',
        width: '100%',
        minHeight: '100%',
        overflow: 'visible'
      }}
    >
      <div className="fast-grid" style={{ marginBottom: '32px' }}>
        <h2 className="fast-text" style={{
          margin: 0,
          color: currentTheme.colors.text,
          fontSize: '20px',
          fontWeight: 600,
          display: 'flex',
          alignItems: 'center',
          gap: '12px'
        }}>
          <SettingOutlined style={{ color: currentTheme.colors.primary }} />
          应用设置
        </h2>
        <p className="fast-text" style={{
          margin: '8px 0 0',
          color: currentTheme.colors.textSecondary,
          fontSize: '14px'
        }}>
          个性化您的FocusOS体验
        </p>
      </div>

      <Tabs
        defaultActiveKey="appearance"
        items={tabItems}
        size="large"
        styles={{
          tab: {
            color: currentTheme.colors.textSecondary,
          },
          activeTab: {
            color: currentTheme.colors.primary,
          }
        }}
      />
    </div>
  );
};

export default Settings;