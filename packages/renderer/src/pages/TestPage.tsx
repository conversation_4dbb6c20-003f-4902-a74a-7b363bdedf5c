import React, { useState, useCallback } from 'react';
import { Card, Button, Space, Typography, Progress, Alert, Collapse, Tag, Divider } from 'antd';
import { 
  PlayCircleOutlined, 
  CheckCircleOutlined, 
  CloseCircleOutlined,
  ClockCircleOutlined,
  BugOutlined,
  RocketOutlined
} from '@ant-design/icons';
import { AIDecompositionTester, TestSuite, TestResult } from '../utils/testUtils';

const { Title, Text, Paragraph } = Typography;
const { Panel } = Collapse;

const TestPage: React.FC = () => {
  const [testing, setTesting] = useState(false);
  const [testResults, setTestResults] = useState<TestSuite[]>([]);
  const [currentTest, setCurrentTest] = useState<string>('');
  const [progress, setProgress] = useState(0);

  const runTests = useCallback(async () => {
    setTesting(true);
    setTestResults([]);
    setProgress(0);

    try {
      const tester = new AIDecompositionTester();
      
      // 运行基础功能测试
      setCurrentTest('运行基础功能测试...');
      setProgress(25);
      const basicTests = await tester.runBasicFunctionalityTests();
      setTestResults(prev => [...prev, basicTests]);

      // 运行边界情况测试
      setCurrentTest('运行边界情况测试...');
      setProgress(50);
      const boundaryTests = await tester.runBoundaryTests();
      setTestResults(prev => [...prev, boundaryTests]);

      // 运行用户场景测试
      setCurrentTest('运行用户场景测试...');
      setProgress(75);
      const scenarioTests = await tester.runUserScenarioTests();
      setTestResults(prev => [...prev, scenarioTests]);

      // 运行性能测试
      setCurrentTest('运行性能测试...');
      setProgress(100);
      const performanceTests = await tester.runPerformanceTests();
      setTestResults(prev => [...prev, performanceTests]);

      setCurrentTest('测试完成');
    } catch (error) {
      console.error('测试运行失败:', error);
      setCurrentTest(`测试失败: ${(error as Error).message}`);
    } finally {
      setTesting(false);
    }
  }, []);

  const getTestStatusIcon = (status: TestResult['status']) => {
    switch (status) {
      case 'pass':
        return <CheckCircleOutlined style={{ color: '#52c41a' }} />;
      case 'fail':
        return <CloseCircleOutlined style={{ color: '#ff4d4f' }} />;
      case 'skip':
        return <ClockCircleOutlined style={{ color: '#faad14' }} />;
      default:
        return null;
    }
  };

  const getTestStatusColor = (status: TestResult['status']) => {
    switch (status) {
      case 'pass':
        return 'success';
      case 'fail':
        return 'error';
      case 'skip':
        return 'warning';
      default:
        return 'default';
    }
  };

  const getTotalStats = () => {
    const total = testResults.reduce((sum, suite) => sum + suite.tests.length, 0);
    const passed = testResults.reduce((sum, suite) => sum + suite.passed, 0);
    const failed = testResults.reduce((sum, suite) => sum + suite.failed, 0);
    const skipped = testResults.reduce((sum, suite) => sum + suite.skipped, 0);
    const successRate = total > 0 ? ((passed / total) * 100).toFixed(1) : '0';

    return { total, passed, failed, skipped, successRate };
  };

  const stats = getTotalStats();

  return (
    <div style={{ padding: '24px', maxWidth: '1200px', margin: '0 auto' }}>
      <div style={{ marginBottom: '24px' }}>
        <Title level={2}>
          <BugOutlined style={{ marginRight: '8px' }} />
          AI目标分解功能测试
        </Title>
        <Paragraph type="secondary">
          验证AI目标分解功能的完整性、稳定性和性能表现
        </Paragraph>
      </div>

      {/* 测试控制面板 */}
      <Card style={{ marginBottom: '24px' }}>
        <Space direction="vertical" style={{ width: '100%' }}>
          <div style={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center' }}>
            <Title level={4} style={{ margin: 0 }}>测试控制</Title>
            <Button 
              type="primary" 
              icon={<RocketOutlined />}
              onClick={runTests}
              loading={testing}
              disabled={testing}
            >
              {testing ? '测试进行中...' : '开始测试'}
            </Button>
          </div>

          {testing && (
            <div>
              <div style={{ marginBottom: '8px' }}>
                <Text>{currentTest}</Text>
              </div>
              <Progress percent={progress} status="active" />
            </div>
          )}
        </Space>
      </Card>

      {/* 测试结果总览 */}
      {testResults.length > 0 && (
        <Card style={{ marginBottom: '24px' }}>
          <Title level={4}>测试结果总览</Title>
          <div style={{ 
            display: 'grid', 
            gridTemplateColumns: 'repeat(auto-fit, minmax(120px, 1fr))', 
            gap: '16px',
            marginTop: '16px'
          }}>
            <div style={{ textAlign: 'center' }}>
              <div style={{ fontSize: '24px', fontWeight: 'bold', color: '#1890ff' }}>
                {stats.total}
              </div>
              <div style={{ color: '#666' }}>总测试数</div>
            </div>
            <div style={{ textAlign: 'center' }}>
              <div style={{ fontSize: '24px', fontWeight: 'bold', color: '#52c41a' }}>
                {stats.passed}
              </div>
              <div style={{ color: '#666' }}>通过</div>
            </div>
            <div style={{ textAlign: 'center' }}>
              <div style={{ fontSize: '24px', fontWeight: 'bold', color: '#ff4d4f' }}>
                {stats.failed}
              </div>
              <div style={{ color: '#666' }}>失败</div>
            </div>
            <div style={{ textAlign: 'center' }}>
              <div style={{ fontSize: '24px', fontWeight: 'bold', color: '#faad14' }}>
                {stats.skipped}
              </div>
              <div style={{ color: '#666' }}>跳过</div>
            </div>
            <div style={{ textAlign: 'center' }}>
              <div style={{ fontSize: '24px', fontWeight: 'bold', color: '#722ed1' }}>
                {stats.successRate}%
              </div>
              <div style={{ color: '#666' }}>成功率</div>
            </div>
          </div>

          {stats.failed > 0 && (
            <Alert
              style={{ marginTop: '16px' }}
              message="发现测试失败"
              description="请检查失败的测试用例，确保功能正常工作"
              type="error"
              showIcon
            />
          )}
        </Card>
      )}

      {/* 详细测试结果 */}
      {testResults.length > 0 && (
        <Card>
          <Title level={4}>详细测试结果</Title>
          <Collapse ghost>
            {testResults.map((suite, suiteIndex) => (
              <Panel
                key={suiteIndex}
                header={
                  <div style={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center' }}>
                    <Space>
                      <Text strong>{suite.name}</Text>
                      <Tag color={suite.failed > 0 ? 'red' : 'green'}>
                        {suite.passed}/{suite.tests.length}
                      </Tag>
                    </Space>
                    <Text type="secondary">{suite.duration}ms</Text>
                  </div>
                }
              >
                <div style={{ paddingLeft: '16px' }}>
                  {suite.tests.map((test, testIndex) => (
                    <div key={testIndex} style={{ marginBottom: '12px' }}>
                      <div style={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center' }}>
                        <Space>
                          {getTestStatusIcon(test.status)}
                          <Text>{test.name}</Text>
                          <Tag color={getTestStatusColor(test.status)} size="small">
                            {test.status}
                          </Tag>
                        </Space>
                        <Text type="secondary">{test.duration}ms</Text>
                      </div>
                      
                      {test.message && (
                        <div style={{ 
                          marginTop: '4px', 
                          marginLeft: '24px',
                          padding: '8px',
                          backgroundColor: test.status === 'fail' ? '#fff2f0' : '#f6ffed',
                          borderRadius: '4px',
                          border: `1px solid ${test.status === 'fail' ? '#ffccc7' : '#d9f7be'}`
                        }}>
                          <Text 
                            type={test.status === 'fail' ? 'danger' : 'secondary'}
                            style={{ fontSize: '12px' }}
                          >
                            {test.message}
                          </Text>
                        </div>
                      )}

                      {test.details && test.status === 'fail' && (
                        <div style={{ 
                          marginTop: '4px', 
                          marginLeft: '24px',
                          padding: '8px',
                          backgroundColor: '#f5f5f5',
                          borderRadius: '4px',
                          fontFamily: 'monospace',
                          fontSize: '11px',
                          maxHeight: '200px',
                          overflow: 'auto'
                        }}>
                          <pre style={{ margin: 0, whiteSpace: 'pre-wrap' }}>
                            {JSON.stringify(test.details, null, 2)}
                          </pre>
                        </div>
                      )}

                      {testIndex < suite.tests.length - 1 && (
                        <Divider style={{ margin: '8px 0' }} />
                      )}
                    </div>
                  ))}
                </div>
              </Panel>
            ))}
          </Collapse>
        </Card>
      )}

      {/* 测试说明 */}
      <Card style={{ marginTop: '24px' }}>
        <Title level={4}>测试说明</Title>
        <div style={{ display: 'grid', gridTemplateColumns: 'repeat(auto-fit, minmax(250px, 1fr))', gap: '16px' }}>
          <div>
            <Title level={5}>基础功能测试</Title>
            <Text type="secondary">
              验证AI分解的核心功能，包括Provider管理、会话创建、统计信息获取等基本操作。
            </Text>
          </div>
          <div>
            <Title level={5}>边界情况测试</Title>
            <Text type="secondary">
              测试异常输入和边界条件，确保系统在各种极端情况下的稳定性和错误处理能力。
            </Text>
          </div>
          <div>
            <Title level={5}>用户场景测试</Title>
            <Text type="secondary">
              模拟真实用户使用场景，验证完整的业务流程和用户体验。
            </Text>
          </div>
          <div>
            <Title level={5}>性能测试</Title>
            <Text type="secondary">
              评估系统性能表现，确保响应时间在可接受范围内。
            </Text>
          </div>
        </div>
      </Card>
    </div>
  );
};

export default TestPage;
