import React, { useState, useEffect, useMemo, useCallback } from 'react';
import { Card, Button, Table, Tag, Space, Modal, Tooltip, Descriptions, Divider, Typography, Input, Select, Form, DatePicker, message } from 'antd';
import { PlusOutlined, EditOutlined, DeleteOutlined, InfoCircleOutlined, EyeOutlined, RobotOutlined, ReloadOutlined, BranchesOutlined, UndoOutlined } from '@ant-design/icons';
import { useSelector, useDispatch } from 'react-redux';
import { RootState, AppDispatch } from '../store/store';
import { Goal, CreateGoalDto } from '../types';
import { loadGoals, createGoalAsync, updateGoalAsync, deleteGoalAsync } from '../store/thunks/goalsThunks';
import GoalFormModal from '../components/GoalFormModal';
import { AIDecompositionConfig } from '../components/AIDecompositionConfig';
import GoalAnalysisDisplay from '../components/GoalAnalysisDisplay';
import AIDecompositionWizard from '../components/AIDecompositionWizard';
import CascadeDeleteConfirmDialog, { DeleteAnalysis } from '../components/CascadeDeleteConfirmDialog';
import SoftDeleteRecoveryPanel from '../components/SoftDeleteRecoveryPanel';
import NavigationService from '../services/NavigationService';
import { GoalCascadeDeleteService } from '../services/GoalCascadeDeleteService';
import type { ColumnsType } from 'antd/es/table';
import dayjs from 'dayjs';
import { DatabaseAPI } from '../services/api';
import { goalBeaconService } from '../services/GoalBeaconService';

// 在开发环境中引入性能测试工具
if (process.env.NODE_ENV === 'development') {
  import('../utils/performanceTest');
}

const { Text, Paragraph } = Typography;
const { TextArea } = Input;
const { Option } = Select;

const Goals: React.FC = () => {
  const dispatch = useDispatch<AppDispatch>();
  const { goals, loading } = useSelector((state: RootState) => state.goals);
  const [isModalVisible, setIsModalVisible] = useState(false);
  const [editingGoal, setEditingGoal] = useState<Goal | null>(null);
  const [goalType, setGoalType] = useState<string>('short-term');
  const [repeatType, setRepeatType] = useState<string>('daily');
  const [customRepeat, setCustomRepeat] = useState<{ interval: number; unit: string; weekdays?: number[] }>({ interval: 1, unit: 'week', weekdays: [] });
  const [searchTerm, setSearchTerm] = useState('');
  const [statusFilter, setStatusFilter] = useState<string>('all');
  const [typeFilter, setTypeFilter] = useState<string>('all');
  const [smartScoreFilter, setSmartScoreFilter] = useState<string>('all');
  const [selectedRowKeys, setSelectedRowKeys] = useState<React.Key[]>([]);
  const [formValues, setFormValues] = useState<Record<string, any>>({});
  const [viewingGoal, setViewingGoal] = useState<Goal | null>(null);
  const [isDetailModalVisible, setIsDetailModalVisible] = useState(false);
  const [form] = Form.useForm();

  // AI分解相关状态
  const [aiDecompositionEnabled, setAIDecompositionEnabled] = useState(false);
  const [aiDecompositionConfig, setAIDecompositionConfig] = useState<AIDecompositionConfig & { isRedecomposition?: boolean }>({
    preferences: {
      maxDepth: 3,
      taskGranularity: 'medium',
      includeTimeEstimates: true,
      maxTaskDuration: 120,
      focusAreas: []
    },
    context: {
      userExperience: 'intermediate',
      availableTime: '每天1-2小时',
      resources: [],
      constraints: []
    }
  });

  // 分解向导状态
  const [isWizardVisible, setIsWizardVisible] = useState(false);
  const [wizardGoal, setWizardGoal] = useState<{
    id: string;
    name: string;
    description: string;
    whyPower: string;
  } | null>(null);

  // 级联删除状态
  const [deleteAnalysis, setDeleteAnalysis] = useState<DeleteAnalysis | null>(null);
  const [isDeleteDialogVisible, setIsDeleteDialogVisible] = useState(false);
  const [deleteLoading, setDeleteLoading] = useState(false);
  const [isRecoveryPanelVisible, setIsRecoveryPanelVisible] = useState(false);
  const [deletingGoalId, setDeletingGoalId] = useState<string | null>(null);

  // 简单的防抖搜索
  const [debouncedSearchTerm, setDebouncedSearchTerm] = useState(searchTerm);

  useEffect(() => {
    const timer = setTimeout(() => {
      setDebouncedSearchTerm(searchTerm);
    }, 300);

    return () => clearTimeout(timer);
  }, [searchTerm]);

  // 组件挂载时加载目标
  useEffect(() => {
    dispatch(loadGoals());
  }, [dispatch]);

  // 当目标列表更新时，设置到目标提醒服务
  useEffect(() => {
    goalBeaconService.setCurrentGoals(goals);
  }, [goals]);

  // 使用useCallback缓存事件处理函数
  const handleAddGoal = useCallback(() => {
    setEditingGoal(null);
    form.resetFields();
    setFormValues({});
    setGoalType('short-term');
    setRepeatType('daily');
    setCustomRepeat({ interval: 1, unit: 'week', weekdays: [] });
    // 重置AI分解状态
    setAIDecompositionEnabled(false);
    setAIDecompositionConfig({
      preferences: {
        maxDepth: 3,
        taskGranularity: 'medium',
        includeTimeEstimates: true,
        maxTaskDuration: 120,
        focusAreas: []
      },
      context: {
        userExperience: 'intermediate',
        availableTime: '每天1-2小时',
        resources: [],
        constraints: []
      }
    });
    setIsModalVisible(true);
  }, [form]);

  const handleEditGoal = useCallback((goal: Goal) => {
    setEditingGoal(goal);
    setGoalType(goal.type);
    if (goal.repeatType) {
      setRepeatType(goal.repeatType);
    }
    if (goal.customRepeat) {
      setCustomRepeat(goal.customRepeat);
    }
    const formData = {
      ...goal,
      startDate: goal.startDate ? dayjs(goal.startDate) : null,
      deadline: goal.deadline ? dayjs(goal.deadline) : null,
    };
    form.setFieldsValue(formData);
    setFormValues(formData);
    setIsModalVisible(true);
  }, [form]);

  const handleFormValuesChange = useCallback((changedValues: any, allValues: any) => {
    // 使用浅比较避免不必要的更新
    setFormValues(prev => {
      const hasChanged = Object.keys(allValues).some(key => prev[key] !== allValues[key]);
      return hasChanged ? allValues : prev;
    });
  }, []);

  // 批量操作处理函数
  const handleBatchStatusChange = useCallback(async (newStatus: Goal['status']) => {
    try {
      const promises = selectedRowKeys.map(id =>
        dispatch(updateGoalAsync({
          id: id as string,
          updates: { status: newStatus }
        }))
      );
      await Promise.all(promises);
      setSelectedRowKeys([]);
    } catch (error) {
      console.error('批量更新状态失败:', error);
    }
  }, [selectedRowKeys, dispatch]);

  const handleBatchDelete = useCallback(async () => {
    try {
      const promises = selectedRowKeys.map(id =>
        dispatch(deleteGoalAsync(id as string))
      );
      await Promise.all(promises);
      setSelectedRowKeys([]);
    } catch (error) {
      console.error('批量删除失败:', error);
    }
  }, [selectedRowKeys, dispatch]);

  const handleViewGoalDetails = useCallback((goal: Goal) => {
    setViewingGoal(goal);
    setIsDetailModalVisible(true);
  }, []);

  // 级联删除相关方法
  const handleDeleteGoal = useCallback(async (goalId: string) => {
    try {
      setDeleteLoading(true);
      setDeletingGoalId(goalId);
      
      // 分析删除影响
      const analysis = await GoalCascadeDeleteService.analyzeGoalDeletion(goalId);
      setDeleteAnalysis(analysis);
      setIsDeleteDialogVisible(true);
    } catch (error) {
      console.error('分析删除影响失败:', error);
      message.error('分析删除影响失败，请稍后重试');
    } finally {
      setDeleteLoading(false);
    }
  }, []);

  const handleConfirmDelete = useCallback(async () => {
    if (!deletingGoalId) return;

    try {
      setDeleteLoading(true);
      
      // 执行级联删除
      const result = await GoalCascadeDeleteService.cascadeDeleteGoal(deletingGoalId);
      
      if (result.success) {
        message.success(`删除成功！共删除了 ${result.deletedItems.goals + result.deletedItems.subGoals + result.deletedItems.milestones + result.deletedItems.tasks} 项数据`);
        
        // 刷新目标列表
        dispatch(loadGoals());
        
        // 关闭对话框
        setIsDeleteDialogVisible(false);
        setDeleteAnalysis(null);
        setDeletingGoalId(null);
      } else {
        throw new Error(result.error || '删除失败');
      }
    } catch (error) {
      console.error('删除失败:', error);
      message.error(error instanceof Error ? error.message : '删除失败，请稍后重试');
    } finally {
      setDeleteLoading(false);
    }
  }, [deletingGoalId, dispatch]);

  const handleCancelDelete = useCallback(() => {
    setIsDeleteDialogVisible(false);
    setDeleteAnalysis(null);
    setDeletingGoalId(null);
  }, []);

  const handleOpenRecoveryPanel = useCallback(() => {
    setIsRecoveryPanelVisible(true);
  }, []);

  const handleCloseRecoveryPanel = useCallback(() => {
    setIsRecoveryPanelVisible(false);
  }, []);

  const handleGoalRestored = useCallback((goalId: string) => {
    message.success('目标恢复成功');
    dispatch(loadGoals()); // 刷新目标列表
  }, [dispatch]);

  // AI分解向导事件处理
  const handleWizardSuccess = useCallback((result: any) => {
    console.log('AI分解成功:', result);
    // 刷新目标列表以显示分解结果
    dispatch(loadGoals());
    setIsWizardVisible(false);
    setWizardGoal(null);
  }, [dispatch]);

  const handleWizardError = useCallback((error: string) => {
    console.error('AI分解失败:', error);
    Modal.error({
      title: 'AI分解失败',
      content: error,
    });
  }, []);

  const handleWizardClose = useCallback(() => {
    setIsWizardVisible(false);
    setWizardGoal(null);
    // 重置为普通分解模式
    setAIDecompositionConfig(prev => ({
      ...prev,
      isRedecomposition: false
    }));
  }, []);

  const handleNavigateToResult = useCallback((goalId: string) => {
    console.log('handleNavigateToResult被调用，goalId:', goalId);
    try {
      const navigationService = NavigationService.getInstance();
      console.log('正在导航到分解结果页面...');
      navigationService.navigateToDecompositionResult(goalId);
      console.log('导航调用完成');
    } catch (error) {
      console.error('导航到分解结果页面失败:', error);
    }
  }, []);

  // AI分解按钮点击处理
  const handleAIDecomposition = useCallback(async (goal: Goal) => {
    console.log('启动AI分解:', goal.name);
    
    // 检查是否有可用的AI Provider
    try {
      const result = await DatabaseAPI.getAvailableAIProviders();
      if (!result.success || !result.providers || result.providers.length === 0) {
        Modal.error({
          title: 'AI服务商未配置',
          content: '请先在设置中配置AI服务商后再使用AI分解功能。',
          onOk: () => {
            // 可以导航到设置页面
            NavigationService.getInstance().navigateTo('settings');
          }
        });
        return;
      }

      // 设置默认的AI Provider（如果还没有设置）
      if (!aiDecompositionConfig.aiProvider && result.providers) {
        const defaultProvider = result.providers.find((p: any) => p.enabled) || result.providers[0];
        setAIDecompositionConfig(prev => ({
          ...prev,
          aiProvider: defaultProvider.id
        }));
      }

      setWizardGoal({
        id: goal.id,
        name: goal.name,
        description: goal.description,
        whyPower: goal.whyPower
      });
      setIsWizardVisible(true);
    } catch (error) {
      console.error('检查AI Provider失败:', error);
      Modal.error({
        title: '检查AI服务商失败',
        content: '无法检查AI服务商配置，请稍后重试。'
      });
    }
  }, [aiDecompositionConfig]);

  // 重新AI分解按钮点击处理
  const handleRedecomposition = useCallback(async (goal: Goal) => {
    console.log('启动重新AI分解:', goal.name);
    
    // 检查是否有可用的AI Provider
    try {
      const result = await DatabaseAPI.getAvailableAIProviders();
      if (!result.success || !result.providers || result.providers.length === 0) {
        Modal.error({
          title: 'AI服务商未配置',
          content: '请先在设置中配置AI服务商后再使用AI分解功能。',
          onOk: () => {
            NavigationService.getInstance().navigateTo('settings');
          }
        });
        return;
      }

      Modal.confirm({
        title: '确认重新分解',
        content: `确定要对目标"${goal.name}"进行重新AI分解吗？这将创建新的分解结果。`,
        onOk: () => {
          // 设置默认的AI Provider（如果还没有设置）
          if (!aiDecompositionConfig.aiProvider && result.providers) {
            const defaultProvider = result.providers.find((p: any) => p.enabled) || result.providers[0];
            setAIDecompositionConfig(prev => ({
              ...prev,
              aiProvider: defaultProvider.id,
              isRedecomposition: true
            }));
          } else {
            setAIDecompositionConfig(prev => ({
              ...prev,
              isRedecomposition: true
            }));
          }

          setWizardGoal({
            id: goal.id,
            name: goal.name,
            description: goal.description,
            whyPower: goal.whyPower
          });
          setIsWizardVisible(true);
        },
      });
    } catch (error) {
      console.error('检查AI Provider失败:', error);
      Modal.error({
        title: '检查AI服务商失败',
        content: '无法检查AI服务商配置，请稍后重试。'
      });
    }
  }, [aiDecompositionConfig]);

  // 过滤和搜索目标
  const filteredGoals = useMemo(() => {
    let filtered = goals;

    // 搜索过滤 - 增强搜索功能，包括领域和关键词
    if (debouncedSearchTerm) {
      filtered = filtered.filter(goal =>
        goal.name.toLowerCase().includes(debouncedSearchTerm.toLowerCase()) ||
        goal.description?.toLowerCase().includes(debouncedSearchTerm.toLowerCase()) ||
        goal.whyPower?.toLowerCase().includes(debouncedSearchTerm.toLowerCase()) ||
        goal.domains?.some(domain => domain.toLowerCase().includes(debouncedSearchTerm.toLowerCase())) ||
        goal.analysis?.keywords?.some(keyword => keyword.toLowerCase().includes(debouncedSearchTerm.toLowerCase()))
      );
    }

    // 状态过滤
    if (statusFilter !== 'all') {
      filtered = filtered.filter(goal => goal.status === statusFilter);
    }

    // 类型过滤
    if (typeFilter !== 'all') {
      filtered = filtered.filter(goal => goal.type === typeFilter);
    }

    // SMART评分过滤
    if (smartScoreFilter !== 'all') {
      filtered = filtered.filter(goal => {
        if (!goal.analysis) return smartScoreFilter === 'unanalyzed';
        const score = goal.analysis.smartScore;
        switch (smartScoreFilter) {
          case 'excellent': return score >= 80;
          case 'good': return score >= 60 && score < 80;
          case 'average': return score >= 40 && score < 60;
          case 'poor': return score < 40;
          case 'unanalyzed': return false;
          default: return true;
        }
      });
    }

    return filtered;
  }, [goals, debouncedSearchTerm, statusFilter, typeFilter, smartScoreFilter]);

  const handleSubmit = async (values: any) => {
    try {
      const goalData = {
        ...values,
        domains: values.domains || [],
        startDate: values.startDate ? values.startDate.toISOString() : undefined,
        deadline: values.deadline ? values.deadline.toISOString() : undefined,
        // 处理重复设置
        repeatType: goalType === 'long-term' ? repeatType : undefined,
        customRepeat: goalType === 'long-term' && repeatType === 'custom' ? customRepeat : undefined,
        // AI分解选项
        enableAIDecomposition: aiDecompositionEnabled,
        aiProvider: aiDecompositionConfig.aiProvider
      };

      if (editingGoal) {
        await dispatch(updateGoalAsync({
          id: editingGoal.id,
          updates: goalData
        })).unwrap();
      } else {
        const result = await dispatch(createGoalAsync(goalData)).unwrap();

        // 如果启用了AI分解，创建目标后立即开始分解
        if (aiDecompositionEnabled && aiDecompositionConfig.aiProvider && result) {
          // 设置向导目标信息并显示向导
          setWizardGoal({
            id: result.id,
            name: result.name,
            description: result.description,
            whyPower: result.whyPower
          });
          setIsWizardVisible(true);
        }
      }

      setIsModalVisible(false);
      form.resetFields();
      setFormValues({});
      setGoalType('short-term');
      setRepeatType('daily');
      setCustomRepeat({ interval: 1, unit: 'week', weekdays: [] });
    } catch (error) {
      console.error('保存目标失败:', error);
    }
  };

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'active':
        return 'green';
      case 'completed':
        return 'blue';
      case 'paused':
        return 'orange';
      case 'cancelled':
        return 'red';
      default:
        return 'default';
    }
  };

  const getStatusText = (status: string) => {
    switch (status) {
      case 'active':
        return '进行中';
      case 'completed':
        return '已完成';
      case 'paused':
        return '已暂停';
      case 'cancelled':
        return '已取消';
      default:
        return status;
    }
  };

  const getTypeText = (type: string) => {
    switch (type) {
      case 'long-term':
        return '长期目标';
      case 'short-term':
        return '短期目标';
      case 'habit':
        return '习惯养成';
      default:
        return type;
    }
  };

  const columns: ColumnsType<Goal> = useMemo(() => [
    {
      title: '目标名称',
      dataIndex: 'name',
      key: 'name',
      width: 200,
      ellipsis: true,
      sorter: (a, b) => a.name.localeCompare(b.name),
      render: (name: string) => (
        <span style={{ fontWeight: 500 }}>{name}</span>
      ),
    },
    {
      title: '描述',
      dataIndex: 'description',
      key: 'description',
      ellipsis: true,
      render: (description?: string) => (
        <span style={{ color: '#666' }}>{description || '-'}</span>
      ),
    },
    {
      title: '类型',
      dataIndex: 'type',
      key: 'type',
      width: 100,
      filters: [
        { text: '长期目标', value: 'long-term' },
        { text: '短期目标', value: 'short-term' },
        { text: '习惯养成', value: 'habit' },
      ],
      onFilter: (value, record) => record.type === value,
      render: (type: string) => (
        <Tag color="blue">{getTypeText(type)}</Tag>
      ),
    },
    {
      title: '状态',
      dataIndex: 'status',
      key: 'status',
      width: 100,
      filters: [
        { text: '进行中', value: 'active' },
        { text: '已完成', value: 'completed' },
        { text: '已暂停', value: 'paused' },
        { text: '已取消', value: 'cancelled' },
      ],
      onFilter: (value, record) => record.status === value,
      render: (status: string) => (
        <Tag color={getStatusColor(status)}>{getStatusText(status)}</Tag>
      ),
    },
    {
      title: '领域',
      dataIndex: 'domains',
      key: 'domains',
      width: 150,
      render: (domains?: string[]) => (
        <Space wrap>
          {domains?.slice(0, 2).map((domain) => (
            <Tag key={domain}>{domain}</Tag>
          ))}
          {domains && domains.length > 2 && (
            <Tag>+{domains.length - 2}</Tag>
          )}
        </Space>
      ),
    },
    {
      title: '截止日期',
      dataIndex: 'deadline',
      key: 'deadline',
      width: 120,
      sorter: (a, b) => {
        if (!a.deadline && !b.deadline) return 0;
        if (!a.deadline) return 1;
        if (!b.deadline) return -1;
        return new Date(a.deadline).getTime() - new Date(b.deadline).getTime();
      },
      render: (deadline?: Date) =>
        deadline ? new Date(deadline).toLocaleDateString() : '-',
    },
    {
      title: 'SMART评分',
      key: 'smartScore',
      width: 100,
      sorter: (a, b) => {
        const scoreA = a.analysis?.smartScore || 0;
        const scoreB = b.analysis?.smartScore || 0;
        return scoreA - scoreB;
      },
      render: (_, record) => {
        if (!record.analysis) {
          return <Tag color="gray">未分析</Tag>;
        }
        const score = record.analysis.smartScore;
        const getColor = () => {
          if (score >= 80) return 'green';
          if (score >= 60) return 'blue';
          if (score >= 40) return 'orange';
          return 'red';
        };
        return (
          <Tooltip title={`SMART原则评分: ${score}分`}>
            <Tag color={getColor()}>{score}分</Tag>
          </Tooltip>
        );
      },
    },
    {
      title: '操作',
      key: 'action',
      width: 200,
      render: (_, record) => (
        <Space size="small">
          <Tooltip title="查看详情">
            <Button
              className="fast-button"
              size="small"
              icon={<EyeOutlined />}
              onClick={() => handleViewGoalDetails(record)}
            />
          </Tooltip>
          <Tooltip title="编辑">
            <Button
              className="fast-button"
              size="small"
              icon={<EditOutlined />}
              onClick={() => handleEditGoal(record)}
            />
          </Tooltip>
          {/* AI分解按钮 - 对所有目标显示 */}
          {!record.hasAIDecomposition ? (
            <Tooltip title="AI分解任务">
              <Button
                className="fast-button"
                size="small"
                icon={<RobotOutlined />}
                onClick={() => handleAIDecomposition(record)}
                style={{ color: '#1890ff' }}
              />
            </Tooltip>
          ) : (
            <Tooltip title="重新AI分解">
              <Button
                className="fast-button"
                size="small"
                icon={<ReloadOutlined />}
                onClick={() => handleRedecomposition(record)}
                style={{ color: '#52c41a' }}
              />
            </Tooltip>
          )}
          <Tooltip title="删除">
            <Button
              className="fast-button"
              size="small"
              icon={<DeleteOutlined />}
              onClick={() => handleDeleteGoal(record.id)}
              danger
            />
          </Tooltip>
        </Space>
      ),
    },
  ], [handleViewGoalDetails, handleEditGoal, handleDeleteGoal, handleAIDecomposition, handleRedecomposition]);

  return (
    <div style={{ padding: '24px' }}>
      <div style={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', marginBottom: 24 }}>
        <h1>目标管理</h1>
        <Space>
          <Button 
            icon={<UndoOutlined />} 
            onClick={handleOpenRecoveryPanel}
            title="回收站"
          >
            回收站
          </Button>
          <Button type="primary" icon={<PlusOutlined />} onClick={handleAddGoal}>
            新建目标
          </Button>
        </Space>
      </div>

      {/* 搜索和过滤区域 */}
      <Card style={{ marginBottom: 16 }}>
        <div style={{ display: 'flex', gap: 16, alignItems: 'center', flexWrap: 'wrap', marginBottom: 16 }}>
          <Input.Search
            placeholder="搜索目标名称、描述、领域、关键词..."
            value={searchTerm}
            onChange={(e) => setSearchTerm(e.target.value)}
            style={{ flex: 1, maxWidth: 400 }}
            allowClear
          />
          <Select
            value={statusFilter}
            onChange={setStatusFilter}
            style={{ width: 120 }}
          >
            <Select.Option value="all">全部状态</Select.Option>
            <Select.Option value="active">进行中</Select.Option>
            <Select.Option value="completed">已完成</Select.Option>
            <Select.Option value="paused">已暂停</Select.Option>
            <Select.Option value="cancelled">已取消</Select.Option>
          </Select>
          <Select
            value={typeFilter}
            onChange={setTypeFilter}
            style={{ width: 120 }}
          >
            <Select.Option value="all">全部类型</Select.Option>
            <Select.Option value="long-term">长期目标</Select.Option>
            <Select.Option value="short-term">短期目标</Select.Option>
            <Select.Option value="habit">习惯养成</Select.Option>
          </Select>
          <Select
            value={smartScoreFilter}
            onChange={setSmartScoreFilter}
            style={{ width: 140 }}
          >
            <Select.Option value="all">全部评分</Select.Option>
            <Select.Option value="excellent">优秀 (80+)</Select.Option>
            <Select.Option value="good">良好 (60-79)</Select.Option>
            <Select.Option value="average">一般 (40-59)</Select.Option>
            <Select.Option value="poor">较差 (40分以下)</Select.Option>
            <Select.Option value="unanalyzed">未分析</Select.Option>
          </Select>
        </div>

        {/* 批量操作区域 */}
        {selectedRowKeys.length > 0 && (
          <div style={{
            padding: '8px 12px',
            backgroundColor: '#e6f7ff',
            borderRadius: '6px',
            display: 'flex',
            justifyContent: 'space-between',
            alignItems: 'center'
          }}>
            <Text>已选择 {selectedRowKeys.length} 个目标</Text>
            <Space>
              <Button size="small" onClick={() => handleBatchStatusChange('completed')}>
                批量完成
              </Button>
              <Button size="small" onClick={() => handleBatchStatusChange('paused')}>
                批量暂停
              </Button>
              <Button size="small" danger onClick={() => handleBatchDelete()}>
                批量删除
              </Button>
              <Button size="small" onClick={() => setSelectedRowKeys([])}>
                取消选择
              </Button>
            </Space>
          </div>
        )}
      </Card>

      <Card>
        <Table
          className="fast-grid"
          columns={columns}
          dataSource={filteredGoals}
          loading={loading}
          rowKey="id"
          rowSelection={{
            selectedRowKeys,
            onChange: setSelectedRowKeys,
            preserveSelectedRowKeys: true,
          }}
          pagination={{
            pageSize: 20,
            showSizeChanger: true,
            showQuickJumper: true,
            showTotal: (total) => `共 ${total} 条记录`,
          }}
          scroll={{ x: 800 }}
        />
      </Card>

      {/* 目标表单Modal */}
      <GoalFormModal
        visible={isModalVisible}
        editingGoal={editingGoal}
        goalType={goalType}
        repeatType={repeatType}
        customRepeat={customRepeat}
        formValues={formValues}
        form={form}
        onCancel={() => {
          setIsModalVisible(false);
          setGoalType('short-term');
          setRepeatType('daily');
          setCustomRepeat({ interval: 1, unit: 'week', weekdays: [] });
          setAIDecompositionEnabled(false);
        }}
        onSubmit={handleSubmit}
        onGoalTypeChange={setGoalType}
        onRepeatTypeChange={setRepeatType}
        onCustomRepeatChange={setCustomRepeat}
        onFormValuesChange={handleFormValuesChange}
        aiDecompositionEnabled={aiDecompositionEnabled}
        aiDecompositionConfig={aiDecompositionConfig}
        onAIDecompositionEnabledChange={setAIDecompositionEnabled}
        onAIDecompositionConfigChange={setAIDecompositionConfig}
        onAIDecomposition={handleAIDecomposition}
        onRedecomposition={handleRedecomposition}
      />


      {/* 目标详情模态框 */}
      <Modal
        title={
          <Space>
            <EyeOutlined />
            <span>目标详情</span>
            {viewingGoal && (
              <Tag color={getStatusColor(viewingGoal.status)}>
                {getStatusText(viewingGoal.status)}
              </Tag>
            )}
          </Space>
        }
        open={isDetailModalVisible}
        onCancel={() => setIsDetailModalVisible(false)}
        footer={[
          <Button key="close" onClick={() => setIsDetailModalVisible(false)}>
            关闭
          </Button>,
          viewingGoal && (
            <Button
              key="edit"
              type="primary"
              icon={<EditOutlined />}
              onClick={() => {
                setIsDetailModalVisible(false);
                handleEditGoal(viewingGoal);
              }}
            >
              编辑目标
            </Button>
          )
        ]}
        width={800}
        style={{ top: 20 }}
      >
        {viewingGoal && (
          <div>
            {/* 基本信息 */}
            <Card size="small" style={{ marginBottom: 16 }}>
              <Descriptions column={2} size="small">
                <Descriptions.Item label="目标名称">
                  <Text strong>{viewingGoal.name}</Text>
                </Descriptions.Item>
                <Descriptions.Item label="目标类型">
                  <Tag color="blue">{getTypeText(viewingGoal.type)}</Tag>
                </Descriptions.Item>
                <Descriptions.Item label="创建时间">
                  {dayjs(viewingGoal.createdAt).format('YYYY-MM-DD HH:mm')}
                </Descriptions.Item>
                <Descriptions.Item label="更新时间">
                  {dayjs(viewingGoal.updatedAt).format('YYYY-MM-DD HH:mm')}
                </Descriptions.Item>
                {viewingGoal.startDate && (
                  <Descriptions.Item label="开始日期">
                    {dayjs(viewingGoal.startDate).format('YYYY-MM-DD')}
                  </Descriptions.Item>
                )}
                {viewingGoal.deadline && (
                  <Descriptions.Item label="截止日期">
                    {dayjs(viewingGoal.deadline).format('YYYY-MM-DD')}
                  </Descriptions.Item>
                )}
              </Descriptions>

              <Divider style={{ margin: '12px 0' }} />

              <div style={{ marginBottom: 12 }}>
                <Text strong>目标描述：</Text>
                <Paragraph style={{ marginTop: 4, marginBottom: 0 }}>
                  {viewingGoal.description}
                </Paragraph>
              </div>

              <div style={{ marginBottom: 12 }}>
                <Text strong>核心驱动力：</Text>
                <Paragraph style={{ marginTop: 4, marginBottom: 0 }}>
                  {viewingGoal.whyPower}
                </Paragraph>
              </div>

              {viewingGoal.domains && viewingGoal.domains.length > 0 && (
                <div>
                  <Text strong>关联领域：</Text>
                  <div style={{ marginTop: 4 }}>
                    {viewingGoal.domains.map(domain => (
                      <Tag key={domain} style={{ margin: '2px' }}>{domain}</Tag>
                    ))}
                  </div>
                </div>
              )}
            </Card>

            {/* 目标分析结果 */}
            {viewingGoal.analysis && (
              <GoalAnalysisDisplay analysis={viewingGoal.analysis} compact={false} />
            )}
          </div>
        )}
      </Modal>

      {/* AI分解向导 */}
      {wizardGoal && (
        <AIDecompositionWizard
          visible={isWizardVisible}
          goalId={wizardGoal.id}
          goalName={wizardGoal.name}
          goalDescription={wizardGoal.description}
          whyPower={wizardGoal.whyPower}
          aiConfig={aiDecompositionConfig}
          onClose={handleWizardClose}
          onSuccess={handleWizardSuccess}
          onError={handleWizardError}
          onNavigateToResult={handleNavigateToResult}
          isRedecomposition={aiDecompositionConfig.isRedecomposition}
        />
      )}

      {/* 级联删除确认对话框 */}
      <CascadeDeleteConfirmDialog
        visible={isDeleteDialogVisible}
        analysis={deleteAnalysis}
        loading={deleteLoading}
        onConfirm={handleConfirmDelete}
        onCancel={handleCancelDelete}
      />

      {/* 软删除恢复面板 */}
      <SoftDeleteRecoveryPanel
        visible={isRecoveryPanelVisible}
        onClose={handleCloseRecoveryPanel}
        onGoalRestored={handleGoalRestored}
      />
    </div>
  );
};

export default Goals;