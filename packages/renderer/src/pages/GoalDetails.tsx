import React, { useState, useEffect } from 'react';
import { useParams, useNavigate } from 'react-router-dom';
import { 
  Card, 
  Button, 
  Space, 
  Typography, 
  Tag, 
  Descriptions, 
  Divider,
  Row,
  Col,
  Timeline,
  Progress,
  Modal,
  Form,
  Input,
  Select,
  message
} from 'antd';
import {
  ArrowLeftOutlined,
  EditOutlined,
  DeleteOutlined,
  CalendarOutlined,
  ClockCircleOutlined,
  TargetOutlined,
  TrophyOutlined,
  RobotOutlined,
  HistoryOutlined,
  ReloadOutlined
} from '@ant-design/icons';
import { useSelector, useDispatch } from 'react-redux';
import { RootState } from '../store/store';
import { Goal } from '../types';
import { updateGoalAsync, deleteGoalAsync } from '../store/thunks/goalsThunks';
import GoalAnalysisDisplay from '../components/GoalAnalysisDisplay';
import RedecompositionModal from '../components/RedecompositionModal';
import DecompositionHistoryModal from '../components/DecompositionHistoryModal';
import AIDecompositionWizard from '../components/AIDecompositionWizard';
import { AIDecompositionConfig } from '../components/AIDecompositionConfig';
import { DatabaseAPI } from '../services/api';
import dayjs from 'dayjs';

const { Title, Text, Paragraph } = Typography;
const { TextArea } = Input;

const GoalDetails: React.FC = () => {
  const { id } = useParams<{ id: string }>();
  const navigate = useNavigate();
  const dispatch = useDispatch();
  const { goals } = useSelector((state: RootState) => state.goals);
  
  const [goal, setGoal] = useState<Goal | null>(null);
  const [isEditModalVisible, setIsEditModalVisible] = useState(false);
  const [form] = Form.useForm();

  // 重新分解相关状态
  const [isRedecompositionModalVisible, setIsRedecompositionModalVisible] = useState(false);
  const [isHistoryModalVisible, setIsHistoryModalVisible] = useState(false);
  const [isWizardVisible, setIsWizardVisible] = useState(false);
  const [redecompositionConfig, setRedecompositionConfig] = useState<AIDecompositionConfig | null>(null);
  const [replacementReason, setReplacementReason] = useState<string>('');
  const [decompositionStructure, setDecompositionStructure] = useState<any>(null);

  useEffect(() => {
    if (id) {
      const foundGoal = goals.find(g => g.id === id);
      setGoal(foundGoal || null);

      // 如果目标有AI分解，加载分解结构
      if (foundGoal?.hasAIDecomposition) {
        loadDecompositionStructure(foundGoal.id);
      }
    }
  }, [id, goals]);

  const loadDecompositionStructure = async (goalId: string) => {
    try {
      const result = await DatabaseAPI.getGoalDecompositionStructure(goalId);
      if (result.success) {
        setDecompositionStructure(result.structure);
      }
    } catch (error) {
      console.error('加载分解结构失败:', error);
    }
  };

  const handleEdit = () => {
    if (goal) {
      form.setFieldsValue({
        name: goal.name,
        description: goal.description,
        whyPower: goal.whyPower,
        domains: goal.domains,
        status: goal.status
      });
      setIsEditModalVisible(true);
    }
  };

  const handleSave = async (values: any) => {
    if (!goal) return;
    
    try {
      await dispatch(updateGoalAsync({
        id: goal.id,
        updates: values
      })).unwrap();
      setIsEditModalVisible(false);
      message.success('目标更新成功');
    } catch (error) {
      message.error('更新失败');
    }
  };

  const handleDelete = () => {
    if (!goal) return;

    Modal.confirm({
      title: '确认删除',
      content: '确定要删除这个目标吗？此操作不可撤销。',
      okText: '删除',
      okType: 'danger',
      cancelText: '取消',
      onOk: async () => {
        try {
          await dispatch(deleteGoalAsync(goal.id)).unwrap();
          message.success('目标删除成功');
          navigate('/goals');
        } catch (error) {
          message.error('删除失败');
        }
      }
    });
  };

  // 重新分解相关处理函数
  const handleRedecomposition = () => {
    setIsRedecompositionModalVisible(true);
  };

  const handleRedecompositionConfirm = (config: AIDecompositionConfig, reason: string) => {
    setRedecompositionConfig(config);
    setReplacementReason(reason);
    setIsRedecompositionModalVisible(false);
    setIsWizardVisible(true);
  };

  const handleWizardSuccess = (result: any) => {
    message.success('重新分解完成！');
    setIsWizardVisible(false);
    // 重新加载分解结构
    if (goal) {
      loadDecompositionStructure(goal.id);
    }
  };

  const handleWizardError = (error: string) => {
    message.error('重新分解失败: ' + error);
  };

  const handleViewHistory = () => {
    setIsHistoryModalVisible(true);
  };

  const handleHistoryRollback = (sessionId: string) => {
    message.success('已回滚到指定版本');
    // 重新加载分解结构
    if (goal) {
      loadDecompositionStructure(goal.id);
    }
  };

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'active': return 'green';
      case 'completed': return 'blue';
      case 'paused': return 'orange';
      case 'cancelled': return 'red';
      default: return 'default';
    }
  };

  const getStatusText = (status: string) => {
    switch (status) {
      case 'active': return '进行中';
      case 'completed': return '已完成';
      case 'paused': return '已暂停';
      case 'cancelled': return '已取消';
      default: return status;
    }
  };

  const getTypeText = (type: string) => {
    switch (type) {
      case 'long-term': return '长期目标';
      case 'short-term': return '短期目标';
      case 'habit': return '习惯养成';
      default: return type;
    }
  };

  if (!goal) {
    return (
      <div style={{ padding: 24, textAlign: 'center' }}>
        <Text>目标不存在或已被删除</Text>
        <br />
        <Button type="primary" onClick={() => navigate('/goals')}>
          返回目标列表
        </Button>
      </div>
    );
  }

  return (
    <div style={{ padding: 24 }}>
      {/* 头部导航 */}
      <div style={{ marginBottom: 24 }}>
        <Space>
          <Button 
            icon={<ArrowLeftOutlined />} 
            onClick={() => navigate('/goals')}
          >
            返回目标列表
          </Button>
          <Title level={2} style={{ margin: 0 }}>
            {goal.name}
          </Title>
        </Space>
      </div>

      <Row gutter={24}>
        <Col span={16}>
          {/* 基本信息 */}
          <Card
            title="基本信息"
            extra={
              <Space>
                <Button icon={<EditOutlined />} onClick={handleEdit}>
                  编辑
                </Button>
                <Button danger icon={<DeleteOutlined />} onClick={handleDelete}>
                  删除
                </Button>
              </Space>
            }
            style={{ marginBottom: 24 }}
          >
            <Descriptions column={2}>
              <Descriptions.Item label="目标类型">
                <Tag color="blue">{getTypeText(goal.type)}</Tag>
              </Descriptions.Item>
              <Descriptions.Item label="状态">
                <Tag color={getStatusColor(goal.status)}>
                  {getStatusText(goal.status)}
                </Tag>
              </Descriptions.Item>
              <Descriptions.Item label="创建时间">
                {dayjs(goal.createdAt).format('YYYY-MM-DD HH:mm')}
              </Descriptions.Item>
              <Descriptions.Item label="更新时间">
                {dayjs(goal.updatedAt).format('YYYY-MM-DD HH:mm')}
              </Descriptions.Item>
              {goal.startDate && (
                <Descriptions.Item label="开始日期">
                  {dayjs(goal.startDate).format('YYYY-MM-DD')}
                </Descriptions.Item>
              )}
              {goal.deadline && (
                <Descriptions.Item label="截止日期">
                  {dayjs(goal.deadline).format('YYYY-MM-DD')}
                </Descriptions.Item>
              )}
            </Descriptions>

            <Divider />

            <div style={{ marginBottom: 16 }}>
              <Text strong>目标描述：</Text>
              <Paragraph style={{ marginTop: 8 }}>
                {goal.description}
              </Paragraph>
            </div>

            <div style={{ marginBottom: 16 }}>
              <Text strong>核心驱动力：</Text>
              <Paragraph style={{ marginTop: 8 }}>
                {goal.whyPower}
              </Paragraph>
            </div>

            {goal.domains && goal.domains.length > 0 && (
              <div>
                <Text strong>关联领域：</Text>
                <div style={{ marginTop: 8 }}>
                  {goal.domains.map(domain => (
                    <Tag key={domain} style={{ margin: '2px' }}>{domain}</Tag>
                  ))}
                </div>
              </div>
            )}
          </Card>

          {/* AI分解状态 */}
          <Card
            title={
              <Space>
                <RobotOutlined />
                <span>AI分解状态</span>
              </Space>
            }
            style={{ marginBottom: 24 }}
          >
            {goal.hasAIDecomposition ? (
              <div>
                <div style={{ marginBottom: 16 }}>
                  <Space>
                    <Tag color="green">已分解</Tag>
                    {decompositionStructure && (
                      <>
                        <Text>子目标: {decompositionStructure.subGoals?.length || 0}个</Text>
                        <Text>总任务: {decompositionStructure.totalTasks || 0}个</Text>
                        <Text>预计时长: {decompositionStructure.estimatedTotalTime || 0}小时</Text>
                      </>
                    )}
                  </Space>
                </div>

                <Space>
                  <Button
                    type="primary"
                    icon={<ReloadOutlined />}
                    onClick={handleRedecomposition}
                  >
                    重新AI分解
                  </Button>
                  <Button
                    icon={<HistoryOutlined />}
                    onClick={handleViewHistory}
                  >
                    查看分解历史
                  </Button>
                  <Button
                    onClick={() => navigate(`/decomposition-result/${goal.id}`)}
                  >
                    查看分解结果
                  </Button>
                </Space>
              </div>
            ) : (
              <div>
                <Text type="secondary">该目标尚未进行AI分解</Text>
                <br />
                <Button
                  type="primary"
                  icon={<RobotOutlined />}
                  style={{ marginTop: 12 }}
                  onClick={() => navigate(`/goals?redecompose=${goal.id}`)}
                >
                  开始AI分解
                </Button>
              </div>
            )}
          </Card>

          {/* 目标分析结果 */}
          {goal.analysis && (
            <GoalAnalysisDisplay analysis={goal.analysis} />
          )}
        </Col>

        <Col span={8}>
          {/* 进度概览 */}
          <Card title="进度概览" style={{ marginBottom: 24 }}>
            <div style={{ textAlign: 'center', marginBottom: 16 }}>
              <Progress
                type="circle"
                percent={goal.status === 'completed' ? 100 : 0}
                format={() => goal.status === 'completed' ? '已完成' : '进行中'}
              />
            </div>
            
            <Descriptions column={1} size="small">
              <Descriptions.Item label="目标状态">
                {getStatusText(goal.status)}
              </Descriptions.Item>
              {goal.analysis && (
                <Descriptions.Item label="SMART评分">
                  {goal.analysis.smartScore}分
                </Descriptions.Item>
              )}
            </Descriptions>
          </Card>

          {/* 时间线 */}
          <Card title="目标历史" style={{ marginBottom: 24 }}>
            <Timeline
              items={[
                {
                  children: `创建目标 - ${dayjs(goal.createdAt).format('MM-DD HH:mm')}`,
                  color: 'blue'
                },
                {
                  children: `最后更新 - ${dayjs(goal.updatedAt).format('MM-DD HH:mm')}`,
                  color: 'green'
                }
              ]}
            />
          </Card>
        </Col>
      </Row>

      {/* 编辑模态框 */}
      <Modal
        title="编辑目标"
        open={isEditModalVisible}
        onCancel={() => setIsEditModalVisible(false)}
        onOk={() => form.submit()}
        width={600}
      >
        <Form
          form={form}
          layout="vertical"
          onFinish={handleSave}
        >
          <Form.Item
            name="name"
            label="目标名称"
            rules={[{ required: true, message: '请输入目标名称' }]}
          >
            <Input />
          </Form.Item>
          
          <Form.Item
            name="description"
            label="目标描述"
            rules={[{ required: true, message: '请输入目标描述' }]}
          >
            <TextArea rows={3} />
          </Form.Item>
          
          <Form.Item
            name="whyPower"
            label="核心驱动力"
            rules={[{ required: true, message: '请输入核心驱动力' }]}
          >
            <TextArea rows={3} />
          </Form.Item>
          
          <Form.Item name="domains" label="关联领域">
            <Select mode="tags" placeholder="输入相关领域标签" />
          </Form.Item>
          
          <Form.Item name="status" label="状态">
            <Select>
              <Select.Option value="active">进行中</Select.Option>
              <Select.Option value="completed">已完成</Select.Option>
              <Select.Option value="paused">已暂停</Select.Option>
              <Select.Option value="cancelled">已取消</Select.Option>
            </Select>
          </Form.Item>
        </Form>
      </Modal>

      {/* 重新分解配置模态框 */}
      <RedecompositionModal
        visible={isRedecompositionModalVisible}
        goal={goal}
        onCancel={() => setIsRedecompositionModalVisible(false)}
        onConfirm={handleRedecompositionConfirm}
      />

      {/* 分解历史模态框 */}
      <DecompositionHistoryModal
        visible={isHistoryModalVisible}
        goalId={goal?.id || null}
        goalName={goal?.name || ''}
        onClose={() => setIsHistoryModalVisible(false)}
        onRollback={handleHistoryRollback}
      />

      {/* AI分解向导 */}
      {isWizardVisible && goal && redecompositionConfig && (
        <AIDecompositionWizard
          visible={isWizardVisible}
          goalId={goal.id}
          goalName={goal.name}
          goalDescription={goal.description}
          whyPower={goal.whyPower}
          aiConfig={redecompositionConfig}
          onClose={() => setIsWizardVisible(false)}
          onSuccess={handleWizardSuccess}
          onError={handleWizardError}
          onNavigateToResult={(goalId) => navigate(`/decomposition-result/${goalId}`)}
          isRedecomposition={true}
          replacementReason={replacementReason}
          replaceExisting={true}
        />
      )}
    </div>
  );
};

export default GoalDetails;
