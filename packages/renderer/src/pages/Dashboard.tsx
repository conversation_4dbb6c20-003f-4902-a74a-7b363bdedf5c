import React from 'react';
import { Card, Row, Col, Statistic, Progress } from 'antd';
import { CheckCircleOutlined, ClockCircleOutlined, FlagOutlined } from '@ant-design/icons';

const Dashboard: React.FC = () => {
  return (
    <div
      className="lazy-content stable-layout"
      style={{
        width: '100%',
        minHeight: '100%',
        overflow: 'visible' // 允许内容溢出，由父容器处理滚动
      }}
    >
      <h1 className="fast-text">仪表盘</h1>
      <Row className="fast-grid" gutter={[16, 16]}>
        <Col xs={24} sm={12} md={8}>
          <Card className="ultra-fast-card">
            <Statistic
              title="今日完成任务"
              value={5}
              prefix={<CheckCircleOutlined />}
              valueStyle={{ color: 'var(--color-success)' }}
            />
          </Card>
        </Col>
        <Col xs={24} sm={12} md={8}>
          <Card className="ultra-fast-card">
            <Statistic
              title="今日专注时长"
              value={120}
              suffix="分钟"
              prefix={<ClockCircleOutlined />}
              valueStyle={{ color: 'var(--color-info)' }}
            />
          </Card>
        </Col>
        <Col xs={24} sm={12} md={8}>
          <Card className="ultra-fast-card">
            <Statistic
              title="活跃目标"
              value={3}
              prefix={<FlagOutlined />}
              valueStyle={{ color: 'var(--color-error)' }}
            />
          </Card>
        </Col>
      </Row>

      <Row className="fast-grid" gutter={[16, 16]} style={{ marginTop: 24 }}>
        <Col xs={24} md={12}>
          <Card className="ultra-fast-card" title="今日进度">
            <Progress
              percent={65}
              status="active"
              strokeColor="var(--color-primary)"
            />
            <p style={{ marginTop: 16, color: 'var(--color-text-secondary)' }}>
              已完成 5/8 个计划任务
            </p>
          </Card>
        </Col>
        <Col xs={24} md={12}>
          <Card className="ultra-fast-card" title="专注效率">
            <Progress
              type="circle"
              percent={85}
              format={(percent) => `${percent}%`}
              strokeColor="var(--color-success)"
            />
            <p style={{ marginTop: 16, textAlign: 'center', color: 'var(--color-text-secondary)' }}>
              今日专注效率良好
            </p>
          </Card>
        </Col>
      </Row>
    </div>
  );
};

export default Dashboard;