import React, { useState, useEffect } from 'react';
import {
  Card,
  Button,
  Space,
  Typography,
  Row,
  Col,
  Alert,
  Divider,
  Tag,
  Progress
} from 'antd';
import {
  PlayCircleOutlined,
  PauseCircleOutlined,
  StopOutlined,
  SoundOutlined,
  CheckCircleOutlined,
  ExclamationCircleOutlined
} from '@ant-design/icons';
import { useTheme } from '../contexts/ThemeContext';
import { 
  backgroundAudioService, 
  BackgroundSoundType 
} from '../services/BackgroundAudioService';
import BackgroundAudioSettings from '../components/BackgroundAudioSettings';
import BackgroundAudioControl from '../components/BackgroundAudioControl';

const { Title, Text, Paragraph } = Typography;

const AudioTest: React.FC = () => {
  const { theme } = useTheme();
  const [isPlaying, setIsPlaying] = useState(false);
  const [currentSound, setCurrentSound] = useState<BackgroundSoundType>('none');
  const [testResults, setTestResults] = useState<Record<string, boolean>>({});
  const [isTestingAll, setIsTestingAll] = useState(false);
  const [testProgress, setTestProgress] = useState(0);

  useEffect(() => {
    // 初始化状态
    setIsPlaying(backgroundAudioService.isCurrentlyPlaying());
    setCurrentSound(backgroundAudioService.getCurrentSoundType());
  }, []);

  const soundConfigs = backgroundAudioService.getSoundConfigs();

  const testSingleSound = async (soundType: BackgroundSoundType) => {
    if (soundType === 'none') return;

    try {
      await backgroundAudioService.startBackgroundSound(soundType);
      setIsPlaying(true);
      setCurrentSound(soundType);
      
      // 播放3秒后自动停止
      setTimeout(async () => {
        await backgroundAudioService.stopBackgroundSound();
        setIsPlaying(false);
        setCurrentSound('none');
        
        // 记录测试结果
        setTestResults(prev => ({ ...prev, [soundType]: true }));
      }, 3000);
      
    } catch (error) {
      console.error(`测试音效 ${soundType} 失败:`, error);
      setTestResults(prev => ({ ...prev, [soundType]: false }));
    }
  };

  const testAllSounds = async () => {
    setIsTestingAll(true);
    setTestResults({});
    setTestProgress(0);

    const soundTypes = Object.keys(soundConfigs).filter(type => type !== 'none') as BackgroundSoundType[];
    
    for (let i = 0; i < soundTypes.length; i++) {
      const soundType = soundTypes[i];
      
      try {
        await backgroundAudioService.startBackgroundSound(soundType);
        setCurrentSound(soundType);
        
        // 播放2秒
        await new Promise(resolve => setTimeout(resolve, 2000));
        
        await backgroundAudioService.stopBackgroundSound();
        setTestResults(prev => ({ ...prev, [soundType]: true }));
        
      } catch (error) {
        console.error(`测试音效 ${soundType} 失败:`, error);
        setTestResults(prev => ({ ...prev, [soundType]: false }));
      }
      
      setTestProgress(((i + 1) / soundTypes.length) * 100);
    }
    
    setIsTestingAll(false);
    setCurrentSound('none');
  };

  const stopCurrentSound = async () => {
    await backgroundAudioService.stopBackgroundSound();
    setIsPlaying(false);
    setCurrentSound('none');
  };

  const getTestResultIcon = (soundType: string) => {
    if (!(soundType in testResults)) return null;
    
    return testResults[soundType] ? (
      <CheckCircleOutlined style={{ color: '#52c41a' }} />
    ) : (
      <ExclamationCircleOutlined style={{ color: '#ff4d4f' }} />
    );
  };

  const getTestResultText = (soundType: string) => {
    if (!(soundType in testResults)) return '未测试';
    return testResults[soundType] ? '成功' : '失败';
  };

  const getTestResultColor = (soundType: string) => {
    if (!(soundType in testResults)) return 'default';
    return testResults[soundType] ? 'success' : 'error';
  };

  return (
    <div style={{ padding: '24px', maxWidth: '1200px', margin: '0 auto' }}>
      <div style={{ marginBottom: '24px' }}>
        <Title level={2}>
          <SoundOutlined style={{ marginRight: '8px' }} />
          背景音频功能测试
        </Title>
        <Paragraph type="secondary">
          测试 FocusOS 背景音频功能的各项特性，确保音效播放正常。
        </Paragraph>
      </div>

      {/* 快速测试区域 */}
      <Card title="快速测试" style={{ marginBottom: '24px' }}>
        <Space direction="vertical" style={{ width: '100%' }}>
          <Alert
            message="测试说明"
            description="点击下方按钮测试各项音频功能。每个音效会播放几秒钟后自动停止。"
            type="info"
            showIcon
            style={{ marginBottom: '16px' }}
          />
          
          <Space wrap>
            <Button
              type="primary"
              icon={<PlayCircleOutlined />}
              onClick={testAllSounds}
              loading={isTestingAll}
              disabled={isPlaying}
            >
              测试所有音效
            </Button>
            
            <Button
              icon={<StopOutlined />}
              onClick={stopCurrentSound}
              disabled={!isPlaying}
            >
              停止播放
            </Button>
          </Space>
          
          {isTestingAll && (
            <div style={{ marginTop: '16px' }}>
              <Text>测试进度:</Text>
              <Progress 
                percent={testProgress} 
                status="active"
                style={{ marginTop: '8px' }}
              />
              {currentSound !== 'none' && (
                <Text type="secondary" style={{ display: 'block', marginTop: '4px' }}>
                  正在测试: {soundConfigs[currentSound]?.name}
                </Text>
              )}
            </div>
          )}
        </Space>
      </Card>

      {/* 单独音效测试 */}
      <Card title="单独音效测试" style={{ marginBottom: '24px' }}>
        <Row gutter={[16, 16]}>
          {Object.entries(soundConfigs).map(([soundType, config]) => {
            if (soundType === 'none') return null;
            
            return (
              <Col key={soundType} xs={12} sm={8} md={6} lg={4}>
                <Card
                  size="small"
                  hoverable
                  style={{
                    textAlign: 'center',
                    border: currentSound === soundType ? `2px solid ${theme.colors.primary}` : '1px solid #d9d9d9'
                  }}
                  actions={[
                    <Button
                      key="test"
                      type="text"
                      icon={<PlayCircleOutlined />}
                      onClick={() => testSingleSound(soundType as BackgroundSoundType)}
                      disabled={isPlaying || isTestingAll}
                      size="small"
                    >
                      测试
                    </Button>
                  ]}
                >
                  <div style={{ marginBottom: '8px', fontSize: '24px' }}>
                    {config.icon}
                  </div>
                  <div style={{ marginBottom: '4px' }}>
                    <Text strong>{config.name}</Text>
                  </div>
                  <div style={{ marginBottom: '8px' }}>
                    <Text type="secondary" style={{ fontSize: '12px' }}>
                      {config.description}
                    </Text>
                  </div>
                  <div style={{ display: 'flex', justifyContent: 'center', alignItems: 'center', gap: '4px' }}>
                    {getTestResultIcon(soundType)}
                    <Tag color={getTestResultColor(soundType)} size="small">
                      {getTestResultText(soundType)}
                    </Tag>
                  </div>
                </Card>
              </Col>
            );
          })}
        </Row>
      </Card>

      {/* 控制组件测试 */}
      <Row gutter={[24, 24]}>
        <Col xs={24} lg={12}>
          <Card title="嵌入式控制组件">
            <BackgroundAudioControl embedded={true} />
          </Card>
        </Col>
        
        <Col xs={24} lg={12}>
          <Card title="完整控制组件">
            <BackgroundAudioControl embedded={false} />
          </Card>
        </Col>
      </Row>

      <Divider />

      {/* 完整设置界面 */}
      <Card title="完整设置界面" style={{ marginTop: '24px' }}>
        <BackgroundAudioSettings />
      </Card>

      {/* 测试结果总结 */}
      {Object.keys(testResults).length > 0 && (
        <Card title="测试结果总结" style={{ marginTop: '24px' }}>
          <Row gutter={[16, 16]}>
            <Col span={8}>
              <Card size="small" style={{ textAlign: 'center' }}>
                <Text strong>总测试数</Text>
                <div style={{ fontSize: '24px', color: theme.colors.primary, marginTop: '8px' }}>
                  {Object.keys(testResults).length}
                </div>
              </Card>
            </Col>
            <Col span={8}>
              <Card size="small" style={{ textAlign: 'center' }}>
                <Text strong>成功数</Text>
                <div style={{ fontSize: '24px', color: '#52c41a', marginTop: '8px' }}>
                  {Object.values(testResults).filter(Boolean).length}
                </div>
              </Card>
            </Col>
            <Col span={8}>
              <Card size="small" style={{ textAlign: 'center' }}>
                <Text strong>失败数</Text>
                <div style={{ fontSize: '24px', color: '#ff4d4f', marginTop: '8px' }}>
                  {Object.values(testResults).filter(result => !result).length}
                </div>
              </Card>
            </Col>
          </Row>
          
          {Object.values(testResults).some(result => !result) && (
            <Alert
              message="部分音效测试失败"
              description="这可能是因为音频文件不存在或浏览器不支持。系统会自动使用合成音效作为备用方案。"
              type="warning"
              showIcon
              style={{ marginTop: '16px' }}
            />
          )}
        </Card>
      )}
    </div>
  );
};

export default AudioTest;
