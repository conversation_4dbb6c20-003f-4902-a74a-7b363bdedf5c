import React, { useState, useEffect } from 'react';
import { Card, Button, Table, Tag, Space, Modal, Input, Select } from 'antd';
import { PlusOutlined, EditOutlined, DeleteOutlined, EyeOutlined } from '@ant-design/icons';
import { useSelector, useDispatch } from 'react-redux';
import { RootState } from '../store/store';
import { loadGoals, createGoalAsync, updateGoalAsync, deleteGoalAsync } from '../store/thunks/goalsThunks';

const GoalsTest: React.FC = () => {
  const dispatch = useDispatch();
  const { goals, loading } = useSelector((state: RootState) => state.goals);
  const [isModalVisible, setIsModalVisible] = useState(false);

  // 组件挂载时加载目标
  useEffect(() => {
    dispatch(loadGoals());
  }, [dispatch]);

  const handleAddGoal = () => {
    setIsModalVisible(true);
  };

  const columns = [
    {
      title: '目标名称',
      dataIndex: 'name',
      key: 'name',
      render: (name: string) => (
        <span style={{ fontWeight: 500 }}>{name}</span>
      ),
    },
    {
      title: '描述',
      dataIndex: 'description',
      key: 'description',
      render: (description?: string) => (
        <span style={{ color: '#666' }}>{description || '-'}</span>
      ),
    },
    {
      title: '类型',
      dataIndex: 'type',
      key: 'type',
      render: (type: string) => (
        <Tag color="blue">{type}</Tag>
      ),
    },
    {
      title: '状态',
      dataIndex: 'status',
      key: 'status',
      render: (status: string) => (
        <Tag color="green">{status}</Tag>
      ),
    },
    {
      title: '操作',
      key: 'action',
      render: (_, record) => (
        <Space size="small">
          <Button size="small" icon={<EyeOutlined />} />
          <Button size="small" icon={<EditOutlined />} />
          <Button size="small" icon={<DeleteOutlined />} danger />
        </Space>
      ),
    },
  ];

  return (
    <div style={{ padding: '24px' }}>
      <div style={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', marginBottom: 24 }}>
        <h1>目标管理（测试版）</h1>
        <Button type="primary" icon={<PlusOutlined />} onClick={handleAddGoal}>
          新建目标
        </Button>
      </div>

      <Card>
        <Table
          columns={columns}
          dataSource={goals}
          loading={loading}
          rowKey="id"
          pagination={{
            pageSize: 10,
            showTotal: (total) => `共 ${total} 条记录`,
          }}
        />
      </Card>

      <Modal
        title="新建目标"
        open={isModalVisible}
        onCancel={() => setIsModalVisible(false)}
        onOk={() => setIsModalVisible(false)}
        width={600}
      >
        <p>这是一个简化的测试版本</p>
      </Modal>
    </div>
  );
};

export default GoalsTest;
