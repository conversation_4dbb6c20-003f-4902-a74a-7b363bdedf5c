import React, { useState, useEffect } from 'react';
import {
  Card,
  Button,
  Space,
  Typography,
  Alert,
  List,
  Tag,
  Statistic,
  Row,
  Col,
  Divider,
  Steps,
  Result
} from 'antd';
import {
  PlayCircleOutlined,
  CheckCircleOutlined,
  ExclamationCircleOutlined,
  ReloadOutlined,
  BugOutlined,
  SafetyOutlined
} from '@ant-design/icons';
import { useTheme } from '../contexts/ThemeContext';
import { applicationMonitorService } from '../services/ApplicationMonitorService';
import { blacklistManagerService, ViolationRecord } from '../services/BlacklistManagerService';

const { Title, Text, Paragraph } = Typography;
const { Step } = Steps;

const FocusShieldTest: React.FC = () => {
  const { theme } = useTheme();
  const [isTestRunning, setIsTestRunning] = useState(false);
  const [testResults, setTestResults] = useState<{
    step: number;
    violations: ViolationRecord[];
    success: boolean;
    error?: string;
  }>({
    step: 0,
    violations: [],
    success: false
  });

  const [violationHistory, setViolationHistory] = useState<ViolationRecord[]>([]);

  useEffect(() => {
    loadViolationHistory();
    
    // 定期刷新违规记录
    const interval = setInterval(loadViolationHistory, 2000);
    return () => clearInterval(interval);
  }, []);

  const loadViolationHistory = () => {
    const violations = blacklistManagerService.getViolationHistory(1) || [];
    setViolationHistory(violations);
  };

  const runFullTest = async () => {
    setIsTestRunning(true);
    setTestResults({ step: 0, violations: [], success: false });

    try {
      // 步骤1：检查黑名单规则
      setTestResults(prev => ({ ...prev, step: 1 }));
      await new Promise(resolve => setTimeout(resolve, 500));

      const blacklistRules = blacklistManagerService.getBlacklistRules();
      const xcomRule = blacklistRules.find(rule => 
        rule.pattern.includes('x.com') && rule.type === 'website'
      );

      if (!xcomRule) {
        throw new Error('未找到 x.com 黑名单规则');
      }

      // 步骤2：清除旧的违规记录
      setTestResults(prev => ({ ...prev, step: 2 }));
      await new Promise(resolve => setTimeout(resolve, 500));

      // 步骤3：模拟访问 x.com
      setTestResults(prev => ({ ...prev, step: 3 }));
      console.log('开始模拟访问 x.com...');
      await applicationMonitorService.testXcomAccess();

      // 步骤4：检查违规记录
      setTestResults(prev => ({ ...prev, step: 4 }));
      await new Promise(resolve => setTimeout(resolve, 1000));

      const newViolations = blacklistManagerService.getViolationHistory(1);
      const xcomViolations = newViolations.filter(v => 
        v.target.includes('x.com') || v.target.includes('X (formerly Twitter)')
      );

      if (xcomViolations.length === 0) {
        throw new Error('未检测到 x.com 访问违规记录');
      }

      // 测试成功
      setTestResults({
        step: 5,
        violations: xcomViolations,
        success: true
      });

    } catch (error) {
      console.error('测试失败:', error);
      setTestResults(prev => ({
        ...prev,
        success: false,
        error: error instanceof Error ? error.message : '未知错误'
      }));
    } finally {
      setIsTestRunning(false);
    }
  };

  const testSteps = [
    {
      title: '检查黑名单规则',
      description: '验证 x.com 规则是否存在'
    },
    {
      title: '准备测试环境',
      description: '清理旧数据，准备测试'
    },
    {
      title: '模拟网站访问',
      description: '模拟访问 x.com 网站'
    },
    {
      title: '检查违规检测',
      description: '验证是否正确检测到违规行为'
    },
    {
      title: '测试完成',
      description: '分析测试结果'
    }
  ];

  const getStepStatus = (stepIndex: number) => {
    if (testResults.step > stepIndex) return 'finish';
    if (testResults.step === stepIndex) return isTestRunning ? 'process' : 'wait';
    return 'wait';
  };

  return (
    <div style={{ padding: '24px', maxWidth: '1200px', margin: '0 auto' }}>
      <div style={{ marginBottom: '24px' }}>
        <Title level={2}>
          <SafetyOutlined style={{ marginRight: '8px' }} />
          Focus Shield 功能测试
        </Title>
        <Paragraph type="secondary">
          测试智能专注力监控系统的网站访问检测和违规记录功能。
        </Paragraph>
      </div>

      {/* 测试控制面板 */}
      <Card title="测试控制" style={{ marginBottom: '24px' }}>
        <Space direction="vertical" style={{ width: '100%' }}>
          <Alert
            message="测试说明"
            description="此测试将模拟访问 x.com 网站，验证 Focus Shield 是否能正确检测并记录违规行为。"
            type="info"
            showIcon
          />
          
          <Space>
            <Button
              type="primary"
              icon={<PlayCircleOutlined />}
              onClick={runFullTest}
              loading={isTestRunning}
              size="large"
            >
              开始完整测试
            </Button>
            
            <Button
              icon={<ReloadOutlined />}
              onClick={loadViolationHistory}
            >
              刷新数据
            </Button>
          </Space>
        </Space>
      </Card>

      {/* 测试步骤 */}
      <Card title="测试进度" style={{ marginBottom: '24px' }}>
        <Steps
          current={testResults.step}
          status={testResults.error ? 'error' : 'process'}
          direction="vertical"
        >
          {testSteps.map((step, index) => (
            <Step
              key={index}
              title={step.title}
              description={step.description}
              status={getStepStatus(index)}
            />
          ))}
        </Steps>
      </Card>

      {/* 测试结果 */}
      {(testResults.success || testResults.error) && (
        <Card title="测试结果" style={{ marginBottom: '24px' }}>
          {testResults.success ? (
            <Result
              status="success"
              title="测试通过！"
              subTitle="Focus Shield 网站监控功能正常工作"
              extra={
                <div>
                  <Text>检测到 {testResults.violations.length} 条违规记录</Text>
                </div>
              }
            />
          ) : (
            <Result
              status="error"
              title="测试失败"
              subTitle={testResults.error}
              extra={
                <Button type="primary" onClick={runFullTest}>
                  重新测试
                </Button>
              }
            />
          )}
        </Card>
      )}

      {/* 统计信息 */}
      <Row gutter={[16, 16]} style={{ marginBottom: '24px' }}>
        <Col span={8}>
          <Card>
            <Statistic
              title="黑名单规则数"
              value={blacklistManagerService.getBlacklistRules().length}
              prefix={<SafetyOutlined />}
            />
          </Card>
        </Col>
        <Col span={8}>
          <Card>
            <Statistic
              title="今日违规记录"
              value={violationHistory.filter(v => {
                const today = new Date().toDateString();
                const violationDate = new Date(v.timestamp).toDateString();
                return violationDate === today;
              }).length}
              prefix={<ExclamationCircleOutlined />}
            />
          </Card>
        </Col>
        <Col span={8}>
          <Card>
            <Statistic
              title="总违规记录"
              value={violationHistory.length}
              prefix={<BugOutlined />}
            />
          </Card>
        </Col>
      </Row>

      {/* 违规记录列表 */}
      <Card title="最近违规记录">
        {violationHistory.length > 0 ? (
          <List
            dataSource={violationHistory.slice(0, 10)}
            renderItem={(record) => (
              <List.Item key={record.id}>
                <div style={{ width: '100%' }}>
                  <div style={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center' }}>
                    <div>
                      <Text strong>{record.target}</Text>
                      <br />
                      <Text type="secondary" style={{ fontSize: '12px' }}>
                        规则: {record.ruleName} | 
                        类型: {record.type === 'app' ? '应用' : '网站'} | 
                        操作: {record.action === 'blocked' ? '已阻止' : record.action === 'warned' ? '已警告' : '已允许'}
                      </Text>
                    </div>
                    <div style={{ textAlign: 'right' }}>
                      <Tag color={record.action === 'blocked' ? 'red' : record.action === 'warned' ? 'orange' : 'green'}>
                        {record.action}
                      </Tag>
                      <br />
                      <Text type="secondary" style={{ fontSize: '12px' }}>
                        {new Date(record.timestamp).toLocaleString()}
                      </Text>
                    </div>
                  </div>
                </div>
              </List.Item>
            )}
          />
        ) : (
          <div style={{ textAlign: 'center', padding: '40px', color: '#999' }}>
            <ExclamationCircleOutlined style={{ fontSize: '48px', marginBottom: '16px' }} />
            <div>暂无违规记录</div>
            <div style={{ marginTop: '8px' }}>
              <Text type="secondary">点击上方"开始完整测试"按钮进行测试</Text>
            </div>
          </div>
        )}
      </Card>

      {/* 调试信息 */}
      <Card title="调试信息" style={{ marginTop: '24px' }}>
        <Space direction="vertical" style={{ width: '100%' }}>
          <div>
            <Text strong>黑名单规则检查：</Text>
            <br />
            {blacklistManagerService.getBlacklistRules().map(rule => (
              <Tag key={rule.id} color={rule.isActive ? 'green' : 'default'}>
                {rule.name} ({rule.pattern})
              </Tag>
            ))}
          </div>
          
          <Divider />
          
          <div>
            <Text strong>监控服务状态：</Text>
            <br />
            <Tag color="blue">应用监控服务已加载</Tag>
            <Tag color="blue">黑名单管理服务已加载</Tag>
          </div>
        </Space>
      </Card>
    </div>
  );
};

export default FocusShieldTest;
