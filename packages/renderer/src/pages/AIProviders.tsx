import React, { useState, useEffect } from 'react';
import {
  Card,
  Button,
  List,
  Switch,
  Modal,
  Form,
  Input,
  Space,
  message,
  Tooltip,
  Typography,
  Row,
  Col,
  Tag,
  Empty
} from 'antd';
import {
  PlusOutlined,
  EditOutlined,
  DeleteOutlined,
  EyeInvisibleOutlined,
  EyeOutlined,
  ApiOutlined,
  SettingOutlined
} from '@ant-design/icons';
import { useTheme } from '../contexts/ThemeContext';
import { AIProvider, CreateAIProviderDto } from '../types';
import { DatabaseAPI } from '../services/api';

const { Text, Title } = Typography;

// 预定义的AI提供商模板
const AI_PROVIDER_TEMPLATES = [
  {
    name: 'OpenAI',
    baseUrl: 'https://api.openai.com/v1',
    modelId: 'gpt-3.5-turbo'
  },
  {
    name: 'OpenRouter',
    baseUrl: 'https://openrouter.ai/api/v1',
    modelId: 'openai/gpt-3.5-turbo'
  },
  {
    name: 'Google AI',
    baseUrl: 'https://generativelanguage.googleapis.com/v1beta',
    modelId: 'gemini-1.5-flash'
  },
  {
    name: 'Anthropic',
    baseUrl: 'https://api.anthropic.com/v1',
    modelId: 'claude-3-sonnet'
  },
  {
    name: 'Azure OpenAI',
    baseUrl: 'https://your-resource.openai.azure.com',
    modelId: 'gpt-4'
  },
  {
    name: 'Aliyun',
    baseUrl: 'https://dashscope.aliyuncs.com/api/v1',
    modelId: 'qwen-turbo'
  },
  {
    name: 'DeepSeek',
    baseUrl: 'https://api.deepseek.com/v1',
    modelId: 'deepseek-chat'
  },
  {
    name: 'Mistral',
    baseUrl: 'https://api.mistral.ai/v1',
    modelId: 'mistral-medium'
  },
  {
    name: 'Ollama',
    baseUrl: 'http://localhost:11434/v1',
    modelId: 'llama2'
  }
];

const AIProviders: React.FC = () => {
  const { theme: currentTheme } = useTheme();
  const [providers, setProviders] = useState<AIProvider[]>([]);
  const [loading, setLoading] = useState(false);
  const [isModalVisible, setIsModalVisible] = useState(false);
  const [editingProvider, setEditingProvider] = useState<AIProvider | null>(null);
  const [selectedProvider, setSelectedProvider] = useState<AIProvider | null>(null);
  const [form] = Form.useForm();
  const [showApiKey, setShowApiKey] = useState<Record<string, boolean>>({});
  const [testingConnections, setTestingConnections] = useState<Record<string, boolean>>({});

  // 模拟数据加载
  useEffect(() => {
    loadProviders();
  }, []);

  const loadProviders = async () => {
    setLoading(true);
    try {
      const providers = await DatabaseAPI.getAIProviders();
      setProviders(providers);
    } catch (error) {
      console.error('加载AI提供商失败:', error);
      message.error('加载AI提供商失败');
    } finally {
      setLoading(false);
    }
  };

  const handleAdd = () => {
    setEditingProvider(null);
    form.resetFields();
    setIsModalVisible(true);
  };

  const handleEdit = (provider: AIProvider) => {
    setEditingProvider(provider);
    form.setFieldsValue({
      name: provider.name,
      baseUrl: provider.baseUrl,
      apiKey: provider.apiKey,
      modelId: provider.modelId,
      enabled: provider.enabled
    });
    setIsModalVisible(true);
  };

  const handleDelete = (id: string) => {
    Modal.confirm({
      title: '确认删除',
      content: '确定要删除这个AI提供商配置吗？',
      onOk: async () => {
        try {
          await DatabaseAPI.deleteAIProvider(id);
          setProviders(prev => prev.filter(p => p.id !== id));
          message.success('删除成功');
        } catch (error) {
          console.error('删除失败:', error);
          message.error('删除失败');
        }
      }
    });
  };

  const handleToggleEnabled = async (id: string, enabled: boolean) => {
    try {
      await DatabaseAPI.updateAIProvider(id, { enabled });
      setProviders(prev =>
        prev.map(p => p.id === id ? { ...p, enabled } : p)
      );
      message.success(enabled ? '已启用' : '已禁用');
    } catch (error) {
      console.error('操作失败:', error);
      message.error('操作失败');
    }
  };

  const handleTestConnection = async (id: string) => {
    setTestingConnections(prev => ({ ...prev, [id]: true }));

    try {
      const result = await DatabaseAPI.testAIProvider(id);

      if (result.success) {
        const successMsg = result.responseTime
          ? `${result.message} (响应时间: ${result.responseTime}ms)`
          : result.message || 'API连接测试成功';
        message.success(successMsg);
      } else {
        const errorMsg = result.error
          ? `${result.message}: ${result.error}`
          : result.message || 'API连接测试失败';
        message.error(errorMsg);
      }
    } catch (error) {
      console.error('测试连接失败:', error);
      message.error('测试连接失败');
    } finally {
      setTestingConnections(prev => ({ ...prev, [id]: false }));
    }
  };

  const handleSubmit = async (values: any) => {
    try {
      const providerData: CreateAIProviderDto = {
        name: values.name,
        baseUrl: values.baseUrl,
        apiKey: values.apiKey,
        modelId: values.modelId,
        enabled: values.enabled ?? true
      };

      if (editingProvider) {
        // 更新
        await DatabaseAPI.updateAIProvider(editingProvider.id, providerData);
        const updatedProvider: AIProvider = {
          ...editingProvider,
          ...providerData,
          updatedAt: new Date()
        };
        setProviders(prev =>
          prev.map(p => p.id === editingProvider.id ? updatedProvider : p)
        );
        if (selectedProvider?.id === editingProvider.id) {
          setSelectedProvider(updatedProvider);
        }
        message.success('更新成功');
      } else {
        // 新增
        const newProvider = await DatabaseAPI.createAIProvider(providerData);
        setProviders(prev => [...prev, newProvider]);
        setSelectedProvider(newProvider);
        message.success('添加成功');
      }

      setIsModalVisible(false);
      form.resetFields();
    } catch (error) {
      console.error('保存失败:', error);
      message.error('保存失败');
    }
  };

  const handleUseTemplate = (template: any) => {
    form.setFieldsValue(template);
  };

  const toggleApiKeyVisibility = (id: string) => {
    setShowApiKey(prev => ({
      ...prev,
      [id]: !prev[id]
    }));
  };

  const maskApiKey = (apiKey: string) => {
    if (!apiKey) return '';
    const visibleLength = 8;
    if (apiKey.length <= visibleLength) return apiKey;
    return apiKey.substring(0, 4) + '•'.repeat(apiKey.length - visibleLength) + apiKey.substring(apiKey.length - 4);
  };

  return (
    <div
      className="lazy-content stable-layout"
      style={{
        width: '100%',
        height: '100%',
        overflow: 'hidden'
      }}
    >
      {/* 左右布局容器 */}
      <Row style={{ height: '100%' }}>
        {/* 左侧：AI提供商列表 */}
        <Col
          span={8}
          style={{
            borderRight: `1px solid ${currentTheme.colors.border}`,
            height: '100%',
            overflow: 'hidden',
            display: 'flex',
            flexDirection: 'column'
          }}
        >
          {/* 左侧标题和添加按钮 */}
          <div style={{
            padding: '24px 16px 16px',
            borderBottom: `1px solid ${currentTheme.colors.borderLight}`,
            flexShrink: 0
          }}>
            <div style={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center' }}>
              <Title level={4} style={{ margin: 0, color: currentTheme.colors.text }}>
                AI 提供商
              </Title>
              <Button
                type="primary"
                icon={<PlusOutlined />}
                onClick={handleAdd}
                size="small"
              >
                添加
              </Button>
            </div>
          </div>

          {/* 提供商列表 */}
          <div style={{ flex: 1, overflow: 'auto', padding: '8px' }}>
            <List
              loading={loading}
              dataSource={providers}
              locale={{ emptyText: '暂无AI提供商' }}
              renderItem={(provider) => (
                <List.Item
                  key={provider.id}
                  style={{
                    padding: '12px',
                    marginBottom: '8px',
                    borderRadius: '8px',
                    background: selectedProvider?.id === provider.id
                      ? currentTheme.colors.primary + '20'
                      : currentTheme.glassMorphism.background,
                    border: selectedProvider?.id === provider.id
                      ? `1px solid ${currentTheme.colors.primary}`
                      : currentTheme.glassMorphism.border,
                    cursor: 'pointer',
                    transition: 'all 0.2s ease'
                  }}
                  onClick={() => setSelectedProvider(provider)}
                >
                  <List.Item.Meta
                    avatar={
                      <div style={{
                        width: '32px',
                        height: '32px',
                        borderRadius: '6px',
                        background: currentTheme.colors.primary,
                        display: 'flex',
                        alignItems: 'center',
                        justifyContent: 'center',
                        color: 'white',
                        fontSize: '16px',
                        fontWeight: 'bold'
                      }}>
                        {provider.name.charAt(0).toUpperCase()}
                      </div>
                    }
                    title={
                      <div style={{ display: 'flex', alignItems: 'center', gap: '8px' }}>
                        <Text strong style={{ color: currentTheme.colors.text }}>
                          {provider.name}
                        </Text>
                        {provider.enabled ? (
                          <Tag color="success">启用</Tag>
                        ) : (
                          <Tag color="default">禁用</Tag>
                        )}
                      </div>
                    }
                    description={
                      <Text
                        type="secondary"
                        style={{ fontSize: '12px' }}
                        ellipsis={{ tooltip: provider.modelId }}
                      >
                        {provider.modelId}
                      </Text>
                    }
                  />
                </List.Item>
              )}
            />
          </div>
        </Col>

        {/* 右侧：详情面板 */}
        <Col span={16} style={{ height: '100%', overflow: 'hidden', display: 'flex', flexDirection: 'column' }}>
          {selectedProvider ? (
            <>
              {/* 详情标题栏 */}
              <div style={{
                padding: '24px 24px 16px',
                borderBottom: `1px solid ${currentTheme.colors.borderLight}`,
                flexShrink: 0
              }}>
                <div style={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center' }}>
                  <div style={{ display: 'flex', alignItems: 'center', gap: '12px' }}>
                    <div style={{
                      width: '40px',
                      height: '40px',
                      borderRadius: '8px',
                      background: currentTheme.colors.primary,
                      display: 'flex',
                      alignItems: 'center',
                      justifyContent: 'center',
                      color: 'white',
                      fontSize: '18px',
                      fontWeight: 'bold'
                    }}>
                      {selectedProvider.name.charAt(0).toUpperCase()}
                    </div>
                    <div>
                      <Title level={4} style={{ margin: 0, color: currentTheme.colors.text }}>
                        {selectedProvider.name}
                      </Title>
                      <Text type="secondary" style={{ fontSize: '14px' }}>
                        {selectedProvider.enabled ? '已启用' : '已禁用'}
                      </Text>
                    </div>
                  </div>
                  <Space>
                    <Switch
                      checked={selectedProvider.enabled}
                      onChange={(checked) => handleToggleEnabled(selectedProvider.id, checked)}
                      checkedChildren="启用"
                      unCheckedChildren="禁用"
                    />
                    <Tooltip title="测试连接">
                      <Button
                        icon={<ApiOutlined />}
                        loading={testingConnections[selectedProvider.id]}
                        onClick={() => handleTestConnection(selectedProvider.id)}
                      >
                        {testingConnections[selectedProvider.id] ? '测试中...' : '测试'}
                      </Button>
                    </Tooltip>
                    <Tooltip title="编辑">
                      <Button
                        icon={<EditOutlined />}
                        onClick={() => handleEdit(selectedProvider)}
                      >
                        编辑
                      </Button>
                    </Tooltip>
                    <Tooltip title="删除">
                      <Button
                        danger
                        icon={<DeleteOutlined />}
                        onClick={() => handleDelete(selectedProvider.id)}
                      >
                        删除
                      </Button>
                    </Tooltip>
                  </Space>
                </div>
              </div>

              {/* 详情内容 */}
              <div style={{ flex: 1, overflow: 'auto', padding: '24px' }}>
                <Card>
                  <Row gutter={[16, 16]}>
                    <Col span={24}>
                      <div>
                        <Text strong style={{ color: currentTheme.colors.text }}>API Base URL</Text>
                        <div style={{
                          marginTop: '8px',
                          padding: '8px 12px',
                          background: currentTheme.colors.backgroundSecondary,
                          borderRadius: '6px',
                          border: `1px solid ${currentTheme.colors.borderLight}`,
                          fontFamily: 'monospace',
                          fontSize: '13px',
                          wordBreak: 'break-all'
                        }}>
                          {selectedProvider.baseUrl}
                        </div>
                      </div>
                    </Col>
                    <Col span={24}>
                      <div>
                        <Text strong style={{ color: currentTheme.colors.text }}>API Key</Text>
                        <div style={{
                          marginTop: '8px',
                          padding: '8px 12px',
                          background: currentTheme.colors.backgroundSecondary,
                          borderRadius: '6px',
                          border: `1px solid ${currentTheme.colors.borderLight}`,
                          fontFamily: 'monospace',
                          fontSize: '13px',
                          display: 'flex',
                          alignItems: 'center',
                          justifyContent: 'space-between'
                        }}>
                          <span style={{ wordBreak: 'break-all' }}>
                            {showApiKey[selectedProvider.id] ? selectedProvider.apiKey : maskApiKey(selectedProvider.apiKey)}
                          </span>
                          <Button
                            type="text"
                            size="small"
                            icon={showApiKey[selectedProvider.id] ? <EyeInvisibleOutlined /> : <EyeOutlined />}
                            onClick={() => toggleApiKeyVisibility(selectedProvider.id)}
                            style={{ marginLeft: '8px', flexShrink: 0 }}
                          />
                        </div>
                      </div>
                    </Col>
                    <Col span={24}>
                      <div>
                        <Text strong style={{ color: currentTheme.colors.text }}>模型 ID</Text>
                        <div style={{
                          marginTop: '8px',
                          padding: '8px 12px',
                          background: currentTheme.colors.backgroundSecondary,
                          borderRadius: '6px',
                          border: `1px solid ${currentTheme.colors.borderLight}`,
                          fontFamily: 'monospace',
                          fontSize: '13px'
                        }}>
                          {selectedProvider.modelId}
                        </div>
                      </div>
                    </Col>
                    <Col span={12}>
                      <div>
                        <Text strong style={{ color: currentTheme.colors.text }}>创建时间</Text>
                        <div style={{ marginTop: '8px', color: currentTheme.colors.textSecondary }}>
                          {selectedProvider.createdAt.toLocaleString()}
                        </div>
                      </div>
                    </Col>
                    <Col span={12}>
                      <div>
                        <Text strong style={{ color: currentTheme.colors.text }}>更新时间</Text>
                        <div style={{ marginTop: '8px', color: currentTheme.colors.textSecondary }}>
                          {selectedProvider.updatedAt.toLocaleString()}
                        </div>
                      </div>
                    </Col>
                  </Row>
                </Card>
              </div>
            </>
          ) : (
            <div style={{
              flex: 1,
              display: 'flex',
              alignItems: 'center',
              justifyContent: 'center',
              flexDirection: 'column'
            }}>
              <Empty
                image={Empty.PRESENTED_IMAGE_SIMPLE}
                description={
                  <Text type="secondary">
                    请从左侧选择一个AI提供商查看详情
                  </Text>
                }
              />
            </div>
          )}
        </Col>
      </Row>

      {/* 添加/编辑提供商Modal */}
      <Modal
        title={
          <div style={{ display: 'flex', alignItems: 'center', gap: '8px' }}>
            <SettingOutlined style={{ color: currentTheme.colors.primary }} />
            {editingProvider ? '编辑AI提供商' : '添加AI提供商'}
          </div>
        }
        open={isModalVisible}
        onCancel={() => {
          setIsModalVisible(false);
          form.resetFields();
        }}
        onOk={() => form.submit()}
        width={700}
      >
        <Form
          form={form}
          layout="vertical"
          onFinish={handleSubmit}
          initialValues={{
            enabled: true,
            maxTokens: 4096,
            temperature: 0.7
          }}
        >
          {/* 快速模板选择 */}
          {!editingProvider && (
            <div style={{ marginBottom: '24px' }}>
              <Text strong style={{ color: currentTheme.colors.text }}>
                快速选择模板：
              </Text>
              <div style={{
                display: 'flex',
                flexWrap: 'wrap',
                gap: '8px',
                marginTop: '8px'
              }}>
                {AI_PROVIDER_TEMPLATES.map((template, index) => (
                  <Button
                    key={index}
                    size="small"
                    onClick={() => handleUseTemplate(template)}
                  >
                    {template.name}
                  </Button>
                ))}
              </div>
            </div>
          )}

          <Form.Item
            name="name"
            label="提供商名称"
            rules={[
              { required: true, message: '请输入提供商名称' },
              { max: 50, message: '名称不能超过50个字符' }
            ]}
          >
            <Input placeholder="例如：OpenAI" />
          </Form.Item>

          <Form.Item
            name="baseUrl"
            label="API Base URL"
            rules={[
              { required: true, message: '请输入API Base URL' },
              { type: 'url', message: '请输入有效的URL' }
            ]}
          >
            <Input placeholder="https://api.openai.com/v1" />
          </Form.Item>

          <Form.Item
            name="apiKey"
            label="API Key"
            rules={[
              { required: true, message: '请输入API Key' },
              { min: 10, message: 'API Key长度至少10个字符' }
            ]}
          >
            <Input.Password
              placeholder="输入您的API Key"
              visibilityToggle={{
                visible: true,
                onVisibleChange: () => {}
              }}
            />
          </Form.Item>

          <Form.Item
            name="modelId"
            label={
              <div style={{ display: 'flex', alignItems: 'center', gap: '8px' }}>
                <span>模型 ID</span>
                <span style={{
                  color: currentTheme.colors.error,
                  fontSize: '14px'
                }}>*</span>
                <Tooltip title="例如 gpt-3.5-turbo">
                  <Button type="text" size="small" icon={<ApiOutlined />} style={{ padding: 0, minWidth: 'auto' }} />
                </Tooltip>
              </div>
            }
            rules={[
              { required: true, message: '请输入模型ID' },
              { max: 100, message: '模型ID不能超过100个字符' }
            ]}
          >
            <Input
              placeholder="必填，例如 gpt-3.5-turbo"
              style={{
                borderColor: currentTheme.colors.primary + '40',
                borderWidth: '2px'
              }}
            />
          </Form.Item>

          <Form.Item
            name="enabled"
            label="启用状态"
            valuePropName="checked"
          >
            <Switch
              checkedChildren="启用"
              unCheckedChildren="禁用"
            />
          </Form.Item>
        </Form>
      </Modal>
    </div>
  );
};

export default AIProviders;
