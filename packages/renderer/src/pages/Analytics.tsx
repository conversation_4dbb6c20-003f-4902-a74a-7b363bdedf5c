import React, { useState, useEffect } from 'react';
import { Card, Row, Col, Statistic, Empty, Tabs, Button, Select, Progress, List, Tag } from 'antd';
import { BarChartOutlined, ClockCircleOutlined, TrophyOutlined, EyeOutlined, DownloadOutlined, CalendarOutlined } from '@ant-design/icons';
import { useSelector } from 'react-redux';
import { RootState } from '../store/store';
import FocusInsights from '../components/FocusInsights';
import { analyticsService } from '../services/AnalyticsService';

const Analytics: React.FC = () => {
  const { goals } = useSelector((state: RootState) => state.goals);
  const { tasks } = useSelector((state: RootState) => state.tasks);
  const [timeRange, setTimeRange] = useState(30);
  const [analytics, setAnalytics] = useState<any>(null);

  useEffect(() => {
    loadAnalytics();
  }, [goals, tasks, timeRange]);

  const loadAnalytics = () => {
    const timeStats = analyticsService.getTimeStatistics(timeRange);
    const completionAnalysis = analyticsService.getCompletionAnalysis(goals, tasks);
    const focusReport = analyticsService.getFocusReport(timeRange);
    const productivityInsights = analyticsService.getProductivityInsights(goals, tasks, timeRange);

    setAnalytics({
      timeStats,
      completionAnalysis,
      focusReport,
      productivityInsights
    });
  };

  const handleExportReport = () => {
    analyticsService.exportReport(goals, tasks, timeRange);
  };

  if (!analytics) {
    return <div style={{ padding: '24px' }}>加载中...</div>;
  }

  const { timeStats, completionAnalysis, focusReport, productivityInsights } = analytics;

  return (
    <div
      className="lazy-content stable-layout"
      style={{
        width: '100%',
        minHeight: '100%',
        overflow: 'visible'
      }}
    >
      <div style={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', marginBottom: 24 }}>
        <h1 className="fast-text">数据分析</h1>
        <div style={{ display: 'flex', gap: 16, alignItems: 'center' }}>
          <Select
            value={timeRange}
            onChange={setTimeRange}
            style={{ width: 120 }}
          >
            <Select.Option value={7}>最近7天</Select.Option>
            <Select.Option value={30}>最近30天</Select.Option>
            <Select.Option value={90}>最近90天</Select.Option>
          </Select>
          <Button
            type="primary"
            icon={<DownloadOutlined />}
            onClick={handleExportReport}
          >
            导出报告
          </Button>
        </div>
      </div>

      <Row className="fast-grid" gutter={[16, 16]}>
        <Col xs={24} sm={12} md={6}>
          <Card className="ultra-fast-card">
            <Statistic
              title="总专注时间"
              value={Math.round(timeStats.totalFocusTime)}
              suffix="分钟"
              prefix={<ClockCircleOutlined />}
            />
          </Card>
        </Col>
        <Col xs={24} sm={12} md={6}>
          <Card className="ultra-fast-card">
            <Statistic
              title="完成任务数"
              value={completionAnalysis.completedTasks}
              suffix="个"
              prefix={<TrophyOutlined />}
            />
          </Card>
        </Col>
        <Col xs={24} sm={12} md={6}>
          <Card className="ultra-fast-card">
            <Statistic
              title="专注效率"
              value={Math.round(timeStats.focusEfficiency)}
              suffix="%"
              prefix={<BarChartOutlined />}
            />
          </Card>
        </Col>
        <Col xs={24} sm={12} md={6}>
          <Card className="ultra-fast-card">
            <Statistic
              title="目标完成率"
              value={Math.round(completionAnalysis.completionRate)}
              suffix="%"
              prefix={<TrophyOutlined />}
            />
          </Card>
        </Col>
      </Row>

      <Card className="ultra-fast-card" style={{ marginTop: 24 }}>
        <Tabs
          defaultActiveKey="overview"
          items={[
            {
              key: 'overview',
              label: (
                <span>
                  <BarChartOutlined />
                  总览
                </span>
              ),
              children: (
                <div>
                  <Card title="生产力评分" style={{ marginBottom: 16 }}>
                    <div style={{ textAlign: 'center', marginBottom: 16 }}>
                      <Progress
                        type="circle"
                        percent={productivityInsights.productivityScore}
                        format={() => `${productivityInsights.productivityScore}分`}
                        size={120}
                        strokeColor={
                          productivityInsights.productivityScore >= 80 ? '#52c41a' :
                          productivityInsights.productivityScore >= 60 ? '#faad14' : '#ff4d4f'
                        }
                      />
                    </div>
                    <div style={{ textAlign: 'center' }}>
                      <Tag color={
                        productivityInsights.productivityScore >= 80 ? 'green' :
                        productivityInsights.productivityScore >= 60 ? 'orange' : 'red'
                      }>
                        {productivityInsights.productivityScore >= 80 ? '优秀' :
                         productivityInsights.productivityScore >= 60 ? '良好' : '需改进'}
                      </Tag>
                    </div>
                  </Card>

                  {productivityInsights.recommendations.length > 0 && (
                    <Card title="改进建议" style={{ marginBottom: 16 }}>
                      <List
                        dataSource={productivityInsights.recommendations}
                        renderItem={(item: string, index: number) => (
                          <List.Item>
                            <div style={{ display: 'flex', alignItems: 'flex-start', gap: 8 }}>
                              <div style={{
                                width: 20,
                                height: 20,
                                borderRadius: '50%',
                                backgroundColor: '#1890ff',
                                color: 'white',
                                fontSize: '12px',
                                display: 'flex',
                                alignItems: 'center',
                                justifyContent: 'center',
                                flexShrink: 0,
                                marginTop: 2
                              }}>
                                {index + 1}
                              </div>
                              <span>{item}</span>
                            </div>
                          </List.Item>
                        )}
                      />
                    </Card>
                  )}

                  {productivityInsights.achievements.length > 0 && (
                    <Card title="获得成就">
                      <div style={{ display: 'flex', flexWrap: 'wrap', gap: 8 }}>
                        {productivityInsights.achievements.map((achievement: string, index: number) => (
                          <Tag key={index} color="gold" style={{ padding: '4px 8px', fontSize: '13px' }}>
                            {achievement}
                          </Tag>
                        ))}
                      </div>
                    </Card>
                  )}
                </div>
              )
            },
            {
              key: 'focus',
              label: (
                <span>
                  <EyeOutlined />
                  专注分析
                </span>
              ),
              children: <FocusInsights />
            }
          ]}
        />
      </Card>
    </div>
  );
};

export default Analytics;