import React, { useState, useEffect } from 'react';
import { Card, Button, Space, Typography, Spin, Alert, Modal, message } from 'antd';
import { ArrowLeftOutlined, EditOutlined, SaveOutlined, ShareAltOutlined } from '@ant-design/icons';
import { DatabaseAPI } from '../services/api';
import DecompositionResultTree from '../components/DecompositionResultTree';
import NavigationService from '../services/NavigationService';
import DecompositionDebugInfo from '../components/DecompositionDebugInfo';

const { Title, Text } = Typography;

interface Goal {
  id: string;
  name: string;
  description: string;
  whyPower: string;
}

interface DecompositionResultProps {
  pageParams?: { goalId: string };
}

const DecompositionResult: React.FC<DecompositionResultProps> = ({ pageParams }) => {
  const goalId = pageParams?.goalId;
  const navigationService = NavigationService.getInstance();
  
  const [loading, setLoading] = useState(true);
  const [goal, setGoal] = useState<Goal | null>(null);
  const [decompositionResult, setDecompositionResult] = useState<any>(null);
  const [sessions, setSessions] = useState<any[]>([]);
  const [currentSessionId, setCurrentSessionId] = useState<string | null>(null);
  const [editable, setEditable] = useState(false);
  const [error, setError] = useState<string | null>(null);

  useEffect(() => {
    console.log('DecompositionResult页面加载，goalId:', goalId);
    if (goalId) {
      loadGoalAndDecomposition();
    } else {
      console.warn('DecompositionResult页面没有收到goalId参数');
      setError('缺少目标ID参数');
      setLoading(false);
    }
  }, [goalId]);

  const loadGoalAndDecomposition = async () => {
    try {
      setLoading(true);
      setError(null);
      console.log('开始加载目标和分解结果，goalId:', goalId);

      // 加载目标信息
      console.log('正在加载目标信息...');
      const goalResult = await DatabaseAPI.getGoal(goalId!);
      console.log('目标加载响应:', goalResult);

      if (!goalResult.success) {
        throw new Error(goalResult.error || '加载目标失败');
      }
      setGoal(goalResult.goal);
      console.log('目标信息加载成功:', goalResult.goal);

      // 加载分解会话
      console.log('正在加载分解会话...');
      const sessionsResult = await DatabaseAPI.getGoalDecompositionSessions(goalId!);
      console.log('分解会话响应:', sessionsResult);

      if (!sessionsResult.success) {
        throw new Error(sessionsResult.error || '加载分解会话失败');
      }

      const availableSessions = sessionsResult.sessions?.filter(s => s.hasResult) || [];
      console.log('可用的分解会话:', availableSessions);
      setSessions(availableSessions);

      if (availableSessions.length > 0) {
        // 加载最新的分解结果
        const latestSession = availableSessions[0];
        console.log('正在加载最新分解结果，sessionId:', latestSession.id);
        setCurrentSessionId(latestSession.id);

        const resultResponse = await DatabaseAPI.getAIDecompositionResult(latestSession.id);
        console.log('分解结果响应:', resultResponse);

        if (resultResponse.success) {
          setDecompositionResult(resultResponse.result);
          console.log('分解结果加载成功:', resultResponse.result);
        } else {
          console.warn('分解结果加载失败:', resultResponse.error);
          throw new Error(resultResponse.error || '加载分解结果失败');
        }
      } else {
        console.warn('没有找到可用的分解会话');
      }

    } catch (error) {
      console.error('加载分解结果失败:', error);
      setError((error as Error).message);
    } finally {
      setLoading(false);
    }
  };

  const handleEditItem = (type: 'subgoal' | 'milestone' | 'task', item: any, path: number[]) => {
    console.log('编辑项目:', type, item, path);
    // TODO: 实现编辑功能
    message.info('编辑功能即将推出');
  };

  const handleDeleteItem = (type: 'subgoal' | 'milestone' | 'task', path: number[]) => {
    Modal.confirm({
      title: '确认删除',
      content: `确定要删除这个${type === 'subgoal' ? '子目标' : type === 'milestone' ? '里程碑' : '任务'}吗？`,
      onOk: () => {
        console.log('删除项目:', type, path);
        // TODO: 实现删除功能
        message.info('删除功能即将推出');
      }
    });
  };

  const handleSave = () => {
    // TODO: 保存修改
    message.success('保存成功');
    setEditable(false);
  };

  const handleShare = () => {
    // TODO: 分享功能
    message.info('分享功能即将推出');
  };

  if (loading) {
    return (
      <div style={{ padding: '24px', textAlign: 'center' }}>
        <Spin size="large" />
        <div style={{ marginTop: 16 }}>
          <Text>正在加载分解结果...</Text>
        </div>
      </div>
    );
  }

  if (error) {
    return (
      <div style={{ padding: '24px' }}>
        <Alert
          message="加载失败"
          description={error}
          type="error"
          showIcon
          action={
            <Space>
              <Button size="small" onClick={loadGoalAndDecomposition}>
                重试
              </Button>
              <Button size="small" onClick={() => navigationService.navigateToGoals()}>
                返回目标列表
              </Button>
            </Space>
          }
        />
      </div>
    );
  }

  if (!goal || !decompositionResult) {
    return (
      <div style={{ padding: '24px' }}>
        <Alert
          message="暂无分解结果"
          description="该目标还没有AI分解结果，请先进行AI分解。"
          type="info"
          showIcon
          action={
            <Button onClick={() => navigationService.navigateToGoals()}>
              返回目标列表
            </Button>
          }
        />
      </div>
    );
  }

  return (
    <div style={{ padding: '24px' }}>
      {/* 页面头部 */}
      <div style={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', marginBottom: 24 }}>
        <Space>
          <Button
            icon={<ArrowLeftOutlined />}
            onClick={() => navigationService.navigateToGoals()}
          >
            返回
          </Button>
          <Title level={2} style={{ margin: 0 }}>
            AI分解结果
          </Title>
        </Space>
        
        <Space>
          {sessions.length > 1 && (
            <Button onClick={() => {
              // TODO: 显示会话选择器
              message.info('会话切换功能即将推出');
            }}>
              切换版本 ({sessions.length})
            </Button>
          )}
          <Button 
            icon={<ShareAltOutlined />}
            onClick={handleShare}
          >
            分享
          </Button>
          {editable ? (
            <Button 
              type="primary"
              icon={<SaveOutlined />}
              onClick={handleSave}
            >
              保存修改
            </Button>
          ) : (
            <Button 
              icon={<EditOutlined />}
              onClick={() => setEditable(true)}
            >
              编辑
            </Button>
          )}
        </Space>
      </div>

      {/* 目标信息 */}
      <Card size="small" style={{ marginBottom: 16 }}>
        <div>
          <Text strong>目标名称: </Text>
          <Text>{goal.name}</Text>
        </div>
        <div style={{ marginTop: 8 }}>
          <Text strong>目标描述: </Text>
          <Text>{goal.description}</Text>
        </div>
        <div style={{ marginTop: 8 }}>
          <Text strong>核心驱动力: </Text>
          <Text>{goal.whyPower}</Text>
        </div>
      </Card>

      {/* 分解结果树 */}
      <DecompositionResultTree
        result={decompositionResult}
        goalName={goal.name}
        onEditItem={handleEditItem}
        onDeleteItem={handleDeleteItem}
        editable={editable}
      />

      {/* 操作提示 */}
      {editable && (
        <Card size="small" style={{ marginTop: 16 }}>
          <Alert
            message="编辑模式"
            description="您现在可以编辑分解结果。点击各项目旁边的编辑或删除按钮进行修改。"
            type="info"
            showIcon
            closable
          />
        </Card>
      )}

      {/* 调试信息组件 */}
      <DecompositionDebugInfo
        goalId={goalId}
        sessionId={currentSessionId}
        result={decompositionResult}
        error={error}
        loading={loading}
        sessions={sessions}
      />
    </div>
  );
};

export default DecompositionResult;
