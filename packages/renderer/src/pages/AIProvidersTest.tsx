import React, { useState, useEffect } from 'react';
import { Card, Button, List, message, Space, Typography } from 'antd';
import { DatabaseAPI } from '../services/api';
import { AIProvider } from '../types';

const { Title, Text } = Typography;

const AIProvidersTest: React.FC = () => {
  const [providers, setProviders] = useState<AIProvider[]>([]);
  const [loading, setLoading] = useState(false);

  useEffect(() => {
    loadProviders();
  }, []);

  const loadProviders = async () => {
    setLoading(true);
    try {
      const data = await DatabaseAPI.getAIProviders();
      setProviders(data);
      console.log('加载的AI提供商:', data);
    } catch (error) {
      console.error('加载失败:', error);
      message.error('加载AI提供商失败');
    } finally {
      setLoading(false);
    }
  };

  const testCreateProvider = async () => {
    try {
      const newProvider = await DatabaseAPI.createAIProvider({
        name: 'Test OpenAI',
        icon: '🤖',
        description: '测试用的OpenAI提供商',
        baseUrl: 'https://api.openai.com/v1',
        apiKey: 'sk-test-key-123456789',
        enabled: true,
        models: ['gpt-4', 'gpt-3.5-turbo'],
        maxTokens: 4096,
        temperature: 0.7
      });
      
      setProviders(prev => [...prev, newProvider]);
      message.success('创建测试提供商成功');
    } catch (error) {
      console.error('创建失败:', error);
      message.error('创建测试提供商失败');
    }
  };

  const testDeleteProvider = async (id: string) => {
    try {
      await DatabaseAPI.deleteAIProvider(id);
      setProviders(prev => prev.filter(p => p.id !== id));
      message.success('删除成功');
    } catch (error) {
      console.error('删除失败:', error);
      message.error('删除失败');
    }
  };

  return (
    <div style={{ padding: '24px', maxWidth: '800px', margin: '0 auto' }}>
      <Title level={2}>AI Provider 测试页面</Title>
      
      <Space direction="vertical" size="large" style={{ width: '100%' }}>
        <Card>
          <Space>
            <Button type="primary" onClick={testCreateProvider}>
              创建测试提供商
            </Button>
            <Button onClick={loadProviders} loading={loading}>
              重新加载
            </Button>
          </Space>
        </Card>

        <Card title="AI提供商列表">
          <List
            loading={loading}
            dataSource={providers}
            locale={{ emptyText: '暂无AI提供商' }}
            renderItem={(provider) => (
              <List.Item
                actions={[
                  <Button 
                    key="delete" 
                    danger 
                    size="small"
                    onClick={() => testDeleteProvider(provider.id)}
                  >
                    删除
                  </Button>
                ]}
              >
                <List.Item.Meta
                  avatar={<span style={{ fontSize: '24px' }}>{provider.icon}</span>}
                  title={provider.name}
                  description={
                    <div>
                      <Text type="secondary">{provider.description}</Text>
                      <br />
                      <Text code>{provider.baseUrl}</Text>
                      <br />
                      <Text type="secondary">
                        模型: {provider.models?.join(', ') || '无'}
                      </Text>
                    </div>
                  }
                />
              </List.Item>
            )}
          />
        </Card>
      </Space>
    </div>
  );
};

export default AIProvidersTest;
