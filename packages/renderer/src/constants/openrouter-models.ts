/**
 * OpenRouter 支持的模型列表
 * 基于OpenRouter官方文档整理
 */

export interface OpenRouterModel {
  id: string;
  name: string;
  provider: string;
  description: string;
  contextLength: number;
  pricing: {
    prompt: string;
    completion: string;
  };
  category: 'recommended' | 'popular' | 'advanced' | 'experimental';
}

export const OPENROUTER_MODELS: OpenRouterModel[] = [
  // 推荐模型
  {
    id: 'openrouter/auto',
    name: 'Auto Router',
    provider: 'OpenRouter',
    description: '自动选择最适合的模型，推荐新手使用',
    contextLength: 2000000,
    pricing: { prompt: '动态', completion: '动态' },
    category: 'recommended'
  },
  {
    id: 'openai/gpt-4o',
    name: 'GPT-4 Omni',
    provider: 'OpenAI',
    description: '最新的GPT-4模型，支持多模态输入',
    contextLength: 128000,
    pricing: { prompt: '0.000005', completion: '0.000015' },
    category: 'recommended'
  },
  {
    id: 'openai/gpt-3.5-turbo',
    name: 'GPT-3.5 Turbo',
    provider: 'OpenAI',
    description: '经济实惠的选择，适合大多数任务',
    contextLength: 16385,
    pricing: { prompt: '0.0000005', completion: '0.0000015' },
    category: 'recommended'
  },
  {
    id: 'anthropic/claude-3.5-sonnet',
    name: 'Claude 3.5 Sonnet',
    provider: 'Anthropic',
    description: '平衡性能和成本的优秀选择',
    contextLength: 200000,
    pricing: { prompt: '0.000003', completion: '0.000015' },
    category: 'recommended'
  },

  // 热门模型
  {
    id: 'openai/gpt-4',
    name: 'GPT-4',
    provider: 'OpenAI',
    description: '强大的推理能力，适合复杂任务',
    contextLength: 8192,
    pricing: { prompt: '0.00003', completion: '0.00006' },
    category: 'popular'
  },
  {
    id: 'anthropic/claude-3-opus',
    name: 'Claude 3 Opus',
    provider: 'Anthropic',
    description: 'Anthropic最强大的模型',
    contextLength: 200000,
    pricing: { prompt: '0.000015', completion: '0.000075' },
    category: 'popular'
  },
  {
    id: 'google/gemini-2.0-flash-exp',
    name: 'Gemini 2.0 Flash',
    provider: 'Google',
    description: 'Google最新的实验性模型',
    contextLength: 1000000,
    pricing: { prompt: '免费', completion: '免费' },
    category: 'popular'
  },

  // 高级模型
  {
    id: 'meta-llama/llama-3.1-405b-instruct',
    name: 'Llama 3.1 405B',
    provider: 'Meta',
    description: '开源模型中的佼佼者',
    contextLength: 131072,
    pricing: { prompt: '0.000005', completion: '0.000015' },
    category: 'advanced'
  },
  {
    id: 'mistralai/mistral-large',
    name: 'Mistral Large',
    provider: 'Mistral AI',
    description: '欧洲领先的AI模型',
    contextLength: 128000,
    pricing: { prompt: '0.000008', completion: '0.000024' },
    category: 'advanced'
  },

  // 实验性模型
  {
    id: 'deepseek/deepseek-r1',
    name: 'DeepSeek R1',
    provider: 'DeepSeek',
    description: '具有推理能力的实验性模型',
    contextLength: 65536,
    pricing: { prompt: '0.000001', completion: '0.000008' },
    category: 'experimental'
  }
];

/**
 * 根据类别获取模型
 */
export const getModelsByCategory = (category: OpenRouterModel['category']) => {
  return OPENROUTER_MODELS.filter(model => model.category === category);
};

/**
 * 获取推荐模型
 */
export const getRecommendedModels = () => {
  return getModelsByCategory('recommended');
};

/**
 * 根据ID查找模型
 */
export const findModelById = (id: string) => {
  return OPENROUTER_MODELS.find(model => model.id === id);
};

/**
 * 获取所有提供商
 */
export const getAllProviders = () => {
  const providers = new Set(OPENROUTER_MODELS.map(model => model.provider));
  return Array.from(providers);
};

/**
 * 根据提供商获取模型
 */
export const getModelsByProvider = (provider: string) => {
  return OPENROUTER_MODELS.filter(model => model.provider === provider);
};
