// 目标解析结果
export interface GoalAnalysis {
  keywords: string[];
  actionVerbs: string[];
  entities: string[];
  metrics: string[];
  timeReferences: string[];
  suggestedDeadline?: Date;
  smartScore: number;
  analyzedAt: Date;
}

// AI分解状态
export type DecompositionStatus = 'not_started' | 'in_progress' | 'completed' | 'failed' | 'user_modified';

// AI分解会话
export interface DecompositionSession {
  id: string;
  goalId: string;
  status: DecompositionStatus;
  aiProvider: string; // AI提供商名称
  originalInput: string; // 用户原始输入
  aiResponse?: string; // AI原始响应
  decompositionResult?: GoalDecompositionResult;
  userModifications?: UserModification[];
  createdAt: Date;
  updatedAt: Date;
}

// 用户修改记录
export interface UserModification {
  id: string;
  type: 'add' | 'edit' | 'delete' | 'reorder';
  targetType: 'subgoal' | 'milestone' | 'task';
  targetId: string;
  oldValue?: any;
  newValue?: any;
  timestamp: Date;
}

// AI分解结果
export interface GoalDecompositionResult {
  subGoals: SubGoalSuggestion[];
  estimatedTotalTime?: number; // 总预估时间（小时）
  complexity: 'low' | 'medium' | 'high';
  confidence: number; // 0-1，AI对分解结果的信心度
  suggestions?: string[]; // AI的额外建议
}

// 目标数据模型
export interface Goal {
  id: string;
  userId: string;
  parentId?: string; // 支持目标层级关系
  name: string;
  description: string;
  type: 'long-term' | 'short-term' | 'habit';
  whyPower: string;
  domains: string[];
  startDate?: Date;
  deadline?: Date;
  status: 'active' | 'completed' | 'paused' | 'cancelled';
  level: number; // 目标层级，0为根目标
  // 长期目标的重复设置
  repeatType?: 'daily' | 'weekly' | 'workdays' | 'monthly' | 'yearly' | 'custom';
  customRepeat?: {
    interval: number; // 间隔数量
    unit: 'day' | 'week' | 'month' | 'year'; // 间隔单位
    weekdays?: number[]; // 对于周重复，选中的星期 (0=周日, 1=周一...)
  };
  // 目标解析结果
  analysis?: GoalAnalysis;
  // AI分解相关
  decompositionStatus?: DecompositionStatus;
  currentDecompositionSessionId?: string;
  hasAIDecomposition: boolean; // 是否使用了AI分解
  aiDecompositionHistory?: string[]; // 历史分解会话ID列表
  createdAt: Date;
  updatedAt: Date;
}

// 子目标数据模型
export interface SubGoal {
  id: string;
  parentGoalId: string;
  name: string;
  description: string;
  type: 'subgoal'; // 固定类型
  priority: 'high' | 'medium' | 'low';
  estimatedTime?: number; // 预估完成时间（小时）
  deadline?: Date;
  status: 'not_started' | 'in_progress' | 'completed' | 'paused' | 'cancelled';
  order: number; // 在同级中的排序
  // AI分解相关
  isAIGenerated: boolean;
  aiConfidence?: number; // AI生成时的信心度
  userModified: boolean; // 是否被用户修改过
  createdAt: Date;
  updatedAt: Date;
}

// 里程碑数据模型
export interface Milestone {
  id: string;
  subGoalId: string;
  name: string;
  description: string;
  type: 'milestone'; // 固定类型
  estimatedTime?: number; // 预估完成时间（小时）
  deadline?: Date;
  status: 'not_started' | 'in_progress' | 'completed' | 'paused' | 'cancelled';
  order: number; // 在同级中的排序
  // AI分解相关
  isAIGenerated: boolean;
  aiConfidence?: number;
  userModified: boolean;
  createdAt: Date;
  updatedAt: Date;
}

// 任务数据模型（扩展）
export interface Task {
  id: string;
  parentId: string; // 可以是subGoalId或milestoneId
  parentType: 'subgoal' | 'milestone'; // 父级类型
  title: string;
  description?: string;
  estimatedTime?: number; // 分钟，最小任务单元应≤120分钟
  actualTime: number; // 分钟，自动累计
  priority: 'high' | 'medium' | 'low';
  deadline?: Date;
  tags: string[];
  status: 'todo' | 'in-progress' | 'completed' | 'paused' | 'cancelled';
  completionPercentage: number; // 0-100
  order: number; // 在同级中的排序
  // AI分解相关
  isAIGenerated: boolean;
  aiConfidence?: number;
  userModified: boolean;
  createdAt: Date;
  updatedAt: Date;
}

// 番茄钟会话数据模型
export interface PomodoroSession {
  id: string;
  taskId: string;
  type: 'work' | 'short-break' | 'long-break';
  startTime: Date;
  endTime?: Date;
  duration: number; // 分钟
  isCompleted: boolean;
  wasInterrupted: boolean;
}

// 番茄钟设置
export interface PomodoroSettings {
  workDuration: number; // 分钟，默认25
  shortBreakDuration: number; // 分钟，默认5
  longBreakDuration: number; // 分钟，默认15
  sessionsUntilLongBreak: number; // 默认4
  autoStartBreaks: boolean;
  autoStartNextSession: boolean;
}

// 番茄钟计时器状态
export interface PomodoroTimer {
  currentSession?: PomodoroSession;
  remainingTime: number; // 秒
  isRunning: boolean;
  isPaused: boolean;
  sessionCount: number; // 当日完成的番茄数
}

// AI分解建议类型
export interface SubGoalSuggestion {
  name: string;
  description: string;
  priority: 'high' | 'medium' | 'low';
  estimatedTime?: number; // 小时
  milestones: MilestoneSuggestion[];
  confidence: number; // 0-1
}

export interface MilestoneSuggestion {
  name: string;
  description: string;
  estimatedTime?: number; // 小时
  tasks: TaskSuggestion[];
  confidence: number; // 0-1
}

export interface TaskSuggestion {
  title: string;
  description: string;
  estimatedTime: number; // 分钟，应≤120
  priority: 'high' | 'medium' | 'low';
  confidence: number; // 0-1
}

// 创建目标的DTO
export interface CreateGoalDto {
  name: string;
  description: string;
  type: 'long-term' | 'short-term' | 'habit';
  whyPower: string;
  domains?: string[];
  startDate?: Date;
  deadline?: Date;
  // AI分解选项
  enableAIDecomposition?: boolean;
  aiProvider?: string; // AI提供商ID
}

// 创建子目标的DTO
export interface CreateSubGoalDto {
  parentGoalId: string;
  name: string;
  description: string;
  priority?: 'high' | 'medium' | 'low';
  estimatedTime?: number;
  deadline?: Date;
  order?: number;
}

// 创建里程碑的DTO
export interface CreateMilestoneDto {
  subGoalId: string;
  name: string;
  description: string;
  estimatedTime?: number;
  deadline?: Date;
  order?: number;
}

// 创建任务的DTO
export interface CreateTaskDto {
  parentId: string;
  parentType: 'subgoal' | 'milestone';
  title: string;
  description?: string;
  estimatedTime?: number;
  priority?: 'high' | 'medium' | 'low';
  deadline?: Date;
  tags?: string[];
  order?: number;
}

// AI分解请求DTO
export interface AIDecompositionRequestDto {
  goalId: string;
  goalDescription: string;
  whyPower: string;
  aiProvider: string;
  preferences?: {
    maxDepth?: number; // 最大分解层级
    taskGranularity?: 'fine' | 'medium' | 'coarse'; // 任务粒度
    includeTimeEstimates?: boolean;
    focusAreas?: string[]; // 重点关注领域
  };
}

// AI分解确认DTO
export interface AIDecompositionConfirmDto {
  sessionId: string;
  confirmedItems: {
    subGoals: string[]; // 确认的子目标ID
    milestones: string[]; // 确认的里程碑ID
    tasks: string[]; // 确认的任务ID
  };
  modifications: UserModification[];
}

// 创建番茄钟会话的DTO
export interface CreatePomodoroSessionDto {
  taskId: string;
  type: 'work' | 'short-break' | 'long-break';
  startTime: Date;
  duration: number; // 分钟
}

// AI API 配置接口
export interface AIProvider {
  id: string;
  name: string;
  baseUrl: string;
  apiKey: string;
  modelId: string;
  enabled: boolean;
  createdAt: Date;
  updatedAt: Date;
}

export interface CreateAIProviderDto {
  name: string;
  baseUrl: string;
  apiKey: string;
  modelId: string;
  enabled?: boolean;
}

// Focus Shield 相关类型定义
export interface FocusShieldConfig {
  isEnabled: boolean;
  enabledInPomodoroMode: boolean;
  enabledInDeepFocusMode: boolean;
  enabledInBreakTime: boolean;
  autoStartWithFocusSession: boolean;
  interventionLevel: 'gentle' | 'warning' | 'firm' | 'block';
  monitorApps: boolean;
  monitorWebsites: boolean;
  enableSmartDetection: boolean;
  notificationSettings: {
    showDesktopNotifications: boolean;
    playAudioAlerts: boolean;
    vibrationEnabled: boolean;
  };
}

export interface ApplicationInfo {
  id: string;
  name: string;
  bundleId?: string;
  executable?: string;
  windowTitle?: string;
  isActive: boolean;
  lastActiveTime: Date;
  category?: 'productivity' | 'social' | 'entertainment' | 'development' | 'other';
}

export interface WebsiteInfo {
  id: string;
  url: string;
  domain: string;
  title: string;
  isActive: boolean;
  lastActiveTime: Date;
  tabId?: number;
  category?: 'work' | 'social' | 'entertainment' | 'news' | 'shopping' | 'other';
}

export interface BlacklistRule {
  id: string;
  name: string;
  type: 'app' | 'website' | 'keyword';
  pattern: string;
  category: string;
  isActive: boolean;
  severity: 'low' | 'medium' | 'high';
  timeRules?: {
    allowedHours?: { start: string; end: string }[];
    allowedDays?: number[];
    maxDailyMinutes?: number;
  };
  contextRules?: {
    allowInBreakTime?: boolean;
    allowWithWhitelistApp?: boolean;
  };
  createdAt: Date;
  updatedAt: Date;
}

export interface WhitelistRule {
  id: string;
  name: string;
  type: 'app' | 'website' | 'keyword';
  pattern: string;
  category: string;
  isActive: boolean;
  priority: 'high' | 'medium' | 'low';
  description?: string;
  createdAt: Date;
  updatedAt: Date;
}

export interface InterventionEvent {
  id: string;
  timestamp: Date;
  target: ApplicationInfo | WebsiteInfo;
  rule: BlacklistRule;
  level: 'gentle' | 'warning' | 'firm' | 'block';
  type: 'notification' | 'modal' | 'delay' | 'block' | 'redirect';
  userResponse: 'blocked' | 'continued' | 'returned' | 'skipped' | 'pending';
  responseTime?: number;
  effectivenessScoredAt?: Date;
  effectivenessScore?: number;
}