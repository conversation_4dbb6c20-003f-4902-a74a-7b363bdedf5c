import React, { useState, useEffect, useMemo } from 'react';
import { Card, Typography, Space, Tag, Progress, List, Alert, Select, DatePicker, Row, Col } from 'antd';
import {
  EyeOutlined,
  ClockCircleOutlined,
  RiseOutlined,
  FallOutlined,
  MinusOutlined,
  BulbOutlined,
  BarChartOutlined
} from '@ant-design/icons';
import { focusMonitorService, FocusInsight } from '../services/FocusMonitorService';
import dayjs from 'dayjs';

const { Title, Text } = Typography;
const { Option } = Select;
const { RangePicker } = DatePicker;

interface FocusInsightsProps {
  style?: React.CSSProperties;
}

const FocusInsights: React.FC<FocusInsightsProps> = ({ style }) => {
  const [insights, setInsights] = useState<FocusInsight[]>([]);
  const [timeRange, setTimeRange] = useState<number>(7); // 默认7天
  const [loading, setLoading] = useState(false);

  useEffect(() => {
    loadInsights();
  }, [timeRange]);

  const loadInsights = async () => {
    setLoading(true);
    try {
      const data = focusMonitorService.getFocusInsights(timeRange);
      setInsights(data);
    } catch (error) {
      console.error('加载专注洞察失败:', error);
    } finally {
      setLoading(false);
    }
  };

  // 计算总体统计
  const overallStats = useMemo(() => {
    if (insights.length === 0) return null;

    const totalFocusTime = insights.reduce((sum, insight) => sum + insight.totalFocusTime, 0);
    const avgFocusScore = insights.reduce((sum, insight) => sum + insight.averageFocusScore, 0) / insights.length;
    const totalDistractions = insights.reduce((sum, insight) => sum + insight.distractionCount, 0);
    const avgDistractions = totalDistractions / insights.length;

    // 计算趋势
    const recentInsights = insights.slice(0, Math.ceil(insights.length / 2));
    const olderInsights = insights.slice(Math.ceil(insights.length / 2));
    
    const recentAvgScore = recentInsights.length > 0 
      ? recentInsights.reduce((sum, i) => sum + i.averageFocusScore, 0) / recentInsights.length 
      : 0;
    const olderAvgScore = olderInsights.length > 0 
      ? olderInsights.reduce((sum, i) => sum + i.averageFocusScore, 0) / olderInsights.length 
      : 0;

    const scoreTrend = recentAvgScore > olderAvgScore + 5 ? 'improving' : 
                      recentAvgScore < olderAvgScore - 5 ? 'declining' : 'stable';

    return {
      totalFocusTime,
      avgFocusScore,
      totalDistractions,
      avgDistractions,
      scoreTrend
    };
  }, [insights]);

  const getTrendIcon = (trend: string) => {
    switch (trend) {
      case 'improving': return <RiseOutlined style={{ color: '#52c41a' }} />;
      case 'declining': return <FallOutlined style={{ color: '#ff4d4f' }} />;
      case 'stable': return <MinusOutlined style={{ color: '#faad14' }} />;
      default: return null;
    }
  };

  const getTrendText = (trend: string) => {
    switch (trend) {
      case 'improving': return '上升趋势';
      case 'declining': return '下降趋势';
      case 'stable': return '保持稳定';
      default: return '无数据';
    }
  };

  const getTrendColor = (trend: string) => {
    switch (trend) {
      case 'improving': return '#52c41a';
      case 'declining': return '#ff4d4f';
      case 'stable': return '#faad14';
      default: return '#d9d9d9';
    }
  };

  const formatTime = (minutes: number) => {
    if (minutes < 60) return `${Math.round(minutes)}分钟`;
    const hours = Math.floor(minutes / 60);
    const remainingMinutes = Math.round(minutes % 60);
    return remainingMinutes > 0 ? `${hours}小时${remainingMinutes}分钟` : `${hours}小时`;
  };

  const getScoreColor = (score: number) => {
    if (score >= 80) return '#52c41a';
    if (score >= 60) return '#faad14';
    return '#ff4d4f';
  };

  const getProductiveTimeText = (hour: number) => {
    if (hour >= 6 && hour < 12) return '上午';
    if (hour >= 12 && hour < 18) return '下午';
    if (hour >= 18 && hour < 22) return '晚上';
    return '深夜';
  };

  // 收集所有建议
  const allSuggestions = useMemo(() => {
    const suggestions = new Set<string>();
    insights.forEach(insight => {
      insight.suggestions.forEach(suggestion => suggestions.add(suggestion));
    });
    return Array.from(suggestions);
  }, [insights]);

  if (insights.length === 0) {
    return (
      <Card style={style}>
        <div style={{ textAlign: 'center', padding: '40px 0' }}>
          <EyeOutlined style={{ fontSize: '48px', color: '#d9d9d9', marginBottom: 16 }} />
          <div>
            <Text type="secondary">暂无专注数据</Text>
          </div>
          <div>
            <Text type="secondary" style={{ fontSize: '12px' }}>
              开始一个专注会话来查看洞察
            </Text>
          </div>
        </div>
      </Card>
    );
  }

  return (
    <div style={style}>
      {/* 时间范围选择 */}
      <div style={{ marginBottom: 16, display: 'flex', justifyContent: 'space-between', alignItems: 'center' }}>
        <Title level={4} style={{ margin: 0 }}>
          <BarChartOutlined /> 专注洞察
        </Title>
        <Select
          value={timeRange}
          onChange={setTimeRange}
          style={{ width: 120 }}
        >
          <Option value={1}>今天</Option>
          <Option value={3}>3天</Option>
          <Option value={7}>7天</Option>
          <Option value={14}>14天</Option>
          <Option value={30}>30天</Option>
        </Select>
      </div>

      {/* 总体统计 */}
      {overallStats && (
        <Row gutter={16} style={{ marginBottom: 24 }}>
          <Col span={6}>
            <Card size="small" style={{ textAlign: 'center' }}>
              <div style={{ fontSize: '20px', fontWeight: 'bold', color: '#1890ff' }}>
                {formatTime(overallStats.totalFocusTime)}
              </div>
              <div style={{ fontSize: '12px', color: '#666' }}>总专注时间</div>
            </Card>
          </Col>
          
          <Col span={6}>
            <Card size="small" style={{ textAlign: 'center' }}>
              <div style={{ fontSize: '20px', fontWeight: 'bold', color: getScoreColor(overallStats.avgFocusScore) }}>
                {Math.round(overallStats.avgFocusScore)}
              </div>
              <div style={{ fontSize: '12px', color: '#666' }}>平均专注分数</div>
            </Card>
          </Col>
          
          <Col span={6}>
            <Card size="small" style={{ textAlign: 'center' }}>
              <div style={{ fontSize: '20px', fontWeight: 'bold', color: '#faad14' }}>
                {Math.round(overallStats.avgDistractions)}
              </div>
              <div style={{ fontSize: '12px', color: '#666' }}>日均分心次数</div>
            </Card>
          </Col>
          
          <Col span={6}>
            <Card size="small" style={{ textAlign: 'center' }}>
              <div style={{ 
                fontSize: '20px', 
                fontWeight: 'bold', 
                color: getTrendColor(overallStats.scoreTrend),
                display: 'flex',
                alignItems: 'center',
                justifyContent: 'center',
                gap: 4
              }}>
                {getTrendIcon(overallStats.scoreTrend)}
                <span>{getTrendText(overallStats.scoreTrend)}</span>
              </div>
              <div style={{ fontSize: '12px', color: '#666' }}>专注趋势</div>
            </Card>
          </Col>
        </Row>
      )}

      {/* 每日详情 */}
      <Card 
        title="每日专注详情" 
        size="small" 
        style={{ marginBottom: 16 }}
      >
        <List
          dataSource={insights}
          renderItem={(insight) => (
            <List.Item style={{ padding: '12px 0' }}>
              <div style={{ width: '100%' }}>
                <div style={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', marginBottom: 8 }}>
                  <div>
                    <Text strong>{insight.date.toLocaleDateString('zh-CN')}</Text>
                    <Text type="secondary" style={{ marginLeft: 8 }}>
                      {insight.date.toLocaleDateString('zh-CN', { weekday: 'short' })}
                    </Text>
                  </div>
                  <Space>
                    <Tag color={getScoreColor(insight.averageFocusScore)}>
                      专注分数: {Math.round(insight.averageFocusScore)}
                    </Tag>
                    <Tag>
                      <ClockCircleOutlined /> {formatTime(insight.totalFocusTime)}
                    </Tag>
                  </Space>
                </div>
                
                <div style={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center' }}>
                  <div>
                    <Text type="secondary" style={{ fontSize: '12px' }}>
                      分心 {insight.distractionCount} 次 • 
                      最佳时段: {getProductiveTimeText(insight.mostProductiveHour)} {insight.mostProductiveHour}:00
                    </Text>
                  </div>
                  <Progress 
                    percent={insight.averageFocusScore} 
                    size="small" 
                    strokeColor={getScoreColor(insight.averageFocusScore)}
                    style={{ width: 100 }}
                  />
                </div>
              </div>
            </List.Item>
          )}
        />
      </Card>

      {/* 改进建议 */}
      {allSuggestions.length > 0 && (
        <Card 
          title={
            <Space>
              <BulbOutlined />
              <span>改进建议</span>
            </Space>
          }
          size="small"
        >
          <List
            dataSource={allSuggestions}
            renderItem={(suggestion, index) => (
              <List.Item style={{ padding: '8px 0', border: 'none' }}>
                <div style={{ display: 'flex', alignItems: 'flex-start', gap: 8 }}>
                  <div style={{ 
                    width: 20, 
                    height: 20, 
                    borderRadius: '50%', 
                    backgroundColor: '#1890ff', 
                    color: 'white', 
                    fontSize: '12px',
                    display: 'flex',
                    alignItems: 'center',
                    justifyContent: 'center',
                    flexShrink: 0,
                    marginTop: 2
                  }}>
                    {index + 1}
                  </div>
                  <Text style={{ lineHeight: '1.5' }}>{suggestion}</Text>
                </div>
              </List.Item>
            )}
          />
        </Card>
      )}
    </div>
  );
};

export default FocusInsights;
