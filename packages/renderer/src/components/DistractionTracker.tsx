import React, { useState } from 'react';
import { Button, Modal, Form, Input, Select, Rate, Space, Typography, Card, Tag, List, Tooltip } from 'antd';
import { 
  ExclamationCircleOutlined, 
  ClockCircleOutlined, 
  PlusOutlined,
  DeleteOutlined,
  EyeOutlined
} from '@ant-design/icons';
import { focusMonitorService, DistractionRecord } from '../services/FocusMonitorService';

const { Title, Text } = Typography;
const { TextArea } = Input;
const { Option } = Select;

interface DistractionTrackerProps {
  visible: boolean;
  onClose: () => void;
}

const DistractionTracker: React.FC<DistractionTrackerProps> = ({
  visible,
  onClose
}) => {
  const [form] = Form.useForm();
  const [showAddForm, setShowAddForm] = useState(false);

  const currentSession = focusMonitorService.getCurrentSession();
  const distractions = currentSession?.distractions || [];

  const handleAddDistraction = async (values: any) => {
    focusMonitorService.recordDistraction({
      type: 'manual',
      description: values.description,
      severity: values.severity,
      category: values.category,
      duration: values.duration ? values.duration * 60 : undefined // 转换为秒
    });

    form.resetFields();
    setShowAddForm(false);
  };

  const getSeverityColor = (severity: string) => {
    switch (severity) {
      case 'high': return '#ff4d4f';
      case 'medium': return '#faad14';
      case 'low': return '#52c41a';
      default: return '#d9d9d9';
    }
  };

  const getSeverityText = (severity: string) => {
    switch (severity) {
      case 'high': return '严重';
      case 'medium': return '中等';
      case 'low': return '轻微';
      default: return severity;
    }
  };

  const getTypeIcon = (type: string) => {
    switch (type) {
      case 'manual': return '👤';
      case 'auto-detected': return '🤖';
      case 'app-switch': return '💻';
      case 'notification': return '🔔';
      default: return '❓';
    }
  };

  const formatDuration = (seconds?: number) => {
    if (!seconds) return '未知';
    if (seconds < 60) return `${seconds}秒`;
    const minutes = Math.floor(seconds / 60);
    const remainingSeconds = seconds % 60;
    return remainingSeconds > 0 ? `${minutes}分${remainingSeconds}秒` : `${minutes}分钟`;
  };

  const formatTime = (date: Date) => {
    return date.toLocaleTimeString('zh-CN', {
      hour: '2-digit',
      minute: '2-digit'
    });
  };

  const distractionCategories = [
    '社交媒体',
    '即时消息',
    '邮件',
    '网页浏览',
    '手机通知',
    '环境噪音',
    '内心想法',
    '身体不适',
    '工作相关',
    '其他'
  ];

  const totalDistractionTime = distractions.reduce((sum, d) => sum + (d.duration || 0), 0);
  const avgSeverity = distractions.length > 0 
    ? distractions.reduce((sum, d) => {
        const severityScore = { low: 1, medium: 2, high: 3 }[d.severity] || 1;
        return sum + severityScore;
      }, 0) / distractions.length
    : 0;

  return (
    <Modal
      title={
        <Space>
          <ExclamationCircleOutlined />
          <span>分心记录</span>
          <Tag color="blue">{distractions.length} 次分心</Tag>
        </Space>
      }
      open={visible}
      onCancel={onClose}
      footer={[
        <Button key="close" onClick={onClose}>
          关闭
        </Button>,
        <Button 
          key="add" 
          type="primary" 
          icon={<PlusOutlined />}
          onClick={() => setShowAddForm(true)}
          disabled={!currentSession}
        >
          记录分心
        </Button>
      ]}
      width={700}
      style={{ top: 20 }}
    >
      {!currentSession ? (
        <div style={{ textAlign: 'center', padding: '40px 0' }}>
          <Text type="secondary">当前没有进行中的专注会话</Text>
        </div>
      ) : (
        <div>
          {/* 统计概览 */}
          <div style={{ display: 'grid', gridTemplateColumns: 'repeat(auto-fit, minmax(150px, 1fr))', gap: 16, marginBottom: 24 }}>
            <Card size="small" style={{ textAlign: 'center' }}>
              <div style={{ fontSize: '24px', fontWeight: 'bold', color: '#1890ff' }}>
                {distractions.length}
              </div>
              <div style={{ fontSize: '12px', color: '#666' }}>分心次数</div>
            </Card>
            
            <Card size="small" style={{ textAlign: 'center' }}>
              <div style={{ fontSize: '24px', fontWeight: 'bold', color: '#faad14' }}>
                {formatDuration(totalDistractionTime)}
              </div>
              <div style={{ fontSize: '12px', color: '#666' }}>总分心时间</div>
            </Card>
            
            <Card size="small" style={{ textAlign: 'center' }}>
              <div style={{ fontSize: '24px', fontWeight: 'bold', color: getSeverityColor(
                avgSeverity >= 2.5 ? 'high' : avgSeverity >= 1.5 ? 'medium' : 'low'
              ) }}>
                {avgSeverity.toFixed(1)}
              </div>
              <div style={{ fontSize: '12px', color: '#666' }}>平均严重程度</div>
            </Card>
            
            <Card size="small" style={{ textAlign: 'center' }}>
              <div style={{ fontSize: '24px', fontWeight: 'bold', color: '#52c41a' }}>
                {currentSession.focusScore}
              </div>
              <div style={{ fontSize: '12px', color: '#666' }}>专注分数</div>
            </Card>
          </div>

          {/* 分心记录列表 */}
          {distractions.length === 0 ? (
            <div style={{ textAlign: 'center', padding: '40px 0' }}>
              <EyeOutlined style={{ fontSize: '48px', color: '#52c41a', marginBottom: 16 }} />
              <div>
                <Text strong style={{ color: '#52c41a' }}>专注状态良好！</Text>
              </div>
              <div>
                <Text type="secondary">目前还没有记录到分心情况</Text>
              </div>
            </div>
          ) : (
            <List
              dataSource={distractions}
              renderItem={(distraction, index) => (
                <List.Item
                  style={{
                    padding: '12px 16px',
                    border: '1px solid #f0f0f0',
                    borderRadius: '8px',
                    marginBottom: '8px',
                    backgroundColor: '#fafafa'
                  }}
                >
                  <div style={{ width: '100%' }}>
                    <div style={{ display: 'flex', justifyContent: 'space-between', alignItems: 'flex-start', marginBottom: 8 }}>
                      <div style={{ display: 'flex', alignItems: 'center', gap: 8 }}>
                        <span style={{ fontSize: '16px' }}>{getTypeIcon(distraction.type)}</span>
                        <Tag color={getSeverityColor(distraction.severity)} size="small">
                          {getSeverityText(distraction.severity)}
                        </Tag>
                        <Tag size="small">{distraction.category}</Tag>
                      </div>
                      <div style={{ display: 'flex', alignItems: 'center', gap: 8 }}>
                        <Text type="secondary" style={{ fontSize: '12px' }}>
                          <ClockCircleOutlined /> {formatTime(distraction.timestamp)}
                        </Text>
                        {distraction.duration && (
                          <Text type="secondary" style={{ fontSize: '12px' }}>
                            持续 {formatDuration(distraction.duration)}
                          </Text>
                        )}
                      </div>
                    </div>
                    
                    <div style={{ fontSize: '14px', lineHeight: '1.4' }}>
                      {distraction.description}
                    </div>
                  </div>
                </List.Item>
              )}
            />
          )}

          {/* 添加分心记录表单 */}
          <Modal
            title="记录分心情况"
            open={showAddForm}
            onCancel={() => {
              setShowAddForm(false);
              form.resetFields();
            }}
            onOk={() => form.submit()}
            width={500}
          >
            <Form
              form={form}
              layout="vertical"
              onFinish={handleAddDistraction}
            >
              <Form.Item
                name="category"
                label="分心类别"
                rules={[{ required: true, message: '请选择分心类别' }]}
              >
                <Select placeholder="选择分心类别">
                  {distractionCategories.map(category => (
                    <Option key={category} value={category}>
                      {category}
                    </Option>
                  ))}
                </Select>
              </Form.Item>

              <Form.Item
                name="description"
                label="详细描述"
                rules={[{ required: true, message: '请描述分心情况' }]}
              >
                <TextArea
                  rows={3}
                  placeholder="描述是什么让你分心了，以及当时的情况..."
                  showCount
                  maxLength={200}
                />
              </Form.Item>

              <Form.Item
                name="severity"
                label="严重程度"
                rules={[{ required: true, message: '请评估严重程度' }]}
              >
                <Select placeholder="选择严重程度">
                  <Option value="low">轻微 - 很快就重新专注了</Option>
                  <Option value="medium">中等 - 需要一些时间才能重新专注</Option>
                  <Option value="high">严重 - 严重影响了专注状态</Option>
                </Select>
              </Form.Item>

              <Form.Item
                name="duration"
                label="持续时间（分钟）"
                extra="可选：估算这次分心持续了多长时间"
              >
                <Rate 
                  count={10}
                  tooltips={['1分钟', '2分钟', '3分钟', '4分钟', '5分钟', '6分钟', '7分钟', '8分钟', '9分钟', '10分钟+']}
                />
              </Form.Item>
            </Form>
          </Modal>
        </div>
      )}
    </Modal>
  );
};

// 快速分心记录按钮
export const QuickDistractionButton: React.FC<{
  style?: React.CSSProperties;
}> = ({ style }) => {
  const [showTracker, setShowTracker] = useState(false);

  const isMonitoring = focusMonitorService.isCurrentlyMonitoring();

  if (!isMonitoring) return null;

  return (
    <>
      <Tooltip title="记录分心情况">
        <Button
          type="text"
          icon={<ExclamationCircleOutlined />}
          onClick={() => setShowTracker(true)}
          style={{
            position: 'fixed',
            bottom: 80,
            right: 20,
            width: 48,
            height: 48,
            borderRadius: '50%',
            backgroundColor: '#fff',
            boxShadow: '0 2px 8px rgba(0,0,0,0.15)',
            border: '1px solid #d9d9d9',
            zIndex: 1000,
            ...style
          }}
        />
      </Tooltip>
      
      <DistractionTracker
        visible={showTracker}
        onClose={() => setShowTracker(false)}
      />
    </>
  );
};

export default DistractionTracker;
