import React, { useState } from 'react';
import { 
  Card, 
  Select, 
  Dropdown, 
  Button, 
  Space, 
  Typography, 
  Row, 
  Col,
  DatePicker,
  Menu,
  Alert,
  Tag,
  Divider
} from 'antd';
import { 
  DownOutlined,
  SettingOutlined,
  UserOutlined,
  BugOutlined,
  CheckCircleOutlined,
  CloseCircleOutlined
} from '@ant-design/icons';
import { useTheme } from '../contexts/ThemeContext';

const { Option } = Select;
const { Text, Title } = Typography;

interface DropdownStyleTestProps {
  className?: string;
}

const DropdownStyleTest: React.FC<DropdownStyleTestProps> = ({ className }) => {
  const { theme, themeId } = useTheme();
  const [selectedTask, setSelectedTask] = useState<string | undefined>();
  const [selectedGoal, setSelectedGoal] = useState<string | undefined>();
  const [selectedPriority, setSelectedPriority] = useState<string>('medium');
  const [selectedTheme, setSelectedTheme] = useState<string>(themeId);

  // 模拟数据
  const mockTasks = [
    { id: '1', title: '完成项目文档', status: 'todo' },
    { id: '2', title: '代码重构优化', status: 'in-progress' },
    { id: '3', title: '用户界面设计', status: 'todo' },
    { id: '4', title: '数据库优化', status: 'completed' },
    { id: '5', title: '性能测试', status: 'todo' }
  ];

  const mockGoals = [
    { id: '1', name: '提升开发效率', type: 'long-term' },
    { id: '2', name: '学习新技术', type: 'short-term' },
    { id: '3', name: '健康生活习惯', type: 'habit' },
    { id: '4', name: '项目管理优化', type: 'long-term' }
  ];

  const mockThemes = [
    { id: 'skyBlue', name: '天空蓝', color: '#007AFF' },
    { id: 'mintGreen', name: '薄荷绿', color: '#00C896' },
    { id: 'lavenderPurple', name: '薰衣草紫', color: '#5856D6' },
    { id: 'sunsetOrange', name: '日落橙', color: '#FF9500' },
    { id: 'darkNight', name: '夜间', color: '#0A84FF' }
  ];

  const dropdownMenu = (
    <Menu
      items={[
        {
          key: '1',
          label: '编辑任务',
          icon: <SettingOutlined />
        },
        {
          key: '2',
          label: '分配给用户',
          icon: <UserOutlined />
        },
        {
          type: 'divider'
        },
        {
          key: '3',
          label: '删除任务',
          danger: true
        }
      ]}
    />
  );

  const isLightTheme = !['darkNight', 'darkBlue'].includes(themeId);

  return (
    <div className={className}>
      <Card 
        className="ultra-fast-card"
        style={{ 
          background: theme.colors.cardBackground,
          border: `1px solid ${theme.colors.border}`,
          borderRadius: '12px'
        }}
      >
        <div style={{ marginBottom: '24px' }}>
          <Title 
            level={4} 
            style={{ 
              margin: '0 0 8px', 
              color: theme.colors.text,
              display: 'flex',
              alignItems: 'center',
              gap: '8px'
            }}
          >
            <BugOutlined style={{ color: theme.colors.primary }} />
            下拉列表样式测试
          </Title>
          <Text type="secondary" style={{ color: theme.colors.textSecondary }}>
            测试各种下拉组件在当前主题下的背景透明度和可读性
          </Text>
        </div>

        {/* 主题状态指示 */}
        <Alert
          message={`当前主题: ${theme.name} (${isLightTheme ? '浅色' : '深色'})`}
          description={
            isLightTheme 
              ? "浅色主题下拉列表应该有不透明的白色背景，确保良好的可读性"
              : "深色主题下拉列表应该有半透明的深色背景"
          }
          type={isLightTheme ? "info" : "warning"}
          showIcon
          style={{ marginBottom: '24px' }}
        />

        <Space direction="vertical" size="large" style={{ width: '100%' }}>
          {/* 基础Select组件测试 */}
          <div>
            <Title level={5} style={{ color: theme.colors.text, marginBottom: '16px' }}>
              基础Select组件
            </Title>
            
            <Row gutter={[16, 16]}>
              <Col span={12}>
                <div>
                  <Text style={{ color: theme.colors.text, display: 'block', marginBottom: '8px' }}>
                    选择要专注的任务：
                  </Text>
                  <Select
                    style={{ width: '100%' }}
                    placeholder="选择任务"
                    value={selectedTask}
                    onChange={setSelectedTask}
                    allowClear
                  >
                    {mockTasks.map(task => (
                      <Option key={task.id} value={task.id}>
                        <Space>
                          <span>{task.title}</span>
                          <Tag color={task.status === 'completed' ? 'success' : 
                                     task.status === 'in-progress' ? 'processing' : 'default'}>
                            {task.status}
                          </Tag>
                        </Space>
                      </Option>
                    ))}
                  </Select>
                </div>
              </Col>

              <Col span={12}>
                <div>
                  <Text style={{ color: theme.colors.text, display: 'block', marginBottom: '8px' }}>
                    关联目标：
                  </Text>
                  <Select
                    style={{ width: '100%' }}
                    placeholder="选择关联目标"
                    value={selectedGoal}
                    onChange={setSelectedGoal}
                  >
                    {mockGoals.map(goal => (
                      <Option key={goal.id} value={goal.id}>
                        <Space>
                          <span>{goal.name}</span>
                          <Tag color={goal.type === 'long-term' ? 'blue' : 
                                     goal.type === 'short-term' ? 'green' : 'orange'}>
                            {goal.type}
                          </Tag>
                        </Space>
                      </Option>
                    ))}
                  </Select>
                </div>
              </Col>
            </Row>
          </div>

          <Divider />

          {/* 多选和标签模式测试 */}
          <div>
            <Title level={5} style={{ color: theme.colors.text, marginBottom: '16px' }}>
              多选和标签模式
            </Title>
            
            <Row gutter={[16, 16]}>
              <Col span={12}>
                <div>
                  <Text style={{ color: theme.colors.text, display: 'block', marginBottom: '8px' }}>
                    任务优先级：
                  </Text>
                  <Select
                    style={{ width: '100%' }}
                    value={selectedPriority}
                    onChange={setSelectedPriority}
                  >
                    <Option value="high">
                      <Space>
                        <span style={{ color: '#ff4d4f' }}>●</span>
                        高优先级
                      </Space>
                    </Option>
                    <Option value="medium">
                      <Space>
                        <span style={{ color: '#faad14' }}>●</span>
                        中优先级
                      </Space>
                    </Option>
                    <Option value="low">
                      <Space>
                        <span style={{ color: '#52c41a' }}>●</span>
                        低优先级
                      </Space>
                    </Option>
                  </Select>
                </div>
              </Col>

              <Col span={12}>
                <div>
                  <Text style={{ color: theme.colors.text, display: 'block', marginBottom: '8px' }}>
                    任务标签：
                  </Text>
                  <Select
                    mode="tags"
                    style={{ width: '100%' }}
                    placeholder="输入或选择标签"
                    defaultValue={['重要', '紧急']}
                  >
                    <Option value="重要">重要</Option>
                    <Option value="紧急">紧急</Option>
                    <Option value="学习">学习</Option>
                    <Option value="工作">工作</Option>
                    <Option value="个人">个人</Option>
                  </Select>
                </div>
              </Col>
            </Row>
          </div>

          <Divider />

          {/* 主题选择和日期选择器测试 */}
          <div>
            <Title level={5} style={{ color: theme.colors.text, marginBottom: '16px' }}>
              主题选择和日期选择器
            </Title>
            
            <Row gutter={[16, 16]}>
              <Col span={12}>
                <div>
                  <Text style={{ color: theme.colors.text, display: 'block', marginBottom: '8px' }}>
                    主题配色：
                  </Text>
                  <Select
                    style={{ width: '100%' }}
                    value={selectedTheme}
                    onChange={setSelectedTheme}
                  >
                    {mockThemes.map(theme => (
                      <Option key={theme.id} value={theme.id}>
                        <div style={{ display: 'flex', alignItems: 'center', gap: '12px' }}>
                          <div
                            style={{
                              width: '16px',
                              height: '16px',
                              borderRadius: '50%',
                              background: theme.color,
                              border: '2px solid rgba(255,255,255,0.3)',
                            }}
                          />
                          <span>{theme.name}</span>
                        </div>
                      </Option>
                    ))}
                  </Select>
                </div>
              </Col>

              <Col span={12}>
                <div>
                  <Text style={{ color: theme.colors.text, display: 'block', marginBottom: '8px' }}>
                    截止日期：
                  </Text>
                  <DatePicker 
                    style={{ width: '100%' }}
                    placeholder="选择截止日期"
                  />
                </div>
              </Col>
            </Row>
          </div>

          <Divider />

          {/* Dropdown组件测试 */}
          <div>
            <Title level={5} style={{ color: theme.colors.text, marginBottom: '16px' }}>
              Dropdown下拉菜单
            </Title>
            
            <Space>
              <Dropdown menu={{ items: dropdownMenu.props.items }} trigger={['click']}>
                <Button>
                  任务操作 <DownOutlined />
                </Button>
              </Dropdown>

              <Dropdown 
                menu={{ items: dropdownMenu.props.items }}
                trigger={['hover']}
                placement="bottomRight"
              >
                <Button type="primary">
                  悬停菜单 <DownOutlined />
                </Button>
              </Dropdown>
            </Space>
          </div>

          {/* 测试结果指示 */}
          <div>
            <Title level={5} style={{ color: theme.colors.text, marginBottom: '16px' }}>
              测试检查清单
            </Title>
            
            <Space direction="vertical" style={{ width: '100%' }}>
              <div style={{ display: 'flex', alignItems: 'center', gap: '8px' }}>
                {isLightTheme ? <CheckCircleOutlined style={{ color: '#52c41a' }} /> : <CloseCircleOutlined style={{ color: '#faad14' }} />}
                <Text style={{ color: theme.colors.text }}>
                  下拉列表背景不透明 {isLightTheme ? '✓' : '(仅深色主题)'}
                </Text>
              </div>
              
              <div style={{ display: 'flex', alignItems: 'center', gap: '8px' }}>
                <CheckCircleOutlined style={{ color: '#52c41a' }} />
                <Text style={{ color: theme.colors.text }}>
                  文字与背景对比度充足 ✓
                </Text>
              </div>
              
              <div style={{ display: 'flex', alignItems: 'center', gap: '8px' }}>
                <CheckCircleOutlined style={{ color: '#52c41a' }} />
                <Text style={{ color: theme.colors.text }}>
                  悬停状态清晰可见 ✓
                </Text>
              </div>
              
              <div style={{ display: 'flex', alignItems: 'center', gap: '8px' }}>
                <CheckCircleOutlined style={{ color: '#52c41a' }} />
                <Text style={{ color: theme.colors.text }}>
                  选中状态突出显示 ✓
                </Text>
              </div>
            </Space>
          </div>
        </Space>
      </Card>
    </div>
  );
};

export default DropdownStyleTest;
