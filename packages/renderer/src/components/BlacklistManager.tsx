import React, { useState, useEffect, useRef } from 'react';
import {
  Card,
  Table,
  Button,
  Modal,
  Form,
  Input,
  Select,
  Switch,
  Space,
  Tag,
  Popconfirm,
  message,
  Tabs,
  Alert,
  Typography,
  Row,
  Col,
  Statistic,
  List,
  InputNumber,
  Checkbox,
  Divider
} from 'antd';
import {
  PlusOutlined,
  EditOutlined,
  DeleteOutlined,
  SafetyOutlined,
  WarningOutlined,
  BulbOutlined,
  HistoryOutlined,
  PlayCircleOutlined
} from '@ant-design/icons';
import { useTheme } from '../contexts/ThemeContext';
import { blacklistManagerService, BlacklistRule, WhitelistRule, ViolationRecord, RuleTemplate } from '../services/BlacklistManagerService';
import { applicationMonitorService } from '../services/ApplicationMonitorService';

const { Title, Text } = Typography;
const { Option } = Select;

interface BlacklistManagerProps {
  onClose?: () => void;
}

const BlacklistManager: React.FC<BlacklistManagerProps> = ({
  onClose
}) => {
  const { theme } = useTheme();
  const [activeTab, setActiveTab] = useState('blacklist');
  const [blacklistRules, setBlacklistRules] = useState<BlacklistRule[]>([]);
  const [whitelistRules, setWhitelistRules] = useState<WhitelistRule[]>([]);
  const [violationHistory, setViolationHistory] = useState<ViolationRecord[]>([]);
  const [templates, setTemplates] = useState<RuleTemplate[]>([]);

  // 添加key重复检测和唯一key生成器
  const usedKeys = useRef<Set<string>>(new Set());
  const keyCounterRef = useRef<number>(0);

  const generateUniqueKey = (prefix: string, id?: string, additionalData?: string): string => {
    keyCounterRef.current++;
    const timestamp = Date.now();
    const counter = keyCounterRef.current;
    const random = Math.random().toString(36).substring(2, 8);
    
    let key: string;
    if (id) {
      key = `${prefix}-${id}`;
    } else {
      key = `${prefix}-${timestamp}-${counter}-${random}`;
      if (additionalData) {
        const hash = additionalData.replace(/[^a-zA-Z0-9]/g, '').substring(0, 8);
        key += `-${hash}`;
      }
    }
    
    // 确保key的唯一性
    let finalKey = key;
    let attempt = 0;
    while (usedKeys.current.has(finalKey) && attempt < 100) {
      attempt++;
      finalKey = `${key}-dup-${attempt}`;
    }
    
    usedKeys.current.add(finalKey);
    return finalKey;
  };

  // 清理已使用的keys
  const clearUsedKeys = () => {
    usedKeys.current.clear();
    keyCounterRef.current = 0;
  };
  
  // 表单状态
  const [isModalVisible, setIsModalVisible] = useState(false);
  const [modalType, setModalType] = useState<'blacklist' | 'whitelist'>('blacklist');
  const [editingRule, setEditingRule] = useState<BlacklistRule | WhitelistRule | null>(null);
  const [form] = Form.useForm();
  
  // 加载数据
  useEffect(() => {
    loadData();
    
    // 定期刷新违规记录 - 优化频率和错误处理
    const interval = setInterval(() => {
      try {
        const violations = blacklistManagerService.getViolationHistory(7) || [];
        setViolationHistory(violations);
      } catch (error) {
        console.error('更新违规记录失败:', error);
        // 不影响用户体验，静默处理错误
      }
    }, 10000); // 改为每10秒刷新一次，减少频率
    
    return () => {
      if (interval) {
        clearInterval(interval);
      }
    };
  }, []);

  const loadData = async () => {
    try {
      // 清理已使用的keys
      clearUsedKeys();

      const blacklist = blacklistManagerService.getBlacklistRules() || [];
      const whitelist = blacklistManagerService.getWhitelistRules() || [];
      const violations = blacklistManagerService.getViolationHistory(7) || [];
      const ruleTemplates = blacklistManagerService.getRuleTemplates() || [];

      // 使用更简单可靠的ID生成策略
      const uniqueBlacklist = blacklist.map((rule, index) => {
        if (rule.id && rule.id.trim() !== '') {
          return rule;
        }
        return {
          ...rule,
          id: `blacklist-${Date.now()}-${index}-${Math.random().toString(36).substring(2, 8)}`
        };
      });

      const uniqueWhitelist = whitelist.map((rule, index) => {
        if (rule.id && rule.id.trim() !== '') {
          return rule;
        }
        return {
          ...rule,
          id: `whitelist-${Date.now()}-${index}-${Math.random().toString(36).substring(2, 8)}`
        };
      });

      setBlacklistRules(uniqueBlacklist);
      setWhitelistRules(uniqueWhitelist);
      setViolationHistory(violations);
      setTemplates(ruleTemplates);

      console.log('数据加载完成:', {
        blacklist: uniqueBlacklist.length,
        whitelist: uniqueWhitelist.length,
        violations: violations.length,
        templates: ruleTemplates.length
      });
    } catch (error) {
      console.error('加载数据失败:', error);
      // 只在开发环境显示错误消息，避免生产环境频繁弹窗
      if (process.env.NODE_ENV === 'development') {
        message.error('加载数据失败');
      }

      // 设置默认空值以避免渲染错误
      setBlacklistRules([]);
      setWhitelistRules([]);
      setViolationHistory([]);
      setTemplates([]);
    }
  };

  // 添加/编辑规则
  const handleSaveRule = async (values: any) => {
    try {
      // 处理时间规则数据转换
      const processedValues = { ...values };
      
      if (modalType === 'blacklist' && values.allowedHours) {
        // 解析时间字符串 "09:00-12:00,14:00-18:00" -> [{ start: "09:00", end: "12:00" }, ...]
        const timeRules = {
          allowedHours: values.allowedHours
            ? values.allowedHours.split(',').map((timeRange: string) => {
                const [start, end] = timeRange.trim().split('-');
                return { start: start?.trim(), end: end?.trim() };
              }).filter((time: any) => time.start && time.end)
            : undefined,
          maxDailyMinutes: values.maxDailyMinutes || undefined,
          allowedDays: values.allowedDays?.length > 0 ? values.allowedDays : undefined
        };
        
        // 只有当有实际值时才添加 timeRules
        if (timeRules.allowedHours?.length > 0 || timeRules.maxDailyMinutes || timeRules.allowedDays?.length > 0) {
          processedValues.timeRules = timeRules;
        }
        
        // 移除原始的表单字段
        delete processedValues.allowedHours;
        delete processedValues.maxDailyMinutes;
        delete processedValues.allowedDays;
      }

      if (modalType === 'blacklist') {
        if (editingRule) {
          await blacklistManagerService.updateBlacklistRule(editingRule.id, processedValues);
          message.success('黑名单规则更新成功');
        } else {
          await blacklistManagerService.addBlacklistRule(processedValues);
          message.success('黑名单规则添加成功');
        }
      } else {
        if (editingRule) {
          await blacklistManagerService.updateWhitelistRule(editingRule.id, processedValues);
          message.success('白名单规则更新成功');
        } else {
          await blacklistManagerService.addWhitelistRule(processedValues);
          message.success('白名单规则添加成功');
        }
      }
      
      setIsModalVisible(false);
      setEditingRule(null);
      form.resetFields();
      await loadData();
    } catch (error) {
      console.error('保存规则失败:', error);
      message.error('保存规则失败');
    }
  };

  // 删除规则
  const handleDeleteRule = async (type: 'blacklist' | 'whitelist', id: string) => {
    try {
      if (type === 'blacklist') {
        await blacklistManagerService.deleteBlacklistRule(id);
        message.success('黑名单规则删除成功');
      } else {
        await blacklistManagerService.deleteWhitelistRule(id);
        message.success('白名单规则删除成功');
      }
      await loadData();
    } catch (error) {
      console.error('删除规则失败:', error);
      message.error('删除规则失败');
    }
  };

  // 应用模板
  const handleApplyTemplate = async (templateId: string) => {
    try {
      await blacklistManagerService.applyTemplate(templateId);
      message.success('模板应用成功');
      await loadData();
    } catch (error) {
      console.error('应用模板失败:', error);
      message.error('应用模板失败');
    }
  };

  // 打开编辑对话框
  const openEditModal = (type: 'blacklist' | 'whitelist', rule?: BlacklistRule | WhitelistRule) => {
    setModalType(type);
    setEditingRule(rule || null);
    setIsModalVisible(true);
    
    if (rule) {
      // 只传递表单需要的字段，避免传递非标准DOM属性
      const formValues = {
        name: rule.name,
        type: rule.type,
        pattern: rule.pattern,
        category: rule.category,
        isActive: rule.isActive,
        description: (rule as any).description || '',
        ...(modalType === 'blacklist' && {
          severity: (rule as BlacklistRule).severity,
          allowedHours: (rule as BlacklistRule).timeRules?.allowedHours?.map(time => `${time.start}-${time.end}`).join(',') || '',
          maxDailyMinutes: (rule as BlacklistRule).timeRules?.maxDailyMinutes || '',
          allowedDays: (rule as BlacklistRule).timeRules?.allowedDays || []
        }),
        ...(modalType === 'whitelist' && {
          priority: (rule as WhitelistRule).priority
        })
      };
      form.setFieldsValue(formValues);
    } else {
      form.resetFields();
    }
  };

  // 黑名单表格列定义
  const blacklistColumns = [
    {
      title: '规则名称',
      dataIndex: 'name',
      key: 'blacklist-name',
      render: (text: string, record: BlacklistRule) => (
        <div>
          <Text strong>{text}</Text>
          <br />
          <Text type="secondary" style={{ fontSize: '12px' }}>
            {record.pattern}
          </Text>
        </div>
      )
    },
    {
      title: '类型',
      dataIndex: 'type',
      key: 'blacklist-type',
      render: (type: string) => (
        <Tag color={type === 'app' ? 'blue' : type === 'website' ? 'green' : 'orange'}>
          {type === 'app' ? '应用' : type === 'website' ? '网站' : '关键词'}
        </Tag>
      )
    },
    {
      title: '分类',
      dataIndex: 'category',
      key: 'blacklist-category',
      render: (category: string) => <Tag>{category}</Tag>
    },
    {
      title: '严重程度',
      dataIndex: 'severity',
      key: 'blacklist-severity',
      render: (severity: string) => (
        <Tag color={severity === 'high' ? 'red' : severity === 'medium' ? 'orange' : 'default'}>
          {severity === 'high' ? '高' : severity === 'medium' ? '中' : '低'}
        </Tag>
      )
    },
    {
      title: '状态',
      dataIndex: 'isActive',
      key: 'blacklist-isActive',
      render: (isActive: boolean, record: BlacklistRule) => (
        <Switch
          checked={isActive}
          onChange={async (checked) => {
            await blacklistManagerService.updateBlacklistRule(record.id, { isActive: checked });
            await loadData();
          }}
        />
      )
    },
    {
      title: '操作',
      key: 'blacklist-actions',
      render: (_: any, record: BlacklistRule) => (
        <Space>
          <Button
            type="link"
            icon={<EditOutlined />}
            onClick={() => openEditModal('blacklist', record)}
          >
            编辑
          </Button>
          <Popconfirm
            title="确定要删除这条规则吗？"
            onConfirm={() => handleDeleteRule('blacklist', record.id)}
          >
            <Button type="link" danger icon={<DeleteOutlined />}>
              删除
            </Button>
          </Popconfirm>
        </Space>
      )
    }
  ];

  // 白名单表格列定义
  const whitelistColumns = [
    {
      title: '规则名称',
      dataIndex: 'name',
      key: 'whitelist-name',
      render: (text: string, record: WhitelistRule) => (
        <div>
          <Text strong>{text}</Text>
          <br />
          <Text type="secondary" style={{ fontSize: '12px' }}>
            {record.pattern}
          </Text>
        </div>
      )
    },
    {
      title: '类型',
      dataIndex: 'type',
      key: 'whitelist-type',
      render: (type: string) => (
        <Tag color={type === 'app' ? 'blue' : type === 'website' ? 'green' : 'orange'}>
          {type === 'app' ? '应用' : type === 'website' ? '网站' : '关键词'}
        </Tag>
      )
    },
    {
      title: '分类',
      dataIndex: 'category',
      key: 'whitelist-category',
      render: (category: string) => <Tag>{category}</Tag>
    },
    {
      title: '优先级',
      dataIndex: 'priority',
      key: 'whitelist-priority',
      render: (priority: string) => (
        <Tag color={priority === 'high' ? 'red' : priority === 'medium' ? 'orange' : 'default'}>
          {priority === 'high' ? '高' : priority === 'medium' ? '中' : '低'}
        </Tag>
      )
    },
    {
      title: '状态',
      dataIndex: 'isActive',
      key: 'whitelist-isActive',
      render: (isActive: boolean, record: WhitelistRule) => (
        <Switch
          checked={isActive}
          onChange={async (checked) => {
            await blacklistManagerService.updateWhitelistRule(record.id, { isActive: checked });
            await loadData();
          }}
        />
      )
    },
    {
      title: '操作',
      key: 'whitelist-actions',
      render: (_: any, record: WhitelistRule) => (
        <Space>
          <Button
            type="link"
            icon={<EditOutlined />}
            onClick={() => openEditModal('whitelist', record)}
          >
            编辑
          </Button>
          <Popconfirm
            title="确定要删除这条规则吗？"
            onConfirm={() => handleDeleteRule('whitelist', record.id)}
          >
            <Button type="link" danger icon={<DeleteOutlined />}>
              删除
            </Button>
          </Popconfirm>
        </Space>
      )
    }
  ];

  // 统计数据
  const getStats = () => {
    const activeBlacklistRules = blacklistRules.filter(rule => rule.isActive).length;
    const activeWhitelistRules = whitelistRules.filter(rule => rule.isActive).length;
    const todayViolations = violationHistory.filter(v => {
      const recordDate = new Date(v.timestamp);
      const today = new Date();
      return recordDate.toDateString() === today.toDateString();
    }).length;

    return { activeBlacklistRules, activeWhitelistRules, todayViolations };
  };

  // 测试x.com访问 - 优化和防抖
  const [isTestingXcom, setIsTestingXcom] = useState(false);
  const testXcomAccess = async () => {
    if (isTestingXcom) {
      message.warning('测试正在进行中，请稍候...');
      return;
    }
    
    try {
      setIsTestingXcom(true);
      console.log('🧪 开始 x.com 访问测试...');
      message.info('开始测试 x.com 访问监控...');
      
      // 记录测试前的违规记录数量
      const currentViolations = violationHistory.length;
      console.log('测试前违规记录数量:', currentViolations);
      
      // 方法1: 通过应用监控服务测试
      await applicationMonitorService.testXcomAccess();
      
      // 等待一段时间确保处理完成
      await new Promise(resolve => setTimeout(resolve, 1000));
      
      // 方法2: 如果方法1没有生成记录，直接创建测试记录
      const violationsAfterMethod1 = blacklistManagerService.getViolationHistory(1).length;
      console.log('方法1后违规记录数量:', violationsAfterMethod1);
      
      if (violationsAfterMethod1 <= currentViolations) {
        console.log('方法1未生成记录，使用方法2直接创建测试记录');
        await blacklistManagerService.testViolationRecord();
      }

      // 刷新数据并检查结果
      await loadData();
      const finalViolations = violationHistory.length;
      console.log('最终违规记录数量:', finalViolations);
      
      if (finalViolations > currentViolations) {
        message.success(`✅ 测试完成！新增 ${finalViolations - currentViolations} 条违规记录`);
      } else {
        message.warning('⚠️ 测试完成，但未生成新的违规记录，请查看控制台日志');
      }
      
    } catch (error) {
      console.error('❌ 测试失败:', error);
      message.error('测试失败: ' + (error as Error).message);
    } finally {
      setIsTestingXcom(false);
    }
  };

  const stats = getStats();

  const tabItems = [
    {
      key: 'blacklist',
      label: (
        <span>
          <SafetyOutlined />
          黑名单
        </span>
      ),
      children: (
        <div>
          <div style={{ marginBottom: 16, display: 'flex', justifyContent: 'space-between', alignItems: 'center' }}>
            <Title level={4} style={{ margin: 0 }}>黑名单管理</Title>
            <Button
              type="primary"
              icon={<PlusOutlined />}
              onClick={() => openEditModal('blacklist')}
            >
              添加黑名单规则
            </Button>
          </div>
          
          <Alert
            message="黑名单规则说明"
            description="黑名单中的应用和网站在专注模式下会被阻止或警告，帮助您远离分心源。"
            type="info"
            showIcon
            style={{ marginBottom: 16 }}
          />
          
          <Table
            columns={blacklistColumns}
            dataSource={blacklistRules}
            rowKey={(record) => {
              // 直接使用record.id，如果没有ID则生成唯一key
              if (record.id) {
                return generateUniqueKey('blacklist', record.id);
              }
              // 为缺少ID的记录生成唯一key
              const fallbackKey = generateUniqueKey('blacklist-noId', undefined, record.name + record.pattern);
              console.warn('黑名单记录缺少ID，生成fallback key:', fallbackKey);
              return fallbackKey;
            }}
            pagination={{ pageSize: 10 }}
            size="small"
          />
        </div>
      )
    },
    {
      key: 'whitelist',
      label: (
        <span>
          <SafetyOutlined />
          白名单
        </span>
      ),
      children: (
        <div>
          <div style={{ marginBottom: 16, display: 'flex', justifyContent: 'space-between', alignItems: 'center' }}>
            <Title level={4} style={{ margin: 0 }}>白名单管理</Title>
            <Button
              type="primary"
              icon={<PlusOutlined />}
              onClick={() => openEditModal('whitelist')}
            >
              添加白名单规则
            </Button>
          </div>
          
          <Alert
            message="白名单规则说明"
            description="白名单中的应用和网站在任何情况下都被允许访问，优先级高于黑名单规则。"
            type="success"
            showIcon
            style={{ marginBottom: 16 }}
          />
          
          <Table
            columns={whitelistColumns}
            dataSource={whitelistRules}
            rowKey={(record) => {
              // 直接使用record.id，如果没有ID则生成唯一key
              if (record.id) {
                return generateUniqueKey('whitelist', record.id);
              }
              // 为缺少ID的记录生成唯一key
              const fallbackKey = generateUniqueKey('whitelist-noId', undefined, record.name + record.pattern);
              console.warn('白名单记录缺少ID，生成fallback key:', fallbackKey);
              return fallbackKey;
            }}
            pagination={{ pageSize: 10 }}
            size="small"
          />
        </div>
      )
    },
    {
      key: 'templates',
      label: (
        <span>
          <BulbOutlined />
          预设模板
        </span>
      ),
      children: (
        <div>
          <Title level={4}>预设模板</Title>
          <Alert
            message="快速设置"
            description="选择适合您角色的预设模板，快速配置黑白名单规则。"
            type="info"
            showIcon
            style={{ marginBottom: 16 }}
          />
          
          <Row gutter={[16, 16]}>
            {templates.map((template, index) => {
              const templateKey = template.id 
                ? generateUniqueKey('template', template.id)
                : generateUniqueKey('template-noId', undefined, template.name + index);
              return (
              <Col xs={24} sm={12} md={8} key={templateKey}>
                <Card
                  title={template.name}
                  extra={
                    <Button
                      type="primary"
                      size="small"
                      onClick={() => handleApplyTemplate(template.id)}
                    >
                      应用
                    </Button>
                  }
                  style={{ height: '100%' }}
                >
                  <Text type="secondary">{template.description}</Text>
                  <div style={{ marginTop: 12 }}>
                    <Text style={{ fontSize: '12px' }}>
                      黑名单: {template.blacklistRules.length} 条 |
                      白名单: {template.whitelistRules.length} 条
                    </Text>
                  </div>
                </Card>
              </Col>
              );
            })}
          </Row>
        </div>
      )
    },
    {
      key: 'history',
      label: (
        <span>
          <HistoryOutlined />
          违规记录
        </span>
      ),
      children: (
        <div>
          <div style={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', marginBottom: 16 }}>
            <Title level={4} style={{ margin: 0 }}>违规记录</Title>
            <Button
              type="primary"
              icon={<PlayCircleOutlined />}
              onClick={testXcomAccess}
              loading={isTestingXcom}
              disabled={isTestingXcom}
            >
              {isTestingXcom ? '测试中...' : '测试 x.com 访问'}
            </Button>
          </div>
          <Alert
            message="违规记录说明"
            description="显示最近7天的黑名单违规记录，帮助您了解分心行为模式。点击右上角按钮可以测试 x.com 访问监控功能。"
            type="warning"
            showIcon
            style={{ marginBottom: 16 }}
          />
          
          {violationHistory.length > 0 ? (
            <List
              dataSource={violationHistory}
              renderItem={(record) => {
                // 使用统一的唯一key生成逻辑
                const uniqueKey = record.id 
                  ? generateUniqueKey('violation', record.id)
                  : generateUniqueKey('violation-noId', undefined, `${record.type}-${record.target}-${record.timestamp.getTime()}`);

                return (
                <List.Item key={uniqueKey}>
                  <div style={{ width: '100%' }}>
                    <div style={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center' }}>
                      <div>
                        <Text strong>{record.target}</Text>
                        <br />
                        <Text type="secondary" style={{ fontSize: '12px' }}>
                          规则: {record.ruleName} |
                          类型: {record.type === 'app' ? '应用' : '网站'} |
                          操作: {record.action === 'blocked' ? '已阻止' : record.action === 'warned' ? '已警告' : '已允许'}
                        </Text>
                      </div>
                      <div>
                        <Text type="secondary" style={{ fontSize: '12px' }}>
                          {new Date(record.timestamp).toLocaleString()}
                        </Text>
                      </div>
                    </div>
                  </div>
                </List.Item>
                );
              }}
              pagination={{ pageSize: 10 }}
            />
          ) : (
            <div style={{ textAlign: 'center', padding: '40px', color: '#999' }}>
              <HistoryOutlined style={{ fontSize: '48px', marginBottom: '16px' }} />
              <div>暂无数据</div>
            </div>
          )}
        </div>
      )
    }
  ];

  const renderModal = () => (
    <Modal
      title={`${editingRule ? '编辑' : '添加'}${modalType === 'blacklist' ? '黑名单' : '白名单'}规则`}
      open={isModalVisible}
      onCancel={() => {
        setIsModalVisible(false);
        setEditingRule(null);
        form.resetFields();
      }}
      onOk={() => form.submit()}
      width={600}
    >
      <Form
        form={form}
        layout="vertical"
        onFinish={handleSaveRule}
      >
        <Form.Item
          name="name"
          label="规则名称"
          rules={[{ required: true, message: '请输入规则名称' }]}
        >
          <Input placeholder="例如：社交媒体" />
        </Form.Item>

        <Form.Item
          name="type"
          label="类型"
          rules={[{ required: true, message: '请选择类型' }]}
        >
          <Select placeholder="选择类型">
            <Option value="app">应用程序</Option>
            <Option value="website">网站</Option>
            <Option value="keyword">关键词</Option>
          </Select>
        </Form.Item>

        <Form.Item
          name="pattern"
          label="匹配模式"
          rules={[{ required: true, message: '请输入匹配模式' }]}
          extra="应用名称、网站域名或关键词，多个用 | 分隔"
        >
          <Input placeholder="例如：facebook.com|twitter.com 或 Photoshop|Sketch" />
        </Form.Item>

        <Form.Item
          name="category"
          label="分类"
          rules={[{ required: true, message: '请输入分类' }]}
        >
          <Input placeholder="例如：社交、娱乐、购物" />
        </Form.Item>

        {modalType === 'blacklist' && (
          <>
            <Form.Item
              name="severity"
              label="严重程度"
              rules={[{ required: true, message: '请选择严重程度' }]}
            >
              <Select placeholder="选择严重程度">
                <Option value="low">低</Option>
                <Option value="medium">中</Option>
                <Option value="high">高</Option>
              </Select>
            </Form.Item>

            <Divider orientation="left">时间限制</Divider>
            
            <Form.Item
              name="allowedHours"
              label="允许访问时间段"
              extra="格式: 09:00-12:00,14:00-18:00 (用逗号分隔多个时间段)"
            >
              <Input placeholder="例如: 09:00-12:00,14:00-18:00" />
            </Form.Item>
            
            <Form.Item
              name="maxDailyMinutes"
              label="每日最大访问分钟数"
            >
              <InputNumber min={0} max={480} placeholder="分钟" style={{ width: '100%' }} />
            </Form.Item>
            
            <Form.Item
              name="allowedDays"
              label="允许访问的星期"
            >
              <Checkbox.Group
                options={[
                  { label: '周日', value: 0 },
                  { label: '周一', value: 1 },
                  { label: '周二', value: 2 },
                  { label: '周三', value: 3 },
                  { label: '周四', value: 4 },
                  { label: '周五', value: 5 },
                  { label: '周六', value: 6 }
                ]}
              />
            </Form.Item>
          </>
        )}

        {modalType === 'whitelist' && (
          <Form.Item
            name="priority"
            label="优先级"
            rules={[{ required: true, message: '请选择优先级' }]}
          >
            <Select placeholder="选择优先级">
              <Option value="low">低</Option>
              <Option value="medium">中</Option>
              <Option value="high">高</Option>
            </Select>
          </Form.Item>
        )}

        <Form.Item
          name="description"
          label="描述"
        >
          <Input.TextArea rows={3} placeholder="可选：规则描述" />
        </Form.Item>

        <Form.Item
          name="isActive"
          label="启用规则"
          valuePropName="checked"
          initialValue={true}
        >
          <Switch />
        </Form.Item>
      </Form>
    </Modal>
  );

  return (
    <Card
      title={
        <div style={{ display: 'flex', alignItems: 'center', gap: '12px' }}>
          <SafetyOutlined style={{ color: theme.colors.primary }} />
          <span>Focus Shield - 专注防护</span>
        </div>
      }
      extra={onClose && (
        <Button onClick={onClose}>关闭</Button>
      )}
      style={{ width: '100%', height: '100%' }}
    >
      {/* 统计概览 */}
      <Row gutter={16} style={{ marginBottom: 24 }}>
        <Col span={8}>
          <Statistic
            title="活跃黑名单规则"
            value={stats.activeBlacklistRules}
            prefix={<SafetyOutlined />}
            valueStyle={{ color: '#cf1322' }}
          />
        </Col>
        <Col span={8}>
          <Statistic
            title="活跃白名单规则"
            value={stats.activeWhitelistRules}
            prefix={<SafetyOutlined />}
            valueStyle={{ color: '#52c41a' }}
          />
        </Col>
        <Col span={8}>
          <Statistic
            title="今日违规次数"
            value={stats.todayViolations}
            prefix={<WarningOutlined />}
            valueStyle={{ color: '#fa8c16' }}
          />
        </Col>
      </Row>

      <Tabs
        activeKey={activeTab}
        onChange={setActiveTab}
        items={tabItems}
        size="small"
      />

      {renderModal()}
    </Card>
  );
};

export default BlacklistManager;