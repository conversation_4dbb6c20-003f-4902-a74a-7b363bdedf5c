import React, { useState, useEffect } from 'react';
import { Modal, Card, Button, Space, Typography, Progress, Tag, Alert } from 'antd';
import { 
  BulbOutlined, 
  EyeOutlined, 
  CloseOutlined,
  PlayCircleOutlined,
  PauseCircleOutlined,
  SettingOutlined
} from '@ant-design/icons';
import { GoalBeaconDisplay, goalBeaconService } from '../services/GoalBeaconService';

const { Title, Text, Paragraph } = Typography;

interface GoalBeaconDisplayComponentProps {
  onActionClick?: (action: string, display: GoalBeaconDisplay) => void;
}

const GoalBeaconDisplayComponent: React.FC<GoalBeaconDisplayComponentProps> = ({
  onActionClick
}) => {
  const [currentDisplay, setCurrentDisplay] = useState<GoalBeaconDisplay | null>(null);
  const [isVisible, setIsVisible] = useState(false);
  const [autoCloseTimer, setAutoCloseTimer] = useState<NodeJS.Timeout | null>(null);

  useEffect(() => {
    // 注册显示监听器
    const unsubscribe = goalBeaconService.onDisplay((display) => {
      showBeacon(display);
    });

    return unsubscribe;
  }, []);

  const showBeacon = (display: GoalBeaconDisplay) => {
    setCurrentDisplay(display);
    setIsVisible(true);

    // 清除之前的定时器
    if (autoCloseTimer) {
      clearTimeout(autoCloseTimer);
    }

    // 设置自动关闭定时器
    const timer = setTimeout(() => {
      setIsVisible(false);
      setCurrentDisplay(null);
    }, display.duration * 1000);

    setAutoCloseTimer(timer);
  };

  const handleClose = () => {
    if (autoCloseTimer) {
      clearTimeout(autoCloseTimer);
      setAutoCloseTimer(null);
    }
    setIsVisible(false);
    setCurrentDisplay(null);
  };

  const handleActionClick = (action: string) => {
    if (currentDisplay && onActionClick) {
      onActionClick(action, currentDisplay);
    }
    handleClose();
  };

  if (!currentDisplay) return null;

  // 浮动样式
  if (currentDisplay.visualStyle === 'floating') {
    return (
      <div
        style={{
          position: 'fixed',
          top: 20,
          right: 20,
          width: 350,
          zIndex: 2000,
          transform: isVisible ? 'translateX(0)' : 'translateX(100%)',
          transition: 'transform 0.3s ease-in-out',
          opacity: isVisible ? 1 : 0,
        }}
      >
        <Card
          size="small"
          style={{
            backgroundColor: 'rgba(255, 255, 255, 0.95)',
            backdropFilter: 'blur(10px)',
            border: '1px solid var(--color-border)',
            borderRadius: 'var(--radius-medium)',
            boxShadow: '0 8px 32px rgba(0, 0, 0, 0.1)',
          }}
          extra={
            <Button
              type="text"
              size="small"
              icon={<CloseOutlined />}
              onClick={handleClose}
              style={{ padding: '2px 4px' }}
            />
          }
        >
          <FloatingBeaconContent 
            display={currentDisplay} 
            onActionClick={handleActionClick}
          />
        </Card>
      </div>
    );
  }

  // 卡片样式
  if (currentDisplay.visualStyle === 'card') {
    return (
      <div
        style={{
          position: 'fixed',
          top: '50%',
          left: '50%',
          transform: 'translate(-50%, -50%)',
          zIndex: 2000,
          opacity: isVisible ? 1 : 0,
          transition: 'opacity 0.3s ease-in-out',
        }}
      >
        <Card
          style={{
            width: 450,
            backgroundColor: 'white',
            borderRadius: 'var(--radius-large)',
            boxShadow: '0 12px 48px rgba(0, 0, 0, 0.15)',
          }}
        >
          <CardBeaconContent 
            display={currentDisplay} 
            onActionClick={handleActionClick}
            onClose={handleClose}
          />
        </Card>
      </div>
    );
  }

  // 覆盖样式
  if (currentDisplay.visualStyle === 'overlay') {
    return (
      <Modal
        title={null}
        open={isVisible}
        onCancel={handleClose}
        footer={null}
        width={600}
        centered
        closable={false}
        maskStyle={{ backgroundColor: 'rgba(0, 0, 0, 0.7)' }}
      >
        <OverlayBeaconContent 
          display={currentDisplay} 
          onActionClick={handleActionClick}
          onClose={handleClose}
        />
      </Modal>
    );
  }

  return null;
};

// 浮动内容组件
const FloatingBeaconContent: React.FC<{
  display: GoalBeaconDisplay;
  onActionClick: (action: string) => void;
}> = ({ display, onActionClick }) => (
  <div>
    <div style={{ marginBottom: 12 }}>
      <div style={{ display: 'flex', alignItems: 'center', gap: 8, marginBottom: 8 }}>
        <BulbOutlined style={{ color: '#1890ff', fontSize: '16px' }} />
        <Text strong style={{ fontSize: '14px' }}>目标提醒</Text>
      </div>
      <Title level={5} style={{ margin: 0, fontSize: '16px', lineHeight: '1.3' }}>
        {display.goalName}
      </Title>
    </div>

    <div style={{ marginBottom: 12 }}>
      <Text style={{ fontSize: '13px', color: '#1890ff', fontWeight: 500 }}>
        {display.motivationalMessage}
      </Text>
    </div>

    {display.whyPower && (
      <div style={{ 
        marginBottom: 12, 
        padding: '8px 12px', 
        backgroundColor: '#f6ffed', 
        border: '1px solid #b7eb8f',
        borderRadius: '6px'
      }}>
        <Text style={{ fontSize: '12px', color: '#389e0d', lineHeight: '1.4' }}>
          💪 {display.whyPower.slice(0, 100)}
          {display.whyPower.length > 100 && '...'}
        </Text>
      </div>
    )}

    <div style={{ display: 'flex', gap: 8 }}>
      {display.actions.slice(0, 2).map((action, index) => (
        <Button
          key={index}
          type={action.primary ? 'primary' : 'default'}
          size="small"
          onClick={() => onActionClick(action.action)}
          style={{ flex: 1, fontSize: '12px' }}
        >
          {action.label}
        </Button>
      ))}
    </div>
  </div>
);

// 卡片内容组件
const CardBeaconContent: React.FC<{
  display: GoalBeaconDisplay;
  onActionClick: (action: string) => void;
  onClose: () => void;
}> = ({ display, onActionClick, onClose }) => (
  <div style={{ textAlign: 'center', padding: '20px 0' }}>
    <div style={{ fontSize: 48, marginBottom: 16 }}>
      🎯
    </div>
    
    <Title level={2} style={{ marginBottom: 8, color: '#1890ff' }}>
      {display.goalName}
    </Title>
    
    <Text style={{ fontSize: '16px', color: '#1890ff', fontWeight: 500, marginBottom: 24, display: 'block' }}>
      {display.motivationalMessage}
    </Text>

    {display.whyPower && (
      <Alert
        message="你的核心驱动力"
        description={display.whyPower}
        type="info"
        showIcon
        style={{ marginBottom: 24, textAlign: 'left' }}
      />
    )}

    {display.progress > 0 && (
      <div style={{ marginBottom: 24 }}>
        <Text type="secondary" style={{ fontSize: '14px', marginBottom: 8, display: 'block' }}>
          目标进度
        </Text>
        <Progress 
          percent={display.progress} 
          strokeColor="#1890ff"
          style={{ marginBottom: 8 }}
        />
      </div>
    )}

    <Space size="large">
      {display.actions.map((action, index) => (
        <Button
          key={index}
          type={action.primary ? 'primary' : 'default'}
          size="large"
          onClick={() => onActionClick(action.action)}
        >
          {action.label}
        </Button>
      ))}
    </Space>

    <div style={{ marginTop: 16 }}>
      <Button type="text" size="small" onClick={onClose}>
        关闭提醒
      </Button>
    </div>
  </div>
);

// 覆盖内容组件
const OverlayBeaconContent: React.FC<{
  display: GoalBeaconDisplay;
  onActionClick: (action: string) => void;
  onClose: () => void;
}> = ({ display, onActionClick, onClose }) => (
  <div style={{ textAlign: 'center', padding: '40px 20px' }}>
    <div style={{ fontSize: 64, marginBottom: 24 }}>
      🌟
    </div>
    
    <Title level={1} style={{ marginBottom: 16, color: '#1890ff' }}>
      {display.goalName}
    </Title>
    
    <Text style={{ fontSize: '20px', color: '#1890ff', fontWeight: 500, marginBottom: 32, display: 'block' }}>
      {display.motivationalMessage}
    </Text>

    {display.whyPower && (
      <Card 
        style={{ 
          marginBottom: 32, 
          backgroundColor: '#f6ffed',
          border: '1px solid #b7eb8f'
        }}
      >
        <Title level={4} style={{ color: '#389e0d', marginBottom: 12 }}>
          💪 记住你的初心
        </Title>
        <Paragraph style={{ color: '#389e0d', fontSize: '16px', lineHeight: '1.6' }}>
          {display.whyPower}
        </Paragraph>
      </Card>
    )}

    <Space size="large" style={{ marginBottom: 24 }}>
      {display.actions.map((action, index) => (
        <Button
          key={index}
          type={action.primary ? 'primary' : 'default'}
          size="large"
          onClick={() => onActionClick(action.action)}
          style={{ minWidth: 120 }}
        >
          {action.label}
        </Button>
      ))}
    </Space>

    <div>
      <Button type="text" onClick={onClose}>
        关闭提醒
      </Button>
    </div>
  </div>
);

// 目标提醒设置组件
export const GoalBeaconSettings: React.FC<{
  onConfigChange?: (config: any) => void;
}> = ({ onConfigChange }) => {
  const [config, setConfig] = useState(goalBeaconService.getConfig());

  const handleConfigUpdate = (updates: any) => {
    const newConfig = { ...config, ...updates };
    setConfig(newConfig);
    goalBeaconService.updateConfig(updates);
    
    if (onConfigChange) {
      onConfigChange(newConfig);
    }
  };

  return (
    <Card title="目标可视化提醒设置" size="small">
      <div style={{ display: 'flex', flexDirection: 'column', gap: 16 }}>
        {/* 启用开关 */}
        <div>
          <div style={{ display: 'flex', alignItems: 'center', gap: 8, marginBottom: 8 }}>
            <input
              type="checkbox"
              checked={config.enabled}
              onChange={(e) => handleConfigUpdate({ enabled: e.target.checked })}
            />
            <span>启用目标可视化提醒</span>
          </div>
        </div>

        {config.enabled && (
          <>
            {/* 触发间隔 */}
            <div>
              <label>
                提醒间隔：每
                <input
                  type="number"
                  value={config.triggerInterval}
                  onChange={(e) => handleConfigUpdate({ triggerInterval: parseInt(e.target.value) })}
                  style={{ width: 60, margin: '0 4px' }}
                  min="5"
                  max="120"
                />
                分钟
              </label>
            </div>

            {/* 触发时机 */}
            <div>
              <div style={{ marginBottom: 8 }}>触发时机：</div>
              <div style={{ marginLeft: 16 }}>
                <div>
                  <input
                    type="checkbox"
                    checked={config.showOnTaskStart}
                    onChange={(e) => handleConfigUpdate({ showOnTaskStart: e.target.checked })}
                  />
                  <span style={{ marginLeft: 8 }}>开始任务时</span>
                </div>
                <div>
                  <input
                    type="checkbox"
                    checked={config.showOnBreak}
                    onChange={(e) => handleConfigUpdate({ showOnBreak: e.target.checked })}
                  />
                  <span style={{ marginLeft: 8 }}>休息时</span>
                </div>
                <div>
                  <input
                    type="checkbox"
                    checked={config.showOnDistraction}
                    onChange={(e) => handleConfigUpdate({ showOnDistraction: e.target.checked })}
                  />
                  <span style={{ marginLeft: 8 }}>分心时</span>
                </div>
                <div>
                  <input
                    type="checkbox"
                    checked={config.showOnLowFocus}
                    onChange={(e) => handleConfigUpdate({ showOnLowFocus: e.target.checked })}
                  />
                  <span style={{ marginLeft: 8 }}>专注度低时</span>
                </div>
              </div>
            </div>

            {/* 提醒样式 */}
            <div>
              <label>
                提醒样式：
                <select
                  value={config.reminderStyle}
                  onChange={(e) => handleConfigUpdate({ reminderStyle: e.target.value })}
                  style={{ marginLeft: 8 }}
                >
                  <option value="subtle">轻量 - 右上角浮动</option>
                  <option value="prominent">显著 - 居中卡片</option>
                  <option value="immersive">沉浸 - 全屏覆盖</option>
                </select>
              </label>
            </div>

            {/* 包含驱动力 */}
            <div>
              <input
                type="checkbox"
                checked={config.includeWhyPower}
                onChange={(e) => handleConfigUpdate({ includeWhyPower: e.target.checked })}
              />
              <span style={{ marginLeft: 8 }}>显示核心驱动力</span>
            </div>
          </>
        )}
      </div>
    </Card>
  );
};

export default GoalBeaconDisplayComponent;
