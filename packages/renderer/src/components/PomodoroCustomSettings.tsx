import React, { useState, useEffect } from 'react';
import { Modal, Form, InputNumber, Switch, Select, Card, Space, Typography, Divider, Slider, Alert, Tabs } from 'antd';
import { ClockCircleOutlined, BellOutlined, SettingOutlined, SoundOutlined } from '@ant-design/icons';
import BackgroundAudioSettings from './BackgroundAudioSettings';

const { Text, Title } = Typography;
const { Option } = Select;
const { TabPane } = Tabs;

interface PomodoroSettings {
  workDuration: number;
  shortBreakDuration: number;
  longBreakDuration: number;
  sessionsUntilLongBreak: number;
  autoStartBreaks: boolean;
  autoStartNextSession: boolean;
  soundEnabled: boolean;
  soundVolume: number;
  notificationEnabled: boolean;
  customWorkDurations: number[];
  customBreakDurations: number[];
  focusMode: 'standard' | 'flexible' | 'custom';
  allowPause: boolean;
  allowSkip: boolean;
  strictMode: boolean;
}

interface PomodoroCustomSettingsProps {
  visible: boolean;
  onCancel: () => void;
  onSave: (settings: PomodoroSettings) => void;
  initialSettings: PomodoroSettings;
}

const PomodoroCustomSettings: React.FC<PomodoroCustomSettingsProps> = ({
  visible,
  onCancel,
  onSave,
  initialSettings
}) => {
  const [form] = Form.useForm();
  const [focusMode, setFocusMode] = useState<'standard' | 'flexible' | 'custom'>('standard');

  useEffect(() => {
    if (visible) {
      form.setFieldsValue(initialSettings);
      setFocusMode(initialSettings.focusMode || 'standard');
    }
  }, [visible, initialSettings, form]);

  const handleSubmit = async () => {
    try {
      const values = await form.validateFields();
      onSave({ ...values, focusMode });
    } catch (error) {
      console.error('表单验证失败:', error);
    }
  };

  const presetConfigs = {
    standard: {
      workDuration: 25,
      shortBreakDuration: 5,
      longBreakDuration: 15,
      sessionsUntilLongBreak: 4
    },
    short: {
      workDuration: 15,
      shortBreakDuration: 3,
      longBreakDuration: 10,
      sessionsUntilLongBreak: 4
    },
    long: {
      workDuration: 45,
      shortBreakDuration: 10,
      longBreakDuration: 30,
      sessionsUntilLongBreak: 3
    },
    study: {
      workDuration: 50,
      shortBreakDuration: 10,
      longBreakDuration: 20,
      sessionsUntilLongBreak: 3
    }
  };

  const applyPreset = (preset: keyof typeof presetConfigs) => {
    const config = presetConfigs[preset];
    form.setFieldsValue(config);
  };

  return (
    <Modal
      title={
        <Space>
          <SettingOutlined />
          <span>番茄钟自定义设置</span>
        </Space>
      }
      open={visible}
      onCancel={onCancel}
      onOk={handleSubmit}
      width={700}
      style={{ top: 20 }}
    >
      <Tabs defaultActiveKey="basic" type="card">
        <TabPane
          tab={
            <Space>
              <ClockCircleOutlined />
              <span>基础设置</span>
            </Space>
          }
          key="basic"
        >
          <Form
            form={form}
            layout="vertical"
            initialValues={initialSettings}
          >
            {/* 专注模式选择 */}
            <Card size="small" style={{ marginBottom: 16 }}>
              <Title level={5}>
                <ClockCircleOutlined /> 专注模式
              </Title>
          <Select
            value={focusMode}
            onChange={setFocusMode}
            style={{ width: '100%' }}
          >
            <Option value="standard">标准模式 - 经典番茄钟</Option>
            <Option value="flexible">灵活模式 - 可调整时长</Option>
            <Option value="custom">自定义模式 - 完全自定义</Option>
          </Select>
          
          {focusMode === 'standard' && (
            <div style={{ marginTop: 12 }}>
              <Text type="secondary">使用经典的25分钟工作+5分钟休息模式</Text>
              <div style={{ marginTop: 8 }}>
                <Space>
                  <Text>快速预设：</Text>
                  <a onClick={() => applyPreset('standard')}>标准(25/5/15)</a>
                  <a onClick={() => applyPreset('short')}>短时(15/3/10)</a>
                  <a onClick={() => applyPreset('long')}>长时(45/10/30)</a>
                  <a onClick={() => applyPreset('study')}>学习(50/10/20)</a>
                </Space>
              </div>
            </div>
          )}
          
          {focusMode === 'flexible' && (
            <Alert
              message="灵活模式"
              description="可以在会话中调整时长，适合不确定任务时间的情况"
              type="info"
              style={{ marginTop: 12 }}
            />
          )}
          
          {focusMode === 'custom' && (
            <Alert
              message="自定义模式"
              description="完全自定义所有参数，包括多种时长选项"
              type="warning"
              style={{ marginTop: 12 }}
            />
          )}
        </Card>

        {/* 时间设置 */}
        <Card size="small" style={{ marginBottom: 16 }}>
          <Title level={5}>时间设置</Title>
          
          <Form.Item
            name="workDuration"
            label="工作时长（分钟）"
            rules={[{ required: true, min: 1, max: 120, type: 'number' }]}
          >
            <Slider
              min={5}
              max={120}
              step={5}
              marks={{
                15: '15分',
                25: '25分',
                45: '45分',
                90: '90分'
              }}
              tooltip={{ formatter: (value) => `${value}分钟` }}
            />
          </Form.Item>

          <Form.Item
            name="shortBreakDuration"
            label="短休息时长（分钟）"
            rules={[{ required: true, min: 1, max: 30, type: 'number' }]}
          >
            <Slider
              min={1}
              max={30}
              step={1}
              marks={{
                3: '3分',
                5: '5分',
                10: '10分',
                15: '15分'
              }}
              tooltip={{ formatter: (value) => `${value}分钟` }}
            />
          </Form.Item>

          <Form.Item
            name="longBreakDuration"
            label="长休息时长（分钟）"
            rules={[{ required: true, min: 5, max: 60, type: 'number' }]}
          >
            <Slider
              min={5}
              max={60}
              step={5}
              marks={{
                15: '15分',
                20: '20分',
                30: '30分',
                45: '45分'
              }}
              tooltip={{ formatter: (value) => `${value}分钟` }}
            />
          </Form.Item>

          <Form.Item
            name="sessionsUntilLongBreak"
            label="长休息间隔（完成几个番茄后）"
            rules={[{ required: true, min: 2, max: 8, type: 'number' }]}
          >
            <InputNumber min={2} max={8} style={{ width: '100%' }} />
          </Form.Item>
        </Card>

        {/* 自动化设置 */}
        <Card size="small" style={{ marginBottom: 16 }}>
          <Title level={5}>
            <BellOutlined /> 自动化设置
          </Title>
          
          <Form.Item
            name="autoStartBreaks"
            label="自动开始休息"
            valuePropName="checked"
            extra="工作时间结束后自动开始休息"
          >
            <Switch />
          </Form.Item>

          <Form.Item
            name="autoStartNextSession"
            label="自动开始下一个工作时段"
            valuePropName="checked"
            extra="休息结束后自动开始下一个工作时段"
          >
            <Switch />
          </Form.Item>

          <Form.Item
            name="allowPause"
            label="允许暂停"
            valuePropName="checked"
            extra="是否允许在专注时间内暂停计时器"
          >
            <Switch />
          </Form.Item>

          <Form.Item
            name="allowSkip"
            label="允许跳过"
            valuePropName="checked"
            extra="是否允许跳过当前时段"
          >
            <Switch />
          </Form.Item>

          <Form.Item
            name="strictMode"
            label="严格模式"
            valuePropName="checked"
            extra="启用后将限制在专注时间内的某些操作"
          >
            <Switch />
          </Form.Item>
        </Card>

        {/* 通知设置 */}
        <Card size="small">
          <Title level={5}>通知设置</Title>
          
          <Form.Item
            name="notificationEnabled"
            label="桌面通知"
            valuePropName="checked"
            extra="在时段结束时显示桌面通知"
          >
            <Switch />
          </Form.Item>

          <Form.Item
            name="soundEnabled"
            label="声音提醒"
            valuePropName="checked"
            extra="在时段结束时播放提示音"
          >
            <Switch />
          </Form.Item>

          <Form.Item
            name="soundVolume"
            label="音量"
            extra="提示音音量大小"
          >
            <Slider
              min={0}
              max={100}
              step={10}
              marks={{
                0: '静音',
                50: '50%',
                100: '100%'
              }}
              tooltip={{ formatter: (value) => `${value}%` }}
            />
          </Form.Item>
        </Card>

        {/* 自定义时长选项（仅在自定义模式下显示） */}
        {focusMode === 'custom' && (
          <Card size="small" style={{ marginTop: 16 }}>
            <Title level={5}>自定义时长选项</Title>
            <Text type="secondary">
              为不同类型的任务预设多种时长选项
            </Text>
            
            <Form.Item
              name="customWorkDurations"
              label="工作时长选项（分钟）"
              extra="用逗号分隔，例如：15,25,45,90"
            >
              <Select
                mode="tags"
                style={{ width: '100%' }}
                placeholder="输入自定义时长"
                tokenSeparators={[',']}
              >
                <Option value="15">15分钟</Option>
                <Option value="25">25分钟</Option>
                <Option value="45">45分钟</Option>
                <Option value="90">90分钟</Option>
              </Select>
            </Form.Item>

            <Form.Item
              name="customBreakDurations"
              label="休息时长选项（分钟）"
              extra="用逗号分隔，例如：3,5,10,15"
            >
              <Select
                mode="tags"
                style={{ width: '100%' }}
                placeholder="输入自定义时长"
                tokenSeparators={[',']}
              >
                <Option value="3">3分钟</Option>
                <Option value="5">5分钟</Option>
                <Option value="10">10分钟</Option>
                <Option value="15">15分钟</Option>
              </Select>
            </Form.Item>
          </Card>
        )}
          </Form>
        </TabPane>

        <TabPane
          tab={
            <Space>
              <SoundOutlined />
              <span>背景音效</span>
            </Space>
          }
          key="audio"
        >
          <BackgroundAudioSettings />
        </TabPane>
      </Tabs>
    </Modal>
  );
};

export default PomodoroCustomSettings;
