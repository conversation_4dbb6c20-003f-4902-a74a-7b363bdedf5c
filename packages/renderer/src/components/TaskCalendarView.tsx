import React, { useState, useMemo } from 'react';
import { Calendar, Badge, Card, Tag, Button, Space, Modal, List, Empty, Tooltip } from 'antd';
import { 
  PlayCircleOutlined, 
  EditOutlined, 
  DeleteOutlined, 
  ClockCircleOutlined,
  FlagOutlined,
  CalendarOutlined
} from '@ant-design/icons';
import { Task } from '../types';
import dayjs, { Dayjs } from 'dayjs';
import type { BadgeProps } from 'antd';

interface TaskCalendarViewProps {
  tasks: Task[];
  onTaskEdit: (task: Task) => void;
  onTaskDelete: (taskId: string) => void;
  onStartPomodoro: (task: Task) => void;
  loading?: boolean;
}

interface DayTasks {
  date: string;
  tasks: Task[];
}

const TaskCalendarView: React.FC<TaskCalendarViewProps> = ({
  tasks,
  onTaskEdit,
  onTaskDelete,
  onStartPomodoro,
  loading = false
}) => {
  const [selectedDate, setSelectedDate] = useState<Dayjs>(dayjs());
  const [showTaskModal, setShowTaskModal] = useState(false);

  // 按日期分组任务
  const tasksByDate = useMemo(() => {
    const grouped: { [key: string]: Task[] } = {};
    
    tasks.forEach(task => {
      if (task.deadline) {
        const dateKey = dayjs(task.deadline).format('YYYY-MM-DD');
        if (!grouped[dateKey]) {
          grouped[dateKey] = [];
        }
        grouped[dateKey].push(task);
      }
    });

    return grouped;
  }, [tasks]);

  // 获取选中日期的任务
  const selectedDateTasks = useMemo(() => {
    const dateKey = selectedDate.format('YYYY-MM-DD');
    return tasksByDate[dateKey] || [];
  }, [tasksByDate, selectedDate]);

  // 获取日期的任务数量和状态
  const getDateCellData = (date: Dayjs) => {
    const dateKey = date.format('YYYY-MM-DD');
    const dayTasks = tasksByDate[dateKey] || [];
    
    if (dayTasks.length === 0) return null;

    const completedCount = dayTasks.filter(task => task.status === 'completed').length;
    const inProgressCount = dayTasks.filter(task => task.status === 'in-progress').length;
    const todoCount = dayTasks.filter(task => task.status === 'todo').length;
    const overdueCount = dayTasks.filter(task => 
      task.status !== 'completed' && dayjs(task.deadline).isBefore(dayjs(), 'day')
    ).length;

    return {
      total: dayTasks.length,
      completed: completedCount,
      inProgress: inProgressCount,
      todo: todoCount,
      overdue: overdueCount,
      tasks: dayTasks
    };
  };

  // 日历单元格渲染
  const dateCellRender = (date: Dayjs) => {
    const cellData = getDateCellData(date);
    if (!cellData) return null;

    const { total, completed, inProgress, overdue } = cellData;

    return (
      <div style={{ fontSize: '12px' }}>
        {overdue > 0 && (
          <Badge 
            status="error" 
            text={`${overdue}逾期`}
            style={{ display: 'block', fontSize: '10px' }}
          />
        )}
        {inProgress > 0 && (
          <Badge 
            status="processing" 
            text={`${inProgress}进行`}
            style={{ display: 'block', fontSize: '10px' }}
          />
        )}
        {completed > 0 && (
          <Badge 
            status="success" 
            text={`${completed}完成`}
            style={{ display: 'block', fontSize: '10px' }}
          />
        )}
        {total - completed - inProgress - overdue > 0 && (
          <Badge 
            status="default" 
            text={`${total - completed - inProgress - overdue}待办`}
            style={{ display: 'block', fontSize: '10px' }}
          />
        )}
      </div>
    );
  };

  // 月份单元格渲染
  const monthCellRender = (date: Dayjs) => {
    const monthTasks = tasks.filter(task => 
      task.deadline && dayjs(task.deadline).isSame(date, 'month')
    );
    
    if (monthTasks.length === 0) return null;

    return (
      <div style={{ fontSize: '12px', textAlign: 'center' }}>
        <Badge 
          count={monthTasks.length} 
          style={{ backgroundColor: '#52c41a' }}
        />
      </div>
    );
  };

  const handleDateSelect = (date: Dayjs) => {
    setSelectedDate(date);
    const cellData = getDateCellData(date);
    if (cellData && cellData.total > 0) {
      setShowTaskModal(true);
    }
  };

  const getPriorityColor = (priority: string) => {
    switch (priority) {
      case 'high': return '#ff4d4f';
      case 'medium': return '#faad14';
      case 'low': return '#52c41a';
      default: return '#d9d9d9';
    }
  };

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'todo': return 'default';
      case 'in-progress': return 'processing';
      case 'completed': return 'success';
      case 'paused': return 'warning';
      case 'cancelled': return 'error';
      default: return 'default';
    }
  };

  const getStatusText = (status: string) => {
    switch (status) {
      case 'todo': return '待办';
      case 'in-progress': return '进行中';
      case 'completed': return '已完成';
      case 'paused': return '已暂停';
      case 'cancelled': return '已取消';
      default: return status;
    }
  };

  const isOverdue = (task: Task) => {
    return task.deadline && 
           task.status !== 'completed' && 
           dayjs(task.deadline).isBefore(dayjs(), 'day');
  };

  if (loading) {
    return (
      <div style={{ textAlign: 'center', padding: '40px' }}>
        <div>加载中...</div>
      </div>
    );
  }

  return (
    <div style={{ height: '100%' }}>
      <Card 
        title={
          <Space>
            <CalendarOutlined />
            <span>任务日历</span>
            <Tag color="blue">{tasks.length} 个任务</Tag>
          </Space>
        }
        style={{ height: '100%' }}
        bodyStyle={{ height: 'calc(100% - 57px)', overflow: 'auto' }}
      >
        <Calendar
          dateCellRender={dateCellRender}
          monthCellRender={monthCellRender}
          onSelect={handleDateSelect}
          style={{ height: '100%' }}
        />
      </Card>

      {/* 任务详情模态框 */}
      <Modal
        title={
          <Space>
            <CalendarOutlined />
            <span>{selectedDate.format('YYYY年MM月DD日')} 的任务</span>
            <Tag color="blue">{selectedDateTasks.length} 个任务</Tag>
          </Space>
        }
        open={showTaskModal}
        onCancel={() => setShowTaskModal(false)}
        footer={null}
        width={600}
        style={{ top: 20 }}
      >
        {selectedDateTasks.length === 0 ? (
          <Empty description="该日期没有任务" />
        ) : (
          <List
            dataSource={selectedDateTasks}
            renderItem={(task) => (
              <List.Item
                style={{
                  backgroundColor: isOverdue(task) ? '#fff2f0' : 'transparent',
                  borderRadius: 'var(--radius-small)',
                  padding: '12px',
                  marginBottom: '8px',
                  border: isOverdue(task) ? '1px solid #ffccc7' : '1px solid var(--color-border)',
                }}
                actions={[
                  <Tooltip title="开始番茄钟" key="pomodoro">
                    <Button
                      type="text"
                      size="small"
                      icon={<PlayCircleOutlined />}
                      onClick={() => {
                        onStartPomodoro(task);
                        setShowTaskModal(false);
                      }}
                    />
                  </Tooltip>,
                  <Tooltip title="编辑任务" key="edit">
                    <Button
                      type="text"
                      size="small"
                      icon={<EditOutlined />}
                      onClick={() => {
                        onTaskEdit(task);
                        setShowTaskModal(false);
                      }}
                    />
                  </Tooltip>,
                  <Tooltip title="删除任务" key="delete">
                    <Button
                      type="text"
                      size="small"
                      icon={<DeleteOutlined />}
                      onClick={() => {
                        onTaskDelete(task.id);
                        setShowTaskModal(false);
                      }}
                      danger
                    />
                  </Tooltip>
                ]}
              >
                <List.Item.Meta
                  title={
                    <div style={{ display: 'flex', alignItems: 'center', gap: 8 }}>
                      <span style={{ 
                        color: isOverdue(task) ? '#ff4d4f' : 'var(--color-text)',
                        fontWeight: 500 
                      }}>
                        {task.title}
                      </span>
                      {isOverdue(task) && (
                        <Tag color="red" size="small">逾期</Tag>
                      )}
                    </div>
                  }
                  description={
                    <div>
                      <div style={{ marginBottom: 8 }}>
                        {task.description}
                      </div>
                      <Space size="small" wrap>
                        <Tag color={getPriorityColor(task.priority)} size="small">
                          <FlagOutlined /> {task.priority === 'high' ? '高' : task.priority === 'medium' ? '中' : '低'}优先级
                        </Tag>
                        <Tag color={getStatusColor(task.status)} size="small">
                          {getStatusText(task.status)}
                        </Tag>
                        {task.estimatedTime && (
                          <Tag size="small">
                            <ClockCircleOutlined /> {task.estimatedTime}分钟
                          </Tag>
                        )}
                      </Space>
                    </div>
                  }
                />
              </List.Item>
            )}
          />
        )}
      </Modal>
    </div>
  );
};

export default TaskCalendarView;
