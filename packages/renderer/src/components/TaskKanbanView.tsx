import React, { useState, useMemo } from 'react';
import { Card, Tag, Button, Space, Avatar, Tooltip, Empty } from 'antd';
import { 
  PlayCircleOutlined, 
  EditOutlined, 
  DeleteOutlined, 
  ClockCircleOutlined,
  FlagOutlined,
  UserOutlined
} from '@ant-design/icons';
import { Task } from '../types';
import { DragDropContext, Droppable, Draggable, DropResult } from 'react-beautiful-dnd';

interface TaskKanbanViewProps {
  tasks: Task[];
  onTaskUpdate: (taskId: string, updates: Partial<Task>) => void;
  onTaskEdit: (task: Task) => void;
  onTaskDelete: (taskId: string) => void;
  onStartPomodoro: (task: Task) => void;
  loading?: boolean;
}

interface KanbanColumn {
  id: string;
  title: string;
  status: string;
  color: string;
  tasks: Task[];
}

const TaskKanbanView: React.FC<TaskKanbanViewProps> = ({
  tasks,
  onTaskUpdate,
  onTaskEdit,
  onTaskDelete,
  onStartPomodoro,
  loading = false
}) => {
  // 看板列定义
  const columns: KanbanColumn[] = useMemo(() => {
    const columnDefs = [
      { id: 'todo', title: '待办', status: 'todo', color: '#f5f5f5' },
      { id: 'in-progress', title: '进行中', status: 'in-progress', color: '#e6f7ff' },
      { id: 'review', title: '待审核', status: 'review', color: '#fff7e6' },
      { id: 'completed', title: '已完成', status: 'completed', color: '#f6ffed' },
    ];

    return columnDefs.map(col => ({
      ...col,
      tasks: tasks.filter(task => task.status === col.status)
    }));
  }, [tasks]);

  // 拖拽处理
  const handleDragEnd = (result: DropResult) => {
    const { destination, source, draggableId } = result;

    // 没有目标位置
    if (!destination) return;

    // 位置没有变化
    if (
      destination.droppableId === source.droppableId &&
      destination.index === source.index
    ) {
      return;
    }

    // 更新任务状态
    const newStatus = destination.droppableId;
    onTaskUpdate(draggableId, { status: newStatus });
  };

  const getPriorityColor = (priority: string) => {
    switch (priority) {
      case 'high': return '#ff4d4f';
      case 'medium': return '#faad14';
      case 'low': return '#52c41a';
      default: return '#d9d9d9';
    }
  };

  const getPriorityText = (priority: string) => {
    switch (priority) {
      case 'high': return '高';
      case 'medium': return '中';
      case 'low': return '低';
      default: return priority;
    }
  };

  const formatDeadline = (deadline?: Date) => {
    if (!deadline) return null;
    
    const date = new Date(deadline);
    const now = new Date();
    const diffDays = Math.ceil((date.getTime() - now.getTime()) / (1000 * 60 * 60 * 24));
    
    let color = 'var(--color-text-secondary)';
    if (diffDays < 0) color = 'var(--color-error)'; // 已过期
    else if (diffDays <= 1) color = 'var(--color-warning)'; // 即将到期
    else if (diffDays <= 3) color = 'var(--color-info)'; // 临近
    
    return (
      <div style={{ fontSize: '12px', color, display: 'flex', alignItems: 'center', gap: 4 }}>
        <ClockCircleOutlined />
        {date.toLocaleDateString()}
        {diffDays < 0 && <span style={{ color: '#ff4d4f' }}>(已过期)</span>}
        {diffDays === 0 && <span style={{ color: '#faad14' }}>(今天)</span>}
        {diffDays === 1 && <span style={{ color: '#faad14' }}>(明天)</span>}
      </div>
    );
  };

  const TaskCard: React.FC<{ task: Task; index: number }> = ({ task, index }) => (
    <Draggable draggableId={task.id} index={index}>
      {(provided, snapshot) => (
        <div
          ref={provided.innerRef}
          {...provided.draggableProps}
          {...provided.dragHandleProps}
          style={{
            ...provided.draggableProps.style,
            marginBottom: 8,
          }}
        >
          <Card
            size="small"
            style={{
              cursor: 'grab',
              backgroundColor: snapshot.isDragging ? '#f0f0f0' : 'white',
              boxShadow: snapshot.isDragging 
                ? '0 4px 12px rgba(0,0,0,0.15)' 
                : '0 1px 3px rgba(0,0,0,0.1)',
              border: '1px solid var(--color-border)',
              borderRadius: 'var(--radius-small)',
            }}
            bodyStyle={{ padding: '12px' }}
          >
            {/* 任务标题 */}
            <div style={{ marginBottom: 8 }}>
              <div style={{ 
                fontWeight: 500, 
                fontSize: '14px',
                lineHeight: '1.4',
                marginBottom: 4,
                color: 'var(--color-text)'
              }}>
                {task.title}
              </div>
              {task.description && (
                <div style={{ 
                  fontSize: '12px', 
                  color: 'var(--color-text-secondary)',
                  lineHeight: '1.3',
                  overflow: 'hidden',
                  textOverflow: 'ellipsis',
                  display: '-webkit-box',
                  WebkitLineClamp: 2,
                  WebkitBoxOrient: 'vertical'
                }}>
                  {task.description}
                </div>
              )}
            </div>

            {/* 优先级和时间 */}
            <div style={{ marginBottom: 8 }}>
              <Space size="small">
                <Tag 
                  color={getPriorityColor(task.priority)} 
                  size="small"
                  icon={<FlagOutlined />}
                >
                  {getPriorityText(task.priority)}
                </Tag>
                {task.estimatedTime && (
                  <Tag size="small" style={{ color: '#666' }}>
                    {task.estimatedTime}分钟
                  </Tag>
                )}
              </Space>
            </div>

            {/* 截止日期 */}
            {task.deadline && (
              <div style={{ marginBottom: 8 }}>
                {formatDeadline(task.deadline)}
              </div>
            )}

            {/* 标签 */}
            {task.tags && task.tags.length > 0 && (
              <div style={{ marginBottom: 8 }}>
                <Space size={4} wrap>
                  {task.tags.slice(0, 3).map((tag, index) => (
                    <Tag key={index} size="small" style={{ fontSize: '10px' }}>
                      {tag}
                    </Tag>
                  ))}
                  {task.tags.length > 3 && (
                    <Tag size="small" style={{ fontSize: '10px' }}>
                      +{task.tags.length - 3}
                    </Tag>
                  )}
                </Space>
              </div>
            )}

            {/* 操作按钮 */}
            <div style={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center' }}>
              <div style={{ display: 'flex', gap: 4 }}>
                <Tooltip title="开始番茄钟">
                  <Button
                    type="text"
                    size="small"
                    icon={<PlayCircleOutlined />}
                    onClick={(e) => {
                      e.stopPropagation();
                      onStartPomodoro(task);
                    }}
                    style={{ padding: '2px 4px', height: 'auto' }}
                  />
                </Tooltip>
                <Tooltip title="编辑任务">
                  <Button
                    type="text"
                    size="small"
                    icon={<EditOutlined />}
                    onClick={(e) => {
                      e.stopPropagation();
                      onTaskEdit(task);
                    }}
                    style={{ padding: '2px 4px', height: 'auto' }}
                  />
                </Tooltip>
                <Tooltip title="删除任务">
                  <Button
                    type="text"
                    size="small"
                    icon={<DeleteOutlined />}
                    onClick={(e) => {
                      e.stopPropagation();
                      onTaskDelete(task.id);
                    }}
                    danger
                    style={{ padding: '2px 4px', height: 'auto' }}
                  />
                </Tooltip>
              </div>
              
              {/* 任务进度指示器 */}
              <div style={{ fontSize: '10px', color: '#999' }}>
                #{task.id.slice(-6)}
              </div>
            </div>
          </Card>
        </div>
      )}
    </Draggable>
  );

  const KanbanColumn: React.FC<{ column: KanbanColumn }> = ({ column }) => (
    <div style={{ 
      flex: 1, 
      minWidth: 280, 
      maxWidth: 320,
      margin: '0 8px'
    }}>
      {/* 列标题 */}
      <div style={{
        padding: '12px 16px',
        backgroundColor: column.color,
        borderRadius: 'var(--radius-small)',
        marginBottom: 12,
        border: '1px solid var(--color-border)',
      }}>
        <div style={{ 
          display: 'flex', 
          justifyContent: 'space-between', 
          alignItems: 'center' 
        }}>
          <span style={{ 
            fontWeight: 600, 
            fontSize: '14px',
            color: 'var(--color-text)'
          }}>
            {column.title}
          </span>
          <Tag size="small" style={{ margin: 0 }}>
            {column.tasks.length}
          </Tag>
        </div>
      </div>

      {/* 任务列表 */}
      <Droppable droppableId={column.id}>
        {(provided, snapshot) => (
          <div
            ref={provided.innerRef}
            {...provided.droppableProps}
            style={{
              minHeight: 400,
              backgroundColor: snapshot.isDraggingOver ? '#f0f0f0' : 'transparent',
              borderRadius: 'var(--radius-small)',
              padding: snapshot.isDraggingOver ? 8 : 0,
              transition: 'all 0.2s ease',
            }}
          >
            {column.tasks.length === 0 ? (
              <div style={{
                padding: '40px 20px',
                textAlign: 'center',
                color: '#999',
                border: '2px dashed #d9d9d9',
                borderRadius: 'var(--radius-small)',
                backgroundColor: '#fafafa'
              }}>
                <div style={{ fontSize: '12px' }}>
                  暂无{column.title}任务
                </div>
                <div style={{ fontSize: '10px', marginTop: 4 }}>
                  拖拽任务到此处
                </div>
              </div>
            ) : (
              column.tasks.map((task, index) => (
                <TaskCard key={task.id} task={task} index={index} />
              ))
            )}
            {provided.placeholder}
          </div>
        )}
      </Droppable>
    </div>
  );

  if (loading) {
    return (
      <div style={{ textAlign: 'center', padding: '40px' }}>
        <div>加载中...</div>
      </div>
    );
  }

  if (tasks.length === 0) {
    return (
      <Empty
        description="暂无任务"
        style={{ padding: '40px' }}
      />
    );
  }

  return (
    <div style={{ height: '100%', overflow: 'hidden' }}>
      <DragDropContext onDragEnd={handleDragEnd}>
        <div style={{
          display: 'flex',
          height: '100%',
          overflowX: 'auto',
          overflowY: 'hidden',
          padding: '0 8px',
          gap: 0
        }}>
          {columns.map(column => (
            <KanbanColumn key={column.id} column={column} />
          ))}
        </div>
      </DragDropContext>
    </div>
  );
};

export default TaskKanbanView;
