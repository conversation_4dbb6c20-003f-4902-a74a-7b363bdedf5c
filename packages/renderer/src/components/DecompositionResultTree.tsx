import React, { useState, useMemo } from 'react';
import { Tree, Card, Tag, Space, Typography, Progress, Tooltip, Button, <PERSON>lapse, Tabs, Alert } from 'antd';
import {
  FlagOutlined,
  TrophyOutlined,
  CheckSquareOutlined,
  ClockCircleOutlined,
  StarOutlined,
  EditOutlined,
  DeleteOutlined,
  UnorderedListOutlined,
  ApartmentOutlined
} from '@ant-design/icons';
import type { DataNode } from 'antd/es/tree';
import EditableTaskList from './EditableTaskList';

const { Text, Title } = Typography;
const { Panel } = Collapse;
const { TabPane } = Tabs;

interface DecompositionResult {
  subGoals: SubGoal[];
  estimatedTotalTime: number;
  complexity: 'low' | 'medium' | 'high';
  confidence: number;
  suggestions: string[];
  warnings: string[];
}

interface SubGoal {
  name: string;
  description: string;
  priority: 'high' | 'medium' | 'low';
  estimatedTime: number;
  confidence: number;
  reasoning: string;
  milestones: Milestone[];
}

interface Milestone {
  name: string;
  description: string;
  estimatedTime: number;
  confidence: number;
  dependencies: string[];
  tasks: Task[];
}

interface Task {
  title: string;
  description: string;
  estimatedTime: number;
  priority: 'high' | 'medium' | 'low';
  confidence: number;
  actionable: boolean;
  resources: string[];
}

interface DecompositionResultTreeProps {
  result: DecompositionResult;
  goalName: string;
  onEditItem?: (type: 'subgoal' | 'milestone' | 'task', item: any, path: number[]) => void;
  onDeleteItem?: (type: 'subgoal' | 'milestone' | 'task', path: number[]) => void;
  editable?: boolean;
}

const DecompositionResultTree: React.FC<DecompositionResultTreeProps> = ({
  result,
  goalName,
  onEditItem,
  onDeleteItem,
  editable = false
}) => {
  const [expandedKeys, setExpandedKeys] = useState<React.Key[]>([]);
  const [selectedKeys, setSelectedKeys] = useState<React.Key[]>([]);
  const [viewMode, setViewMode] = useState<'tree' | 'list'>('tree');

  // 添加错误边界和数据验证
  if (!result) {
    console.error('DecompositionResultTree: result为空');
    return (
      <Card>
        <Alert
          message="数据错误"
          description="分解结果数据为空，请重新加载。"
          type="error"
          showIcon
        />
      </Card>
    );
  }

  if (!result.subGoals || !Array.isArray(result.subGoals)) {
    console.error('DecompositionResultTree: subGoals数据无效', result);
    return (
      <Card>
        <Alert
          message="数据格式错误"
          description="分解结果格式不正确，请重新进行AI分解。"
          type="error"
          showIcon
        />
      </Card>
    );
  }

  // 获取优先级颜色
  const getPriorityColor = (priority: string) => {
    switch (priority) {
      case 'high': return 'red';
      case 'medium': return 'orange';
      case 'low': return 'green';
      default: return 'default';
    }
  };

  // 获取信心度颜色
  const getConfidenceColor = (confidence: number) => {
    if (confidence >= 0.8) return '#52c41a';
    if (confidence >= 0.6) return '#faad14';
    return '#ff4d4f';
  };

  // 扁平化所有任务
  const flatTasks = useMemo(() => {
    const tasks: any[] = [];
    result.subGoals?.forEach((subGoal, subGoalIndex) => {
      subGoal.milestones?.forEach((milestone, milestoneIndex) => {
        milestone.tasks?.forEach((task, taskIndex) => {
          tasks.push({
            ...task,
            id: `${subGoalIndex}-${milestoneIndex}-${taskIndex}`,
            subGoalName: subGoal.name,
            milestoneName: milestone.name,
            path: [subGoalIndex, milestoneIndex, taskIndex]
          });
        });
      });
    });
    return tasks;
  }, [result]);

  // 处理任务列表变化
  const handleTasksChange = (newTasks: any[]) => {
    // TODO: 实现任务更新逻辑
    console.log('任务列表更新:', newTasks);
  };

  // 构建树形数据
  const treeData: DataNode[] = useMemo(() => {
    const nodes: DataNode[] = [];

    result.subGoals?.forEach((subGoal, subGoalIndex) => {
      const subGoalKey = `subgoal-${subGoalIndex}`;
      const milestoneNodes: DataNode[] = [];

      subGoal.milestones?.forEach((milestone, milestoneIndex) => {
        const milestoneKey = `${subGoalKey}-milestone-${milestoneIndex}`;
        const taskNodes: DataNode[] = [];

        milestone.tasks?.forEach((task, taskIndex) => {
          const taskKey = `${milestoneKey}-task-${taskIndex}`;
          taskNodes.push({
            key: taskKey,
            title: (
              <div style={{ display: 'flex', alignItems: 'center', justifyContent: 'space-between', width: '100%' }}>
                <div style={{ flex: 1 }}>
                  <Space>
                    <CheckSquareOutlined style={{ color: '#1890ff' }} />
                    <Text strong>{task.title}</Text>
                    <Tag color={getPriorityColor(task.priority)} size="small">
                      {task.priority}
                    </Tag>
                    <Tag color="blue" size="small">
                      {task.estimatedTime}分钟
                    </Tag>
                    <Tooltip title={`信心度: ${(task.confidence * 100).toFixed(0)}%`}>
                      <div style={{ 
                        width: 40, 
                        height: 6, 
                        backgroundColor: '#f0f0f0', 
                        borderRadius: 3,
                        overflow: 'hidden'
                      }}>
                        <div 
                          style={{ 
                            width: `${task.confidence * 100}%`, 
                            height: '100%', 
                            backgroundColor: getConfidenceColor(task.confidence)
                          }} 
                        />
                      </div>
                    </Tooltip>
                  </Space>
                  <div style={{ marginTop: 4, marginLeft: 20 }}>
                    <Text type="secondary" style={{ fontSize: '12px' }}>
                      {task.description}
                    </Text>
                  </div>
                  {task.resources && task.resources.length > 0 && (
                    <div style={{ marginTop: 4, marginLeft: 20 }}>
                      <Text type="secondary" style={{ fontSize: '11px' }}>
                        资源: {task.resources.join(', ')}
                      </Text>
                    </div>
                  )}
                </div>
                {editable && (
                  <Space size="small">
                    <Button 
                      size="small" 
                      type="text" 
                      icon={<EditOutlined />}
                      onClick={(e) => {
                        e.stopPropagation();
                        onEditItem?.('task', task, [subGoalIndex, milestoneIndex, taskIndex]);
                      }}
                    />
                    <Button 
                      size="small" 
                      type="text" 
                      danger
                      icon={<DeleteOutlined />}
                      onClick={(e) => {
                        e.stopPropagation();
                        onDeleteItem?.('task', [subGoalIndex, milestoneIndex, taskIndex]);
                      }}
                    />
                  </Space>
                )}
              </div>
            ),
            isLeaf: true
          });
        });

        milestoneNodes.push({
          key: milestoneKey,
          title: (
            <div style={{ display: 'flex', alignItems: 'center', justifyContent: 'space-between', width: '100%' }}>
              <div style={{ flex: 1 }}>
                <Space>
                  <TrophyOutlined style={{ color: '#faad14' }} />
                  <Text strong>{milestone.name}</Text>
                  <Tag color="orange" size="small">
                    {milestone.estimatedTime}小时
                  </Tag>
                  <Tooltip title={`信心度: ${(milestone.confidence * 100).toFixed(0)}%`}>
                    <div style={{ 
                      width: 40, 
                      height: 6, 
                      backgroundColor: '#f0f0f0', 
                      borderRadius: 3,
                      overflow: 'hidden'
                    }}>
                      <div 
                        style={{ 
                          width: `${milestone.confidence * 100}%`, 
                          height: '100%', 
                          backgroundColor: getConfidenceColor(milestone.confidence)
                        }} 
                      />
                    </div>
                  </Tooltip>
                </Space>
                <div style={{ marginTop: 4, marginLeft: 24 }}>
                  <Text type="secondary" style={{ fontSize: '12px' }}>
                    {milestone.description}
                  </Text>
                </div>
                {milestone.dependencies && milestone.dependencies.length > 0 && (
                  <div style={{ marginTop: 4, marginLeft: 24 }}>
                    <Text type="secondary" style={{ fontSize: '11px' }}>
                      依赖: {milestone.dependencies.join(', ')}
                    </Text>
                  </div>
                )}
              </div>
              {editable && (
                <Space size="small">
                  <Button 
                    size="small" 
                    type="text" 
                    icon={<EditOutlined />}
                    onClick={(e) => {
                      e.stopPropagation();
                      onEditItem?.('milestone', milestone, [subGoalIndex, milestoneIndex]);
                    }}
                  />
                  <Button 
                    size="small" 
                    type="text" 
                    danger
                    icon={<DeleteOutlined />}
                    onClick={(e) => {
                      e.stopPropagation();
                      onDeleteItem?.('milestone', [subGoalIndex, milestoneIndex]);
                    }}
                  />
                </Space>
              )}
            </div>
          ),
          children: taskNodes
        });
      });

      nodes.push({
        key: subGoalKey,
        title: (
          <div style={{ display: 'flex', alignItems: 'center', justifyContent: 'space-between', width: '100%' }}>
            <div style={{ flex: 1 }}>
              <Space>
                <FlagOutlined style={{ color: '#52c41a' }} />
                <Text strong style={{ fontSize: '14px' }}>{subGoal.name}</Text>
                <Tag color={getPriorityColor(subGoal.priority)}>
                  {subGoal.priority}
                </Tag>
                <Tag color="green">
                  {subGoal.estimatedTime}小时
                </Tag>
                <Tooltip title={`信心度: ${(subGoal.confidence * 100).toFixed(0)}%`}>
                  <div style={{ 
                    width: 50, 
                    height: 8, 
                    backgroundColor: '#f0f0f0', 
                    borderRadius: 4,
                    overflow: 'hidden'
                  }}>
                    <div 
                      style={{ 
                        width: `${subGoal.confidence * 100}%`, 
                        height: '100%', 
                        backgroundColor: getConfidenceColor(subGoal.confidence)
                      }} 
                    />
                  </div>
                </Tooltip>
              </Space>
              <div style={{ marginTop: 4, marginLeft: 20 }}>
                <Text type="secondary" style={{ fontSize: '13px' }}>
                  {subGoal.description}
                </Text>
              </div>
              <div style={{ marginTop: 4, marginLeft: 20 }}>
                <Text type="secondary" style={{ fontSize: '11px' }}>
                  分析: {subGoal.reasoning}
                </Text>
              </div>
            </div>
            {editable && (
              <Space size="small">
                <Button 
                  size="small" 
                  type="text" 
                  icon={<EditOutlined />}
                  onClick={(e) => {
                    e.stopPropagation();
                    onEditItem?.('subgoal', subGoal, [subGoalIndex]);
                  }}
                />
                <Button 
                  size="small" 
                  type="text" 
                  danger
                  icon={<DeleteOutlined />}
                  onClick={(e) => {
                    e.stopPropagation();
                    onDeleteItem?.('subgoal', [subGoalIndex]);
                  }}
                />
              </Space>
            )}
          </div>
        ),
        children: milestoneNodes
      });
    });

    return nodes;
  }, [result, editable, onEditItem, onDeleteItem]);

  // 自动展开所有节点
  React.useEffect(() => {
    const allKeys: React.Key[] = [];
    const collectKeys = (nodes: DataNode[]) => {
      nodes.forEach(node => {
        allKeys.push(node.key);
        if (node.children) {
          collectKeys(node.children);
        }
      });
    };
    collectKeys(treeData);
    setExpandedKeys(allKeys);
  }, [treeData]);

  return (
    <div>
      {/* 总览信息 */}
      <Card size="small" style={{ marginBottom: 16 }}>
        <div style={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', marginBottom: 12 }}>
          <Title level={4} style={{ margin: 0 }}>
            目标分解结果: {goalName}
          </Title>
          <Space>
            <Tag color="blue">
              <ClockCircleOutlined /> {result.estimatedTotalTime}小时
            </Tag>
            <Tag color={result.complexity === 'high' ? 'red' : result.complexity === 'medium' ? 'orange' : 'green'}>
              复杂度: {result.complexity}
            </Tag>
            <Tooltip title={`整体信心度: ${(result.confidence * 100).toFixed(0)}%`}>
              <Tag color={result.confidence >= 0.8 ? 'green' : result.confidence >= 0.6 ? 'orange' : 'red'}>
                <StarOutlined /> {(result.confidence * 100).toFixed(0)}%
              </Tag>
            </Tooltip>
          </Space>
        </div>
        
        <div style={{ display: 'flex', gap: 16 }}>
          <div>
            <Text type="secondary">子目标数量: </Text>
            <Text strong>{result.subGoals.length}</Text>
          </div>
          <div>
            <Text type="secondary">里程碑数量: </Text>
            <Text strong>{result.subGoals.reduce((sum, sg) => sum + (sg.milestones?.length || 0), 0)}</Text>
          </div>
          <div>
            <Text type="secondary">任务数量: </Text>
            <Text strong>
              {result.subGoals.reduce((sum, sg) =>
                sum + (sg.milestones || []).reduce((mSum, m) => mSum + (m.tasks?.length || 0), 0), 0
              )}
            </Text>
          </div>
        </div>
      </Card>

      {/* 分解结构 */}
      <Card
        title="分解结构"
        size="small"
        style={{ marginBottom: 16 }}
        tabList={[
          {
            key: 'tree',
            tab: (
              <Space>
                <ApartmentOutlined />
                <span>树状视图</span>
              </Space>
            )
          },
          {
            key: 'list',
            tab: (
              <Space>
                <UnorderedListOutlined />
                <span>任务列表</span>
              </Space>
            )
          }
        ]}
        activeTabKey={viewMode}
        onTabChange={(key) => setViewMode(key as 'tree' | 'list')}
      >
        {viewMode === 'tree' ? (
          <Tree
            treeData={treeData}
            expandedKeys={expandedKeys}
            selectedKeys={selectedKeys}
            onExpand={setExpandedKeys}
            onSelect={setSelectedKeys}
            showLine={{ showLeafIcon: false }}
            blockNode
          />
        ) : (
          <EditableTaskList
            tasks={flatTasks}
            title="所有任务"
            onTasksChange={handleTasksChange}
            editable={editable}
            showBatchOperations={editable}
          />
        )}
      </Card>

      {/* 建议和警告 */}
      {((result.suggestions && result.suggestions.length > 0) || (result.warnings && result.warnings.length > 0)) && (
        <Collapse size="small" defaultActiveKey={['suggestions', 'warnings']}>
          {result.suggestions && result.suggestions.length > 0 && (
            <Panel header="AI建议" key="suggestions">
              <ul style={{ margin: 0, paddingLeft: 20 }}>
                {result.suggestions.map((suggestion, index) => (
                  <li key={index} style={{ marginBottom: 4 }}>
                    <Text>{suggestion}</Text>
                  </li>
                ))}
              </ul>
            </Panel>
          )}
          {result.warnings && result.warnings.length > 0 && (
            <Panel header="注意事项" key="warnings">
              <ul style={{ margin: 0, paddingLeft: 20 }}>
                {result.warnings.map((warning, index) => (
                  <li key={index} style={{ marginBottom: 4 }}>
                    <Text type="warning">{warning}</Text>
                  </li>
                ))}
              </ul>
            </Panel>
          )}
        </Collapse>
      )}
    </div>
  );
};

export default DecompositionResultTree;
