import React, { useState, useCallback } from 'react';
import { Card, Button, Space, Typography, Checkbox, Modal, message, Select, InputNumber } from 'antd';
import { 
  PlusOutlined, 
  DeleteOutlined, 
  EditOutlined, 
  SortAscendingOutlined,
  CheckSquareOutlined,
  UndoOutlined
} from '@ant-design/icons';
import { DndProvider } from 'react-dnd';
import { HTML5Backend } from 'react-dnd-html5-backend';
import EditableTaskCard from './EditableTaskCard';

const { Title, Text } = Typography;
const { Option } = Select;

interface Task {
  id?: string;
  title: string;
  description: string;
  estimatedTime: number;
  priority: 'high' | 'medium' | 'low';
  confidence: number;
  actionable: boolean;
  resources: string[];
  completed?: boolean;
}

interface EditableTaskListProps {
  tasks: Task[];
  title?: string;
  onTasksChange: (tasks: Task[]) => void;
  editable?: boolean;
  showBatchOperations?: boolean;
}

const EditableTaskList: React.FC<EditableTaskListProps> = ({
  tasks,
  title = "任务列表",
  onTasksChange,
  editable = false,
  showBatchOperations = false
}) => {
  const [selectedTasks, setSelectedTasks] = useState<number[]>([]);
  const [sortBy, setSortBy] = useState<'priority' | 'time' | 'confidence'>('priority');

  // 创建新任务
  const handleAddTask = useCallback(() => {
    const newTask: Task = {
      title: '新任务',
      description: '请描述任务内容',
      estimatedTime: 30,
      priority: 'medium',
      confidence: 0.7,
      actionable: true,
      resources: []
    };
    
    onTasksChange([...tasks, newTask]);
  }, [tasks, onTasksChange]);

  // 更新任务
  const handleUpdateTask = useCallback((index: number, updatedTask: Task) => {
    const newTasks = [...tasks];
    newTasks[index] = updatedTask;
    onTasksChange(newTasks);
    message.success('任务更新成功');
  }, [tasks, onTasksChange]);

  // 删除任务
  const handleDeleteTask = useCallback((index: number) => {
    Modal.confirm({
      title: '确认删除',
      content: '确定要删除这个任务吗？',
      onOk: () => {
        const newTasks = tasks.filter((_, i) => i !== index);
        onTasksChange(newTasks);
        message.success('任务删除成功');
      }
    });
  }, [tasks, onTasksChange]);

  // 移动任务
  const handleMoveTask = useCallback((dragIndex: number, hoverIndex: number) => {
    const newTasks = [...tasks];
    const draggedTask = newTasks[dragIndex];
    newTasks.splice(dragIndex, 1);
    newTasks.splice(hoverIndex, 0, draggedTask);
    onTasksChange(newTasks);
  }, [tasks, onTasksChange]);

  // 选择任务
  const handleSelectTask = useCallback((index: number, checked: boolean) => {
    if (checked) {
      setSelectedTasks(prev => [...prev, index]);
    } else {
      setSelectedTasks(prev => prev.filter(i => i !== index));
    }
  }, []);

  // 全选/取消全选
  const handleSelectAll = useCallback((checked: boolean) => {
    if (checked) {
      setSelectedTasks(tasks.map((_, index) => index));
    } else {
      setSelectedTasks([]);
    }
  }, [tasks]);

  // 批量删除
  const handleBatchDelete = useCallback(() => {
    if (selectedTasks.length === 0) {
      message.warning('请先选择要删除的任务');
      return;
    }

    Modal.confirm({
      title: '批量删除',
      content: `确定要删除选中的 ${selectedTasks.length} 个任务吗？`,
      onOk: () => {
        const newTasks = tasks.filter((_, index) => !selectedTasks.includes(index));
        onTasksChange(newTasks);
        setSelectedTasks([]);
        message.success(`成功删除 ${selectedTasks.length} 个任务`);
      }
    });
  }, [tasks, selectedTasks, onTasksChange]);

  // 批量修改优先级
  const handleBatchUpdatePriority = useCallback((priority: 'high' | 'medium' | 'low') => {
    if (selectedTasks.length === 0) {
      message.warning('请先选择要修改的任务');
      return;
    }

    const newTasks = tasks.map((task, index) => {
      if (selectedTasks.includes(index)) {
        return { ...task, priority };
      }
      return task;
    });

    onTasksChange(newTasks);
    setSelectedTasks([]);
    message.success(`成功修改 ${selectedTasks.length} 个任务的优先级`);
  }, [tasks, selectedTasks, onTasksChange]);

  // 批量修改预估时间
  const handleBatchUpdateTime = useCallback((estimatedTime: number) => {
    if (selectedTasks.length === 0) {
      message.warning('请先选择要修改的任务');
      return;
    }

    const newTasks = tasks.map((task, index) => {
      if (selectedTasks.includes(index)) {
        return { ...task, estimatedTime };
      }
      return task;
    });

    onTasksChange(newTasks);
    setSelectedTasks([]);
    message.success(`成功修改 ${selectedTasks.length} 个任务的预估时间`);
  }, [tasks, selectedTasks, onTasksChange]);

  // 排序任务
  const handleSort = useCallback((sortType: 'priority' | 'time' | 'confidence') => {
    const priorityOrder = { high: 3, medium: 2, low: 1 };
    
    const sortedTasks = [...tasks].sort((a, b) => {
      switch (sortType) {
        case 'priority':
          return priorityOrder[b.priority] - priorityOrder[a.priority];
        case 'time':
          return a.estimatedTime - b.estimatedTime;
        case 'confidence':
          return b.confidence - a.confidence;
        default:
          return 0;
      }
    });

    onTasksChange(sortedTasks);
    setSortBy(sortType);
    message.success(`按${sortType === 'priority' ? '优先级' : sortType === 'time' ? '时间' : '信心度'}排序完成`);
  }, [tasks, onTasksChange]);

  const totalTime = tasks.reduce((sum, task) => sum + task.estimatedTime, 0);
  const averageConfidence = tasks.length > 0 
    ? tasks.reduce((sum, task) => sum + task.confidence, 0) / tasks.length 
    : 0;

  return (
    <DndProvider backend={HTML5Backend}>
      <Card
        title={
          <div style={{ display: 'flex', alignItems: 'center', justifyContent: 'space-between' }}>
            <Title level={4} style={{ margin: 0 }}>{title}</Title>
            <Space>
              <Text type="secondary">
                {tasks.length} 个任务 · {Math.round(totalTime / 60 * 10) / 10} 小时
              </Text>
              <Text type="secondary">
                平均信心度: {(averageConfidence * 100).toFixed(0)}%
              </Text>
            </Space>
          </div>
        }
        extra={
          editable && (
            <Space>
              <Button 
                type="primary" 
                icon={<PlusOutlined />} 
                onClick={handleAddTask}
                size="small"
              >
                添加任务
              </Button>
            </Space>
          )
        }
      >
        {/* 批量操作工具栏 */}
        {showBatchOperations && editable && (
          <div style={{ 
            marginBottom: 16, 
            padding: 12, 
            backgroundColor: '#fafafa', 
            borderRadius: 6,
            border: '1px solid #f0f0f0'
          }}>
            <div style={{ display: 'flex', alignItems: 'center', justifyContent: 'space-between', marginBottom: 8 }}>
              <Space>
                <Checkbox
                  checked={selectedTasks.length === tasks.length && tasks.length > 0}
                  indeterminate={selectedTasks.length > 0 && selectedTasks.length < tasks.length}
                  onChange={(e) => handleSelectAll(e.target.checked)}
                >
                  全选 ({selectedTasks.length}/{tasks.length})
                </Checkbox>
              </Space>

              <Space>
                <Select
                  placeholder="排序方式"
                  value={sortBy}
                  onChange={handleSort}
                  size="small"
                  style={{ width: 120 }}
                >
                  <Option value="priority">按优先级</Option>
                  <Option value="time">按时间</Option>
                  <Option value="confidence">按信心度</Option>
                </Select>
              </Space>
            </div>

            {selectedTasks.length > 0 && (
              <Space wrap>
                <Button 
                  size="small" 
                  danger 
                  icon={<DeleteOutlined />}
                  onClick={handleBatchDelete}
                >
                  删除选中 ({selectedTasks.length})
                </Button>
                
                <Select
                  placeholder="批量设置优先级"
                  onChange={handleBatchUpdatePriority}
                  size="small"
                  style={{ width: 140 }}
                >
                  <Option value="high">高优先级</Option>
                  <Option value="medium">中优先级</Option>
                  <Option value="low">低优先级</Option>
                </Select>

                <InputNumber
                  placeholder="批量设置时间"
                  min={1}
                  max={480}
                  size="small"
                  style={{ width: 120 }}
                  addonAfter="分钟"
                  onChange={(value) => value && handleBatchUpdateTime(value)}
                />
              </Space>
            )}
          </div>
        )}

        {/* 任务列表 */}
        <div>
          {tasks.length === 0 ? (
            <div style={{ 
              textAlign: 'center', 
              padding: '40px 20px',
              color: '#999'
            }}>
              <CheckSquareOutlined style={{ fontSize: 48, marginBottom: 16 }} />
              <div>暂无任务</div>
              {editable && (
                <Button 
                  type="link" 
                  icon={<PlusOutlined />}
                  onClick={handleAddTask}
                  style={{ marginTop: 8 }}
                >
                  添加第一个任务
                </Button>
              )}
            </div>
          ) : (
            tasks.map((task, index) => (
              <div key={index} style={{ display: 'flex', alignItems: 'flex-start', gap: 8 }}>
                {showBatchOperations && editable && (
                  <Checkbox
                    checked={selectedTasks.includes(index)}
                    onChange={(e) => handleSelectTask(index, e.target.checked)}
                    style={{ marginTop: 16 }}
                  />
                )}
                <div style={{ flex: 1 }}>
                  <EditableTaskCard
                    task={task}
                    index={index}
                    onUpdate={handleUpdateTask}
                    onDelete={handleDeleteTask}
                    onMove={handleMoveTask}
                    editable={editable}
                    showDragHandle={editable}
                  />
                </div>
              </div>
            ))
          )}
        </div>

        {/* 统计信息 */}
        {tasks.length > 0 && (
          <div style={{ 
            marginTop: 16, 
            padding: 12, 
            backgroundColor: '#f8f9fa', 
            borderRadius: 6,
            fontSize: '12px',
            color: '#666'
          }}>
            <div style={{ display: 'grid', gridTemplateColumns: 'repeat(auto-fit, minmax(120px, 1fr))', gap: 16 }}>
              <div>
                <strong>总任务数:</strong> {tasks.length}
              </div>
              <div>
                <strong>预估总时长:</strong> {Math.round(totalTime / 60 * 10) / 10} 小时
              </div>
              <div>
                <strong>平均信心度:</strong> {(averageConfidence * 100).toFixed(0)}%
              </div>
              <div>
                <strong>高优先级:</strong> {tasks.filter(t => t.priority === 'high').length}
              </div>
              <div>
                <strong>可执行任务:</strong> {tasks.filter(t => t.actionable).length}
              </div>
            </div>
          </div>
        )}
      </Card>
    </DndProvider>
  );
};

export default EditableTaskList;
