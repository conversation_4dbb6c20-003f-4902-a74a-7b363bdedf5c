import React, { createContext, useContext, useState, useCallback, ReactNode } from 'react';
import { Spin, Progress, Typography, Space, Card } from 'antd';
import { LoadingOutlined, CheckCircleOutlined, ExclamationCircleOutlined } from '@ant-design/icons';

const { Text } = Typography;

// 加载状态类型
export interface LoadingState {
  id: string;
  message: string;
  progress?: number;
  status: 'loading' | 'success' | 'error';
  details?: string;
  timestamp: number;
}

// 加载管理器上下文
interface LoadingContextType {
  loadingStates: LoadingState[];
  startLoading: (id: string, message: string) => void;
  updateLoading: (id: string, updates: Partial<LoadingState>) => void;
  finishLoading: (id: string, status: 'success' | 'error', message?: string) => void;
  clearLoading: (id: string) => void;
  clearAllLoading: () => void;
  isLoading: (id?: string) => boolean;
  getLoadingState: (id: string) => LoadingState | undefined;
}

const LoadingContext = createContext<LoadingContextType | undefined>(undefined);

// 加载管理器Provider
export const LoadingProvider: React.FC<{ children: ReactNode }> = ({ children }) => {
  const [loadingStates, setLoadingStates] = useState<LoadingState[]>([]);

  const startLoading = useCallback((id: string, message: string) => {
    setLoadingStates(prev => {
      const filtered = prev.filter(state => state.id !== id);
      return [...filtered, {
        id,
        message,
        status: 'loading',
        timestamp: Date.now()
      }];
    });
  }, []);

  const updateLoading = useCallback((id: string, updates: Partial<LoadingState>) => {
    setLoadingStates(prev => prev.map(state => 
      state.id === id ? { ...state, ...updates } : state
    ));
  }, []);

  const finishLoading = useCallback((id: string, status: 'success' | 'error', message?: string) => {
    setLoadingStates(prev => prev.map(state => 
      state.id === id 
        ? { ...state, status, message: message || state.message, progress: 100 }
        : state
    ));

    // 3秒后自动清除成功/错误状态
    setTimeout(() => {
      setLoadingStates(prev => prev.filter(state => state.id !== id));
    }, 3000);
  }, []);

  const clearLoading = useCallback((id: string) => {
    setLoadingStates(prev => prev.filter(state => state.id !== id));
  }, []);

  const clearAllLoading = useCallback(() => {
    setLoadingStates([]);
  }, []);

  const isLoading = useCallback((id?: string) => {
    if (id) {
      return loadingStates.some(state => state.id === id && state.status === 'loading');
    }
    return loadingStates.some(state => state.status === 'loading');
  }, [loadingStates]);

  const getLoadingState = useCallback((id: string) => {
    return loadingStates.find(state => state.id === id);
  }, [loadingStates]);

  return (
    <LoadingContext.Provider value={{
      loadingStates,
      startLoading,
      updateLoading,
      finishLoading,
      clearLoading,
      clearAllLoading,
      isLoading,
      getLoadingState
    }}>
      {children}
    </LoadingContext.Provider>
  );
};

// Hook for using loading context
export const useLoading = () => {
  const context = useContext(LoadingContext);
  if (!context) {
    throw new Error('useLoading must be used within a LoadingProvider');
  }
  return context;
};

// 加载指示器组件
interface LoadingIndicatorProps {
  id?: string;
  size?: 'small' | 'default' | 'large';
  tip?: string;
  showProgress?: boolean;
  inline?: boolean;
}

export const LoadingIndicator: React.FC<LoadingIndicatorProps> = ({
  id,
  size = 'default',
  tip,
  showProgress = false,
  inline = false
}) => {
  const { loadingStates, isLoading } = useLoading();

  const currentState = id ? loadingStates.find(state => state.id === id) : undefined;
  const loading = id ? isLoading(id) : isLoading();

  if (!loading && !currentState) {
    return null;
  }

  const message = currentState?.message || tip || '加载中...';
  const progress = currentState?.progress;
  const status = currentState?.status || 'loading';

  const getIcon = () => {
    switch (status) {
      case 'success':
        return <CheckCircleOutlined style={{ color: '#52c41a' }} />;
      case 'error':
        return <ExclamationCircleOutlined style={{ color: '#ff4d4f' }} />;
      default:
        return <LoadingOutlined />;
    }
  };

  if (inline) {
    return (
      <Space size="small">
        {getIcon()}
        <Text type={status === 'error' ? 'danger' : status === 'success' ? 'success' : undefined}>
          {message}
        </Text>
        {showProgress && typeof progress === 'number' && (
          <Text type="secondary">({progress}%)</Text>
        )}
      </Space>
    );
  }

  return (
    <div style={{ textAlign: 'center', padding: '20px' }}>
      <Spin 
        size={size} 
        indicator={status === 'loading' ? undefined : getIcon()}
        spinning={status === 'loading'}
      >
        <div style={{ marginTop: 8 }}>
          <Text>{message}</Text>
          {showProgress && typeof progress === 'number' && (
            <div style={{ marginTop: 8, maxWidth: 200, margin: '8px auto 0' }}>
              <Progress 
                percent={progress} 
                size="small" 
                status={status === 'error' ? 'exception' : status === 'success' ? 'success' : 'active'}
              />
            </div>
          )}
        </div>
      </Spin>
    </div>
  );
};

// 全局加载状态显示组件
export const GlobalLoadingIndicator: React.FC = () => {
  const { loadingStates } = useLoading();

  const activeStates = loadingStates.filter(state => 
    state.status === 'loading' || 
    (state.status !== 'loading' && Date.now() - state.timestamp < 3000)
  );

  if (activeStates.length === 0) {
    return null;
  }

  return (
    <div style={{
      position: 'fixed',
      top: 16,
      right: 16,
      zIndex: 9999,
      maxWidth: 300
    }}>
      {activeStates.map(state => (
        <Card 
          key={state.id}
          size="small" 
          style={{ 
            marginBottom: 8,
            boxShadow: '0 4px 12px rgba(0, 0, 0, 0.15)'
          }}
        >
          <LoadingIndicator 
            id={state.id} 
            inline 
            showProgress={typeof state.progress === 'number'}
          />
        </Card>
      ))}
    </div>
  );
};

// Hook for managing async operations with loading states
export const useAsyncOperation = () => {
  const { startLoading, updateLoading, finishLoading } = useLoading();

  const executeWithLoading = useCallback(async <T>(
    id: string,
    operation: (updateProgress?: (progress: number, message?: string) => void) => Promise<T>,
    initialMessage: string = '处理中...'
  ): Promise<T> => {
    try {
      startLoading(id, initialMessage);

      const updateProgress = (progress: number, message?: string) => {
        updateLoading(id, { 
          progress, 
          message: message || initialMessage 
        });
      };

      const result = await operation(updateProgress);
      
      finishLoading(id, 'success', '操作完成');
      return result;
    } catch (error) {
      finishLoading(id, 'error', `操作失败: ${(error as Error).message}`);
      throw error;
    }
  }, [startLoading, updateLoading, finishLoading]);

  return { executeWithLoading };
};

// 页面级加载包装器
interface PageLoadingWrapperProps {
  loading?: boolean;
  error?: Error | null;
  children: ReactNode;
  loadingMessage?: string;
  errorMessage?: string;
  onRetry?: () => void;
}

export const PageLoadingWrapper: React.FC<PageLoadingWrapperProps> = ({
  loading = false,
  error = null,
  children,
  loadingMessage = '页面加载中...',
  errorMessage,
  onRetry
}) => {
  if (error) {
    return (
      <div style={{ textAlign: 'center', padding: '40px 20px' }}>
        <ExclamationCircleOutlined style={{ fontSize: 48, color: '#ff4d4f', marginBottom: 16 }} />
        <div style={{ marginBottom: 16 }}>
          <Text type="danger">
            {errorMessage || error.message || '页面加载失败'}
          </Text>
        </div>
        {onRetry && (
          <button onClick={onRetry} style={{
            padding: '8px 16px',
            backgroundColor: '#1890ff',
            color: 'white',
            border: 'none',
            borderRadius: '4px',
            cursor: 'pointer'
          }}>
            重试
          </button>
        )}
      </div>
    );
  }

  if (loading) {
    return (
      <div style={{ 
        display: 'flex', 
        justifyContent: 'center', 
        alignItems: 'center', 
        minHeight: '200px',
        padding: '40px 20px'
      }}>
        <Spin size="large" tip={loadingMessage} />
      </div>
    );
  }

  return <>{children}</>;
};

// 骨架屏组件
interface SkeletonLoaderProps {
  rows?: number;
  avatar?: boolean;
  title?: boolean;
  loading?: boolean;
  children?: ReactNode;
}

export const SkeletonLoader: React.FC<SkeletonLoaderProps> = ({
  rows = 3,
  avatar = false,
  title = true,
  loading = true,
  children
}) => {
  if (!loading && children) {
    return <>{children}</>;
  }

  return (
    <div style={{ padding: '16px' }}>
      {avatar && (
        <div style={{ 
          width: 40, 
          height: 40, 
          borderRadius: '50%', 
          backgroundColor: '#f0f0f0',
          marginBottom: 16
        }} />
      )}
      
      {title && (
        <div style={{ 
          height: 20, 
          backgroundColor: '#f0f0f0', 
          borderRadius: 4,
          marginBottom: 16,
          width: '60%'
        }} />
      )}
      
      {Array.from({ length: rows }).map((_, index) => (
        <div 
          key={index}
          style={{ 
            height: 16, 
            backgroundColor: '#f0f0f0', 
            borderRadius: 4,
            marginBottom: 12,
            width: index === rows - 1 ? '80%' : '100%'
          }} 
        />
      ))}
    </div>
  );
};
