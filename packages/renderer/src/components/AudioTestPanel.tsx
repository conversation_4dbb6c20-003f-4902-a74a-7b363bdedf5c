import React, { useState, useEffect } from 'react';
import { Card, Button, Space, Typography, Alert, Row, Col, Progress, Tag } from 'antd';
import { 
  PlayCircleOutlined, 
  CheckCircleOutlined,
  ExclamationCircleOutlined,
  InfoCircleOutlined,
  SoundOutlined
} from '@ant-design/icons';
import { NotificationService } from '../services/NotificationService';
import { AudioService, SoundType } from '../services/AudioService';
import { useTheme } from '../contexts/ThemeContext';

const { Text, Title } = Typography;

interface AudioTestPanelProps {
  className?: string;
}

const AudioTestPanel: React.FC<AudioTestPanelProps> = ({ className }) => {
  const { theme } = useTheme();
  const [notificationService] = useState(() => NotificationService.getInstance());
  const [audioService] = useState(() => AudioService.getInstance());
  const [audioStatus, setAudioStatus] = useState(audioService.getAudioStatus());
  const [testResults, setTestResults] = useState<Record<string, 'success' | 'error' | 'pending'>>({});

  // 测试场景配置
  const testScenarios = [
    {
      key: 'task-complete',
      title: '任务完成测试',
      description: '测试任务完成通知和音效',
      action: () => notificationService.notifyTaskComplete('测试任务'),
      soundType: 'task-complete' as SoundType
    },
    {
      key: 'pomodoro-complete',
      title: '番茄钟完成测试',
      description: '测试番茄钟完成通知和音效',
      action: () => notificationService.notifyPomodoroComplete('work', 1),
      soundType: 'pomodoro-complete' as SoundType
    },
    {
      key: 'break-complete',
      title: '休息结束测试',
      description: '测试休息结束通知和音效',
      action: () => notificationService.notifyPomodoroComplete('break', 1),
      soundType: 'break-complete' as SoundType
    },
    {
      key: 'goal-achieved',
      title: '目标达成测试',
      description: '测试目标达成通知和音效',
      action: () => notificationService.notifyGoalAchieved('测试目标'),
      soundType: 'goal-achieved' as SoundType
    },
    {
      key: 'system-alert',
      title: '系统提示测试',
      description: '测试系统重要提示和音效',
      action: () => notificationService.notifySystemAlert('系统测试', '这是一个测试消息'),
      soundType: 'system-alert' as SoundType
    },
    {
      key: 'gentle-reminder',
      title: '轻柔提醒测试',
      description: '测试轻柔提醒音效',
      action: () => notificationService.notifyInactivity(),
      soundType: 'gentle-reminder' as SoundType
    }
  ];

  useEffect(() => {
    // 定期更新音频状态
    const interval = setInterval(() => {
      setAudioStatus(audioService.getAudioStatus());
    }, 1000);

    return () => clearInterval(interval);
  }, [audioService]);

  // 执行单个测试
  const runTest = async (scenario: typeof testScenarios[0]) => {
    setTestResults(prev => ({ ...prev, [scenario.key]: 'pending' }));
    
    try {
      // 执行测试动作
      await scenario.action();
      
      // 标记为成功
      setTestResults(prev => ({ ...prev, [scenario.key]: 'success' }));
    } catch (error) {
      console.error(`Test ${scenario.key} failed:`, error);
      setTestResults(prev => ({ ...prev, [scenario.key]: 'error' }));
    }
  };

  // 执行所有测试
  const runAllTests = async () => {
    for (const scenario of testScenarios) {
      await runTest(scenario);
      // 添加延迟避免音频重叠
      await new Promise(resolve => setTimeout(resolve, 2000));
    }
  };

  // 测试纯音频播放
  const testAudioOnly = async (soundType: SoundType) => {
    try {
      await audioService.testSound(soundType);
    } catch (error) {
      console.error(`Audio test failed for ${soundType}:`, error);
    }
  };

  // 获取状态颜色
  const getStatusColor = (status: string) => {
    switch (status) {
      case 'success': return '#52c41a';
      case 'error': return '#ff4d4f';
      case 'pending': return '#faad14';
      default: return '#d9d9d9';
    }
  };

  // 获取状态图标
  const getStatusIcon = (status: string) => {
    switch (status) {
      case 'success': return <CheckCircleOutlined style={{ color: '#52c41a' }} />;
      case 'error': return <ExclamationCircleOutlined style={{ color: '#ff4d4f' }} />;
      case 'pending': return <InfoCircleOutlined style={{ color: '#faad14' }} />;
      default: return null;
    }
  };

  return (
    <div className={className}>
      <Card 
        className="ultra-fast-card"
        style={{ 
          background: theme.colors.cardBackground,
          border: `1px solid ${theme.colors.border}`,
          borderRadius: '12px'
        }}
      >
        <div style={{ marginBottom: '24px' }}>
          <Title 
            level={4} 
            style={{ 
              margin: '0 0 8px', 
              color: theme.colors.text,
              display: 'flex',
              alignItems: 'center',
              gap: '8px'
            }}
          >
            <SoundOutlined style={{ color: theme.colors.primary }} />
            音频功能测试
          </Title>
          <Text type="secondary" style={{ color: theme.colors.textSecondary }}>
            测试各种通知场景的音频播放功能
          </Text>
        </div>

        {/* 音频状态概览 */}
        <Alert
          message="音频状态"
          description={
            <div>
              <Row gutter={[16, 8]}>
                <Col span={12}>
                  <Tag color={audioStatus.webAudioSupported ? 'green' : 'red'}>
                    Web Audio: {audioStatus.webAudioSupported ? '支持' : '不支持'}
                  </Tag>
                </Col>
                <Col span={12}>
                  <Tag color={audioStatus.htmlAudioSupported ? 'green' : 'red'}>
                    HTML5 Audio: {audioStatus.htmlAudioSupported ? '支持' : '不支持'}
                  </Tag>
                </Col>
                <Col span={12}>
                  <Tag color={audioStatus.userInteracted ? 'green' : 'orange'}>
                    用户交互: {audioStatus.userInteracted ? '已激活' : '待激活'}
                  </Tag>
                </Col>
                <Col span={12}>
                  <Tag color={audioStatus.initialized ? 'green' : 'orange'}>
                    初始化: {audioStatus.initialized ? '完成' : '进行中'}
                  </Tag>
                </Col>
              </Row>
              <div style={{ marginTop: '8px' }}>
                <Text type="secondary">
                  已加载音效: {audioStatus.loadedSounds.length} / {testScenarios.length}
                </Text>
                <Progress 
                  percent={Math.round((audioStatus.loadedSounds.length / testScenarios.length) * 100)}
                  size="small"
                  style={{ marginLeft: '12px', width: '100px' }}
                />
              </div>
            </div>
          }
          type={audioStatus.userInteracted && audioStatus.initialized ? 'success' : 'warning'}
          showIcon
          style={{ marginBottom: '24px' }}
        />

        <Space direction="vertical" size="large" style={{ width: '100%' }}>
          {/* 批量测试按钮 */}
          <div style={{ textAlign: 'center' }}>
            <Button 
              type="primary" 
              size="large"
              onClick={runAllTests}
              disabled={!audioStatus.userInteracted}
            >
              运行所有测试
            </Button>
          </div>

          {/* 单项测试 */}
          <div>
            <Title level={5} style={{ color: theme.colors.text, marginBottom: '16px' }}>
              通知场景测试
            </Title>
            
            <div style={{ display: 'grid', gridTemplateColumns: 'repeat(auto-fit, minmax(300px, 1fr))', gap: '16px' }}>
              {testScenarios.map(scenario => (
                <Card
                  key={scenario.key}
                  size="small"
                  style={{
                    background: theme.colors.background,
                    border: `1px solid ${theme.colors.border}`,
                  }}
                >
                  <div style={{ display: 'flex', justifyContent: 'space-between', alignItems: 'flex-start' }}>
                    <div style={{ flex: 1 }}>
                      <div style={{ display: 'flex', alignItems: 'center', gap: '8px', marginBottom: '4px' }}>
                        <Text strong style={{ color: theme.colors.text }}>{scenario.title}</Text>
                        {getStatusIcon(testResults[scenario.key])}
                      </div>
                      <Text type="secondary" style={{ fontSize: '12px' }}>
                        {scenario.description}
                      </Text>
                    </div>
                    
                    <Space>
                      <Button
                        type="text"
                        size="small"
                        icon={<SoundOutlined />}
                        onClick={() => testAudioOnly(scenario.soundType)}
                        title="仅测试音效"
                      />
                      <Button
                        type="primary"
                        size="small"
                        icon={<PlayCircleOutlined />}
                        onClick={() => runTest(scenario)}
                        loading={testResults[scenario.key] === 'pending'}
                        disabled={!audioStatus.userInteracted}
                      >
                        测试
                      </Button>
                    </Space>
                  </div>
                </Card>
              ))}
            </div>
          </div>

          {/* 测试说明 */}
          <Alert
            message="测试说明"
            description={
              <ul style={{ margin: 0, paddingLeft: '20px' }}>
                <li>请确保浏览器允许音频播放（点击页面任意位置激活）</li>
                <li>测试时请调整合适的音量</li>
                <li>每个测试会同时触发桌面通知和音效播放</li>
                <li>如果音效无法播放，系统会自动使用合成音频作为备用</li>
                <li>在Electron环境中会同时显示系统级通知</li>
              </ul>
            }
            type="info"
            showIcon
          />
        </Space>
      </Card>
    </div>
  );
};

export default AudioTestPanel;
