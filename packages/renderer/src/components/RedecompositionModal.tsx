import React, { useState, useEffect } from 'react';
import { Modal, Form, Input, Select, Alert, Space, Button, Typography, Divider } from 'antd';
import { ExclamationCircleOutlined, HistoryOutlined, RobotOutlined } from '@ant-design/icons';
import { Goal } from '../types';
import AIDecompositionConfig, { AIDecompositionConfig as AIConfig } from './AIDecompositionConfig';
import { DatabaseAPI } from '../services/api';

const { TextArea } = Input;
const { Text, Title } = Typography;

interface RedecompositionModalProps {
  visible: boolean;
  goal: Goal | null;
  onCancel: () => void;
  onConfirm: (config: AIConfig, reason: string) => void;
  loading?: boolean;
}

const RedecompositionModal: React.FC<RedecompositionModalProps> = ({
  visible,
  goal,
  onCancel,
  onConfirm,
  loading = false
}) => {
  const [form] = Form.useForm();
  const [aiConfig, setAIConfig] = useState<AIConfig>({
    aiProvider: '',
    preferences: {
      maxDepth: 3,
      taskGranularity: 'medium',
      includeTimeEstimates: true,
      maxTaskDuration: 120,
      focusAreas: [],
      avoidAreas: []
    },
    context: {
      userBackground: '',
      availableTime: '',
      resources: [],
      constraints: []
    }
  });
  const [decompositionHistory, setDecompositionHistory] = useState<any>(null);

  // 获取分解历史
  useEffect(() => {
    if (visible && goal) {
      loadDecompositionHistory();
    }
  }, [visible, goal]);

  const loadDecompositionHistory = async () => {
    if (!goal) return;
    
    try {
      const result = await DatabaseAPI.getDecompositionHistory(goal.id);
      if (result.success) {
        setDecompositionHistory(result.history);
      }
    } catch (error) {
      console.error('获取分解历史失败:', error);
    }
  };

  const handleConfirm = () => {
    form.validateFields().then(values => {
      onConfirm(aiConfig, values.reason || '用户主动重新分解');
    });
  };

  const handleReset = () => {
    form.resetFields();
    setAIConfig({
      aiProvider: '',
      preferences: {
        maxDepth: 3,
        taskGranularity: 'medium',
        includeTimeEstimates: true,
        maxTaskDuration: 120,
        focusAreas: [],
        avoidAreas: []
      },
      context: {
        userBackground: '',
        availableTime: '',
        resources: [],
        constraints: []
      }
    });
  };

  return (
    <Modal
      title={
        <Space>
          <RobotOutlined />
          <span>重新AI分解目标</span>
        </Space>
      }
      open={visible}
      onCancel={onCancel}
      width={800}
      footer={[
        <Button key="cancel" onClick={onCancel}>
          取消
        </Button>,
        <Button key="reset" onClick={handleReset}>
          重置配置
        </Button>,
        <Button 
          key="confirm" 
          type="primary" 
          onClick={handleConfirm}
          loading={loading}
          disabled={!aiConfig.aiProvider}
        >
          开始重新分解
        </Button>
      ]}
    >
      <div style={{ maxHeight: '70vh', overflowY: 'auto' }}>
        {/* 警告提示 */}
        <Alert
          message="重要提示"
          description="重新分解将会替换当前的AI分解结果。建议在执行前查看分解历史，确保了解当前的分解状态。"
          type="warning"
          icon={<ExclamationCircleOutlined />}
          showIcon
          style={{ marginBottom: 16 }}
        />

        {/* 目标信息 */}
        {goal && (
          <div style={{ marginBottom: 24 }}>
            <Title level={5}>目标信息</Title>
            <div style={{ background: '#f5f5f5', padding: 12, borderRadius: 6 }}>
              <Text strong>{goal.name}</Text>
              <br />
              <Text type="secondary">{goal.description}</Text>
            </div>
          </div>
        )}

        {/* 分解历史概览 */}
        {decompositionHistory && (
          <div style={{ marginBottom: 24 }}>
            <Title level={5}>
              <HistoryOutlined /> 分解历史概览
            </Title>
            <div style={{ background: '#f9f9f9', padding: 12, borderRadius: 6 }}>
              <Text>
                总版本数: <Text strong>{decompositionHistory.totalVersions}</Text>
              </Text>
              <br />
              <Text>
                当前活跃版本: {decompositionHistory.activeSession ? (
                  <Text strong>
                    版本 {decompositionHistory.activeSession.version || 1} 
                    ({new Date(decompositionHistory.activeSession.createdAt).toLocaleDateString()})
                  </Text>
                ) : (
                  <Text type="secondary">无</Text>
                )}
              </Text>
            </div>
          </div>
        )}

        <Divider />

        {/* 重新分解配置 */}
        <Form form={form} layout="vertical">
          <Form.Item
            label="重新分解原因"
            name="reason"
            rules={[{ required: true, message: '请说明重新分解的原因' }]}
          >
            <Select placeholder="请选择重新分解的原因">
              <Select.Option value="不满意当前结果">对当前分解结果不满意</Select.Option>
              <Select.Option value="目标内容变更">目标内容发生了变更</Select.Option>
              <Select.Option value="更换AI模型">想要使用不同的AI模型</Select.Option>
              <Select.Option value="调整分解策略">需要调整分解策略和偏好</Select.Option>
              <Select.Option value="初次分解失败">初次分解失败，重新尝试</Select.Option>
              <Select.Option value="其他原因">其他原因</Select.Option>
            </Select>
          </Form.Item>

          <Form.Item
            label="详细说明（可选）"
            name="description"
          >
            <TextArea 
              rows={3} 
              placeholder="请详细说明重新分解的具体原因和期望..."
            />
          </Form.Item>
        </Form>

        <Divider />

        {/* AI分解配置 */}
        <AIDecompositionConfig
          enabled={true}
          onEnabledChange={() => {}} // 重新分解时始终启用
          config={aiConfig}
          onConfigChange={setAIConfig}
          goalType={goal?.type || 'short-term'}
          goalDescription={goal?.description || ''}
          title="新的AI分解配置"
          showTitle={true}
        />

        {/* 配置说明 */}
        <Alert
          message="配置建议"
          description={
            <div>
              <Text>• 如果对当前结果不满意，建议调整任务粒度或分解深度</Text><br />
              <Text>• 如果目标内容有变更，建议更新用户上下文信息</Text><br />
              <Text>• 可以尝试不同的AI Provider来获得不同风格的分解结果</Text>
            </div>
          }
          type="info"
          style={{ marginTop: 16 }}
        />
      </div>
    </Modal>
  );
};

export default RedecompositionModal;
