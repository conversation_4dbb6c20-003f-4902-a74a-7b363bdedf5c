import React from 'react';
import { Card, Tag, Space, Typography, Progress, Tooltip, Divider, Row, Col } from 'antd';
import { 
  BulbOutlined, 
  ClockCircleOutlined, 
  TagsOutlined, 
  TargetOutlined,
  TrophyOutlined,
  CalendarOutlined
} from '@ant-design/icons';
import { GoalAnalysis } from '../types';
import dayjs from 'dayjs';

const { Text, Paragraph } = Typography;

interface GoalAnalysisDisplayProps {
  analysis: GoalAnalysis;
  compact?: boolean;
}

const GoalAnalysisDisplay: React.FC<GoalAnalysisDisplayProps> = ({
  analysis,
  compact = false
}) => {
  const getSmartScoreColor = (score: number) => {
    if (score >= 80) return '#52c41a';
    if (score >= 60) return '#1890ff';
    if (score >= 40) return '#faad14';
    return '#ff4d4f';
  };

  const getSmartScoreText = (score: number) => {
    if (score >= 80) return '优秀';
    if (score >= 60) return '良好';
    if (score >= 40) return '一般';
    return '需改进';
  };

  if (compact) {
    return (
      <Card size="small" style={{ marginBottom: 16 }}>
        <Space direction="vertical" size="small" style={{ width: '100%' }}>
          <div style={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center' }}>
            <Text strong style={{ fontSize: '13px' }}>
              <BulbOutlined style={{ marginRight: 4 }} />
              目标分析结果
            </Text>
            <div style={{ display: 'flex', alignItems: 'center' }}>
              <Text style={{ fontSize: '12px', marginRight: 8 }}>SMART评分:</Text>
              <Progress
                type="circle"
                size={24}
                percent={analysis.smartScore}
                strokeColor={getSmartScoreColor(analysis.smartScore)}
                format={() => <span style={{ fontSize: '10px' }}>{analysis.smartScore}</span>}
              />
            </div>
          </div>

          <Row gutter={16}>
            <Col span={12}>
              <div>
                <Text style={{ fontSize: '11px', color: '#666' }}>关键词:</Text>
                <div style={{ marginTop: 2 }}>
                  {analysis.keywords.slice(0, 3).map((keyword, index) => (
                    <Tag key={index} size="small" style={{ fontSize: '10px', margin: '1px' }}>
                      {keyword}
                    </Tag>
                  ))}
                  {analysis.keywords.length > 3 && (
                    <Text style={{ fontSize: '10px', color: '#999' }}>
                      +{analysis.keywords.length - 3}
                    </Text>
                  )}
                </div>
              </div>
            </Col>
            <Col span={12}>
              <div>
                <Text style={{ fontSize: '11px', color: '#666' }}>时间信息:</Text>
                <div style={{ marginTop: 2 }}>
                  {analysis.timeReferences.length > 0 ? (
                    analysis.timeReferences.slice(0, 2).map((time, index) => (
                      <Tag key={index} size="small" color="blue" style={{ fontSize: '10px', margin: '1px' }}>
                        {time}
                      </Tag>
                    ))
                  ) : (
                    <Text style={{ fontSize: '10px', color: '#999' }}>未识别</Text>
                  )}
                </div>
              </div>
            </Col>
          </Row>

          {analysis.suggestedDeadline && (
            <div style={{ 
              padding: '4px 8px', 
              backgroundColor: '#e6f7ff', 
              borderRadius: '4px',
              fontSize: '11px'
            }}>
              <CalendarOutlined style={{ marginRight: 4, color: '#1890ff' }} />
              建议截止日期: {dayjs(analysis.suggestedDeadline).format('YYYY-MM-DD')}
            </div>
          )}
        </Space>
      </Card>
    );
  }

  return (
    <Card 
      title={
        <Space>
          <BulbOutlined />
          <span>目标分析结果</span>
          <Text type="secondary" style={{ fontWeight: 'normal', fontSize: '12px' }}>
            基于AI文本分析
          </Text>
        </Space>
      }
      size="small"
      style={{ marginBottom: 16 }}
    >
      <Space direction="vertical" size="middle" style={{ width: '100%' }}>
        {/* SMART评分 */}
        <div>
          <div style={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', marginBottom: 8 }}>
            <Text strong>SMART原则评分</Text>
            <Text style={{ color: getSmartScoreColor(analysis.smartScore) }}>
              {getSmartScoreText(analysis.smartScore)}
            </Text>
          </div>
          <Progress
            percent={analysis.smartScore}
            strokeColor={getSmartScoreColor(analysis.smartScore)}
            size="small"
          />
          <Text style={{ fontSize: '12px', color: '#666' }}>
            评分基于目标的具体性、可衡量性、时间框架等因素
          </Text>
        </div>

        <Divider style={{ margin: '12px 0' }} />

        {/* 关键词分析 */}
        <div>
          <Text strong style={{ display: 'flex', alignItems: 'center', marginBottom: 8 }}>
            <TagsOutlined style={{ marginRight: 4 }} />
            关键词分析
          </Text>
          
          <Row gutter={16}>
            <Col span={12}>
              <div style={{ marginBottom: 8 }}>
                <Text style={{ fontSize: '12px', color: '#666' }}>核心关键词:</Text>
                <div style={{ marginTop: 4 }}>
                  {analysis.keywords.map((keyword, index) => (
                    <Tag key={index} style={{ margin: '2px' }}>{keyword}</Tag>
                  ))}
                </div>
              </div>
            </Col>
            <Col span={12}>
              <div style={{ marginBottom: 8 }}>
                <Text style={{ fontSize: '12px', color: '#666' }}>行动动词:</Text>
                <div style={{ marginTop: 4 }}>
                  {analysis.actionVerbs.map((verb, index) => (
                    <Tag key={index} color="green" style={{ margin: '2px' }}>{verb}</Tag>
                  ))}
                </div>
              </div>
            </Col>
          </Row>

          {analysis.entities.length > 0 && (
            <div style={{ marginBottom: 8 }}>
              <Text style={{ fontSize: '12px', color: '#666' }}>相关实体:</Text>
              <div style={{ marginTop: 4 }}>
                {analysis.entities.map((entity, index) => (
                  <Tag key={index} color="blue" style={{ margin: '2px' }}>{entity}</Tag>
                ))}
              </div>
            </div>
          )}

          {analysis.metrics.length > 0 && (
            <div>
              <Text style={{ fontSize: '12px', color: '#666' }}>度量指标:</Text>
              <div style={{ marginTop: 4 }}>
                {analysis.metrics.map((metric, index) => (
                  <Tag key={index} color="orange" style={{ margin: '2px' }}>{metric}</Tag>
                ))}
              </div>
            </div>
          )}
        </div>

        <Divider style={{ margin: '12px 0' }} />

        {/* 时间分析 */}
        <div>
          <Text strong style={{ display: 'flex', alignItems: 'center', marginBottom: 8 }}>
            <ClockCircleOutlined style={{ marginRight: 4 }} />
            时间分析
          </Text>
          
          {analysis.timeReferences.length > 0 ? (
            <div style={{ marginBottom: 8 }}>
              <Text style={{ fontSize: '12px', color: '#666' }}>识别的时间信息:</Text>
              <div style={{ marginTop: 4 }}>
                {analysis.timeReferences.map((time, index) => (
                  <Tag key={index} color="purple" style={{ margin: '2px' }}>{time}</Tag>
                ))}
              </div>
            </div>
          ) : (
            <Text style={{ fontSize: '12px', color: '#999' }}>
              未识别到明确的时间信息，建议在目标描述中添加时间期限
            </Text>
          )}

          {analysis.suggestedDeadline && (
            <div style={{ 
              padding: '8px 12px', 
              backgroundColor: '#e6f7ff', 
              border: '1px solid #91d5ff',
              borderRadius: '6px',
              marginTop: 8
            }}>
              <CalendarOutlined style={{ marginRight: 8, color: '#1890ff' }} />
              <Text strong style={{ color: '#1890ff' }}>建议截止日期: </Text>
              <Text style={{ color: '#1890ff' }}>
                {dayjs(analysis.suggestedDeadline).format('YYYY年MM月DD日')}
              </Text>
            </div>
          )}
        </div>

        <div style={{ fontSize: '11px', color: '#999', textAlign: 'center', marginTop: 8 }}>
          分析时间: {dayjs(analysis.analyzedAt).format('YYYY-MM-DD HH:mm')}
        </div>
      </Space>
    </Card>
  );
};

export default GoalAnalysisDisplay;
