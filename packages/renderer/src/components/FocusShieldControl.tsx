import React, { useState, useEffect } from 'react';
import {
  Card,
  Switch,
  Button,
  Space,
  Typography,
  Alert,
  Statistic,
  Row,
  Col,
  Tag,
  List,
  Progress,
  Modal,
  Form,
  Select,
  Checkbox,
  Divider,
  Tooltip
} from 'antd';
import {
  SafetyOutlined,
  WarningOutlined,
  SettingOutlined,
  EyeOutlined,
  TrophyOutlined,
  BugOutlined,
  PlayCircleOutlined,
  PauseCircleOutlined,
  StopOutlined
} from '@ant-design/icons';
import { useTheme } from '../contexts/ThemeContext';
import { focusShieldService, FocusShieldConfig, FocusShieldStatus, FocusShieldReport } from '../services/FocusShieldService';
import { InterventionEvent } from '../services/InterventionEngineService';
import { applicationMonitorService } from '../services/ApplicationMonitorService';
import BlacklistManager from './BlacklistManager';

const { Title, Text, Paragraph } = Typography;
const { Option } = Select;

interface FocusShieldControlProps {
  embedded?: boolean; // 是否嵌入在其他组件中
  showFullControls?: boolean; // 是否显示完整控制界面
}

const FocusShieldControl: React.FC<FocusShieldControlProps> = ({
  embedded = false,
  showFullControls = true
}) => {
  const { theme } = useTheme();
  const [status, setStatus] = useState<FocusShieldStatus>(focusShieldService.getStatus());
  const [config, setConfig] = useState<FocusShieldConfig>(focusShieldService.getConfig());
  const [report, setReport] = useState<FocusShieldReport | null>(null);
  const [showSettings, setShowSettings] = useState(false);
  const [showBlacklist, setShowBlacklist] = useState(false);
  const [recentInterventions, setRecentInterventions] = useState<InterventionEvent[]>([]);
  const [form] = Form.useForm();

  useEffect(() => {
    // 监听状态变化
    focusShieldService.on('statusChange', (newStatus) => {
      setStatus(newStatus);
    });

    // 监听干预事件
    focusShieldService.on('intervention', (event) => {
      setRecentInterventions(prev => [event, ...prev.slice(0, 4)]);
    });

    // 加载今日报告
    loadTodayReport();

    // 设置定时更新
    const interval = setInterval(() => {
      setStatus(focusShieldService.getStatus());
      loadTodayReport();
    }, 30000); // 每30秒更新一次

    return () => {
      clearInterval(interval);
    };
  }, []);

  const loadTodayReport = () => {
    try {
      const todayReport = focusShieldService.getTodayReport();
      setReport(todayReport);
    } catch (error) {
      console.error('加载今日报告失败:', error);
    }
  };

  const handleStart = async () => {
    try {
      await focusShieldService.start('manual');
    } catch (error) {
      console.error('启动Focus Shield失败:', error);
    }
  };

  const handleStop = async () => {
    try {
      await focusShieldService.stop();
    } catch (error) {
      console.error('停止Focus Shield失败:', error);
    }
  };

  const handlePause = () => {
    focusShieldService.pause();
  };

  const handleResume = () => {
    focusShieldService.resume();
  };

  const handleConfigUpdate = (updates: Partial<FocusShieldConfig>) => {
    const newConfig = { ...config, ...updates };
    setConfig(newConfig);
    focusShieldService.updateConfig(updates);
  };

  const handleTestIntervention = async () => {
    try {
      await focusShieldService.testIntervention('warning');
    } catch (error) {
      console.error('测试干预失败:', error);
    }
  };

  const handleTestXcomVisit = async () => {
    try {
      console.log('测试X.com访问...');
      
      // 确保Focus Shield正在运行
      if (!status.isActive) {
        console.log('启动Focus Shield...');
        await focusShieldService.start('manual');
        // 等待Focus Shield完全启动
        await new Promise(resolve => setTimeout(resolve, 1000));
      }
      
      // 模拟访问x.com网站
      console.log('模拟访问 x.com...');
      applicationMonitorService.simulateWebsiteVisit('https://x.com', 'X (formerly Twitter)');
      
      // 给一点时间让检测完成
      setTimeout(() => {
        console.log('X.com访问测试完成，请检查违规记录');
      }, 1000);
      
    } catch (error) {
      console.error('模拟访问x.com失败:', error);
    }
  };

  const renderStatusIndicator = () => {
    const getStatusColor = () => {
      if (!status.isActive) return '#999999';
      if (!status.isMonitoring) return '#faad14';
      return '#52c41a';
    };

    const getStatusText = () => {
      if (!status.isActive) return '未启动';
      if (!status.isMonitoring) return '已暂停';
      return '运行中';
    };

    return (
      <div style={{ display: 'flex', alignItems: 'center', gap: '8px' }}>
        <div
          className={status.isActive && status.isMonitoring ? 'pulse-animation' : ''}
          style={{
            width: '12px',
            height: '12px',
            borderRadius: '50%',
            backgroundColor: getStatusColor()
          }}
        />
        <Text style={{ color: getStatusColor(), fontWeight: 'bold' }}>
          {getStatusText()}
        </Text>
        {status.currentMode !== 'off' && (
          <Tag color="blue" size="small">
            {status.currentMode}
          </Tag>
        )}
      </div>
    );
  };

  const renderQuickStats = () => (
    <Row gutter={[12, 12]}>
      <Col span={8}>
        <Statistic
          title="今日干预"
          value={status.totalInterventionsToday}
          prefix={<WarningOutlined />}
          valueStyle={{ fontSize: '18px', color: '#faad14' }}
        />
      </Col>
      <Col span={8}>
        <Statistic
          title="成功阻止"
          value={status.successfulBlocksToday}
          prefix={<SafetyOutlined />}
          valueStyle={{ fontSize: '18px', color: '#52c41a' }}
        />
      </Col>
      <Col span={8}>
        <Statistic
          title="专注分数"
          value={report?.focusScore || 0}
          suffix="/100"
          prefix={<TrophyOutlined />}
          valueStyle={{ 
            fontSize: '18px',
            color: (report?.focusScore || 0) >= 80 ? '#52c41a' : (report?.focusScore || 0) >= 60 ? '#faad14' : '#ff4d4f'
          }}
        />
      </Col>
    </Row>
  );

  const renderControlButtons = () => (
    <Space size="small">
      {!status.isActive ? (
        <Button
          type="primary"
          icon={<PlayCircleOutlined />}
          onClick={handleStart}
          disabled={!config.isEnabled}
        >
          启动
        </Button>
      ) : (
        <>
          {status.isMonitoring ? (
            <Button icon={<PauseCircleOutlined />} onClick={handlePause}>
              暂停
            </Button>
          ) : (
            <Button type="primary" icon={<PlayCircleOutlined />} onClick={handleResume}>
              恢复
            </Button>
          )}
          <Button icon={<StopOutlined />} onClick={handleStop}>
            停止
          </Button>
        </>
      )}
      
      {showFullControls && (
        <>
          <Button icon={<SettingOutlined />} onClick={() => setShowSettings(true)}>
            设置
          </Button>
          <Button icon={<SafetyOutlined />} onClick={() => setShowBlacklist(true)}>
            黑名单
          </Button>
          <Button icon={<BugOutlined />} onClick={handleTestIntervention}>
            测试干预
          </Button>
          <Tooltip title="模拟访问x.com来测试黑名单检测">
            <Button icon={<EyeOutlined />} onClick={handleTestXcomVisit}>
              测试X.com
            </Button>
          </Tooltip>
        </>
      )}
    </Space>
  );

  const renderLastActivity = () => {
    if (!status.lastActivity) return null;

    return (
      <Alert
        message={
          <div style={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center' }}>
            <div>
              <Text strong>最近活动: </Text>
              <Text>{status.lastActivity.name}</Text>
              {status.lastActivity.isBlacklisted && (
                <Tag color="red" size="small" style={{ marginLeft: '8px' }}>
                  黑名单
                </Tag>
              )}
            </div>
            <Text type="secondary" style={{ fontSize: '12px' }}>
              {status.lastActivity.timestamp.toLocaleTimeString()}
            </Text>
          </div>
        }
        type={status.lastActivity.isBlacklisted ? 'warning' : 'info'}
        showIcon
        style={{ marginTop: 12 }}
      />
    );
  };

  const renderRecentInterventions = () => {
    if (recentInterventions.length === 0) return null;

    return (
      <Card size="small" title="最近干预" style={{ marginTop: 12 }}>
        <List
          size="small"
          dataSource={recentInterventions}
          renderItem={(intervention) => (
            <List.Item>
              <div style={{ width: '100%' }}>
                <div style={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center' }}>
                  <div>
                    <Text strong>
                      {intervention.target.name || (intervention.target as any).domain}
                    </Text>
                    <br />
                    <Text type="secondary" style={{ fontSize: '12px' }}>
                      级别: {intervention.level} | 
                      响应: {intervention.userResponse === 'pending' ? '等待中' : 
                            intervention.userResponse === 'returned' ? '返回工作' :
                            intervention.userResponse === 'continued' ? '继续访问' : '已阻止'}
                    </Text>
                  </div>
                  <Text type="secondary" style={{ fontSize: '12px' }}>
                    {intervention.timestamp.toLocaleTimeString()}
                  </Text>
                </div>
              </div>
            </List.Item>
          )}
        />
      </Card>
    );
  };

  const renderEmbeddedView = () => (
    <Card size="small" style={{ width: '100%' }}>
      <div style={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center' }}>
        <div>
          <div style={{ display: 'flex', alignItems: 'center', gap: '8px', marginBottom: '4px' }}>
            <SafetyOutlined style={{ color: theme.colors.primary }} />
            <Text strong>Focus Shield</Text>
            {renderStatusIndicator()}
          </div>
          <Text type="secondary" style={{ fontSize: '12px' }}>
            今日: {status.totalInterventionsToday} 次干预 | {status.successfulBlocksToday} 次成功
          </Text>
        </div>
        <Switch
          checked={config.isEnabled}
          onChange={(checked) => handleConfigUpdate({ isEnabled: checked })}
          size="small"
        />
      </div>
    </Card>
  );

  const renderSettingsModal = () => (
    <Modal
      title="Focus Shield 设置"
      open={showSettings}
      onCancel={() => setShowSettings(false)}
      onOk={() => form.submit()}
      width={600}
    >
      <Form
        form={form}
        layout="vertical"
        initialValues={config}
        onFinish={(values) => {
          handleConfigUpdate(values);
          setShowSettings(false);
        }}
      >
        <Form.Item name="isEnabled" label="启用Focus Shield" valuePropName="checked">
          <Switch />
        </Form.Item>

        <Divider />

        <Form.Item label="监控模式">
          <Form.Item name="enabledInPomodoroMode" valuePropName="checked" style={{ marginBottom: 8 }}>
            <Checkbox>番茄工作法模式</Checkbox>
          </Form.Item>
          <Form.Item name="enabledInDeepFocusMode" valuePropName="checked" style={{ marginBottom: 8 }}>
            <Checkbox>深度专注模式</Checkbox>
          </Form.Item>
          <Form.Item name="enabledInBreakTime" valuePropName="checked">
            <Checkbox>休息时间</Checkbox>
          </Form.Item>
        </Form.Item>

        <Form.Item label="监控内容">
          <Form.Item name="monitorApps" valuePropName="checked" style={{ marginBottom: 8 }}>
            <Checkbox>监控应用程序</Checkbox>
          </Form.Item>
          <Form.Item name="monitorWebsites" valuePropName="checked">
            <Checkbox>监控网站访问</Checkbox>
          </Form.Item>
        </Form.Item>

        <Form.Item name="interventionLevel" label="干预级别">
          <Select>
            <Option value="gentle">温和提醒</Option>
            <Option value="warning">警告提示</Option>
            <Option value="firm">强制延迟</Option>
            <Option value="block">完全阻止</Option>
          </Select>
        </Form.Item>

        <Form.Item name="autoStartWithFocusSession" label="自动启动" valuePropName="checked">
          <Switch />
        </Form.Item>

        <Form.Item name="enableSmartDetection" label="智能检测" valuePropName="checked">
          <Switch />
        </Form.Item>

        <Divider />

        <Form.Item label="通知设置">
          <Form.Item name={['notificationSettings', 'showDesktopNotifications']} valuePropName="checked" style={{ marginBottom: 8 }}>
            <Checkbox>桌面通知</Checkbox>
          </Form.Item>
          <Form.Item name={['notificationSettings', 'playAudioAlerts']} valuePropName="checked">
            <Checkbox>音频提醒</Checkbox>
          </Form.Item>
        </Form.Item>
      </Form>
    </Modal>
  );

  const renderFullView = () => (
    <>
      <Card
        title={
          <div style={{ display: 'flex', alignItems: 'center', gap: '12px' }}>
            <SafetyOutlined style={{ color: theme.colors.primary }} />
            <span>Focus Shield - 数字结界</span>
            {renderStatusIndicator()}
          </div>
        }
        extra={
          <Space>
            <Switch
              checked={config.isEnabled}
              onChange={(checked) => handleConfigUpdate({ isEnabled: checked })}
            />
            <Text type="secondary">启用</Text>
          </Space>
        }
        style={{ marginBottom: 16 }}
      >
        <Paragraph type="secondary">
          Focus Shield 是您的专注守护者，智能监控应用和网站使用，在您分心时及时提醒，帮助维持专注状态。
        </Paragraph>

        {/* 快速统计 */}
        {renderQuickStats()}

        {/* 控制按钮 */}
        <div style={{ marginTop: 16, textAlign: 'center' }}>
          {renderControlButtons()}
        </div>

        {/* 最近活动 */}
        {renderLastActivity()}
      </Card>

      {/* 今日报告 */}
      {report && (
        <Card title="今日报告" style={{ marginBottom: 16 }}>
          <Row gutter={16}>
            <Col span={12}>
              <div style={{ marginBottom: 16 }}>
                <Text strong>专注分数</Text>
                <Progress
                  percent={report.focusScore}
                  strokeColor={{
                    '0%': '#ff4d4f',
                    '50%': '#faad14',
                    '100%': '#52c41a',
                  }}
                  style={{ marginTop: 8 }}
                />
              </div>
              
              <Row gutter={8}>
                <Col span={12}>
                  <Statistic
                    title="监控时长"
                    value={report.totalMonitoringTime}
                    suffix="分钟"
                    valueStyle={{ fontSize: '16px' }}
                  />
                </Col>
                <Col span={12}>
                  <Statistic
                    title="成功率"
                    value={Math.round(report.interventions.successRate)}
                    suffix="%"
                    valueStyle={{ fontSize: '16px' }}
                  />
                </Col>
              </Row>
            </Col>
            
            <Col span={12}>
              <div>
                <Text strong>主要分心源</Text>
                <List
                  size="small"
                  dataSource={report.topDistractions.slice(0, 3)}
                  renderItem={(item) => (
                    <List.Item>
                      <div style={{ width: '100%', display: 'flex', justifyContent: 'space-between' }}>
                        <Text>{item.name}</Text>
                        <Text type="secondary">{item.count}次</Text>
                      </div>
                    </List.Item>
                  )}
                  style={{ marginTop: 8 }}
                />
              </div>
            </Col>
          </Row>

          {report.recommendations.length > 0 && (
            <div style={{ marginTop: 16 }}>
              <Text strong>今日建议</Text>
              <List
                size="small"
                dataSource={report.recommendations}
                renderItem={(item) => (
                  <List.Item>
                    <Text type="secondary">• {item}</Text>
                  </List.Item>
                )}
                style={{ marginTop: 8 }}
              />
            </div>
          )}
        </Card>
      )}

      {/* 最近干预 */}
      {renderRecentInterventions()}

      {/* 设置对话框 */}
      {renderSettingsModal()}

      {/* 黑名单管理 */}
      <Modal
        title="黑名单管理"
        open={showBlacklist}
        onCancel={() => setShowBlacklist(false)}
        footer={null}
        width="90%"
        style={{ top: 20 }}
      >
        <BlacklistManager onClose={() => setShowBlacklist(false)} />
      </Modal>


    </>
  );

  if (embedded) {
    return renderEmbeddedView();
  }

  return (
    <div style={{ width: '100%', height: '100%', padding: embedded ? 0 : 16 }}>
      {renderFullView()}
    </div>
  );
};

export default FocusShieldControl;