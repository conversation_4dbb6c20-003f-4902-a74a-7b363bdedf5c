import React, { useState } from 'react';
import { Card, Collapse, Typography, Tag, Space, Button, Alert } from 'antd';
import { BugOutlined, EyeOutlined, EyeInvisibleOutlined } from '@ant-design/icons';

const { Panel } = Collapse;
const { Text, Paragraph } = Typography;

interface DecompositionDebugInfoProps {
  goalId?: string;
  sessionId?: string;
  result?: any;
  error?: string;
  loading?: boolean;
  sessions?: any[];
}

const DecompositionDebugInfo: React.FC<DecompositionDebugInfoProps> = ({
  goalId,
  sessionId,
  result,
  error,
  loading,
  sessions
}) => {
  const [visible, setVisible] = useState(false);

  if (!visible) {
    return (
      <div style={{ position: 'fixed', bottom: 20, right: 20, zIndex: 1000 }}>
        <Button
          type="primary"
          icon={<BugOutlined />}
          onClick={() => setVisible(true)}
          style={{ backgroundColor: '#722ed1' }}
        >
          调试信息
        </Button>
      </div>
    );
  }

  return (
    <div style={{ 
      position: 'fixed', 
      bottom: 20, 
      right: 20, 
      width: 400, 
      maxHeight: 600,
      overflow: 'auto',
      zIndex: 1000,
      backgroundColor: 'white',
      border: '1px solid #d9d9d9',
      borderRadius: 6,
      boxShadow: '0 4px 12px rgba(0, 0, 0, 0.15)'
    }}>
      <Card
        title={
          <Space>
            <BugOutlined />
            <span>分解结果调试信息</span>
          </Space>
        }
        size="small"
        extra={
          <Button
            type="text"
            icon={<EyeInvisibleOutlined />}
            onClick={() => setVisible(false)}
            size="small"
          />
        }
      >
        <Collapse size="small" ghost>
          {/* 基础信息 */}
          <Panel header="基础信息" key="basic">
            <div style={{ fontSize: 12 }}>
              <div><Text strong>Goal ID:</Text> {goalId || '未设置'}</div>
              <div><Text strong>Session ID:</Text> {sessionId || '未设置'}</div>
              <div><Text strong>Loading:</Text> <Tag color={loading ? 'orange' : 'green'}>{loading ? '是' : '否'}</Tag></div>
              <div><Text strong>Error:</Text> {error ? <Tag color="red">{error}</Tag> : <Tag color="green">无</Tag>}</div>
            </div>
          </Panel>

          {/* 会话信息 */}
          <Panel header={`分解会话 (${sessions?.length || 0})`} key="sessions">
            {sessions && sessions.length > 0 ? (
              <div style={{ fontSize: 12 }}>
                {sessions.map((session, index) => (
                  <div key={session.id} style={{ marginBottom: 8, padding: 8, backgroundColor: '#f5f5f5', borderRadius: 4 }}>
                    <div><Text strong>会话 {index + 1}:</Text> {session.id}</div>
                    <div><Text strong>状态:</Text> <Tag color={session.status === 'completed' ? 'green' : 'orange'}>{session.status}</Tag></div>
                    <div><Text strong>有结果:</Text> <Tag color={session.hasResult ? 'green' : 'red'}>{session.hasResult ? '是' : '否'}</Tag></div>
                    <div><Text strong>创建时间:</Text> {session.createdAt ? new Date(session.createdAt).toLocaleString() : '未知'}</div>
                  </div>
                ))}
              </div>
            ) : (
              <Text type="secondary">没有找到分解会话</Text>
            )}
          </Panel>

          {/* 分解结果 */}
          <Panel header="分解结果" key="result">
            {result ? (
              <div style={{ fontSize: 12 }}>
                <div><Text strong>子目标数量:</Text> {result.subGoals?.length || 0}</div>
                <div><Text strong>预计时长:</Text> {result.estimatedTotalTime || 0} 小时</div>
                <div><Text strong>复杂度:</Text> <Tag color={
                  result.complexity === 'high' ? 'red' : 
                  result.complexity === 'medium' ? 'orange' : 'green'
                }>{result.complexity}</Tag></div>
                <div><Text strong>信心度:</Text> {((result.confidence || 0) * 100).toFixed(0)}%</div>
                <div><Text strong>建议数量:</Text> {result.suggestions?.length || 0}</div>
                <div><Text strong>警告数量:</Text> {result.warnings?.length || 0}</div>
                
                {result.subGoals && result.subGoals.length > 0 && (
                  <div style={{ marginTop: 8 }}>
                    <Text strong>子目标列表:</Text>
                    {result.subGoals.map((subGoal: any, index: number) => (
                      <div key={index} style={{ marginLeft: 8, marginTop: 4 }}>
                        <Text>• {subGoal.name}</Text>
                        <div style={{ marginLeft: 16, fontSize: 11, color: '#666' }}>
                          里程碑: {subGoal.milestones?.length || 0} 个
                        </div>
                      </div>
                    ))}
                  </div>
                )}
              </div>
            ) : (
              <Text type="secondary">没有分解结果数据</Text>
            )}
          </Panel>

          {/* 错误详情 */}
          {error && (
            <Panel header="错误详情" key="error">
              <Alert
                message="错误信息"
                description={error}
                type="error"
                size="small"
              />
            </Panel>
          )}

          {/* 原始数据 */}
          <Panel header="原始数据" key="raw">
            <div style={{ fontSize: 11, fontFamily: 'monospace' }}>
              <Paragraph copyable={{ text: JSON.stringify({ goalId, sessionId, result, error, sessions }, null, 2) }}>
                <pre style={{ 
                  maxHeight: 200, 
                  overflow: 'auto', 
                  backgroundColor: '#f5f5f5', 
                  padding: 8, 
                  borderRadius: 4,
                  fontSize: 10
                }}>
                  {JSON.stringify({ goalId, sessionId, result, error, sessions }, null, 2)}
                </pre>
              </Paragraph>
            </div>
          </Panel>
        </Collapse>

        {/* 操作按钮 */}
        <div style={{ marginTop: 12, textAlign: 'center' }}>
          <Space>
            <Button 
              size="small" 
              onClick={() => {
                console.log('=== 分解结果调试信息 ===');
                console.log('Goal ID:', goalId);
                console.log('Session ID:', sessionId);
                console.log('Result:', result);
                console.log('Error:', error);
                console.log('Sessions:', sessions);
                console.log('========================');
              }}
            >
              输出到控制台
            </Button>
            <Button 
              size="small" 
              onClick={() => {
                const debugData = { goalId, sessionId, result, error, sessions };
                const dataStr = JSON.stringify(debugData, null, 2);
                const dataBlob = new Blob([dataStr], { type: 'application/json' });
                const link = document.createElement('a');
                link.href = URL.createObjectURL(dataBlob);
                link.download = `decomposition-debug-${Date.now()}.json`;
                link.click();
              }}
            >
              导出调试数据
            </Button>
          </Space>
        </div>
      </Card>
    </div>
  );
};

export default DecompositionDebugInfo;
