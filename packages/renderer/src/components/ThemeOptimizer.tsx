import React, { useState, useEffect } from 'react';
import { Card, Switch, Slider, Select, Space, Typography, Divider, Button, ColorPicker, Alert } from 'antd';
import { 
  BgColorsOutlined, 
  EyeOutlined, 
  SettingOutlined,
  SunOutlined,
  MoonOutlined,
  DesktopOutlined
} from '@ant-design/icons';
import { useTheme } from '../contexts/ThemeContext';

const { Title, Text } = Typography;
const { Option } = Select;

interface ThemeSettings {
  // 基础主题
  mode: 'light' | 'dark' | 'auto';
  
  // 颜色设置
  primaryColor: string;
  accentColor: string;
  backgroundColor: string;
  surfaceColor: string;
  
  // 字体设置
  fontSize: number;
  fontFamily: string;
  lineHeight: number;
  
  // 间距设置
  borderRadius: number;
  spacing: number;
  
  // 动画设置
  animationSpeed: number;
  enableAnimations: boolean;
  
  // 可访问性
  highContrast: boolean;
  reducedMotion: boolean;
  
  // 性能设置
  enableBlur: boolean;
  enableShadows: boolean;
  enableGradients: boolean;
}

const ThemeOptimizer: React.FC = () => {
  const { theme, updateTheme } = useTheme();
  const [settings, setSettings] = useState<ThemeSettings>({
    mode: 'light',
    primaryColor: '#1890ff',
    accentColor: '#52c41a',
    backgroundColor: '#ffffff',
    surfaceColor: '#fafafa',
    fontSize: 14,
    fontFamily: 'system-ui',
    lineHeight: 1.5,
    borderRadius: 6,
    spacing: 16,
    animationSpeed: 1,
    enableAnimations: true,
    highContrast: false,
    reducedMotion: false,
    enableBlur: true,
    enableShadows: true,
    enableGradients: true
  });

  useEffect(() => {
    loadSettings();
  }, []);

  const loadSettings = () => {
    try {
      const saved = localStorage.getItem('focusOS_theme_settings');
      if (saved) {
        const savedSettings = JSON.parse(saved);
        setSettings(prev => ({ ...prev, ...savedSettings }));
        applyThemeSettings(savedSettings);
      }
    } catch (error) {
      console.error('加载主题设置失败:', error);
    }
  };

  const saveSettings = (newSettings: Partial<ThemeSettings>) => {
    const updatedSettings = { ...settings, ...newSettings };
    setSettings(updatedSettings);
    
    try {
      localStorage.setItem('focusOS_theme_settings', JSON.stringify(updatedSettings));
      applyThemeSettings(updatedSettings);
    } catch (error) {
      console.error('保存主题设置失败:', error);
    }
  };

  const applyThemeSettings = (themeSettings: ThemeSettings) => {
    const root = document.documentElement;
    
    // 应用CSS变量
    root.style.setProperty('--primary-color', themeSettings.primaryColor);
    root.style.setProperty('--accent-color', themeSettings.accentColor);
    root.style.setProperty('--background-color', themeSettings.backgroundColor);
    root.style.setProperty('--surface-color', themeSettings.surfaceColor);
    root.style.setProperty('--font-size-base', `${themeSettings.fontSize}px`);
    root.style.setProperty('--line-height-base', themeSettings.lineHeight.toString());
    root.style.setProperty('--border-radius-base', `${themeSettings.borderRadius}px`);
    root.style.setProperty('--spacing-base', `${themeSettings.spacing}px`);
    root.style.setProperty('--animation-duration', `${themeSettings.animationSpeed}s`);
    
    // 应用字体
    root.style.setProperty('--font-family-base', themeSettings.fontFamily);
    
    // 应用主题模式
    if (themeSettings.mode === 'auto') {
      const prefersDark = window.matchMedia('(prefers-color-scheme: dark)').matches;
      updateTheme(prefersDark ? 'dark' : 'light');
    } else {
      updateTheme(themeSettings.mode);
    }
    
    // 应用可访问性设置
    if (themeSettings.highContrast) {
      root.classList.add('high-contrast');
    } else {
      root.classList.remove('high-contrast');
    }
    
    if (themeSettings.reducedMotion) {
      root.classList.add('reduced-motion');
    } else {
      root.classList.remove('reduced-motion');
    }
    
    // 应用性能设置
    if (!themeSettings.enableAnimations) {
      root.classList.add('no-animations');
    } else {
      root.classList.remove('no-animations');
    }
    
    if (!themeSettings.enableBlur) {
      root.classList.add('no-blur');
    } else {
      root.classList.remove('no-blur');
    }
    
    if (!themeSettings.enableShadows) {
      root.classList.add('no-shadows');
    } else {
      root.classList.remove('no-shadows');
    }
    
    if (!themeSettings.enableGradients) {
      root.classList.add('no-gradients');
    } else {
      root.classList.remove('no-gradients');
    }
  };

  const resetToDefaults = () => {
    const defaultSettings: ThemeSettings = {
      mode: 'light',
      primaryColor: '#1890ff',
      accentColor: '#52c41a',
      backgroundColor: '#ffffff',
      surfaceColor: '#fafafa',
      fontSize: 14,
      fontFamily: 'system-ui',
      lineHeight: 1.5,
      borderRadius: 6,
      spacing: 16,
      animationSpeed: 1,
      enableAnimations: true,
      highContrast: false,
      reducedMotion: false,
      enableBlur: true,
      enableShadows: true,
      enableGradients: true
    };
    
    saveSettings(defaultSettings);
  };

  const presetThemes = [
    {
      name: '默认蓝色',
      settings: {
        primaryColor: '#1890ff',
        accentColor: '#52c41a',
        backgroundColor: '#ffffff',
        surfaceColor: '#fafafa'
      }
    },
    {
      name: '优雅紫色',
      settings: {
        primaryColor: '#722ed1',
        accentColor: '#eb2f96',
        backgroundColor: '#ffffff',
        surfaceColor: '#f9f0ff'
      }
    },
    {
      name: '温暖橙色',
      settings: {
        primaryColor: '#fa8c16',
        accentColor: '#faad14',
        backgroundColor: '#ffffff',
        surfaceColor: '#fff7e6'
      }
    },
    {
      name: '自然绿色',
      settings: {
        primaryColor: '#52c41a',
        accentColor: '#13c2c2',
        backgroundColor: '#ffffff',
        surfaceColor: '#f6ffed'
      }
    }
  ];

  return (
    <div style={{ maxWidth: 800 }}>
      <Card 
        title={
          <Space>
            <BgColorsOutlined />
            <span>主题与外观</span>
          </Space>
        }
        extra={
          <Button onClick={resetToDefaults} size="small">
            重置默认
          </Button>
        }
      >
        {/* 主题模式 */}
        <div style={{ marginBottom: 24 }}>
          <Title level={5}>主题模式</Title>
          <Select
            value={settings.mode}
            onChange={(value) => saveSettings({ mode: value })}
            style={{ width: 200 }}
          >
            <Option value="light">
              <Space>
                <SunOutlined />
                浅色模式
              </Space>
            </Option>
            <Option value="dark">
              <Space>
                <MoonOutlined />
                深色模式
              </Space>
            </Option>
            <Option value="auto">
              <Space>
                <DesktopOutlined />
                跟随系统
              </Space>
            </Option>
          </Select>
        </div>

        <Divider />

        {/* 预设主题 */}
        <div style={{ marginBottom: 24 }}>
          <Title level={5}>预设主题</Title>
          <div style={{ display: 'grid', gridTemplateColumns: 'repeat(auto-fit, minmax(150px, 1fr))', gap: 12 }}>
            {presetThemes.map((preset, index) => (
              <Card
                key={index}
                size="small"
                hoverable
                onClick={() => saveSettings(preset.settings)}
                style={{
                  cursor: 'pointer',
                  border: settings.primaryColor === preset.settings.primaryColor ? '2px solid var(--primary-color)' : undefined
                }}
              >
                <div style={{ textAlign: 'center' }}>
                  <div style={{
                    width: '100%',
                    height: 40,
                    borderRadius: 4,
                    background: `linear-gradient(45deg, ${preset.settings.primaryColor}, ${preset.settings.accentColor})`,
                    marginBottom: 8
                  }} />
                  <Text style={{ fontSize: 12 }}>{preset.name}</Text>
                </div>
              </Card>
            ))}
          </div>
        </div>

        <Divider />

        {/* 颜色设置 */}
        <div style={{ marginBottom: 24 }}>
          <Title level={5}>颜色设置</Title>
          <div style={{ display: 'grid', gridTemplateColumns: 'repeat(auto-fit, minmax(200px, 1fr))', gap: 16 }}>
            <div>
              <Text>主色调</Text>
              <div style={{ marginTop: 8 }}>
                <ColorPicker
                  value={settings.primaryColor}
                  onChange={(color) => saveSettings({ primaryColor: color.toHexString() })}
                  showText
                />
              </div>
            </div>
            <div>
              <Text>强调色</Text>
              <div style={{ marginTop: 8 }}>
                <ColorPicker
                  value={settings.accentColor}
                  onChange={(color) => saveSettings({ accentColor: color.toHexString() })}
                  showText
                />
              </div>
            </div>
          </div>
        </div>

        <Divider />

        {/* 字体设置 */}
        <div style={{ marginBottom: 24 }}>
          <Title level={5}>字体设置</Title>
          <div style={{ display: 'grid', gridTemplateColumns: 'repeat(auto-fit, minmax(200px, 1fr))', gap: 16 }}>
            <div>
              <Text>字体大小: {settings.fontSize}px</Text>
              <Slider
                min={12}
                max={18}
                step={1}
                value={settings.fontSize}
                onChange={(value) => saveSettings({ fontSize: value })}
                marks={{ 12: '12px', 14: '14px', 16: '16px', 18: '18px' }}
              />
            </div>
            <div>
              <Text>行高: {settings.lineHeight}</Text>
              <Slider
                min={1.2}
                max={2.0}
                step={0.1}
                value={settings.lineHeight}
                onChange={(value) => saveSettings({ lineHeight: value })}
                marks={{ 1.2: '1.2', 1.5: '1.5', 1.8: '1.8', 2.0: '2.0' }}
              />
            </div>
          </div>
          <div style={{ marginTop: 16 }}>
            <Text>字体族</Text>
            <Select
              value={settings.fontFamily}
              onChange={(value) => saveSettings({ fontFamily: value })}
              style={{ width: '100%', marginTop: 8 }}
            >
              <Option value="system-ui">系统默认</Option>
              <Option value="-apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto">现代无衬线</Option>
              <Option value="'Times New Roman', Times, serif">衬线字体</Option>
              <Option value="'Courier New', Courier, monospace">等宽字体</Option>
            </Select>
          </div>
        </div>

        <Divider />

        {/* 界面设置 */}
        <div style={{ marginBottom: 24 }}>
          <Title level={5}>界面设置</Title>
          <div style={{ display: 'grid', gridTemplateColumns: 'repeat(auto-fit, minmax(200px, 1fr))', gap: 16 }}>
            <div>
              <Text>圆角大小: {settings.borderRadius}px</Text>
              <Slider
                min={0}
                max={16}
                step={2}
                value={settings.borderRadius}
                onChange={(value) => saveSettings({ borderRadius: value })}
                marks={{ 0: '0px', 6: '6px', 12: '12px', 16: '16px' }}
              />
            </div>
            <div>
              <Text>间距大小: {settings.spacing}px</Text>
              <Slider
                min={8}
                max={32}
                step={4}
                value={settings.spacing}
                onChange={(value) => saveSettings({ spacing: value })}
                marks={{ 8: '8px', 16: '16px', 24: '24px', 32: '32px' }}
              />
            </div>
          </div>
        </div>

        <Divider />

        {/* 动画与性能 */}
        <div style={{ marginBottom: 24 }}>
          <Title level={5}>动画与性能</Title>
          <Space direction="vertical" style={{ width: '100%' }}>
            <div style={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center' }}>
              <Text>启用动画</Text>
              <Switch
                checked={settings.enableAnimations}
                onChange={(checked) => saveSettings({ enableAnimations: checked })}
              />
            </div>
            
            {settings.enableAnimations && (
              <div>
                <Text>动画速度: {settings.animationSpeed}x</Text>
                <Slider
                  min={0.5}
                  max={2.0}
                  step={0.1}
                  value={settings.animationSpeed}
                  onChange={(value) => saveSettings({ animationSpeed: value })}
                  marks={{ 0.5: '0.5x', 1: '1x', 1.5: '1.5x', 2: '2x' }}
                />
              </div>
            )}
            
            <div style={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center' }}>
              <Text>启用模糊效果</Text>
              <Switch
                checked={settings.enableBlur}
                onChange={(checked) => saveSettings({ enableBlur: checked })}
              />
            </div>
            
            <div style={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center' }}>
              <Text>启用阴影效果</Text>
              <Switch
                checked={settings.enableShadows}
                onChange={(checked) => saveSettings({ enableShadows: checked })}
              />
            </div>
            
            <div style={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center' }}>
              <Text>启用渐变效果</Text>
              <Switch
                checked={settings.enableGradients}
                onChange={(checked) => saveSettings({ enableGradients: checked })}
              />
            </div>
          </Space>
        </div>

        <Divider />

        {/* 可访问性 */}
        <div>
          <Title level={5}>可访问性</Title>
          <Space direction="vertical" style={{ width: '100%' }}>
            <div style={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center' }}>
              <Text>高对比度模式</Text>
              <Switch
                checked={settings.highContrast}
                onChange={(checked) => saveSettings({ highContrast: checked })}
              />
            </div>
            
            <div style={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center' }}>
              <Text>减少动画</Text>
              <Switch
                checked={settings.reducedMotion}
                onChange={(checked) => saveSettings({ reducedMotion: checked })}
              />
            </div>
          </Space>
          
          <Alert
            message="可访问性提示"
            description="这些设置有助于改善视觉障碍用户的使用体验。高对比度模式会增强文本和背景的对比度，减少动画会降低动画效果对注意力的干扰。"
            type="info"
            showIcon
            style={{ marginTop: 16 }}
          />
        </div>
      </Card>
    </div>
  );
};

export default ThemeOptimizer;
