import React, { useState, useMemo } from 'react';
import { Tree, Card, Tag, Button, Space, Tooltip, Empty, Input, Select } from 'antd';
import { 
  PlayCircleOutlined, 
  EditOutlined, 
  DeleteOutlined, 
  FlagOutlined,
  ClockCircleOutlined,
  ApartmentOutlined,
  SearchOutlined,
  FilterOutlined
} from '@ant-design/icons';
import { Task, Goal } from '../types';
import type { DataNode } from 'antd/es/tree';

interface TaskHierarchyViewProps {
  tasks: Task[];
  goals: Goal[];
  onTaskEdit: (task: Task) => void;
  onTaskDelete: (taskId: string) => void;
  onStartPomodoro: (task: Task) => void;
  loading?: boolean;
}

interface HierarchyNode extends DataNode {
  type: 'goal' | 'subgoal' | 'milestone' | 'task';
  data?: Goal | Task;
  children?: HierarchyNode[];
}

const TaskHierarchyView: React.FC<TaskHierarchyViewProps> = ({
  tasks,
  goals,
  onTaskEdit,
  onTaskDelete,
  onStartPomodoro,
  loading = false
}) => {
  const [expandedKeys, setExpandedKeys] = useState<React.Key[]>([]);
  const [searchValue, setSearchValue] = useState('');
  const [statusFilter, setStatusFilter] = useState<string>('all');
  const [priorityFilter, setPriorityFilter] = useState<string>('all');

  // 构建层级树数据
  const treeData = useMemo(() => {
    const filteredTasks = tasks.filter(task => {
      const matchesSearch = !searchValue || 
        task.title.toLowerCase().includes(searchValue.toLowerCase()) ||
        task.description?.toLowerCase().includes(searchValue.toLowerCase());
      
      const matchesStatus = statusFilter === 'all' || task.status === statusFilter;
      const matchesPriority = priorityFilter === 'all' || task.priority === priorityFilter;
      
      return matchesSearch && matchesStatus && matchesPriority;
    });

    // 按目标分组任务
    const tasksByGoal: { [goalId: string]: Task[] } = {};
    filteredTasks.forEach(task => {
      if (task.goalNodeId) {
        if (!tasksByGoal[task.goalNodeId]) {
          tasksByGoal[task.goalNodeId] = [];
        }
        tasksByGoal[task.goalNodeId].push(task);
      }
    });

    // 构建树结构
    const nodes: HierarchyNode[] = [];

    goals.forEach(goal => {
      const goalTasks = tasksByGoal[goal.id] || [];
      
      if (goalTasks.length === 0 && searchValue) return; // 搜索时隐藏无任务的目标

      const goalNode: HierarchyNode = {
        key: `goal-${goal.id}`,
        title: renderGoalTitle(goal, goalTasks),
        type: 'goal',
        data: goal,
        children: []
      };

      // 如果目标有AI分解结构，使用分解结构
      if (goal.hasAIDecomposition && goal.decompositionResult) {
        goalNode.children = buildAIDecompositionTree(goal.decompositionResult, goalTasks);
      } else {
        // 否则直接显示任务
        goalNode.children = goalTasks.map(task => ({
          key: `task-${task.id}`,
          title: renderTaskTitle(task),
          type: 'task',
          data: task,
          isLeaf: true
        }));
      }

      nodes.push(goalNode);
    });

    return nodes;
  }, [tasks, goals, searchValue, statusFilter, priorityFilter]);

  // 构建AI分解的树结构
  const buildAIDecompositionTree = (decompositionResult: any, goalTasks: Task[]): HierarchyNode[] => {
    if (!decompositionResult.subGoals) return [];

    return decompositionResult.subGoals.map((subGoal: any, sgIndex: number) => {
      const subGoalNode: HierarchyNode = {
        key: `subgoal-${sgIndex}`,
        title: renderSubGoalTitle(subGoal),
        type: 'subgoal',
        children: []
      };

      if (subGoal.milestones) {
        subGoalNode.children = subGoal.milestones.map((milestone: any, mIndex: number) => {
          const milestoneNode: HierarchyNode = {
            key: `milestone-${sgIndex}-${mIndex}`,
            title: renderMilestoneTitle(milestone),
            type: 'milestone',
            children: []
          };

          if (milestone.tasks) {
            milestoneNode.children = milestone.tasks.map((aiTask: any, tIndex: number) => {
              // 查找对应的实际任务
              const actualTask = goalTasks.find(task => 
                task.title === aiTask.name || 
                task.description?.includes(aiTask.name)
              );

              return {
                key: `ai-task-${sgIndex}-${mIndex}-${tIndex}`,
                title: actualTask ? renderTaskTitle(actualTask) : renderAITaskTitle(aiTask),
                type: 'task',
                data: actualTask,
                isLeaf: true
              };
            });
          }

          return milestoneNode;
        });
      }

      return subGoalNode;
    });
  };

  // 渲染目标标题
  const renderGoalTitle = (goal: Goal, tasks: Task[]) => {
    const completedTasks = tasks.filter(task => task.status === 'completed').length;
    const totalTasks = tasks.length;
    const progress = totalTasks > 0 ? Math.round((completedTasks / totalTasks) * 100) : 0;

    return (
      <div style={{ display: 'flex', alignItems: 'center', justifyContent: 'space-between', width: '100%' }}>
        <div style={{ display: 'flex', alignItems: 'center', gap: 8 }}>
          <ApartmentOutlined style={{ color: '#1890ff' }} />
          <span style={{ fontWeight: 600, fontSize: '14px' }}>{goal.name}</span>
          {goal.hasAIDecomposition && (
            <Tag color="blue" size="small">AI分解</Tag>
          )}
        </div>
        <div style={{ display: 'flex', alignItems: 'center', gap: 8 }}>
          <Tag color={progress === 100 ? 'green' : progress > 0 ? 'orange' : 'default'}>
            {completedTasks}/{totalTasks} ({progress}%)
          </Tag>
        </div>
      </div>
    );
  };

  // 渲染子目标标题
  const renderSubGoalTitle = (subGoal: any) => (
    <div style={{ display: 'flex', alignItems: 'center', gap: 8 }}>
      <FlagOutlined style={{ color: '#52c41a' }} />
      <span style={{ fontWeight: 500 }}>{subGoal.name}</span>
      {subGoal.estimatedTime && (
        <Tag size="small">
          <ClockCircleOutlined /> {subGoal.estimatedTime}h
        </Tag>
      )}
    </div>
  );

  // 渲染里程碑标题
  const renderMilestoneTitle = (milestone: any) => (
    <div style={{ display: 'flex', alignItems: 'center', gap: 8 }}>
      <span style={{ color: '#faad14' }}>🎯</span>
      <span>{milestone.name}</span>
      {milestone.estimatedTime && (
        <Tag size="small">
          <ClockCircleOutlined /> {milestone.estimatedTime}h
        </Tag>
      )}
    </div>
  );

  // 渲染任务标题
  const renderTaskTitle = (task: Task) => {
    const getPriorityColor = (priority: string) => {
      switch (priority) {
        case 'high': return '#ff4d4f';
        case 'medium': return '#faad14';
        case 'low': return '#52c41a';
        default: return '#d9d9d9';
      }
    };

    const getStatusColor = (status: string) => {
      switch (status) {
        case 'todo': return 'default';
        case 'in-progress': return 'processing';
        case 'completed': return 'success';
        case 'paused': return 'warning';
        case 'cancelled': return 'error';
        default: return 'default';
      }
    };

    const isOverdue = task.deadline && 
                     task.status !== 'completed' && 
                     new Date(task.deadline) < new Date();

    return (
      <div style={{ 
        display: 'flex', 
        alignItems: 'center', 
        justifyContent: 'space-between', 
        width: '100%',
        backgroundColor: isOverdue ? '#fff2f0' : 'transparent',
        padding: '4px 8px',
        borderRadius: '4px',
        border: isOverdue ? '1px solid #ffccc7' : 'none'
      }}>
        <div style={{ display: 'flex', alignItems: 'center', gap: 8, flex: 1 }}>
          <span style={{ 
            color: isOverdue ? '#ff4d4f' : 'var(--color-text)',
            textDecoration: task.status === 'completed' ? 'line-through' : 'none'
          }}>
            {task.title}
          </span>
          <Space size={4}>
            <Tag color={getPriorityColor(task.priority)} size="small">
              {task.priority === 'high' ? '高' : task.priority === 'medium' ? '中' : '低'}
            </Tag>
            <Tag color={getStatusColor(task.status)} size="small">
              {task.status === 'todo' ? '待办' : 
               task.status === 'in-progress' ? '进行中' : 
               task.status === 'completed' ? '已完成' : task.status}
            </Tag>
            {isOverdue && <Tag color="red" size="small">逾期</Tag>}
          </Space>
        </div>
        <div style={{ display: 'flex', gap: 4 }}>
          <Tooltip title="开始番茄钟">
            <Button
              type="text"
              size="small"
              icon={<PlayCircleOutlined />}
              onClick={(e) => {
                e.stopPropagation();
                onStartPomodoro(task);
              }}
              style={{ padding: '2px 4px', height: 'auto' }}
            />
          </Tooltip>
          <Tooltip title="编辑任务">
            <Button
              type="text"
              size="small"
              icon={<EditOutlined />}
              onClick={(e) => {
                e.stopPropagation();
                onTaskEdit(task);
              }}
              style={{ padding: '2px 4px', height: 'auto' }}
            />
          </Tooltip>
          <Tooltip title="删除任务">
            <Button
              type="text"
              size="small"
              icon={<DeleteOutlined />}
              onClick={(e) => {
                e.stopPropagation();
                onTaskDelete(task.id);
              }}
              danger
              style={{ padding: '2px 4px', height: 'auto' }}
            />
          </Tooltip>
        </div>
      </div>
    );
  };

  // 渲染AI任务标题（未创建的任务）
  const renderAITaskTitle = (aiTask: any) => (
    <div style={{ display: 'flex', alignItems: 'center', gap: 8 }}>
      <span style={{ color: '#999', fontStyle: 'italic' }}>{aiTask.name}</span>
      <Tag size="small" color="orange">待创建</Tag>
      {aiTask.estimatedTime && (
        <Tag size="small">
          <ClockCircleOutlined /> {aiTask.estimatedTime}h
        </Tag>
      )}
    </div>
  );

  if (loading) {
    return (
      <div style={{ textAlign: 'center', padding: '40px' }}>
        <div>加载中...</div>
      </div>
    );
  }

  return (
    <div style={{ height: '100%' }}>
      <Card 
        title={
          <Space>
            <ApartmentOutlined />
            <span>层级视图</span>
            <Tag color="blue">{tasks.length} 个任务</Tag>
          </Space>
        }
        extra={
          <Space>
            <Input
              placeholder="搜索任务..."
              prefix={<SearchOutlined />}
              value={searchValue}
              onChange={(e) => setSearchValue(e.target.value)}
              style={{ width: 200 }}
              allowClear
            />
            <Select
              value={statusFilter}
              onChange={setStatusFilter}
              style={{ width: 100 }}
              size="small"
            >
              <Select.Option value="all">全部状态</Select.Option>
              <Select.Option value="todo">待办</Select.Option>
              <Select.Option value="in-progress">进行中</Select.Option>
              <Select.Option value="completed">已完成</Select.Option>
            </Select>
            <Select
              value={priorityFilter}
              onChange={setPriorityFilter}
              style={{ width: 100 }}
              size="small"
            >
              <Select.Option value="all">全部优先级</Select.Option>
              <Select.Option value="high">高</Select.Option>
              <Select.Option value="medium">中</Select.Option>
              <Select.Option value="low">低</Select.Option>
            </Select>
          </Space>
        }
        style={{ height: '100%' }}
        bodyStyle={{ height: 'calc(100% - 57px)', overflow: 'auto' }}
      >
        {treeData.length === 0 ? (
          <Empty description="暂无任务数据" />
        ) : (
          <Tree
            treeData={treeData}
            expandedKeys={expandedKeys}
            onExpand={setExpandedKeys}
            showLine={{ showLeafIcon: false }}
            defaultExpandAll
            style={{ fontSize: '13px' }}
          />
        )}
      </Card>
    </div>
  );
};

export default TaskHierarchyView;
