import React, { useState, useEffect, useCallback } from 'react';
import { Layout, Drawer, But<PERSON>, Grid } from 'antd';
import { MenuOutlined, CloseOutlined } from '@ant-design/icons';

const { Header, Sider, Content } = Layout;
const { useBreakpoint } = Grid;

interface ResponsiveLayoutProps {
  children: React.ReactNode;
  sidebar?: React.ReactNode;
  header?: React.ReactNode;
  siderWidth?: number;
  collapsible?: boolean;
  theme?: 'light' | 'dark';
}

interface BreakpointConfig {
  xs: number;  // < 576px
  sm: number;  // >= 576px
  md: number;  // >= 768px
  lg: number;  // >= 992px
  xl: number;  // >= 1200px
  xxl: number; // >= 1600px
}

const ResponsiveLayout: React.FC<ResponsiveLayoutProps> = ({
  children,
  sidebar,
  header,
  siderWidth = 240,
  collapsible = true,
  theme = 'light'
}) => {
  const screens = useBreakpoint();
  const [collapsed, setCollapsed] = useState(false);
  const [mobileDrawerVisible, setMobileDrawerVisible] = useState(false);
  const [isMobile, setIsMobile] = useState(false);

  // 响应式断点配置
  const breakpoints: BreakpointConfig = {
    xs: 576,
    sm: 576,
    md: 768,
    lg: 992,
    xl: 1200,
    xxl: 1600
  };

  // 检测移动设备
  useEffect(() => {
    const checkMobile = () => {
      const mobile = !screens.md;
      setIsMobile(mobile);
      
      // 在移动设备上自动折叠侧边栏
      if (mobile) {
        setCollapsed(true);
        setMobileDrawerVisible(false);
      }
    };

    checkMobile();
  }, [screens]);

  // 处理窗口大小变化
  useEffect(() => {
    const handleResize = () => {
      const width = window.innerWidth;
      
      // 根据屏幕宽度自动调整布局
      if (width < breakpoints.md) {
        setIsMobile(true);
        setCollapsed(true);
      } else if (width < breakpoints.lg) {
        setIsMobile(false);
        setCollapsed(true);
      } else {
        setIsMobile(false);
        if (collapsible) {
          setCollapsed(false);
        }
      }
    };

    window.addEventListener('resize', handleResize);
    return () => window.removeEventListener('resize', handleResize);
  }, [collapsible, breakpoints]);

  const toggleSidebar = useCallback(() => {
    if (isMobile) {
      setMobileDrawerVisible(!mobileDrawerVisible);
    } else {
      setCollapsed(!collapsed);
    }
  }, [isMobile, mobileDrawerVisible, collapsed]);

  const closeMobileDrawer = useCallback(() => {
    setMobileDrawerVisible(false);
  }, []);

  // 获取响应式的侧边栏宽度
  const getSiderWidth = () => {
    if (isMobile) return 0;
    if (collapsed) return 80;
    return siderWidth;
  };

  // 获取响应式的内容边距
  const getContentStyle = () => {
    const baseStyle: React.CSSProperties = {
      minHeight: '100vh',
      transition: 'all 0.2s ease-in-out',
    };

    if (isMobile) {
      return {
        ...baseStyle,
        padding: screens.xs ? '16px 8px' : '16px 16px',
      };
    }

    return {
      ...baseStyle,
      padding: screens.lg ? '24px' : '16px',
      marginLeft: getSiderWidth(),
    };
  };

  // 移动端抽屉侧边栏
  const MobileDrawer = () => (
    <Drawer
      title={null}
      placement="left"
      closable={false}
      onClose={closeMobileDrawer}
      open={mobileDrawerVisible}
      bodyStyle={{ padding: 0 }}
      width={siderWidth}
      style={{ zIndex: 1001 }}
    >
      <div style={{ 
        display: 'flex', 
        justifyContent: 'flex-end', 
        padding: '16px',
        borderBottom: '1px solid #f0f0f0'
      }}>
        <Button 
          type="text" 
          icon={<CloseOutlined />} 
          onClick={closeMobileDrawer}
          size="small"
        />
      </div>
      {sidebar}
    </Drawer>
  );

  // 桌面端侧边栏
  const DesktopSider = () => (
    <Sider
      theme={theme}
      collapsible={collapsible}
      collapsed={collapsed}
      onCollapse={setCollapsed}
      width={siderWidth}
      collapsedWidth={80}
      style={{
        position: 'fixed',
        left: 0,
        top: 0,
        bottom: 0,
        zIndex: 1000,
        overflow: 'auto',
        height: '100vh',
        transition: 'all 0.2s ease-in-out',
      }}
      breakpoint="lg"
      onBreakpoint={(broken) => {
        if (broken) {
          setCollapsed(true);
        }
      }}
    >
      {sidebar}
    </Sider>
  );

  // 响应式头部
  const ResponsiveHeader = () => {
    if (!header && !isMobile) return null;

    return (
      <Header
        style={{
          position: 'fixed',
          top: 0,
          left: isMobile ? 0 : getSiderWidth(),
          right: 0,
          zIndex: 999,
          padding: isMobile ? '0 16px' : '0 24px',
          backgroundColor: theme === 'dark' ? '#001529' : '#fff',
          borderBottom: '1px solid #f0f0f0',
          display: 'flex',
          alignItems: 'center',
          justifyContent: 'space-between',
          transition: 'all 0.2s ease-in-out',
          height: 64,
        }}
      >
        {isMobile && (
          <Button
            type="text"
            icon={<MenuOutlined />}
            onClick={toggleSidebar}
            style={{ marginRight: 16 }}
          />
        )}
        {header}
      </Header>
    );
  };

  return (
    <Layout style={{ minHeight: '100vh' }}>
      {/* 移动端抽屉 */}
      {isMobile && <MobileDrawer />}
      
      {/* 桌面端侧边栏 */}
      {!isMobile && sidebar && <DesktopSider />}
      
      {/* 头部 */}
      <ResponsiveHeader />
      
      {/* 主内容区 */}
      <Content
        style={{
          ...getContentStyle(),
          marginTop: (header || isMobile) ? 64 : 0,
        }}
      >
        <div
          style={{
            maxWidth: '100%',
            margin: '0 auto',
            // 根据屏幕大小调整最大宽度
            ...(screens.xxl && { maxWidth: 1600 }),
            ...(screens.xl && !screens.xxl && { maxWidth: 1200 }),
            ...(screens.lg && !screens.xl && { maxWidth: 992 }),
          }}
        >
          {children}
        </div>
      </Content>
    </Layout>
  );
};

// 响应式容器组件
export const ResponsiveContainer: React.FC<{
  children: React.ReactNode;
  fluid?: boolean;
  className?: string;
  style?: React.CSSProperties;
}> = ({ children, fluid = false, className, style }) => {
  const screens = useBreakpoint();

  const getContainerStyle = (): React.CSSProperties => {
    if (fluid) {
      return {
        width: '100%',
        padding: screens.xs ? '0 8px' : '0 16px',
        ...style,
      };
    }

    return {
      maxWidth: screens.xxl ? 1600 : screens.xl ? 1200 : screens.lg ? 992 : screens.md ? 768 : '100%',
      margin: '0 auto',
      padding: screens.xs ? '0 8px' : '0 16px',
      ...style,
    };
  };

  return (
    <div className={className} style={getContainerStyle()}>
      {children}
    </div>
  );
};

// 响应式网格组件
export const ResponsiveGrid: React.FC<{
  children: React.ReactNode;
  columns?: {
    xs?: number;
    sm?: number;
    md?: number;
    lg?: number;
    xl?: number;
    xxl?: number;
  };
  gap?: number | string;
  className?: string;
  style?: React.CSSProperties;
}> = ({ 
  children, 
  columns = { xs: 1, sm: 2, md: 3, lg: 4, xl: 5, xxl: 6 },
  gap = 16,
  className,
  style 
}) => {
  const screens = useBreakpoint();

  const getCurrentColumns = () => {
    if (screens.xxl) return columns.xxl || columns.xl || columns.lg || columns.md || columns.sm || columns.xs || 1;
    if (screens.xl) return columns.xl || columns.lg || columns.md || columns.sm || columns.xs || 1;
    if (screens.lg) return columns.lg || columns.md || columns.sm || columns.xs || 1;
    if (screens.md) return columns.md || columns.sm || columns.xs || 1;
    if (screens.sm) return columns.sm || columns.xs || 1;
    return columns.xs || 1;
  };

  const gridStyle: React.CSSProperties = {
    display: 'grid',
    gridTemplateColumns: `repeat(${getCurrentColumns()}, 1fr)`,
    gap: typeof gap === 'number' ? `${gap}px` : gap,
    ...style,
  };

  return (
    <div className={className} style={gridStyle}>
      {children}
    </div>
  );
};

// 响应式Hook
export const useResponsive = () => {
  const screens = useBreakpoint();
  const [windowSize, setWindowSize] = useState({
    width: window.innerWidth,
    height: window.innerHeight,
  });

  useEffect(() => {
    const handleResize = () => {
      setWindowSize({
        width: window.innerWidth,
        height: window.innerHeight,
      });
    };

    window.addEventListener('resize', handleResize);
    return () => window.removeEventListener('resize', handleResize);
  }, []);

  return {
    screens,
    windowSize,
    isMobile: !screens.md,
    isTablet: screens.md && !screens.lg,
    isDesktop: screens.lg,
    isLargeDesktop: screens.xl,
  };
};

export default ResponsiveLayout;
