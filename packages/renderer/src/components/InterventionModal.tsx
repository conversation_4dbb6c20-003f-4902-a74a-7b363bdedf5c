import React, { useState, useEffect } from 'react';
import {
  Modal,
  Button,
  Typography,
  Space,
  Progress,
  Alert,
  Card,
  Row,
  Col,
  Statistic,
  Tag,
  Divider
} from 'antd';
import {
  WarningOutlined,
  SafetyOutlined,
  ClockCircleOutlined,
  CheckCircleOutlined,
  StopOutlined,
  ArrowLeftOutlined,
  TrophyOutlined
} from '@ant-design/icons';
import { useTheme } from '../contexts/ThemeContext';
import { InterventionEvent, InterventionConfig, interventionEngineService } from '../services/InterventionEngineService';
import { ApplicationInfo, WebsiteInfo } from '../services/ApplicationMonitorService';
import { focusMonitorService } from '../services/FocusMonitorService';

const { Title, Text, Paragraph } = Typography;

interface InterventionModalProps {
  visible: boolean;
  event: InterventionEvent | null;
  config: InterventionConfig | null;
  onClose: () => void;
}

const InterventionModal: React.FC<InterventionModalProps> = ({
  visible,
  event,
  config,
  onClose
}) => {
  const { theme } = useTheme();
  const [countdown, setCountdown] = useState(0);
  const [canSkip, setCanSkip] = useState(false);
  const [userChoice, setUserChoice] = useState<'continue' | 'return' | null>(null);
  const [responseStartTime] = useState(Date.now());

  useEffect(() => {
    if (visible && config?.delaySeconds) {
      setCountdown(config.delaySeconds);
      setCanSkip(config.allowSkip || false);
      
      const timer = setInterval(() => {
        setCountdown(prev => {
          if (prev <= 1) {
            clearInterval(timer);
            setCanSkip(true);
            return 0;
          }
          return prev - 1;
        });
      }, 1000);

      return () => clearInterval(timer);
    } else {
      setCanSkip(config?.allowSkip || true);
    }
  }, [visible, config]);

  if (!event || !config) return null;

  const targetName = event.target.name || (event.target as WebsiteInfo).domain;
  const isApp = 'bundleId' in event.target || 'executable' in event.target;

  const handleUserChoice = (choice: 'continue' | 'return') => {
    setUserChoice(choice);
    const responseTime = Date.now() - responseStartTime;
    
    setTimeout(() => {
      if (choice === 'continue') {
        interventionEngineService.handleUserResponse(event.id, 'continued', responseTime);
      } else {
        interventionEngineService.handleUserResponse(event.id, 'returned', responseTime);
      }
      onClose();
    }, 500);
  };

  const handleSkip = () => {
    const responseTime = Date.now() - responseStartTime;
    interventionEngineService.handleUserResponse(event.id, 'skipped', responseTime);
    onClose();
  };

  const getInterventionIcon = () => {
    switch (event.level) {
      case 'gentle':
        return <CheckCircleOutlined style={{ color: '#52c41a', fontSize: '48px' }} />;
      case 'warning':
        return <WarningOutlined style={{ color: '#faad14', fontSize: '48px' }} />;
      case 'firm':
        return <StopOutlined style={{ color: '#ff4d4f', fontSize: '48px' }} />;
      case 'block':
        return <SafetyOutlined style={{ color: '#ff4d4f', fontSize: '48px' }} />;
      default:
        return <WarningOutlined style={{ color: '#faad14', fontSize: '48px' }} />;
    }
  };

  const getInterventionTitle = () => {
    switch (event.level) {
      case 'gentle':
        return '🤔 专注提醒';
      case 'warning':
        return '⚠️ 注意分心';
      case 'firm':
        return '🛑 请停止分心';
      case 'block':
        return '🚫 访问被阻止';
      default:
        return '💡 专注提醒';
    }
  };

  const getInterventionMessage = () => {
    const targetType = isApp ? '应用' : '网站';
    
    switch (event.level) {
      case 'gentle':
        return `您正在访问 "${targetName}"，这可能会分散您的注意力。需要继续访问吗？`;
      case 'warning':
        return `检测到您访问了分心${targetType} "${targetName}"。请考虑回到您的工作任务。`;
      case 'firm':
        return `您已多次访问分心${targetType}。为了保持专注，建议您立即返回工作。`;
      case 'block':
        return `对不起，当前专注模式下不允许访问 "${targetName}"。请专注于您的工作任务。`;
      default:
        return `请注意，您正在访问 "${targetName}"。`;
    }
  };

  const getCurrentTask = () => {
    const session = focusMonitorService.getCurrentSession();
    return session?.taskName || '当前任务';
  };

  const getFocusStats = () => {
    const session = focusMonitorService.getCurrentSession();
    const insights = focusMonitorService.getFocusInsights(1);
    const todayInsight = insights[0];
    
    return {
      currentScore: session?.focusScore || 100,
      distractionsToday: todayInsight?.distractionCount || 0,
      focusTimeToday: todayInsight?.totalFocusTime || 0
    };
  };

  const stats = getFocusStats();

  const renderCountdownProgress = () => {
    if (!config.delaySeconds || countdown === 0) return null;
    
    const progress = ((config.delaySeconds - countdown) / config.delaySeconds) * 100;
    
    return (
      <div style={{ textAlign: 'center', marginBottom: 24 }}>
        <Progress
          type="circle"
          percent={progress}
          format={() => countdown}
          size={120}
          strokeColor={{
            '0%': '#ff4d4f',
            '100%': '#52c41a',
          }}
        />
        <div style={{ marginTop: 12 }}>
          <Text type="secondary">
            {countdown > 0 ? `请等待 ${countdown} 秒后选择` : '现在可以选择操作'}
          </Text>
        </div>
      </div>
    );
  };

  const renderFocusStats = () => (
    <Card size="small" style={{ marginBottom: 16 }}>
      <Row gutter={16}>
        <Col span={8}>
          <Statistic
            title="专注分数"
            value={stats.currentScore}
            suffix="/100"
            valueStyle={{ 
              color: stats.currentScore >= 80 ? '#52c41a' : stats.currentScore >= 60 ? '#faad14' : '#ff4d4f',
              fontSize: '18px'
            }}
          />
        </Col>
        <Col span={8}>
          <Statistic
            title="今日分心"
            value={stats.distractionsToday}
            suffix="次"
            valueStyle={{ fontSize: '18px' }}
          />
        </Col>
        <Col span={8}>
          <Statistic
            title="专注时长"
            value={stats.focusTimeToday}
            suffix="分钟"
            valueStyle={{ fontSize: '18px' }}
          />
        </Col>
      </Row>
    </Card>
  );

  const renderActionButtons = () => {
    if (event.level === 'block') {
      return (
        <Space size="large" style={{ width: '100%', justifyContent: 'center' }}>
          <Button
            type="primary"
            size="large"
            icon={<ArrowLeftOutlined />}
            onClick={() => handleUserChoice('return')}
            style={{ minWidth: '120px' }}
          >
            返回工作
          </Button>
        </Space>
      );
    }

    return (
      <Space size="large" style={{ width: '100%', justifyContent: 'center' }}>
        <Button
          type="primary"
          size="large"
          icon={<ArrowLeftOutlined />}
          onClick={() => handleUserChoice('return')}
          disabled={countdown > 0}
          style={{ minWidth: '120px' }}
        >
          返回工作
        </Button>
        <Button
          size="large"
          onClick={() => handleUserChoice('continue')}
          disabled={countdown > 0}
          style={{ minWidth: '120px' }}
        >
          继续访问
        </Button>
        {canSkip && config.allowSkip && (
          <Button
            type="link"
            onClick={handleSkip}
            style={{ color: theme.colors.textSecondary }}
          >
            跳过此次提醒
          </Button>
        )}
      </Space>
    );
  };

  const renderMotivationalMessage = () => {
    const messages = [
      '🎯 保持专注，您正在朝着目标前进！',
      '🌟 每一次选择专注，都是在投资您的未来',
      '💪 坚持就是胜利，您可以做到的！',
      '🚀 专注的您是最棒的！',
      '⭐ 您的目标值得这份专注'
    ];
    
    const randomMessage = messages[Math.floor(Math.random() * messages.length)];
    
    return (
      <Alert
        message={randomMessage}
        type="info"
        showIcon
        icon={<TrophyOutlined />}
        style={{ marginBottom: 16 }}
      />
    );
  };

  if (userChoice) {
    return (
      <Modal
        open={visible}
        onCancel={onClose}
        footer={null}
        centered
        width={400}
        closable={false}
      >
        <div style={{ textAlign: 'center', padding: '24px 0' }}>
          {userChoice === 'return' ? (
            <>
              <CheckCircleOutlined style={{ fontSize: '64px', color: '#52c41a', marginBottom: 16 }} />
              <Title level={3} style={{ color: '#52c41a', marginBottom: 8 }}>
                很好的选择！
              </Title>
              <Text type="secondary">
                您选择了继续专注，这是明智的决定。
              </Text>
            </>
          ) : (
            <>
              <WarningOutlined style={{ fontSize: '64px', color: '#faad14', marginBottom: 16 }} />
              <Title level={3} style={{ color: '#faad14', marginBottom: 8 }}>
                提醒已记录
              </Title>
              <Text type="secondary">
                请尽快回到您的工作任务。
              </Text>
            </>
          )}
        </div>
      </Modal>
    );
  }

  return (
    <Modal
      open={visible}
      onCancel={event.level !== 'block' ? onClose : undefined}
      footer={null}
      centered
      width={600}
      closable={event.level !== 'block'}
      maskClosable={false}
      style={{
        background: theme.colors.background
      }}
    >
      <div style={{ textAlign: 'center', padding: '12px 0' }}>
        {/* 标题和图标 */}
        <div style={{ marginBottom: 24 }}>
          {getInterventionIcon()}
          <Title level={2} style={{ marginTop: 16, marginBottom: 8 }}>
            {getInterventionTitle()}
          </Title>
          <Tag color={event.rule.severity === 'high' ? 'red' : event.rule.severity === 'medium' ? 'orange' : 'default'}>
            {event.rule.name} - {event.rule.category}
          </Tag>
        </div>

        {/* 主要消息 */}
        <Card style={{ marginBottom: 16, textAlign: 'left' }}>
          <Paragraph style={{ fontSize: '16px', marginBottom: 16 }}>
            {getInterventionMessage()}
          </Paragraph>
          <Divider />
          <div style={{ display: 'flex', alignItems: 'center', gap: '8px' }}>
            <ClockCircleOutlined style={{ color: theme.colors.primary }} />
            <Text strong>当前任务: </Text>
            <Text>{getCurrentTask()}</Text>
          </div>
        </Card>

        {/* 专注统计 */}
        {renderFocusStats()}

        {/* 激励消息 */}
        {renderMotivationalMessage()}

        {/* 倒计时进度 */}
        {renderCountdownProgress()}

        {/* 操作按钮 */}
        {renderActionButtons()}

        {/* 规则信息 */}
        <div style={{ marginTop: 24, textAlign: 'left' }}>
          <Text type="secondary" style={{ fontSize: '12px' }}>
            触发规则: {event.rule.name} | 
            干预级别: {event.level} | 
            类型: {isApp ? '应用程序' : '网站'}
          </Text>
        </div>
      </div>
    </Modal>
  );
};

// 全局干预管理器组件
export const InterventionManager: React.FC = () => {
  const [modalVisible, setModalVisible] = useState(false);
  const [currentEvent, setCurrentEvent] = useState<InterventionEvent | null>(null);
  const [currentConfig, setCurrentConfig] = useState<InterventionConfig | null>(null);

  useEffect(() => {
    // 监听干预事件
    const handleInterventionModal = (e: CustomEvent) => {
      const { event, config } = e.detail;
      setCurrentEvent(event);
      setCurrentConfig(config);
      setModalVisible(true);
    };

    const handleInterventionDelay = (e: CustomEvent) => {
      const { event, config } = e.detail;
      setCurrentEvent(event);
      setCurrentConfig(config);
      setModalVisible(true);
    };

    const handleInterventionBlock = (e: CustomEvent) => {
      const { event, config } = e.detail;
      setCurrentEvent(event);
      setCurrentConfig(config);
      setModalVisible(true);
    };

    window.addEventListener('show-intervention-modal', handleInterventionModal as EventListener);
    window.addEventListener('show-intervention-delay', handleInterventionDelay as EventListener);
    window.addEventListener('show-intervention-block', handleInterventionBlock as EventListener);

    return () => {
      window.removeEventListener('show-intervention-modal', handleInterventionModal as EventListener);
      window.removeEventListener('show-intervention-delay', handleInterventionDelay as EventListener);
      window.removeEventListener('show-intervention-block', handleInterventionBlock as EventListener);
    };
  }, []);

  const handleClose = () => {
    setModalVisible(false);
    setCurrentEvent(null);
    setCurrentConfig(null);
  };

  return (
    <InterventionModal
      visible={modalVisible}
      event={currentEvent}
      config={currentConfig}
      onClose={handleClose}
    />
  );
};

export default InterventionModal;