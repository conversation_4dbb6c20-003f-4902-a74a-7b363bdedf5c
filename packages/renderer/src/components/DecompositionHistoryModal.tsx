import React, { useState, useEffect } from 'react';
import { Modal, Table, Button, Space, Tag, Tooltip, message, Popconfirm, Typography } from 'antd';
import { HistoryOutlined, RollbackOutlined, EyeOutlined, DeleteOutlined } from '@ant-design/icons';
import { DatabaseAPI } from '../services/api';
import type { ColumnsType } from 'antd/es/table';

const { Text } = Typography;

interface DecompositionHistoryModalProps {
  visible: boolean;
  goalId: string | null;
  goalName: string;
  onClose: () => void;
  onRollback?: (sessionId: string) => void;
}

interface HistorySession {
  id: string;
  version: number;
  status: string;
  aiProvider: string;
  isActive: boolean;
  replacedBy?: string;
  replacementReason?: string;
  createdAt: string;
  updatedAt: string;
}

const DecompositionHistoryModal: React.FC<DecompositionHistoryModalProps> = ({
  visible,
  goalId,
  goalName,
  onClose,
  onRollback
}) => {
  const [loading, setLoading] = useState(false);
  const [history, setHistory] = useState<{
    sessions: HistorySession[];
    activeSession: HistorySession | null;
    totalVersions: number;
  } | null>(null);

  useEffect(() => {
    if (visible && goalId) {
      loadHistory();
    }
  }, [visible, goalId]);

  const loadHistory = async () => {
    if (!goalId) return;
    
    setLoading(true);
    try {
      const result = await DatabaseAPI.getDecompositionHistory(goalId);
      if (result.success) {
        setHistory(result.history);
      } else {
        message.error('获取分解历史失败: ' + result.error);
      }
    } catch (error) {
      console.error('获取分解历史失败:', error);
      message.error('获取分解历史失败');
    } finally {
      setLoading(false);
    }
  };

  const handleRollback = async (sessionId: string) => {
    if (!goalId) return;
    
    setLoading(true);
    try {
      const result = await DatabaseAPI.rollbackToVersion(goalId, sessionId);
      if (result.success) {
        message.success('成功回滚到指定版本');
        await loadHistory(); // 重新加载历史
        if (onRollback) {
          onRollback(sessionId);
        }
      } else {
        message.error('回滚失败: ' + result.error);
      }
    } catch (error) {
      console.error('回滚失败:', error);
      message.error('回滚失败');
    } finally {
      setLoading(false);
    }
  };

  const getStatusTag = (session: HistorySession) => {
    // 修复：使用合法的状态值，'applied' 改为 'completed'
    if (session.isActive && session.status === 'completed') {
      return <Tag color="green">当前活跃</Tag>;
    }

    switch (session.status) {
      case 'completed':
        return session.isActive ? <Tag color="green">已应用</Tag> : <Tag color="blue">已完成</Tag>;
      case 'failed':
        return <Tag color="red">失败</Tag>;
      case 'in_progress':
        return <Tag color="processing">进行中</Tag>;
      case 'user_modified':
        return <Tag color="orange">用户修改</Tag>;
      case 'not_started':
        return <Tag color="default">未开始</Tag>;
      default:
        return <Tag>{session.status}</Tag>;
    }
  };

  const columns: ColumnsType<HistorySession> = [
    {
      title: '版本',
      dataIndex: 'version',
      key: 'version',
      width: 80,
      render: (version: number) => <Text strong>v{version}</Text>
    },
    {
      title: '状态',
      key: 'status',
      width: 120,
      render: (_, session) => getStatusTag(session)
    },
    {
      title: 'AI Provider',
      dataIndex: 'aiProvider',
      key: 'aiProvider',
      width: 150,
      render: (provider: string) => {
        // 从provider ID获取provider名称的简化显示
        const providerName = provider.split('-').pop() || provider;
        return <Text>{providerName}</Text>;
      }
    },
    {
      title: '创建时间',
      dataIndex: 'createdAt',
      key: 'createdAt',
      width: 150,
      render: (date: string) => new Date(date).toLocaleString()
    },
    {
      title: '替换原因',
      dataIndex: 'replacementReason',
      key: 'replacementReason',
      ellipsis: true,
      render: (reason: string) => reason ? (
        <Tooltip title={reason}>
          <Text type="secondary">{reason}</Text>
        </Tooltip>
      ) : '-'
    },
    {
      title: '操作',
      key: 'actions',
      width: 150,
      render: (_, session) => (
        <Space size="small">
          <Tooltip title="查看详情">
            <Button 
              type="text" 
              icon={<EyeOutlined />} 
              size="small"
              onClick={() => {
                // TODO: 实现查看详情功能
                message.info('查看详情功能开发中');
              }}
            />
          </Tooltip>
          
          {!session.isActive && session.status === 'completed' && (
            <Popconfirm
              title="确认回滚"
              description={`确定要回滚到版本 ${session.version} 吗？这将替换当前的分解结果。`}
              onConfirm={() => handleRollback(session.id)}
              okText="确认回滚"
              cancelText="取消"
            >
              <Tooltip title="回滚到此版本">
                <Button
                  type="text"
                  icon={<RollbackOutlined />}
                  size="small"
                  disabled={loading}
                />
              </Tooltip>
            </Popconfirm>
          )}
        </Space>
      )
    }
  ];

  return (
    <Modal
      title={
        <Space>
          <HistoryOutlined />
          <span>分解历史 - {goalName}</span>
        </Space>
      }
      open={visible}
      onCancel={onClose}
      width={900}
      footer={[
        <Button key="close" onClick={onClose}>
          关闭
        </Button>
      ]}
    >
      <div style={{ marginBottom: 16 }}>
        {history && (
          <Space>
            <Text>总版本数: <Text strong>{history.totalVersions}</Text></Text>
            <Text>当前活跃版本: {
              history.activeSession ? (
                <Text strong>v{history.activeSession.version}</Text>
              ) : (
                <Text type="secondary">无</Text>
              )
            }</Text>
          </Space>
        )}
      </div>

      <Table
        columns={columns}
        dataSource={history?.sessions || []}
        rowKey="id"
        loading={loading}
        pagination={{
          pageSize: 10,
          showSizeChanger: false,
          showQuickJumper: true,
          showTotal: (total) => `共 ${total} 个版本`
        }}
        scroll={{ y: 400 }}
        size="small"
      />

      <div style={{ marginTop: 16, padding: 12, background: '#f5f5f5', borderRadius: 6 }}>
        <Text type="secondary" style={{ fontSize: 12 }}>
          <strong>说明：</strong>
          <br />
          • 绿色标签表示当前活跃的分解版本
          <br />
          • 可以回滚到任何已完成的历史版本
          <br />
          • 回滚操作会替换当前的分解结果，请谨慎操作
        </Text>
      </div>
    </Modal>
  );
};

export default DecompositionHistoryModal;
