import React, { useState, useEffect } from 'react';
import { <PERSON><PERSON>, Steps, Card, Button, Space, Typography, Progress, Alert, Spin, Result } from 'antd';
import { RobotOutlined, CheckCircleOutlined, LoadingOutlined, ExclamationCircleOutlined } from '@ant-design/icons';
import { DatabaseAPI } from '../services/api';
import { AIDecompositionConfig } from './AIDecompositionConfig';
import DecompositionConfirmation from './DecompositionConfirmation';

const { Text, Title } = Typography;
const { Step } = Steps;

interface AIDecompositionWizardProps {
  visible: boolean;
  goalId: string;
  goalName: string;
  goalDescription: string;
  whyPower: string;
  aiConfig: AIDecompositionConfig;
  onClose: () => void;
  onSuccess: (result: any) => void;
  onError: (error: string) => void;
  onNavigateToResult?: (goalId: string) => void;
  // 重新分解相关
  isRedecomposition?: boolean;
  replacementReason?: string;
  replaceExisting?: boolean;
}

type WizardStep = 'preparing' | 'analyzing' | 'decomposing' | 'completed' | 'confirming' | 'error';

interface DecompositionProgress {
  step: WizardStep;
  progress: number;
  message: string;
  sessionId?: string;
  result?: any;
  error?: string;
}

const AIDecompositionWizard: React.FC<AIDecompositionWizardProps> = ({
  visible,
  goalId,
  goalName,
  goalDescription,
  whyPower,
  aiConfig,
  onClose,
  onSuccess,
  onError,
  onNavigateToResult,
  isRedecomposition = false,
  replacementReason,
  replaceExisting = true
}) => {
  const [progress, setProgress] = useState<DecompositionProgress>({
    step: 'preparing',
    progress: 0,
    message: '准备开始AI分解...'
  });

  const [showConfirmation, setShowConfirmation] = useState(false);
  const [confirmationLoading, setConfirmationLoading] = useState(false);

  // 重置状态
  useEffect(() => {
    if (visible) {
      setProgress({
        step: 'preparing',
        progress: 0,
        message: '准备开始AI分解...'
      });
    }
  }, [visible]);

  // 开始分解流程
  useEffect(() => {
    if (visible && progress.step === 'preparing') {
      startDecomposition();
    }
  }, [visible, progress.step]);

  const startDecomposition = async () => {
    try {
      // 防止重复执行
      if (progress.step === 'analyzing' || progress.step === 'decomposing') {
        console.warn('AI分解已在进行中，忽略重复请求');
        return;
      }

      // 步骤1: 创建分解会话
      setProgress({
        step: 'analyzing',
        progress: 20,
        message: '正在创建AI分解会话...'
      });

      console.log('AI分解请求配置:', {
        goalName,
        aiProvider: aiConfig.aiProvider,
        preferences: aiConfig.preferences,
        isRedecomposition,
        replacementReason
      });

      // 根据是否为重新分解选择不同的API
      const sessionResult = isRedecomposition ?
        await DatabaseAPI.redecomposeGoal({
          goalId,
          goalName,
          goalDescription,
          whyPower,
          aiProvider: aiConfig.aiProvider!,
          preferences: aiConfig.preferences,
          context: aiConfig.context,
          replacementReason,
          replaceExisting
        }) :
        await DatabaseAPI.startAIDecomposition({
          goalId,
          goalName,
          goalDescription,
          whyPower,
          aiProvider: aiConfig.aiProvider!,
          preferences: aiConfig.preferences,
          context: aiConfig.context
        });

      if (!sessionResult.success) {
        throw new Error(sessionResult.error || '创建分解会话失败');
      }

      const sessionId = sessionResult.sessionId!;

      // 步骤2: 执行AI分解
      setProgress({
        step: 'decomposing',
        progress: 50,
        message: '正在进行AI智能分解，请稍候...',
        sessionId
      });

      const decompositionResult = await DatabaseAPI.performAIDecomposition(sessionId);

      if (!decompositionResult.success) {
        throw new Error(decompositionResult.error || 'AI分解失败');
      }

      // 步骤3: 获取分解结果
      setProgress({
        step: 'decomposing',
        progress: 80,
        message: '正在获取分解结果...',
        sessionId
      });

      const resultResponse = await DatabaseAPI.getAIDecompositionResult(sessionId);

      if (!resultResponse.success) {
        throw new Error(resultResponse.error || '获取分解结果失败');
      }

      // 完成
      setProgress({
        step: 'completed',
        progress: 100,
        message: 'AI分解完成！',
        sessionId,
        result: resultResponse.result
      });

      // 自动显示确认对话框
      console.log('AI分解完成，准备显示确认对话框');
      setTimeout(() => {
        console.log('显示确认对话框');
        setShowConfirmation(true);
      }, 1000);

    } catch (error) {
      console.error('AI分解失败:', error);
      setProgress({
        step: 'error',
        progress: 0,
        message: '分解失败',
        error: (error as Error).message
      });
      onError((error as Error).message);
    }
  };

  const handleRetry = () => {
    setProgress({
      step: 'preparing',
      progress: 0,
      message: '准备重新开始AI分解...'
    });
  };

  const handleComplete = () => {
    if (progress.result) {
      onSuccess(progress.result);
    }
    onClose();
  };

  const handleViewResult = () => {
    if (onNavigateToResult) {
      onNavigateToResult(goalId);
    }
    onClose();
  };

  const handleConfirmation = async (confirmed: boolean, modifications?: any) => {
    if (confirmed) {
      // 分解结果已在DecompositionConfirmation中保存
      setShowConfirmation(false);
      onSuccess(progress.result);

      if (onNavigateToResult) {
        onNavigateToResult(goalId);
      }
      onClose();
    } else {
      // 用户拒绝分解结果
      setShowConfirmation(false);
      setProgress({
        step: 'preparing',
        progress: 0,
        message: '准备重新开始AI分解...'
      });
    }
  };

  const handleEditResult = () => {
    setShowConfirmation(false);
    if (onNavigateToResult) {
      onNavigateToResult(goalId);
    }
    onClose();
  };

  const getStepStatus = (step: WizardStep) => {
    const currentStepIndex = ['preparing', 'analyzing', 'decomposing', 'completed'].indexOf(progress.step);
    const stepIndex = ['preparing', 'analyzing', 'decomposing', 'completed'].indexOf(step);
    
    if (progress.step === 'error') {
      return stepIndex <= currentStepIndex ? 'error' : 'wait';
    }
    
    if (stepIndex < currentStepIndex) return 'finish';
    if (stepIndex === currentStepIndex) return 'process';
    return 'wait';
  };

  const renderStepContent = () => {
    switch (progress.step) {
      case 'preparing':
      case 'analyzing':
        return (
          <Card>
            <div style={{ textAlign: 'center', padding: '20px 0' }}>
              <Spin size="large" />
              <div style={{ marginTop: 16 }}>
                <Text>{progress.message}</Text>
              </div>
            </div>
          </Card>
        );

      case 'decomposing':
        return (
          <Card>
            <div style={{ textAlign: 'center', padding: '20px 0' }}>
              <div style={{ marginBottom: 20 }}>
                <RobotOutlined style={{ fontSize: 48, color: '#1890ff' }} />
              </div>
              <Title level={4}>AI正在智能分解您的目标</Title>
              <Text type="secondary">
                AI正在运用第一性原理，将您的目标分解为可执行的具体任务...
              </Text>
              <div style={{ margin: '20px 0' }}>
                <Progress 
                  percent={progress.progress} 
                  status="active"
                  strokeColor={{
                    '0%': '#108ee9',
                    '100%': '#87d068',
                  }}
                />
              </div>
              <Text>{progress.message}</Text>
            </div>
          </Card>
        );

      case 'completed':
        return (
          <Card>
            <Result
              status="success"
              title="AI分解完成！"
              subTitle={`成功将目标"${goalName}"分解为具体的执行计划`}
              extra={[
                <Button type="primary" key="confirm" onClick={() => setShowConfirmation(true)}>
                  确认分解结果
                </Button>,
                <Button key="view" onClick={handleViewResult}>
                  查看详情
                </Button>,
                <Button key="close" onClick={onClose}>
                  稍后处理
                </Button>
              ]}
            />
            {progress.result && (
              <div style={{ marginTop: 16, textAlign: 'center' }}>
                <Text type="secondary">
                  共生成 {progress.result.subGoals?.length || 0} 个子目标，
                  预计总时长 {progress.result.estimatedTotalTime || 0} 小时
                </Text>
              </div>
            )}
          </Card>
        );

      case 'error':
        return (
          <Card>
            <Result
              status="error"
              title="分解失败"
              subTitle={progress.error}
              extra={[
                <Button type="primary" key="retry" onClick={handleRetry}>
                  重试
                </Button>,
                <Button key="close" onClick={onClose}>
                  关闭
                </Button>
              ]}
            />
          </Card>
        );

      default:
        return null;
    }
  };

  return (
    <Modal
      title={
        <Space>
          <RobotOutlined />
          <span>{isRedecomposition ? 'AI重新分解向导' : 'AI智能分解向导'}</span>
        </Space>
      }
      open={visible}
      onCancel={onClose}
      footer={null}
      width={600}
      closable={progress.step === 'completed' || progress.step === 'error'}
      maskClosable={false}
    >
      <div style={{ marginBottom: 24 }}>
        <Alert
          message={`${isRedecomposition ? '正在重新分解' : '正在分解'}目标: ${goalName}`}
          description={
            <div>
              <div>{goalDescription}</div>
              {isRedecomposition && replacementReason && (
                <div style={{ marginTop: 8 }}>
                  <Text type="secondary">重新分解原因: {replacementReason}</Text>
                </div>
              )}
            </div>
          }
          type={isRedecomposition ? "warning" : "info"}
          showIcon
          style={{ marginBottom: 16 }}
        />

        <Steps current={['preparing', 'analyzing', 'decomposing', 'completed'].indexOf(progress.step)}>
          <Step 
            title="准备分析" 
            status={getStepStatus('preparing')}
            icon={progress.step === 'preparing' ? <LoadingOutlined /> : undefined}
          />
          <Step 
            title="AI分析" 
            status={getStepStatus('analyzing')}
            icon={progress.step === 'analyzing' ? <LoadingOutlined /> : undefined}
          />
          <Step 
            title="智能分解" 
            status={getStepStatus('decomposing')}
            icon={progress.step === 'decomposing' ? <LoadingOutlined /> : undefined}
          />
          <Step 
            title="完成" 
            status={getStepStatus('completed')}
            icon={progress.step === 'completed' ? <CheckCircleOutlined /> : undefined}
          />
        </Steps>
      </div>

      {renderStepContent()}

      {/* 分解结果确认对话框 */}
      {showConfirmation && progress.result && progress.sessionId && (
        <DecompositionConfirmation
          visible={showConfirmation}
          goalId={goalId}
          goalName={goalName}
          sessionId={progress.sessionId}
          originalGoal={{
            name: goalName,
            description: goalDescription,
            whyPower: whyPower
          }}
          decompositionResult={progress.result}
          onConfirm={handleConfirmation}
          onCancel={() => setShowConfirmation(false)}
          onEdit={handleEditResult}
          loading={confirmationLoading}
        />
      )}
    </Modal>
  );
};

export default AIDecompositionWizard;
