import React, { useState, useEffect, useRef } from 'react';
import { Card, Statistic, Row, Col, Progress, Button, Collapse } from 'antd';
import { MonitorOutlined, ReloadOutlined } from '@ant-design/icons';

interface PerformanceMetrics {
  fps: number;
  memoryUsage: number;
  renderTime: number;
  domNodes: number;
  networkRequests: number;
  cacheHitRate: number;
}

const PerformanceMonitor: React.FC = () => {
  const [metrics, setMetrics] = useState<PerformanceMetrics>({
    fps: 0,
    memoryUsage: 0,
    renderTime: 0,
    domNodes: 0,
    networkRequests: 0,
    cacheHitRate: 0,
  });
  
  const [isVisible, setIsVisible] = useState(false);
  const frameCount = useRef(0);
  const lastTime = useRef(performance.now());
  const animationFrame = useRef<number>();

  // FPS计算
  const calculateFPS = () => {
    frameCount.current++;
    const currentTime = performance.now();
    
    if (currentTime - lastTime.current >= 1000) {
      const fps = Math.round((frameCount.current * 1000) / (currentTime - lastTime.current));
      setMetrics(prev => ({ ...prev, fps }));
      frameCount.current = 0;
      lastTime.current = currentTime;
    }
    
    animationFrame.current = requestAnimationFrame(calculateFPS);
  };

  // 内存使用情况
  const getMemoryUsage = () => {
    if ('memory' in performance) {
      const memory = (performance as any).memory;
      return Math.round(memory.usedJSHeapSize / 1024 / 1024);
    }
    return 0;
  };

  // DOM节点数量
  const getDOMNodeCount = () => {
    return document.querySelectorAll('*').length;
  };

  // 渲染时间
  const getRenderTime = () => {
    const navigation = performance.getEntriesByType('navigation')[0] as PerformanceNavigationTiming;
    if (navigation) {
      return Math.round(navigation.loadEventEnd - navigation.fetchStart);
    }
    return 0;
  };

  // 网络请求数量
  const getNetworkRequests = () => {
    return performance.getEntriesByType('resource').length;
  };

  // 更新所有指标
  const updateMetrics = () => {
    setMetrics(prev => ({
      ...prev,
      memoryUsage: getMemoryUsage(),
      domNodes: getDOMNodeCount(),
      renderTime: getRenderTime(),
      networkRequests: getNetworkRequests(),
      cacheHitRate: Math.random() * 100, // 模拟缓存命中率
    }));
  };

  // 清理性能数据
  const clearPerformanceData = () => {
    if (performance.clearResourceTimings) {
      performance.clearResourceTimings();
    }
    if (performance.clearMarks) {
      performance.clearMarks();
    }
    if (performance.clearMeasures) {
      performance.clearMeasures();
    }
    updateMetrics();
  };

  useEffect(() => {
    if (isVisible) {
      // 开始FPS监控
      animationFrame.current = requestAnimationFrame(calculateFPS);
      
      // 定期更新其他指标
      const interval = setInterval(updateMetrics, 2000);
      
      return () => {
        if (animationFrame.current) {
          cancelAnimationFrame(animationFrame.current);
        }
        clearInterval(interval);
      };
    }
  }, [isVisible]);

  // 获取性能等级颜色
  const getPerformanceColor = (value: number, thresholds: [number, number]) => {
    if (value >= thresholds[1]) return '#52c41a'; // 绿色 - 好
    if (value >= thresholds[0]) return '#faad14'; // 黄色 - 一般
    return '#f5222d'; // 红色 - 差
  };

  const getFPSColor = (fps: number) => getPerformanceColor(fps, [30, 50]);
  const getMemoryColor = (memory: number) => {
    // 内存使用越少越好
    if (memory <= 50) return '#52c41a';
    if (memory <= 100) return '#faad14';
    return '#f5222d';
  };

  if (!isVisible) {
    return (
      <div
        style={{
          position: 'fixed',
          top: 20,
          right: 20,
          zIndex: 9999,
        }}
      >
        <Button
          type="primary"
          icon={<MonitorOutlined />}
          onClick={() => setIsVisible(true)}
          size="small"
        >
          性能监控
        </Button>
      </div>
    );
  }

  return (
    <div
      style={{
        position: 'fixed',
        top: 20,
        right: 20,
        width: 400,
        zIndex: 9999,
        maxHeight: '80vh',
        overflow: 'auto',
      }}
    >
      <Card
        title="性能监控"
        size="small"
        extra={
          <div>
            <Button
              size="small"
              icon={<ReloadOutlined />}
              onClick={clearPerformanceData}
              style={{ marginRight: 8 }}
            >
              清理
            </Button>
            <Button
              size="small"
              onClick={() => setIsVisible(false)}
            >
              隐藏
            </Button>
          </div>
        }
        style={{
          background: 'rgba(0, 0, 0, 0.8)',
          color: 'white',
          backdropFilter: 'blur(10px)',
        }}
      >
        <Row gutter={[16, 16]}>
          <Col span={12}>
            <Statistic
              title="FPS"
              value={metrics.fps}
              valueStyle={{ color: getFPSColor(metrics.fps), fontSize: '18px' }}
            />
          </Col>
          <Col span={12}>
            <Statistic
              title="内存使用"
              value={metrics.memoryUsage}
              suffix="MB"
              valueStyle={{ color: getMemoryColor(metrics.memoryUsage), fontSize: '18px' }}
            />
          </Col>
          <Col span={12}>
            <Statistic
              title="DOM节点"
              value={metrics.domNodes}
              valueStyle={{ fontSize: '18px' }}
            />
          </Col>
          <Col span={12}>
            <Statistic
              title="网络请求"
              value={metrics.networkRequests}
              valueStyle={{ fontSize: '18px' }}
            />
          </Col>
        </Row>

        <div style={{ marginTop: 16 }}>
          <div style={{ marginBottom: 8 }}>
            <span style={{ color: 'white' }}>缓存命中率</span>
          </div>
          <Progress
            percent={Math.round(metrics.cacheHitRate)}
            strokeColor={{
              '0%': '#f5222d',
              '50%': '#faad14',
              '100%': '#52c41a',
            }}
            size="small"
          />
        </div>

        <Collapse
          size="small"
          style={{ marginTop: 16 }}
          items={[
            {
              key: 'details',
              label: '详细信息',
              children: (
                <div style={{ fontSize: '12px', color: '#ccc' }}>
                  <p>渲染时间: {metrics.renderTime}ms</p>
                  <p>页面加载: {getRenderTime()}ms</p>
                  <p>
                    性能建议:
                    {metrics.fps < 30 && <div>• FPS过低，检查动画和重绘</div>}
                    {metrics.memoryUsage > 100 && <div>• 内存使用过高，检查内存泄漏</div>}
                    {metrics.domNodes > 5000 && <div>• DOM节点过多，考虑虚拟化</div>}
                  </p>
                </div>
              ),
            },
          ]}
        />
      </Card>
    </div>
  );
};

// 性能监控Hook
export const usePerformanceMonitor = () => {
  const [metrics, setMetrics] = useState({
    renderCount: 0,
    lastRenderTime: 0,
  });

  const startTime = useRef(performance.now());

  useEffect(() => {
    const endTime = performance.now();
    const renderTime = endTime - startTime.current;
    
    setMetrics(prev => ({
      renderCount: prev.renderCount + 1,
      lastRenderTime: renderTime,
    }));
    
    startTime.current = endTime;
  });

  return metrics;
};

// 性能标记工具
export const performanceMark = {
  start: (name: string) => {
    if (performance.mark) {
      performance.mark(`${name}-start`);
    }
  },
  
  end: (name: string) => {
    if (performance.mark && performance.measure) {
      performance.mark(`${name}-end`);
      performance.measure(name, `${name}-start`, `${name}-end`);
    }
  },
  
  get: (name: string) => {
    if (performance.getEntriesByName) {
      const measures = performance.getEntriesByName(name, 'measure');
      return measures.length > 0 ? measures[measures.length - 1].duration : 0;
    }
    return 0;
  },
};

export default PerformanceMonitor;
