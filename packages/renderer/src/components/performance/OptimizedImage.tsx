import React, { useState, useRef, useEffect, useCallback } from 'react';

interface OptimizedImageProps {
  src: string;
  alt: string;
  width?: number;
  height?: number;
  className?: string;
  style?: React.CSSProperties;
  placeholder?: string;
  lazy?: boolean;
  webpSrc?: string;
  sizes?: string;
  srcSet?: string;
  onLoad?: () => void;
  onError?: () => void;
}

export const OptimizedImage: React.FC<OptimizedImageProps> = ({
  src,
  alt,
  width,
  height,
  className = '',
  style = {},
  placeholder,
  lazy = true,
  webpSrc,
  sizes,
  srcSet,
  onLoad,
  onError,
}) => {
  const [isLoaded, setIsLoaded] = useState(false);
  const [isInView, setIsInView] = useState(!lazy);
  const [hasError, setHasError] = useState(false);
  const imgRef = useRef<HTMLImageElement>(null);
  const observerRef = useRef<IntersectionObserver | null>(null);

  // 检查WebP支持
  const [supportsWebP, setSupportsWebP] = useState(false);

  useEffect(() => {
    const checkWebPSupport = () => {
      const canvas = document.createElement('canvas');
      canvas.width = 1;
      canvas.height = 1;
      const ctx = canvas.getContext('2d');
      if (ctx) {
        const dataURL = canvas.toDataURL('image/webp');
        setSupportsWebP(dataURL.indexOf('data:image/webp') === 0);
      }
    };

    checkWebPSupport();
  }, []);

  // 懒加载逻辑
  useEffect(() => {
    if (!lazy || !imgRef.current) return;

    observerRef.current = new IntersectionObserver(
      (entries) => {
        entries.forEach((entry) => {
          if (entry.isIntersecting) {
            setIsInView(true);
            observerRef.current?.disconnect();
          }
        });
      },
      {
        rootMargin: '50px', // 提前50px开始加载
        threshold: 0.1,
      }
    );

    observerRef.current.observe(imgRef.current);

    return () => {
      observerRef.current?.disconnect();
    };
  }, [lazy]);

  // 图片加载处理
  const handleLoad = useCallback(() => {
    setIsLoaded(true);
    onLoad?.();
  }, [onLoad]);

  const handleError = useCallback(() => {
    setHasError(true);
    onError?.();
  }, [onError]);

  // 获取最优图片源
  const getOptimizedSrc = () => {
    if (hasError) return placeholder || src;
    if (!isInView) return placeholder || '';
    
    // 优先使用WebP格式
    if (supportsWebP && webpSrc) {
      return webpSrc;
    }
    
    return src;
  };

  const imageStyle: React.CSSProperties = {
    ...style,
    width,
    height,
    opacity: isLoaded ? 1 : 0,
    transition: 'opacity 0.3s cubic-bezier(0.4, 0, 0.2, 1)',
    // 性能优化
    transform: 'translateZ(0)',
    willChange: 'opacity',
    contain: 'layout',
  };

  const placeholderStyle: React.CSSProperties = {
    position: 'absolute',
    top: 0,
    left: 0,
    width: '100%',
    height: '100%',
    background: 'linear-gradient(90deg, transparent, rgba(255,255,255,0.2), transparent)',
    backgroundSize: '200px 100%',
    animation: isLoaded ? 'none' : 'shimmer 1.5s infinite',
    opacity: isLoaded ? 0 : 1,
    transition: 'opacity 0.3s cubic-bezier(0.4, 0, 0.2, 1)',
    // 性能优化
    transform: 'translateZ(0)',
    willChange: 'opacity',
  };

  const containerStyle: React.CSSProperties = {
    position: 'relative',
    display: 'inline-block',
    overflow: 'hidden',
    width,
    height,
    // 性能优化
    contain: 'layout style paint',
  };

  return (
    <div style={containerStyle} className={`optimized-image-container ${className}`}>
      {/* 占位符 */}
      {!isLoaded && (
        <div style={placeholderStyle} className="image-placeholder" />
      )}
      
      {/* 实际图片 */}
      <img
        ref={imgRef}
        src={getOptimizedSrc()}
        alt={alt}
        style={imageStyle}
        className="optimized-image"
        onLoad={handleLoad}
        onError={handleError}
        loading={lazy ? 'lazy' : 'eager'}
        decoding="async"
        sizes={sizes}
        srcSet={srcSet}
      />
    </div>
  );
};

// 响应式图片组件
interface ResponsiveImageProps extends OptimizedImageProps {
  breakpoints?: {
    mobile: string;
    tablet: string;
    desktop: string;
  };
}

export const ResponsiveImage: React.FC<ResponsiveImageProps> = ({
  breakpoints,
  ...props
}) => {
  const [currentSrc, setCurrentSrc] = useState(props.src);

  useEffect(() => {
    if (!breakpoints) return;

    const updateSrc = () => {
      const width = window.innerWidth;
      
      if (width < 768) {
        setCurrentSrc(breakpoints.mobile);
      } else if (width < 1024) {
        setCurrentSrc(breakpoints.tablet);
      } else {
        setCurrentSrc(breakpoints.desktop);
      }
    };

    updateSrc();
    window.addEventListener('resize', updateSrc);

    return () => {
      window.removeEventListener('resize', updateSrc);
    };
  }, [breakpoints]);

  return <OptimizedImage {...props} src={currentSrc} />;
};

// 图片预加载Hook
export const useImagePreloader = (urls: string[]) => {
  const [loadedImages, setLoadedImages] = useState<Set<string>>(new Set());

  useEffect(() => {
    const preloadImage = (url: string) => {
      return new Promise<void>((resolve, reject) => {
        const img = new Image();
        img.onload = () => {
          setLoadedImages(prev => new Set(prev).add(url));
          resolve();
        };
        img.onerror = reject;
        img.src = url;
      });
    };

    // 使用requestIdleCallback在空闲时预加载
    if ('requestIdleCallback' in window) {
      window.requestIdleCallback(() => {
        urls.forEach(url => {
          if (!loadedImages.has(url)) {
            preloadImage(url).catch(console.error);
          }
        });
      });
    } else {
      // 降级方案
      setTimeout(() => {
        urls.forEach(url => {
          if (!loadedImages.has(url)) {
            preloadImage(url).catch(console.error);
          }
        });
      }, 100);
    }
  }, [urls, loadedImages]);

  return loadedImages;
};

// 图片压缩工具函数
export const compressImage = (
  file: File,
  maxWidth: number = 1920,
  maxHeight: number = 1080,
  quality: number = 0.8
): Promise<Blob> => {
  return new Promise((resolve, reject) => {
    const canvas = document.createElement('canvas');
    const ctx = canvas.getContext('2d');
    const img = new Image();

    img.onload = () => {
      // 计算新尺寸
      let { width, height } = img;
      
      if (width > height) {
        if (width > maxWidth) {
          height = (height * maxWidth) / width;
          width = maxWidth;
        }
      } else {
        if (height > maxHeight) {
          width = (width * maxHeight) / height;
          height = maxHeight;
        }
      }

      canvas.width = width;
      canvas.height = height;

      // 绘制压缩后的图片
      ctx?.drawImage(img, 0, 0, width, height);

      canvas.toBlob(
        (blob) => {
          if (blob) {
            resolve(blob);
          } else {
            reject(new Error('图片压缩失败'));
          }
        },
        'image/jpeg',
        quality
      );
    };

    img.onerror = () => reject(new Error('图片加载失败'));
    img.src = URL.createObjectURL(file);
  });
};
