/* React 组件性能优化 - 减少不必要的重新渲染 */

import React, { memo, useMemo, useCallback } from 'react';
import { Card, Button, List } from 'antd';

// 高性能卡片组件 - 避免不必要的重渲染
export const PerformanceCard = memo<{
  children: React.ReactNode;
  title?: string;
  className?: string;
  style?: React.CSSProperties;
  loading?: boolean;
}>(({ children, title, className = '', style = {}, loading = false }) => {
  const cardClassName = useMemo(() => 
    `ultra-fast-card force-gpu ${className}`.trim(), 
    [className]
  );

  const cardStyle = useMemo(() => ({
    willChange: 'contents',
    contain: 'layout style paint',
    ...style
  }), [style]);

  return (
    <Card 
      className={cardClassName}
      style={cardStyle}
      title={title}
      loading={loading}
    >
      {children}
    </Card>
  );
});

PerformanceCard.displayName = 'PerformanceCard';

// 高性能按钮组件
export const PerformanceButton = memo<{
  children: React.ReactNode;
  onClick?: () => void;
  type?: 'primary' | 'default' | 'ghost' | 'link' | 'text' | 'dashed';
  loading?: boolean;
  disabled?: boolean;
  icon?: React.ReactNode;
  size?: 'large' | 'middle' | 'small';
  className?: string;
  style?: React.CSSProperties;
}>(({ 
  children, 
  onClick, 
  type = 'default', 
  loading = false, 
  disabled = false, 
  icon, 
  size = 'middle',
  className = '',
  style = {}
}) => {
  const handleClick = useCallback(() => {
    if (!loading && !disabled && onClick) {
      onClick();
    }
  }, [loading, disabled, onClick]);

  const buttonClassName = useMemo(() => 
    `fast-button force-gpu ${className}`.trim(), 
    [className]
  );

  const buttonStyle = useMemo(() => ({
    contain: 'layout style',
    ...style
  }), [style]);

  return (
    <Button
      className={buttonClassName}
      style={buttonStyle}
      type={type}
      loading={loading}
      disabled={disabled}
      icon={icon}
      size={size}
      onClick={handleClick}
    >
      {children}
    </Button>
  );
});

PerformanceButton.displayName = 'PerformanceButton';

// 高性能列表组件 - 支持虚拟滚动概念
export const PerformanceList = memo<{
  dataSource: any[];
  renderItem: (item: any, index: number) => React.ReactNode;
  loading?: boolean;
  className?: string;
  style?: React.CSSProperties;
  grid?: any;
}>(({ dataSource, renderItem, loading = false, className = '', style = {}, grid }) => {
  const listClassName = useMemo(() => 
    `virtual-scroll fast-grid ${className}`.trim(), 
    [className]
  );

  const listStyle = useMemo(() => ({
    contain: 'layout style paint',
    contentVisibility: 'auto',
    ...style
  }), [style]);

  // 使用 useMemo 缓存渲染项，避免每次都重新创建
  const memoizedItems = useMemo(() => 
    dataSource.map((item, index) => (
      <div key={item.id || index} className="fast-list-item">
        {renderItem(item, index)}
      </div>
    )), 
    [dataSource, renderItem]
  );

  return (
    <List
      className={listClassName}
      style={listStyle}
      loading={loading}
      grid={grid}
      dataSource={dataSource}
      renderItem={renderItem}
    />
  );
});

PerformanceList.displayName = 'PerformanceList';

// 高性能文本组件
export const PerformanceText = memo<{
  children: React.ReactNode;
  type?: 'primary' | 'secondary' | 'success' | 'warning' | 'danger';
  className?: string;
  style?: React.CSSProperties;
}>(({ children, type, className = '', style = {} }) => {
  const textClassName = useMemo(() => 
    `fast-text ${className}`.trim(), 
    [className]
  );

  const textStyle = useMemo(() => ({
    contain: 'layout',
    ...style
  }), [style]);

  return (
    <span className={textClassName} style={textStyle} data-type={type}>
      {children}
    </span>
  );
});

PerformanceText.displayName = 'PerformanceText';

// 高性能容器组件
export const PerformanceContainer = memo<{
  children: React.ReactNode;
  className?: string;
  style?: React.CSSProperties;
  lazy?: boolean;
}>(({ children, className = '', style = {}, lazy = true }) => {
  const containerClassName = useMemo(() => {
    const baseClasses = 'stable-layout force-gpu';
    const lazyClass = lazy ? 'lazy-content' : '';
    return `${baseClasses} ${lazyClass} ${className}`.trim();
  }, [className, lazy]);

  const containerStyle = useMemo(() => ({
    contain: 'layout style paint',
    willChange: 'contents',
    width: '100%',
    minHeight: '100%',
    overflow: 'visible', // 允许内容溢出，由父容器处理滚动
    ...style
  }), [style]);

  return (
    <div className={containerClassName} style={containerStyle}>
      {children}
    </div>
  );
});

PerformanceContainer.displayName = 'PerformanceContainer';

// React.memo 的深度比较函数，用于复杂对象
export const deepMemoCompare = (prevProps: any, nextProps: any) => {
  // 简单的深度比较，适用于大多数场景
  const keys1 = Object.keys(prevProps);
  const keys2 = Object.keys(nextProps);
  
  if (keys1.length !== keys2.length) {
    return false;
  }
  
  for (let key of keys1) {
    if (prevProps[key] !== nextProps[key]) {
      // 如果是函数，跳过比较（假设函数是稳定的）
      if (typeof prevProps[key] === 'function' && typeof nextProps[key] === 'function') {
        continue;
      }
      return false;
    }
  }
  
  return true;
};

// HOC 用于自动应用性能优化
export const withPerformanceOptimization = <P extends object>(
  Component: React.ComponentType<P>
) => {
  const PerformanceWrappedComponent = memo((props: P) => {
    return (
      <div className="content-optimized">
        <Component {...props} />
      </div>
    );
  }, deepMemoCompare);

  PerformanceWrappedComponent.displayName = `withPerformanceOptimization(${Component.displayName || Component.name})`;
  
  return PerformanceWrappedComponent;
};

// 性能监控 Hook
export const usePerformanceMonitor = (componentName: string) => {
  const startTime = useMemo(() => performance.now(), []);
  
  React.useEffect(() => {
    const endTime = performance.now();
    const renderTime = endTime - startTime;
    
    if (renderTime > 16) { // 超过一帧的时间
      console.warn(`Performance Warning: ${componentName} took ${renderTime.toFixed(2)}ms to render`);
    }
  });
  
  return { startTime };
};