import React, { useState, useMemo, useCallback, memo } from 'react';
import { Tree, Card, Tag, Space, Typography, Tooltip, Button } from 'antd';
import { 
  FlagOutlined, 
  TrophyOutlined, 
  CheckSquareOutlined, 
  ClockCircleOutlined,
  StarOutlined,
  EditOutlined,
  DeleteOutlined
} from '@ant-design/icons';
import type { DataNode } from 'antd/es/tree';
import { FixedSizeList as List } from 'react-window';

const { Text, Title } = Typography;

interface DecompositionResult {
  subGoals: SubGoal[];
  estimatedTotalTime: number;
  complexity: 'low' | 'medium' | 'high';
  confidence: number;
  suggestions: string[];
  warnings: string[];
}

interface SubGoal {
  name: string;
  description: string;
  priority: 'high' | 'medium' | 'low';
  estimatedTime: number;
  confidence: number;
  reasoning: string;
  milestones: Milestone[];
}

interface Milestone {
  name: string;
  description: string;
  estimatedTime: number;
  confidence: number;
  dependencies: string[];
  tasks: Task[];
}

interface Task {
  title: string;
  description: string;
  estimatedTime: number;
  priority: 'high' | 'medium' | 'low';
  confidence: number;
  actionable: boolean;
  resources: string[];
}

interface OptimizedDecompositionTreeProps {
  result: DecompositionResult;
  goalName: string;
  onEditItem?: (type: 'subgoal' | 'milestone' | 'task', item: any, path: number[]) => void;
  onDeleteItem?: (type: 'subgoal' | 'milestone' | 'task', path: number[]) => void;
  editable?: boolean;
  virtualizeThreshold?: number;
}

// 优化的任务项组件
const TaskItem = memo<{
  task: Task;
  path: number[];
  editable: boolean;
  onEdit?: (type: 'task', item: any, path: number[]) => void;
  onDelete?: (type: 'task', path: number[]) => void;
}>(({ task, path, editable, onEdit, onDelete }) => {
  const getPriorityColor = useCallback((priority: string) => {
    switch (priority) {
      case 'high': return 'red';
      case 'medium': return 'orange';
      case 'low': return 'green';
      default: return 'default';
    }
  }, []);

  const getConfidenceColor = useCallback((confidence: number) => {
    if (confidence >= 0.8) return '#52c41a';
    if (confidence >= 0.6) return '#faad14';
    return '#ff4d4f';
  }, []);

  return (
    <div style={{ display: 'flex', alignItems: 'center', justifyContent: 'space-between', width: '100%' }}>
      <div style={{ flex: 1 }}>
        <Space>
          <CheckSquareOutlined style={{ color: '#1890ff' }} />
          <Text strong>{task.title}</Text>
          <Tag color={getPriorityColor(task.priority)} size="small">
            {task.priority}
          </Tag>
          <Tag color="blue" size="small">
            {task.estimatedTime}分钟
          </Tag>
          <Tooltip title={`信心度: ${(task.confidence * 100).toFixed(0)}%`}>
            <div style={{ 
              width: 40, 
              height: 6, 
              backgroundColor: '#f0f0f0', 
              borderRadius: 3,
              overflow: 'hidden'
            }}>
              <div 
                style={{ 
                  width: `${task.confidence * 100}%`, 
                  height: '100%', 
                  backgroundColor: getConfidenceColor(task.confidence)
                }} 
              />
            </div>
          </Tooltip>
        </Space>
        <div style={{ marginTop: 4, marginLeft: 20 }}>
          <Text type="secondary" style={{ fontSize: '12px' }}>
            {task.description}
          </Text>
        </div>
        {task.resources.length > 0 && (
          <div style={{ marginTop: 4, marginLeft: 20 }}>
            <Text type="secondary" style={{ fontSize: '11px' }}>
              资源: {task.resources.join(', ')}
            </Text>
          </div>
        )}
      </div>
      {editable && (
        <Space size="small">
          <Button 
            size="small" 
            type="text" 
            icon={<EditOutlined />}
            onClick={(e) => {
              e.stopPropagation();
              onEdit?.('task', task, path);
            }}
          />
          <Button 
            size="small" 
            type="text" 
            danger
            icon={<DeleteOutlined />}
            onClick={(e) => {
              e.stopPropagation();
              onDelete?.('task', path);
            }}
          />
        </Space>
      )}
    </div>
  );
});

// 虚拟化任务列表组件
const VirtualizedTaskList = memo<{
  tasks: Array<{ task: Task; path: number[] }>;
  editable: boolean;
  onEdit?: (type: 'task', item: any, path: number[]) => void;
  onDelete?: (type: 'task', path: number[]) => void;
}>(({ tasks, editable, onEdit, onDelete }) => {
  const Row = useCallback(({ index, style }: { index: number; style: React.CSSProperties }) => {
    const { task, path } = tasks[index];
    return (
      <div style={style}>
        <TaskItem
          task={task}
          path={path}
          editable={editable}
          onEdit={onEdit}
          onDelete={onDelete}
        />
      </div>
    );
  }, [tasks, editable, onEdit, onDelete]);

  if (tasks.length <= 10) {
    // 少量任务直接渲染
    return (
      <div>
        {tasks.map(({ task, path }, index) => (
          <TaskItem
            key={index}
            task={task}
            path={path}
            editable={editable}
            onEdit={onEdit}
            onDelete={onDelete}
          />
        ))}
      </div>
    );
  }

  // 大量任务使用虚拟化
  return (
    <List
      height={Math.min(tasks.length * 80, 400)}
      itemCount={tasks.length}
      itemSize={80}
    >
      {Row}
    </List>
  );
});

const OptimizedDecompositionTree: React.FC<OptimizedDecompositionTreeProps> = memo(({
  result,
  goalName,
  onEditItem,
  onDeleteItem,
  editable = false,
  virtualizeThreshold = 20
}) => {
  const [expandedKeys, setExpandedKeys] = useState<React.Key[]>([]);
  const [selectedKeys, setSelectedKeys] = useState<React.Key[]>([]);

  // 获取优先级颜色
  const getPriorityColor = useCallback((priority: string) => {
    switch (priority) {
      case 'high': return 'red';
      case 'medium': return 'orange';
      case 'low': return 'green';
      default: return 'default';
    }
  }, []);

  // 获取信心度颜色
  const getConfidenceColor = useCallback((confidence: number) => {
    if (confidence >= 0.8) return '#52c41a';
    if (confidence >= 0.6) return '#faad14';
    return '#ff4d4f';
  }, []);

  // 扁平化所有任务（用于虚拟化）
  const flatTasks = useMemo(() => {
    const tasks: Array<{ task: Task; path: number[] }> = [];
    result.subGoals.forEach((subGoal, subGoalIndex) => {
      subGoal.milestones.forEach((milestone, milestoneIndex) => {
        milestone.tasks.forEach((task, taskIndex) => {
          tasks.push({
            task,
            path: [subGoalIndex, milestoneIndex, taskIndex]
          });
        });
      });
    });
    return tasks;
  }, [result]);

  // 构建树形数据（优化版本）
  const treeData: DataNode[] = useMemo(() => {
    const totalTasks = flatTasks.length;
    const shouldVirtualize = totalTasks > virtualizeThreshold;

    if (shouldVirtualize) {
      // 大数据量时使用简化的树结构
      return [{
        key: 'virtual-root',
        title: (
          <div>
            <Title level={5}>所有任务 ({totalTasks})</Title>
            <VirtualizedTaskList
              tasks={flatTasks}
              editable={editable}
              onEdit={onEditItem}
              onDelete={onDeleteItem}
            />
          </div>
        ),
        children: []
      }];
    }

    // 正常数据量时使用完整的树结构
    const nodes: DataNode[] = [];

    result.subGoals.forEach((subGoal, subGoalIndex) => {
      const subGoalKey = `subgoal-${subGoalIndex}`;
      const milestoneNodes: DataNode[] = [];

      subGoal.milestones.forEach((milestone, milestoneIndex) => {
        const milestoneKey = `${subGoalKey}-milestone-${milestoneIndex}`;
        const taskNodes: DataNode[] = [];

        milestone.tasks.forEach((task, taskIndex) => {
          const taskKey = `${milestoneKey}-task-${taskIndex}`;
          taskNodes.push({
            key: taskKey,
            title: (
              <TaskItem
                task={task}
                path={[subGoalIndex, milestoneIndex, taskIndex]}
                editable={editable}
                onEdit={onEditItem}
                onDelete={onDeleteItem}
              />
            ),
            isLeaf: true
          });
        });

        milestoneNodes.push({
          key: milestoneKey,
          title: (
            <div style={{ display: 'flex', alignItems: 'center', justifyContent: 'space-between', width: '100%' }}>
              <div style={{ flex: 1 }}>
                <Space>
                  <TrophyOutlined style={{ color: '#faad14' }} />
                  <Text strong>{milestone.name}</Text>
                  <Tag color="orange" size="small">
                    {milestone.estimatedTime}小时
                  </Tag>
                  <Tooltip title={`信心度: ${(milestone.confidence * 100).toFixed(0)}%`}>
                    <div style={{ 
                      width: 40, 
                      height: 6, 
                      backgroundColor: '#f0f0f0', 
                      borderRadius: 3,
                      overflow: 'hidden'
                    }}>
                      <div 
                        style={{ 
                          width: `${milestone.confidence * 100}%`, 
                          height: '100%', 
                          backgroundColor: getConfidenceColor(milestone.confidence)
                        }} 
                      />
                    </div>
                  </Tooltip>
                </Space>
              </div>
            </div>
          ),
          children: taskNodes
        });
      });

      nodes.push({
        key: subGoalKey,
        title: (
          <div style={{ display: 'flex', alignItems: 'center', justifyContent: 'space-between', width: '100%' }}>
            <div style={{ flex: 1 }}>
              <Space>
                <FlagOutlined style={{ color: '#52c41a' }} />
                <Text strong style={{ fontSize: '14px' }}>{subGoal.name}</Text>
                <Tag color={getPriorityColor(subGoal.priority)}>
                  {subGoal.priority}
                </Tag>
                <Tag color="green">
                  {subGoal.estimatedTime}小时
                </Tag>
                <Tooltip title={`信心度: ${(subGoal.confidence * 100).toFixed(0)}%`}>
                  <div style={{ 
                    width: 50, 
                    height: 8, 
                    backgroundColor: '#f0f0f0', 
                    borderRadius: 4,
                    overflow: 'hidden'
                  }}>
                    <div 
                      style={{ 
                        width: `${subGoal.confidence * 100}%`, 
                        height: '100%', 
                        backgroundColor: getConfidenceColor(subGoal.confidence)
                      }} 
                    />
                  </div>
                </Tooltip>
              </Space>
            </div>
          </div>
        ),
        children: milestoneNodes
      });
    });

    return nodes;
  }, [result, editable, onEditItem, onDeleteItem, flatTasks, virtualizeThreshold, getPriorityColor, getConfidenceColor]);

  // 自动展开所有节点（仅在非虚拟化模式下）
  React.useEffect(() => {
    if (flatTasks.length <= virtualizeThreshold) {
      const allKeys: React.Key[] = [];
      const collectKeys = (nodes: DataNode[]) => {
        nodes.forEach(node => {
          allKeys.push(node.key);
          if (node.children) {
            collectKeys(node.children);
          }
        });
      };
      collectKeys(treeData);
      setExpandedKeys(allKeys);
    }
  }, [treeData, flatTasks.length, virtualizeThreshold]);

  return (
    <Card title={`分解结构 - ${goalName}`} size="small">
      <Tree
        treeData={treeData}
        expandedKeys={expandedKeys}
        selectedKeys={selectedKeys}
        onExpand={setExpandedKeys}
        onSelect={setSelectedKeys}
        showLine={{ showLeafIcon: false }}
        blockNode
        virtual={flatTasks.length > virtualizeThreshold}
        height={flatTasks.length > virtualizeThreshold ? 600 : undefined}
      />
    </Card>
  );
});

export default OptimizedDecompositionTree;
