import React, { useState, useEffect, useMemo, useCallback, useRef } from 'react';

interface VirtualListProps<T> {
  items: T[];
  itemHeight: number;
  containerHeight: number;
  renderItem: (item: T, index: number) => React.ReactNode;
  overscan?: number; // 预渲染的额外项目数量
  className?: string;
  style?: React.CSSProperties;
}

export function VirtualList<T>({
  items,
  itemHeight,
  containerHeight,
  renderItem,
  overscan = 5,
  className = '',
  style = {},
}: VirtualListProps<T>) {
  const [scrollTop, setScrollTop] = useState(0);
  const containerRef = useRef<HTMLDivElement>(null);

  // 计算可见范围
  const visibleRange = useMemo(() => {
    const visibleStart = Math.floor(scrollTop / itemHeight);
    const visibleEnd = Math.min(
      visibleStart + Math.ceil(containerHeight / itemHeight),
      items.length - 1
    );

    return {
      start: Math.max(0, visibleStart - overscan),
      end: Math.min(items.length - 1, visibleEnd + overscan),
    };
  }, [scrollTop, itemHeight, containerHeight, items.length, overscan]);

  // 获取可见项目
  const visibleItems = useMemo(() => {
    return items.slice(visibleRange.start, visibleRange.end + 1);
  }, [items, visibleRange]);

  // 处理滚动事件
  const handleScroll = useCallback((e: React.UIEvent<HTMLDivElement>) => {
    setScrollTop(e.currentTarget.scrollTop);
  }, []);

  // 总高度
  const totalHeight = items.length * itemHeight;

  // 偏移量
  const offsetY = visibleRange.start * itemHeight;

  const containerStyle: React.CSSProperties = {
    height: containerHeight,
    overflow: 'auto',
    position: 'relative',
    // 性能优化
    contain: 'layout style paint',
    transform: 'translateZ(0)',
    willChange: 'scroll-position',
    ...style,
  };

  const innerStyle: React.CSSProperties = {
    height: totalHeight,
    position: 'relative',
  };

  const contentStyle: React.CSSProperties = {
    transform: `translateY(${offsetY}px)`,
    position: 'absolute',
    top: 0,
    left: 0,
    right: 0,
    // 性能优化
    willChange: 'transform',
  };

  return (
    <div
      ref={containerRef}
      className={`virtual-list ${className}`}
      style={containerStyle}
      onScroll={handleScroll}
    >
      <div style={innerStyle}>
        <div style={contentStyle}>
          {visibleItems.map((item, index) => {
            const actualIndex = visibleRange.start + index;
            return (
              <div
                key={actualIndex}
                style={{
                  height: itemHeight,
                  // 性能优化
                  contain: 'layout style paint',
                }}
              >
                {renderItem(item, actualIndex)}
              </div>
            );
          })}
        </div>
      </div>
    </div>
  );
}

// 高性能表格虚拟滚动组件
interface VirtualTableProps<T> {
  items: T[];
  rowHeight: number;
  containerHeight: number;
  columns: Array<{
    key: string;
    title: string;
    width?: number;
    render?: (value: any, record: T, index: number) => React.ReactNode;
  }>;
  className?: string;
  style?: React.CSSProperties;
}

export function VirtualTable<T extends Record<string, any>>({
  items,
  rowHeight,
  containerHeight,
  columns,
  className = '',
  style = {},
}: VirtualTableProps<T>) {
  const renderRow = useCallback((item: T, index: number) => (
    <div
      className="virtual-table-row"
      style={{
        display: 'flex',
        alignItems: 'center',
        borderBottom: '1px solid rgba(255, 255, 255, 0.1)',
        padding: '0 16px',
        // 性能优化
        contain: 'layout style',
      }}
    >
      {columns.map((column) => (
        <div
          key={column.key}
          className="virtual-table-cell"
          style={{
            width: column.width || 'auto',
            flex: column.width ? 'none' : 1,
            padding: '0 8px',
            overflow: 'hidden',
            textOverflow: 'ellipsis',
            whiteSpace: 'nowrap',
          }}
        >
          {column.render
            ? column.render(item[column.key], item, index)
            : item[column.key]
          }
        </div>
      ))}
    </div>
  ), [columns]);

  const tableStyle: React.CSSProperties = {
    ...style,
    border: '1px solid rgba(255, 255, 255, 0.2)',
    borderRadius: '8px',
    overflow: 'hidden',
  };

  const headerStyle: React.CSSProperties = {
    display: 'flex',
    alignItems: 'center',
    height: rowHeight,
    background: 'rgba(255, 255, 255, 0.1)',
    borderBottom: '1px solid rgba(255, 255, 255, 0.2)',
    padding: '0 16px',
    fontWeight: 600,
    // 性能优化
    contain: 'layout style',
  };

  return (
    <div className={`virtual-table ${className}`} style={tableStyle}>
      {/* 表头 */}
      <div style={headerStyle}>
        {columns.map((column) => (
          <div
            key={column.key}
            style={{
              width: column.width || 'auto',
              flex: column.width ? 'none' : 1,
              padding: '0 8px',
            }}
          >
            {column.title}
          </div>
        ))}
      </div>
      
      {/* 虚拟滚动内容 */}
      <VirtualList
        items={items}
        itemHeight={rowHeight}
        containerHeight={containerHeight - rowHeight}
        renderItem={renderRow}
      />
    </div>
  );
}

// 高性能网格虚拟滚动组件
interface VirtualGridProps<T> {
  items: T[];
  itemWidth: number;
  itemHeight: number;
  containerWidth: number;
  containerHeight: number;
  renderItem: (item: T, index: number) => React.ReactNode;
  gap?: number;
  className?: string;
  style?: React.CSSProperties;
}

export function VirtualGrid<T>({
  items,
  itemWidth,
  itemHeight,
  containerWidth,
  containerHeight,
  renderItem,
  gap = 16,
  className = '',
  style = {},
}: VirtualGridProps<T>) {
  const [scrollTop, setScrollTop] = useState(0);

  // 计算每行可以显示的项目数
  const itemsPerRow = Math.floor((containerWidth + gap) / (itemWidth + gap));
  
  // 计算总行数
  const totalRows = Math.ceil(items.length / itemsPerRow);
  
  // 行高（包括间距）
  const rowHeight = itemHeight + gap;

  // 计算可见行范围
  const visibleRowRange = useMemo(() => {
    const visibleStart = Math.floor(scrollTop / rowHeight);
    const visibleEnd = Math.min(
      visibleStart + Math.ceil(containerHeight / rowHeight),
      totalRows - 1
    );

    return {
      start: Math.max(0, visibleStart - 1),
      end: Math.min(totalRows - 1, visibleEnd + 1),
    };
  }, [scrollTop, rowHeight, containerHeight, totalRows]);

  // 获取可见项目
  const visibleItems = useMemo(() => {
    const startIndex = visibleRowRange.start * itemsPerRow;
    const endIndex = Math.min((visibleRowRange.end + 1) * itemsPerRow, items.length);
    return items.slice(startIndex, endIndex);
  }, [items, visibleRowRange, itemsPerRow]);

  const handleScroll = useCallback((e: React.UIEvent<HTMLDivElement>) => {
    setScrollTop(e.currentTarget.scrollTop);
  }, []);

  const totalHeight = totalRows * rowHeight;
  const offsetY = visibleRowRange.start * rowHeight;

  const containerStyle: React.CSSProperties = {
    height: containerHeight,
    overflow: 'auto',
    position: 'relative',
    // 性能优化
    contain: 'layout style paint',
    transform: 'translateZ(0)',
    willChange: 'scroll-position',
    ...style,
  };

  const innerStyle: React.CSSProperties = {
    height: totalHeight,
    position: 'relative',
  };

  const contentStyle: React.CSSProperties = {
    transform: `translateY(${offsetY}px)`,
    position: 'absolute',
    top: 0,
    left: 0,
    right: 0,
    display: 'flex',
    flexWrap: 'wrap',
    gap: `${gap}px`,
    padding: `${gap / 2}px`,
    // 性能优化
    willChange: 'transform',
  };

  return (
    <div
      className={`virtual-grid ${className}`}
      style={containerStyle}
      onScroll={handleScroll}
    >
      <div style={innerStyle}>
        <div style={contentStyle}>
          {visibleItems.map((item, index) => {
            const actualIndex = visibleRowRange.start * itemsPerRow + index;
            return (
              <div
                key={actualIndex}
                style={{
                  width: itemWidth,
                  height: itemHeight,
                  // 性能优化
                  contain: 'layout style paint',
                }}
              >
                {renderItem(item, actualIndex)}
              </div>
            );
          })}
        </div>
      </div>
    </div>
  );
}
