import React, { Suspense, lazy, useMemo, useCallback, useEffect, useState } from 'react';
import { Spin } from 'antd';
import NavigationService from '../../services/NavigationService';

// 懒加载页面组件
const Dashboard = lazy(() => import('../../pages/Dashboard'));
const Goals = lazy(() => import('../../pages/Goals'));
const Tasks = lazy(() => import('../../pages/Tasks'));
const EnhancedPomodoro = lazy(() => import('../../pages/EnhancedPomodoro'));
const Analytics = lazy(() => import('../../pages/Analytics'));
const Settings = lazy(() => import('../../pages/Settings'));
const DecompositionResult = lazy(() => import('../../pages/DecompositionResult'));

// 页面配置
const PAGE_CONFIGS = {
  dashboard: {
    component: Dashboard,
    preload: true, // 预加载重要页面
  },
  goals: {
    component: Goals,
    preload: false,
  },
  tasks: {
    component: Tasks,
    preload: true, // 预加载重要页面
  },
  pomodoro: {
    component: EnhancedPomodoro,
    preload: true, // 预加载重要页面
  },
  analytics: {
    component: Analytics,
    preload: false,
  },
  settings: {
    component: Settings,
    preload: false,
  },
  decomposition: {
    component: DecompositionResult,
    preload: false,
  },
} as const;

// 预加载组件的Hook
const usePreloadPages = () => {
  React.useEffect(() => {
    // 预加载重要页面
    const preloadPages = Object.entries(PAGE_CONFIGS)
      .filter(([_, config]) => config.preload)
      .map(([_, config]) => config.component);

    // 使用requestIdleCallback在空闲时预加载
    if ('requestIdleCallback' in window) {
      preloadPages.forEach((PageComponent) => {
        window.requestIdleCallback(() => {
          // 触发懒加载 - 正确的方式是导入模块
          PageComponent().catch(() => {
            // 忽略预加载错误
          });
        });
      });
    } else {
      // 降级方案：使用setTimeout
      setTimeout(() => {
        preloadPages.forEach((PageComponent) => {
          PageComponent().catch(() => {
            // 忽略预加载错误
          });
        });
      }, 100);
    }
  }, []);
};

// 高性能加载指示器
const LoadingSpinner = React.memo(() => (
  <div 
    className="loading-container"
    style={{
      display: 'flex',
      justifyContent: 'center',
      alignItems: 'center',
      height: '200px',
      transform: 'translateZ(0)', // GPU加速
    }}
  >
    <Spin size="large" />
  </div>
));

LoadingSpinner.displayName = 'LoadingSpinner';

// 页面容器组件
const PageContainer = React.memo<{
  children: React.ReactNode;
  pageKey: string;
}>(({ children, pageKey }) => {
  const containerStyle = useMemo(() => ({
    width: '100%',
    height: '100%',
    // 性能优化样式
    contain: 'layout style paint',
    willChange: 'contents',
    transform: 'translateZ(0)',
  }), []);

  return (
    <div 
      key={pageKey}
      className="page-container"
      style={containerStyle}
    >
      {children}
    </div>
  );
});

PageContainer.displayName = 'PageContainer';

// 主路由组件
interface PageRouterProps {
  selectedKey: string;
}

export const PageRouter: React.FC<PageRouterProps> = React.memo(({ selectedKey }) => {
  const [pageParams, setPageParams] = useState<any>(null);

  // 预加载页面
  usePreloadPages();

  // 监听导航参数变化
  useEffect(() => {
    const navigationService = NavigationService.getInstance();
    const unsubscribe = navigationService.addListener((page, params) => {
      setPageParams(params);
    });

    // 获取当前参数
    setPageParams(navigationService.getCurrentParams());

    return unsubscribe;
  }, []);

  // 获取当前页面组件
  const CurrentPageComponent = useMemo(() => {
    const config = PAGE_CONFIGS[selectedKey as keyof typeof PAGE_CONFIGS];
    return config?.component || Dashboard;
  }, [selectedKey]);

  // 渲染页面内容
  const renderPageContent = useCallback(() => (
    <Suspense fallback={<LoadingSpinner />}>
      <CurrentPageComponent pageParams={pageParams} />
    </Suspense>
  ), [CurrentPageComponent, pageParams]);

  return (
    <PageContainer pageKey={selectedKey}>
      {renderPageContent()}
    </PageContainer>
  );
});

PageRouter.displayName = 'PageRouter';

// 导出页面配置用于其他组件
export { PAGE_CONFIGS };
