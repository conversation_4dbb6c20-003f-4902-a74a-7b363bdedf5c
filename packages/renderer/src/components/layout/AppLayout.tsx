import React, { useState, useMemo, useCallback, useEffect } from 'react';
import { Layout, <PERSON>u, Button } from 'antd';
import {
  DashboardOutlined,
  FlagOutlined,
  CheckSquareOutlined,
  ClockCircleOutlined,
  Bar<PERSON>hartOutlined,
  SettingOutlined,
  MenuOutlined,
} from '@ant-design/icons';
import { useTheme } from '../../contexts/ThemeContext';
import { PageRouter } from './PageRouter';
import NavigationService from '../../services/NavigationService';
import ReminderNotificationComponent, { requestNotificationPermission } from '../ReminderNotification';
import { reminderService } from '../../services/ReminderService';
import { FocusCheckTrigger } from '../FocusCheckDialog';
import { QuickDistractionButton } from '../DistractionTracker';
import GoalBeaconDisplayComponent from '../GoalBeaconDisplay';
import { goalBeaconService } from '../../services/GoalBeaconService';
import PerformanceMonitor from '../PerformanceMonitor';

const { Sider, Content } = Layout;

const AppLayout: React.FC = () => {
  const [selectedKey, setSelectedKey] = useState('dashboard');
  const [collapsed, setCollapsed] = useState(false);
  const { theme: currentTheme } = useTheme();

  // 监听导航服务
  useEffect(() => {
    const navigationService = NavigationService.getInstance();
    const unsubscribe = navigationService.addListener((page, params) => {
      setSelectedKey(page);
    });

    return unsubscribe;
  }, []);

  // 使用useMemo缓存菜单项，避免每次渲染都重新创建
  const menuItems = useMemo(() => [
    {
      key: 'dashboard',
      icon: <DashboardOutlined />,
      label: '仪表盘',
    },
    {
      key: 'goals',
      icon: <FlagOutlined />,
      label: '目标管理',
    },
    {
      key: 'tasks',
      icon: <CheckSquareOutlined />,
      label: '任务管理',
    },
    {
      key: 'pomodoro',
      icon: <ClockCircleOutlined />,
      label: '番茄钟',
    },
    {
      key: 'analytics',
      icon: <BarChartOutlined />,
      label: '数据分析',
    },
    {
      key: 'settings',
      icon: <SettingOutlined />,
      label: '设置',
    },
  ], []);

  // 使用useCallback缓存事件处理函数
  const handleMenuClick = useCallback((key: string) => {
    const navigationService = NavigationService.getInstance();
    navigationService.navigateTo(key);
  }, []);

  const handleToggleCollapse = useCallback(() => {
    setCollapsed(prev => !prev);
  }, []);

  // 缓存当前页面标题
  const currentPageTitle = useMemo(() => {
    return menuItems.find(item => item.key === selectedKey)?.label || '仪表盘';
  }, [menuItems, selectedKey]);

  // 初始化提醒系统
  useEffect(() => {
    // 请求通知权限
    requestNotificationPermission();
  }, []);

  return (
    <Layout
      style={{
        height: '100vh',
        background: 'var(--color-background) !important',
      }}
    >
      {/* 自定义样式 */}
      <style>{`
        /* 最高优先级的Layout样式覆盖 */
        .ant-layout {
          background: var(--color-background) !important;
        }
        
        /* 强制覆盖左侧菜单文字颜色 */
        .ant-menu-dark .ant-menu-item,
        .ant-menu-dark .ant-menu-item-group-title,
        .ant-menu-dark .ant-menu-submenu-title,
        .ant-menu .ant-menu-item,
        .ant-menu .ant-menu-item-group-title,
        .ant-menu .ant-menu-submenu-title {
          color: var(--color-text) !important;
        }
        
        /* 确保菜单图标也使用正确颜色 */
        .ant-menu-dark .ant-menu-item .anticon,
        .ant-menu-dark .ant-menu-submenu-title .anticon,
        .ant-menu .ant-menu-item .anticon,
        .ant-menu .ant-menu-submenu-title .anticon {
          color: var(--color-text-secondary) !important;
        }

        .ant-menu-dark .ant-menu-item:hover,
        .ant-menu-dark .ant-menu-submenu-title:hover {
          color: var(--color-primary) !important;
          background-color: var(--color-surface) !important;
        }

        .ant-menu-dark .ant-menu-item-selected {
          background-color: var(--color-primary) !important;
          color: var(--color-text-inverse) !important;
        }

        .ant-menu-dark .ant-menu-item-selected .anticon,
        .ant-menu-dark .ant-menu-item-selected span {
          color: var(--color-text-inverse) !important;
        }

        .ant-menu-dark .ant-menu-item .anticon,
        .ant-menu-dark .ant-menu-submenu-title .anticon {
          color: var(--color-text-secondary) !important;
        }

        .ant-menu-dark .ant-menu-item:hover .anticon,
        .ant-menu-dark .ant-menu-submenu-title:hover .anticon {
          color: var(--color-primary) !important;
        }

        /* 全局深色模式适配 - 强制覆盖Card样式 */
        .ant-card {
          background: var(--color-background-secondary) !important;
          border-color: var(--color-border) !important;
          color: var(--color-text) !important;
        }

        .ant-card-head {
          background: var(--color-background-secondary) !important;
          border-bottom-color: var(--color-border) !important;
          color: var(--color-text) !important;
        }

        .ant-card-head-title {
          color: var(--color-text) !important;
        }

        .ant-card-body {
          background: var(--color-background-secondary) !important;
          color: var(--color-text) !important;
        }

        /* 针对深色主题的Card强制样式 */
        [data-theme="darkNight"] .ant-card,
        [data-theme="darkBlue"] .ant-card {
          background: rgba(28, 28, 30, 0.95) !important;
          border-color: rgba(255, 255, 255, 0.1) !important;
          color: #FFFFFF !important;
        }

        [data-theme="darkNight"] .ant-card-head,
        [data-theme="darkBlue"] .ant-card-head {
          background: rgba(28, 28, 30, 0.95) !important;
          border-bottom-color: rgba(255, 255, 255, 0.1) !important;
          color: #FFFFFF !important;
        }

        [data-theme="darkNight"] .ant-card-body,
        [data-theme="darkBlue"] .ant-card-body {
          background: rgba(28, 28, 30, 0.95) !important;
          color: #FFFFFF !important;
        }

        .ant-table {
          background: var(--color-background-secondary) !important;
          color: var(--color-text) !important;
        }

        .ant-table-thead > tr > th {
          background: var(--color-background-tertiary) !important;
          border-bottom-color: var(--color-border) !important;
          color: var(--color-text) !important;
        }

        .ant-table-tbody > tr > td {
          border-bottom-color: var(--color-border) !important;
          color: var(--color-text) !important;
        }

        .ant-table-tbody > tr:hover > td {
          background: var(--color-surface) !important;
        }

        .ant-input {
          background: var(--color-background-secondary) !important;
          border-color: var(--color-border) !important;
          color: var(--color-text) !important;
        }

        .ant-input:focus,
        .ant-input-focused {
          border-color: var(--color-primary) !important;
          box-shadow: 0 0 0 2px rgba(var(--color-primary-rgb), 0.2) !important;
        }

        .ant-select-selector {
          background: var(--color-background-secondary) !important;
          border-color: var(--color-border) !important;
          color: var(--color-text) !important;
        }

        .ant-select-dropdown {
          background: var(--color-background-secondary) !important;
          border-color: var(--color-border) !important;
        }

        .ant-select-item {
          color: var(--color-text) !important;
        }

        .ant-select-item-option-selected {
          background: var(--color-primary) !important;
          color: #ffffff !important;
        }

        .ant-input-password {
          background: var(--color-background-secondary) !important;
          border-color: var(--color-border) !important;
          color: var(--color-text) !important;
        }

        .ant-input-password .ant-input {
          background: transparent !important;
          color: var(--color-text) !important;
        }

        .ant-form-item-label {
          color: var(--color-text) !important;
        }

        .ant-form-item-explain-error {
          color: var(--color-error) !important;
        }

        .ant-tabs-tab {
          color: var(--color-text-secondary) !important;
        }

        .ant-tabs-tab-active {
          color: var(--color-primary) !important;
        }

        .ant-tabs-content-holder {
          background: transparent !important;
        }

        .ant-tabs-ink-bar {
          background: var(--color-primary) !important;
        }

        .ant-tabs-tab:hover {
          color: var(--color-primary) !important;
        }

        .ant-list-item {
          border-bottom-color: var(--color-border) !important;
          color: var(--color-text) !important;
        }

        .ant-list-item:hover {
          background: var(--color-surface) !important;
        }

        .ant-tooltip-inner {
          background: var(--color-background-secondary) !important;
          color: var(--color-text) !important;
        }

        .ant-tooltip-arrow::before {
          background: var(--color-background-secondary) !important;
        }

        .ant-btn {
          border-color: var(--color-border) !important;
          color: var(--color-text) !important;
        }

        .ant-btn:not(.ant-btn-primary):hover {
          border-color: var(--color-primary) !important;
          color: var(--color-primary) !important;
        }

        .ant-btn-primary {
          background: var(--color-primary) !important;
          border-color: var(--color-primary) !important;
          color: #ffffff !important;
        }

        .ant-btn-primary:hover {
          background: var(--color-primary-hover) !important;
          border-color: var(--color-primary-hover) !important;
        }

        .ant-btn-danger {
          background: var(--color-error) !important;
          border-color: var(--color-error) !important;
          color: #ffffff !important;
        }

        .ant-btn-ghost {
          background: transparent !important;
          border-color: var(--color-border) !important;
          color: var(--color-text) !important;
        }

        .ant-btn-ghost:hover {
          border-color: var(--color-primary) !important;
          color: var(--color-primary) !important;
        }

        .ant-modal-content {
          background: var(--color-background-secondary) !important;
          color: var(--color-text) !important;
        }

        .ant-modal-header {
          background: var(--color-background-secondary) !important;
          border-bottom-color: var(--color-border) !important;
        }

        .ant-modal-title {
          color: var(--color-text) !important;
        }

        .ant-modal-footer {
          background: var(--color-background-secondary) !important;
          border-top-color: var(--color-border) !important;
        }

        /* 深色主题特殊处理 */
        .dark-theme .ant-layout-sider {
          background: var(--color-background-secondary) !important;
        }

        .dark-theme .ant-layout-content {
          background: var(--color-background) !important;
        }

        /* 强制覆盖Ant Design的默认样式 - 最高优先级 */
        .ant-layout-sider {
          background: var(--color-background-secondary) !important;
        }

        .ant-layout-content {
          background: var(--color-background) !important;
        }

        /* 针对深色主题的强制样式 */
        [data-theme="darkNight"] .ant-layout,
        [data-theme="darkBlue"] .ant-layout {
          background: linear-gradient(135deg, #000000 0%, #1C1C1E 50%, #2C2C2E 100%) !important;
        }

        [data-theme="darkNight"] .ant-layout-sider,
        [data-theme="darkBlue"] .ant-layout-sider {
          background: rgba(28, 28, 30, 0.95) !important;
        }
        
        /* 强制设置Sider内所有文字颜色 */
        [data-theme="darkNight"] .ant-layout-sider *,
        [data-theme="darkBlue"] .ant-layout-sider * {
          color: #FFFFFF !important;
        }
        
        /* 确保选中的菜单项文字使用反色 */
        [data-theme="darkNight"] .ant-menu-item-selected,
        [data-theme="darkNight"] .ant-menu-item-selected *,
        [data-theme="darkBlue"] .ant-menu-item-selected,
        [data-theme="darkBlue"] .ant-menu-item-selected * {
          color: var(--color-text-inverse) !important;
        }
        
        /* 强制设置Content内所有文字颜色 */
        [data-theme="darkNight"] .ant-layout-content *,
        [data-theme="darkBlue"] .ant-layout-content * {
          color: #FFFFFF !important;
        }

        [data-theme="darkNight"] .ant-layout-content,
        [data-theme="darkBlue"] .ant-layout-content {
          background: linear-gradient(135deg, #000000 0%, #1C1C1E 50%, #2C2C2E 100%) !important;
        }

        /* 深色主题特殊样式 */
        .dark-theme {
          color-scheme: dark;
        }

        .dark-theme .ant-layout {
          background: var(--color-background) !important;
        }
        
        /* 强制深色主题下所有文字可见 */
        .dark-theme *,
        [data-theme="darkNight"] *,
        [data-theme="darkBlue"] * {
          color: var(--color-text) !important;
        }
        
        /* 但保持某些特殊元素的颜色 */
        .dark-theme .ant-btn-primary,
        [data-theme="darkNight"] .ant-btn-primary,
        [data-theme="darkBlue"] .ant-btn-primary {
          color: var(--color-text-inverse) !important;
        }
        
        /* 浅色主题菜单选中状态 */
        .light-theme .ant-menu-item-selected,
        [data-theme="skyBlue"] .ant-menu-item-selected,
        [data-theme="lavender"] .ant-menu-item-selected,
        [data-theme="forestGreen"] .ant-menu-item-selected,
        [data-theme="sunsetOrange"] .ant-menu-item-selected,
        [data-theme="sakuraPink"] .ant-menu-item-selected,
        [data-theme="deepBlue"] .ant-menu-item-selected,
        [data-theme="limeGreen"] .ant-menu-item-selected,
        [data-theme="twilightGold"] .ant-menu-item-selected,
        [data-theme="mintGreen"] .ant-menu-item-selected,
        [data-theme="cosmicGray"] .ant-menu-item-selected {
          color: var(--color-text-inverse) !important;
          background: var(--color-primary) !important;
        }

        /* 深色主题菜单选中状态 */
        .dark-theme .ant-menu-item-selected,
        [data-theme="darkNight"] .ant-menu-item-selected,
        [data-theme="darkBlue"] .ant-menu-item-selected {
          color: var(--color-text-inverse) !important;
          background: var(--color-primary) !important;
        }

        .dark-theme .ant-form-item-label > label {
          color: var(--color-text) !important;
        }

        .dark-theme .ant-switch {
          background-color: rgba(255, 255, 255, 0.25) !important;
        }

        .dark-theme .ant-switch-checked {
          background-color: var(--color-primary) !important;
        }

        .dark-theme .ant-typography {
          color: var(--color-text) !important;
        }

        .dark-theme .ant-divider {
          border-color: var(--color-border) !important;
        }

        .dark-theme .ant-space-item {
          color: var(--color-text) !important;
        }

        /* 确保所有文本在深色背景下可见 */
        .dark-theme * {
          border-color: var(--color-border) !important;
        }

        .dark-theme .ant-btn-text {
          color: var(--color-text) !important;
        }

        .dark-theme .ant-btn-text:hover {
          color: var(--color-primary) !important;
          background: var(--color-surface) !important;
        }

        /* 全局深色模式强制覆盖 - 确保没有白色背景 */
        [data-theme="darkNight"] *,
        [data-theme="darkBlue"] * {
          background-color: transparent !important;
        }

        [data-theme="darkNight"] .ant-tabs-content,
        [data-theme="darkBlue"] .ant-tabs-content {
          background: transparent !important;
        }

        [data-theme="darkNight"] .ant-tabs-tabpane,
        [data-theme="darkBlue"] .ant-tabs-tabpane {
          background: transparent !important;
        }

        [data-theme="darkNight"] .ant-space,
        [data-theme="darkBlue"] .ant-space {
          background: transparent !important;
        }

        [data-theme="darkNight"] .ant-row,
        [data-theme="darkBlue"] .ant-row {
          background: transparent !important;
        }

        [data-theme="darkNight"] .ant-col,
        [data-theme="darkBlue"] .ant-col {
          background: transparent !important;
        }

        /* 强制设置根元素背景 */
        [data-theme="darkNight"] body,
        [data-theme="darkBlue"] body {
          background: linear-gradient(135deg, #000000 0%, #1C1C1E 50%, #2C2C2E 100%) !important;
        }

        [data-theme="darkNight"] html,
        [data-theme="darkBlue"] html {
          background: linear-gradient(135deg, #000000 0%, #1C1C1E 50%, #2C2C2E 100%) !important;
        }

        [data-theme="darkNight"] #root,
        [data-theme="darkBlue"] #root {
          background: linear-gradient(135deg, #000000 0%, #1C1C1E 50%, #2C2C2E 100%) !important;
        }
      `}</style>

      {/* 侧边栏 */}
      <Sider
        collapsed={collapsed}
        width={280}
        collapsedWidth={80}
        style={{
          background: 'var(--color-background-secondary) !important',
          borderRight: '1px solid var(--color-border)',
        }}
      >
        {/* Logo/标题区域 */}
        <div
          style={{
            display: 'flex',
            alignItems: 'center',
            justifyContent: collapsed ? 'center' : 'space-between',
            padding: '16px',
            borderBottom: '1px solid var(--color-border)',
          }}
        >
          {!collapsed && (
            <div
              style={{
                fontSize: '18px',
                fontWeight: 'var(--font-weight-bold)',
                color: 'var(--color-primary)',
                letterSpacing: '-0.5px',
              }}
            >
              心流锚定
            </div>
          )}
          <Button
            type="text"
            icon={<MenuOutlined />}
            onClick={handleToggleCollapse}
            style={{
              width: '32px',
              height: '32px',
              padding: 0,
              minWidth: '32px',
            }}
          />
        </div>

        {/* 导航菜单 */}
        <Menu
          mode="inline"
          selectedKeys={[selectedKey]}
          style={{
            background: 'transparent',
            border: 'none',
            padding: '16px 8px',
          }}
          theme="dark"
          items={menuItems.map((item) => ({
            key: item.key,
            icon: item.icon,
            label: item.label,
            onClick: () => handleMenuClick(item.key),
          }))}
        />

        {/* 底部装饰 */}
        {!collapsed && (
          <div
            style={{
              position: 'absolute',
              bottom: '16px',
              left: '16px',
              right: '16px',
              textAlign: 'center',
            }}
          >
            <div
              style={{
                fontSize: 'var(--font-size-xs)',
                color: 'var(--color-text-tertiary)',
                opacity: 0.7,
              }}
            >
              FocusOS v1.0
            </div>
          </div>
        )}
      </Sider>

      {/* 主内容区域 */}
      <Content
        style={{
          background: 'var(--color-background) !important',
        }}
      >
        {/* 头部栏 */}
        <div
          style={{
            height: '80px',
            padding: '20px 32px',
            display: 'flex',
            alignItems: 'center',
            justifyContent: 'space-between',
            background: 'var(--color-background)',
            borderBottom: '1px solid var(--color-border)',
            zIndex: 10,
          }}
        >
          <div>
            <h1
              style={{
                margin: 0,
                fontSize: 'var(--font-size-2xl)',
                fontWeight: 'var(--font-weight-bold)',
                color: 'var(--color-text)',
                letterSpacing: '-0.5px',
              }}
            >
              {currentPageTitle}
            </h1>
          </div>

          {/* 右侧操作区域 */}
          <div style={{ display: 'flex', alignItems: 'center', gap: '12px' }}>
            <div
              style={{
                padding: '8px 12px',
                background: 'var(--color-background-secondary)',
                borderRadius: 'var(--radius-small)',
                fontSize: 'var(--font-size-sm)',
                color: 'var(--color-text-secondary)',
                border: '1px solid var(--color-border)',
              }}
            >
              {new Date().toLocaleDateString('zh-CN', {
                month: 'long',
                day: 'numeric',
                weekday: 'short'
              })}
            </div>
          </div>
        </div>

        {/* 内容区域 */}
        <div
          style={{
            flex: 1,
            overflow: 'auto',
            padding: 'var(--spacing-xl)',
            background: 'var(--color-background) !important',
            minHeight: 'calc(100vh - 80px)',
          }}
        >
          <div
            style={{
              maxWidth: '1200px',
              margin: '0 auto',
              minHeight: 'calc(100vh - 144px)',
            }}
          >
            <PageRouter selectedKey={selectedKey} />
          </div>
        </div>
      </Content>

      {/* 提醒通知组件 */}
      <ReminderNotificationComponent
        onActionClick={(action, notification) => {
          // 处理提醒操作
          switch (action) {
            case 'start-task':
            case 'view-task':
              handleMenuClick('tasks');
              break;
            case 'view-goals':
              handleMenuClick('goals');
              break;
            case 'view-progress':
              handleMenuClick('analytics');
              break;
            case 'back-to-task':
              handleMenuClick('tasks');
              break;
            default:
              break;
          }
        }}
      />

      {/* 专注检查触发器 */}
      <FocusCheckTrigger />

      {/* 快速分心记录按钮 */}
      <QuickDistractionButton />

      {/* 目标可视化提醒 */}
      <GoalBeaconDisplayComponent
        onActionClick={(action, display) => {
          // 处理目标提醒操作
          switch (action) {
            case 'view-goal':
              handleMenuClick('goals');
              break;
            case 'continue-focus':
            case 'refocus':
            case 'start-task':
              handleMenuClick('tasks');
              break;
            case 'take-break':
              handleMenuClick('pomodoro');
              break;
            case 'record-distraction':
              // 触发分心记录
              break;
            default:
              break;
          }
        }}
      />

      {/* 性能监控 */}
      <PerformanceMonitor enabled={false} showDetails={false} />
    </Layout>
  );
};

export default AppLayout;