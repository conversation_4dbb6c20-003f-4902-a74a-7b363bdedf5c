import React, { useState, useEffect, useMemo } from 'react';
import { Card, Switch, Select, Slider, Checkbox, Space, Typography, Tooltip, Alert, Spin } from 'antd';
import { RobotOutlined, InfoCircleOutlined, SettingOutlined } from '@ant-design/icons';
import { DatabaseAPI } from '../services/api';

const { Text, Title } = Typography;
const { Option } = Select;

interface AIDecompositionConfigProps {
  enabled: boolean;
  onEnabledChange: (enabled: boolean) => void;
  config: AIDecompositionConfig;
  onConfigChange: (config: AIDecompositionConfig) => void;
  goalType?: string;
  goalDescription?: string;
}

export interface AIDecompositionConfig {
  aiProvider?: string;
  templateType?: string;
  preferences: {
    maxDepth: number;
    taskGranularity: 'fine' | 'medium' | 'coarse';
    includeTimeEstimates: boolean;
    maxTaskDuration: number;
    focusAreas: string[];
  };
  context: {
    userExperience: 'beginner' | 'intermediate' | 'expert';
    availableTime: string;
    resources: string[];
    constraints: string[];
  };
}

const AIDecompositionConfig: React.FC<AIDecompositionConfigProps> = ({
  enabled,
  onEnabledChange,
  config,
  onConfigChange,
  goalType,
  goalDescription
}) => {
  const [providers, setProviders] = useState<any[]>([]);
  const [templates, setTemplates] = useState<any[]>([]);
  const [loading, setLoading] = useState(false);

  // 加载可用的AI Provider
  useEffect(() => {
    const loadProviders = async () => {
      try {
        const result = await DatabaseAPI.getAvailableAIProviders();
        if (result.success) {
          setProviders(result.providers || []);
        }
      } catch (error) {
        console.error('加载AI Provider失败:', error);
      }
    };

    if (enabled) {
      loadProviders();
    }
  }, [enabled]);

  // 加载可用的模板
  useEffect(() => {
    const loadTemplates = async () => {
      try {
        const result = await DatabaseAPI.getAIDecompositionTemplates();
        if (result.success) {
          setTemplates(result.templates || []);
        }
      } catch (error) {
        console.error('加载分解模板失败:', error);
      }
    };

    if (enabled) {
      loadTemplates();
    }
  }, [enabled]);

  // 根据目标类型推荐模板
  const recommendedTemplate = useMemo(() => {
    if (!goalDescription) return null;
    
    const description = goalDescription.toLowerCase();
    if (description.includes('学习') || description.includes('技能')) {
      return 'skill_learning';
    }
    if (description.includes('项目') || description.includes('开发')) {
      return 'project_breakdown';
    }
    if (description.includes('习惯')) {
      return 'habit_formation';
    }
    return 'goal_decomposition';
  }, [goalDescription]);

  const handleConfigChange = (key: string, value: any) => {
    const newConfig = { ...config };
    if (key.includes('.')) {
      const [parent, child] = key.split('.');
      newConfig[parent as keyof AIDecompositionConfig] = {
        ...newConfig[parent as keyof AIDecompositionConfig],
        [child]: value
      };
    } else {
      (newConfig as any)[key] = value;
    }
    onConfigChange(newConfig);
  };

  if (!enabled) {
    return (
      <Card size="small" style={{ marginBottom: 16 }}>
        <div style={{ display: 'flex', alignItems: 'center', justifyContent: 'space-between' }}>
          <Space>
            <RobotOutlined style={{ color: '#1890ff' }} />
            <Text strong>AI智能分解</Text>
            <Tooltip title="使用AI自动将目标分解为可执行的任务">
              <InfoCircleOutlined style={{ color: '#999' }} />
            </Tooltip>
          </Space>
          <Switch
            checked={enabled}
            onChange={onEnabledChange}
            checkedChildren="启用"
            unCheckedChildren="关闭"
          />
        </div>
        <Text type="secondary" style={{ fontSize: '12px', marginTop: 8, display: 'block' }}>
          启用后，AI将帮助你将目标智能分解为子目标、里程碑和具体任务
        </Text>
      </Card>
    );
  }

  return (
    <Card 
      size="small" 
      style={{ marginBottom: 16 }}
      title={
        <div style={{ display: 'flex', alignItems: 'center', justifyContent: 'space-between' }}>
          <Space>
            <RobotOutlined style={{ color: '#1890ff' }} />
            <Text strong>AI智能分解配置</Text>
          </Space>
          <Switch
            checked={enabled}
            onChange={onEnabledChange}
            checkedChildren="启用"
            unCheckedChildren="关闭"
          />
        </div>
      }
    >
      {loading && <Spin size="small" />}
      
      {/* AI Provider 选择 */}
      <div style={{ marginBottom: 16 }}>
        <Text strong style={{ display: 'block', marginBottom: 8 }}>AI服务商</Text>
        <Select
          style={{ width: '100%' }}
          placeholder="选择AI服务商"
          value={config.aiProvider}
          onChange={(value) => handleConfigChange('aiProvider', value)}
        >
          {providers.map(provider => (
            <Option key={provider.id} value={provider.id}>
              <Space>
                <span>{provider.name}</span>
                <Text type="secondary" style={{ fontSize: '12px' }}>
                  {provider.modelId}
                </Text>
              </Space>
            </Option>
          ))}
        </Select>
        {providers.length === 0 && (
          <Alert
            message="请先在设置中配置AI服务商"
            type="warning"
            size="small"
            style={{ marginTop: 8 }}
          />
        )}
      </div>

      {/* 分解模板选择 */}
      <div style={{ marginBottom: 16 }}>
        <Text strong style={{ display: 'block', marginBottom: 8 }}>分解模板</Text>
        <Select
          style={{ width: '100%' }}
          placeholder="选择分解模板（留空自动选择）"
          value={config.templateType}
          onChange={(value) => handleConfigChange('templateType', value)}
          allowClear
        >
          {templates.map(template => (
            <Option key={template.type} value={template.type}>
              <div>
                <Text>{template.name}</Text>
                {template.type === recommendedTemplate && (
                  <Text type="success" style={{ fontSize: '12px', marginLeft: 8 }}>
                    (推荐)
                  </Text>
                )}
                <br />
                <Text type="secondary" style={{ fontSize: '12px' }}>
                  {template.description}
                </Text>
              </div>
            </Option>
          ))}
        </Select>
      </div>

      {/* 分解偏好设置 */}
      <div style={{ marginBottom: 16 }}>
        <Text strong style={{ display: 'block', marginBottom: 12 }}>分解偏好</Text>
        
        <div style={{ marginBottom: 12 }}>
          <Text style={{ fontSize: '13px' }}>分解层级深度: {config.preferences.maxDepth}层</Text>
          <Slider
            min={2}
            max={4}
            value={config.preferences.maxDepth}
            onChange={(value) => handleConfigChange('preferences.maxDepth', value)}
            marks={{
              2: '2层',
              3: '3层',
              4: '4层'
            }}
          />
        </div>

        <div style={{ marginBottom: 12 }}>
          <Text style={{ fontSize: '13px' }}>任务粒度</Text>
          <Select
            style={{ width: '100%', marginTop: 4 }}
            value={config.preferences.taskGranularity}
            onChange={(value) => handleConfigChange('preferences.taskGranularity', value)}
          >
            <Option value="coarse">粗粒度 - 较大的任务块</Option>
            <Option value="medium">中等粒度 - 平衡的任务大小</Option>
            <Option value="fine">细粒度 - 详细的小任务</Option>
          </Select>
        </div>

        <div style={{ marginBottom: 12 }}>
          <Text style={{ fontSize: '13px' }}>单任务最大时长: {config.preferences.maxTaskDuration}分钟</Text>
          <Slider
            min={30}
            max={240}
            step={30}
            value={config.preferences.maxTaskDuration}
            onChange={(value) => handleConfigChange('preferences.maxTaskDuration', value)}
            marks={{
              30: '30分',
              120: '2小时',
              240: '4小时'
            }}
          />
        </div>

        <Checkbox
          checked={config.preferences.includeTimeEstimates}
          onChange={(e) => handleConfigChange('preferences.includeTimeEstimates', e.target.checked)}
        >
          <Text style={{ fontSize: '13px' }}>包含时间估算</Text>
        </Checkbox>
      </div>

      {/* 用户上下文 */}
      <div>
        <Text strong style={{ display: 'block', marginBottom: 12 }}>个人情况</Text>
        
        <div style={{ marginBottom: 8 }}>
          <Text style={{ fontSize: '13px' }}>经验水平</Text>
          <Select
            style={{ width: '100%', marginTop: 4 }}
            value={config.context.userExperience}
            onChange={(value) => handleConfigChange('context.userExperience', value)}
          >
            <Option value="beginner">初学者</Option>
            <Option value="intermediate">中等水平</Option>
            <Option value="expert">专家级别</Option>
          </Select>
        </div>

        <div style={{ marginBottom: 8 }}>
          <Text style={{ fontSize: '13px' }}>可用时间</Text>
          <Select
            style={{ width: '100%', marginTop: 4 }}
            value={config.context.availableTime}
            onChange={(value) => handleConfigChange('context.availableTime', value)}
          >
            <Option value="每天30分钟">每天30分钟</Option>
            <Option value="每天1-2小时">每天1-2小时</Option>
            <Option value="每天3-4小时">每天3-4小时</Option>
            <Option value="周末集中时间">周末集中时间</Option>
            <Option value="灵活安排">灵活安排</Option>
          </Select>
        </div>
      </div>
    </Card>
  );
};

export default AIDecompositionConfig;
