import React, { useState, useEffect, useMemo } from 'react';
import { Card, Table, Tag, Space, Button, Progress, Typography, Alert, Spin, message } from 'antd';
import { 
  PlayCircleOutlined, 
  CheckCircleOutlined, 
  ClockCircleOutlined,
  RobotOutlined,
  SyncOutlined,
  EyeOutlined
} from '@ant-design/icons';
import { useDispatch } from 'react-redux';
import { DatabaseAPI } from '../services/api';
import { createPomodoroSessionAsync } from '../store/thunks/pomodoroThunks';
import { setCurrentSession, resetTimer } from '../store/slices/pomodoroSlice';
import NavigationService from '../services/NavigationService';
import type { ColumnsType } from 'antd/es/table';

const { Title, Text } = Typography;

interface AITask {
  id: string;
  title: string;
  description: string;
  estimatedTime: number;
  priority: 'high' | 'medium' | 'low';
  confidence: number;
  actionable: boolean;
  resources: string[];
  status: string;
  subGoalName: string;
  milestoneName: string;
  isAIGenerated: boolean;
  aiConfidence: number;
}

interface AITasksViewProps {
  goalId: string;
  goalName: string;
}

const AITasksView: React.FC<AITasksViewProps> = ({ goalId, goalName }) => {
  const dispatch = useDispatch();
  const [loading, setLoading] = useState(true);
  const [structure, setStructure] = useState<any>(null);
  const [error, setError] = useState<string | null>(null);

  useEffect(() => {
    loadGoalStructure();
  }, [goalId]);

  const loadGoalStructure = async () => {
    try {
      setLoading(true);
      setError(null);
      
      const result = await DatabaseAPI.getGoalDecompositionStructure(goalId);
      
      if (result.success) {
        setStructure(result.structure);
      } else {
        setError(result.error || '加载失败');
      }
    } catch (error) {
      console.error('加载目标结构失败:', error);
      setError('加载失败: ' + (error as Error).message);
    } finally {
      setLoading(false);
    }
  };

  // 扁平化所有任务
  const allTasks = useMemo(() => {
    if (!structure) return [];
    
    const tasks: AITask[] = [];
    structure.subGoals.forEach((subGoal: any) => {
      subGoal.milestones.forEach((milestone: any) => {
        milestone.tasks.forEach((task: any) => {
          tasks.push({
            ...task,
            subGoalName: subGoal.name,
            milestoneName: milestone.name
          });
        });
      });
    });
    
    return tasks;
  }, [structure]);

  const handleStartPomodoro = async (task: AITask) => {
    try {
      const sessionData = {
        taskId: task.id,
        type: 'work' as const,
        startTime: new Date(),
        duration: 25,
      };

      const session = await dispatch(createPomodoroSessionAsync(sessionData)).unwrap();
      dispatch(setCurrentSession(session));
      dispatch(resetTimer());

      message.success(`已为AI任务"${task.title}"创建番茄钟会话`);
    } catch (error) {
      console.error('创建番茄钟会话失败:', error);
      message.error('创建番茄钟会话失败');
    }
  };

  const handleViewDetails = () => {
    const navigationService = NavigationService.getInstance();
    navigationService.navigateToDecompositionResult(goalId);
  };

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'pending': return 'default';
      case 'in-progress': return 'processing';
      case 'completed': return 'success';
      case 'paused': return 'warning';
      default: return 'default';
    }
  };

  const getStatusText = (status: string) => {
    switch (status) {
      case 'pending': return '待处理';
      case 'in-progress': return '进行中';
      case 'completed': return '已完成';
      case 'paused': return '已暂停';
      default: return status;
    }
  };

  const getPriorityColor = (priority: string) => {
    switch (priority) {
      case 'high': return 'red';
      case 'medium': return 'orange';
      case 'low': return 'green';
      default: return 'default';
    }
  };

  const getPriorityText = (priority: string) => {
    switch (priority) {
      case 'high': return '高';
      case 'medium': return '中';
      case 'low': return '低';
      default: return priority;
    }
  };

  const getConfidenceColor = (confidence: number) => {
    if (confidence >= 0.8) return '#52c41a';
    if (confidence >= 0.6) return '#faad14';
    return '#ff4d4f';
  };

  const columns: ColumnsType<AITask> = [
    {
      title: '任务名称',
      dataIndex: 'title',
      key: 'title',
      width: 200,
      render: (title: string, record: AITask) => (
        <div>
          <div style={{ display: 'flex', alignItems: 'center', gap: 4 }}>
            <RobotOutlined style={{ color: '#1890ff', fontSize: '12px' }} />
            <Text strong>{title}</Text>
          </div>
          <div style={{ fontSize: '11px', color: '#999', marginTop: 2 }}>
            {record.subGoalName} → {record.milestoneName}
          </div>
        </div>
      ),
    },
    {
      title: '描述',
      dataIndex: 'description',
      key: 'description',
      width: 250,
      render: (description: string) => (
        <Text style={{ fontSize: '12px' }} ellipsis={{ tooltip: description }}>
          {description}
        </Text>
      ),
    },
    {
      title: '优先级',
      dataIndex: 'priority',
      key: 'priority',
      width: 80,
      render: (priority: string) => (
        <Tag color={getPriorityColor(priority)} size="small">
          {getPriorityText(priority)}
        </Tag>
      ),
    },
    {
      title: '状态',
      dataIndex: 'status',
      key: 'status',
      width: 100,
      render: (status: string) => (
        <Tag color={getStatusColor(status)} size="small">
          {getStatusText(status)}
        </Tag>
      ),
    },
    {
      title: '预估时间',
      dataIndex: 'estimatedTime',
      key: 'estimatedTime',
      width: 100,
      render: (time: number) => (
        <Space>
          <ClockCircleOutlined style={{ fontSize: '12px' }} />
          <Text style={{ fontSize: '12px' }}>{time}分钟</Text>
        </Space>
      ),
    },
    {
      title: 'AI信心度',
      dataIndex: 'aiConfidence',
      key: 'aiConfidence',
      width: 120,
      render: (confidence: number) => (
        <div style={{ display: 'flex', alignItems: 'center', gap: 8 }}>
          <Progress
            percent={confidence * 100}
            size="small"
            strokeColor={getConfidenceColor(confidence)}
            showInfo={false}
            style={{ flex: 1, minWidth: 60 }}
          />
          <Text style={{ fontSize: '11px', color: '#666' }}>
            {(confidence * 100).toFixed(0)}%
          </Text>
        </div>
      ),
    },
    {
      title: '可执行',
      dataIndex: 'actionable',
      key: 'actionable',
      width: 80,
      render: (actionable: boolean) => (
        actionable ? (
          <CheckCircleOutlined style={{ color: '#52c41a' }} />
        ) : (
          <ClockCircleOutlined style={{ color: '#faad14' }} />
        )
      ),
    },
    {
      title: '操作',
      key: 'action',
      width: 120,
      render: (_, record) => (
        <Space size="small">
          <Button
            size="small"
            icon={<PlayCircleOutlined />}
            title="开始番茄钟"
            onClick={() => handleStartPomodoro(record)}
            disabled={record.status === 'completed'}
          />
        </Space>
      ),
    },
  ];

  if (loading) {
    return (
      <Card>
        <div style={{ textAlign: 'center', padding: '40px 20px' }}>
          <Spin size="large" />
          <div style={{ marginTop: 16 }}>
            <Text>正在加载AI分解任务...</Text>
          </div>
        </div>
      </Card>
    );
  }

  if (error) {
    return (
      <Card>
        <Alert
          message="加载失败"
          description={error}
          type="error"
          showIcon
          action={
            <Space>
              <Button size="small" onClick={loadGoalStructure}>
                重试
              </Button>
            </Space>
          }
        />
      </Card>
    );
  }

  if (!structure || allTasks.length === 0) {
    return (
      <Card>
        <div style={{ textAlign: 'center', padding: '40px 20px' }}>
          <RobotOutlined style={{ fontSize: 48, color: '#d9d9d9', marginBottom: 16 }} />
          <div>
            <Text type="secondary">该目标还没有AI分解任务</Text>
          </div>
          <div style={{ marginTop: 8 }}>
            <Text type="secondary" style={{ fontSize: '12px' }}>
              请先在目标创建时启用AI分解功能
            </Text>
          </div>
        </div>
      </Card>
    );
  }

  return (
    <Card
      title={
        <div style={{ display: 'flex', alignItems: 'center', justifyContent: 'space-between' }}>
          <Space>
            <RobotOutlined style={{ color: '#1890ff' }} />
            <Title level={4} style={{ margin: 0 }}>
              AI分解任务 - {goalName}
            </Title>
          </Space>
          <Space>
            <Button 
              icon={<SyncOutlined />} 
              onClick={loadGoalStructure}
              size="small"
            >
              刷新
            </Button>
            <Button 
              icon={<EyeOutlined />} 
              onClick={handleViewDetails}
              size="small"
            >
              查看详情
            </Button>
          </Space>
        </div>
      }
    >
      {/* 统计信息 */}
      <div style={{ 
        marginBottom: 16, 
        padding: 12, 
        backgroundColor: '#f8f9fa', 
        borderRadius: 6,
        display: 'grid',
        gridTemplateColumns: 'repeat(auto-fit, minmax(120px, 1fr))',
        gap: 16
      }}>
        <div style={{ textAlign: 'center' }}>
          <div style={{ fontSize: '20px', fontWeight: 'bold', color: '#1890ff' }}>
            {structure.subGoals.length}
          </div>
          <div style={{ fontSize: '12px', color: '#666' }}>子目标</div>
        </div>
        <div style={{ textAlign: 'center' }}>
          <div style={{ fontSize: '20px', fontWeight: 'bold', color: '#52c41a' }}>
            {allTasks.length}
          </div>
          <div style={{ fontSize: '12px', color: '#666' }}>总任务</div>
        </div>
        <div style={{ textAlign: 'center' }}>
          <div style={{ fontSize: '20px', fontWeight: 'bold', color: '#faad14' }}>
            {structure.completedTasks}
          </div>
          <div style={{ fontSize: '12px', color: '#666' }}>已完成</div>
        </div>
        <div style={{ textAlign: 'center' }}>
          <div style={{ fontSize: '20px', fontWeight: 'bold', color: '#722ed1' }}>
            {structure.estimatedTotalTime}h
          </div>
          <div style={{ fontSize: '12px', color: '#666' }}>预估时长</div>
        </div>
      </div>

      {/* 任务表格 */}
      <Table
        columns={columns}
        dataSource={allTasks}
        rowKey="id"
        size="small"
        pagination={{
          pageSize: 10,
          showSizeChanger: true,
          showQuickJumper: true,
          showTotal: (total) => `共 ${total} 个AI任务`,
        }}
        scroll={{ x: 1000 }}
      />
    </Card>
  );
};

export default AITasksView;
