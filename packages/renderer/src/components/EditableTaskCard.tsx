import React, { useState, useRef } from 'react';
import { Card, Input, Select, Tag, Space, Button, Tooltip, InputNumber, Checkbox } from 'antd';
import { 
  EditOutlined, 
  SaveOutlined, 
  CloseOutlined, 
  DeleteOutlined,
  DragOutlined,
  ClockCircleOutlined,
  FlagOutlined
} from '@ant-design/icons';
import { useDrag, useDrop } from 'react-dnd';

const { TextArea } = Input;
const { Option } = Select;

interface Task {
  id?: string;
  title: string;
  description: string;
  estimatedTime: number;
  priority: 'high' | 'medium' | 'low';
  confidence: number;
  actionable: boolean;
  resources: string[];
  completed?: boolean;
}

interface EditableTaskCardProps {
  task: Task;
  index: number;
  onUpdate: (index: number, task: Task) => void;
  onDelete: (index: number) => void;
  onMove: (dragIndex: number, hoverIndex: number) => void;
  editable?: boolean;
  showDragHandle?: boolean;
}

const EditableTaskCard: React.FC<EditableTaskCardProps> = ({
  task,
  index,
  onUpdate,
  onDelete,
  onMove,
  editable = false,
  showDragHandle = false
}) => {
  const [isEditing, setIsEditing] = useState(false);
  const [editingTask, setEditingTask] = useState<Task>(task);
  const ref = useRef<HTMLDivElement>(null);

  // 拖拽功能
  const [{ isDragging }, drag, preview] = useDrag({
    type: 'task',
    item: { index },
    collect: (monitor) => ({
      isDragging: monitor.isDragging(),
    }),
  });

  const [, drop] = useDrop({
    accept: 'task',
    hover: (item: { index: number }) => {
      if (!ref.current) return;
      
      const dragIndex = item.index;
      const hoverIndex = index;
      
      if (dragIndex === hoverIndex) return;
      
      onMove(dragIndex, hoverIndex);
      item.index = hoverIndex;
    },
  });

  // 连接拖拽引用
  if (showDragHandle) {
    preview(drop(ref));
  }

  const handleEdit = () => {
    setEditingTask({ ...task });
    setIsEditing(true);
  };

  const handleSave = () => {
    onUpdate(index, editingTask);
    setIsEditing(false);
  };

  const handleCancel = () => {
    setEditingTask({ ...task });
    setIsEditing(false);
  };

  const handleDelete = () => {
    onDelete(index);
  };

  const handleFieldChange = (field: keyof Task, value: any) => {
    setEditingTask(prev => ({
      ...prev,
      [field]: value
    }));
  };

  const handleResourcesChange = (resources: string[]) => {
    setEditingTask(prev => ({
      ...prev,
      resources
    }));
  };

  const getPriorityColor = (priority: string) => {
    switch (priority) {
      case 'high': return 'red';
      case 'medium': return 'orange';
      case 'low': return 'green';
      default: return 'default';
    }
  };

  const getConfidenceColor = (confidence: number) => {
    if (confidence >= 0.8) return '#52c41a';
    if (confidence >= 0.6) return '#faad14';
    return '#ff4d4f';
  };

  if (isEditing) {
    return (
      <Card
        ref={ref}
        size="small"
        style={{ 
          marginBottom: 8,
          opacity: isDragging ? 0.5 : 1,
          border: '2px dashed #1890ff'
        }}
        title={
          <Space>
            <EditOutlined style={{ color: '#1890ff' }} />
            <span>编辑任务</span>
          </Space>
        }
        extra={
          <Space>
            <Button size="small" type="primary" icon={<SaveOutlined />} onClick={handleSave}>
              保存
            </Button>
            <Button size="small" icon={<CloseOutlined />} onClick={handleCancel}>
              取消
            </Button>
          </Space>
        }
      >
        <div style={{ display: 'flex', flexDirection: 'column', gap: 12 }}>
          {/* 任务标题 */}
          <div>
            <label style={{ fontSize: '12px', color: '#666', marginBottom: 4, display: 'block' }}>
              任务标题
            </label>
            <Input
              value={editingTask.title}
              onChange={(e) => handleFieldChange('title', e.target.value)}
              placeholder="输入任务标题"
              maxLength={100}
            />
          </div>

          {/* 任务描述 */}
          <div>
            <label style={{ fontSize: '12px', color: '#666', marginBottom: 4, display: 'block' }}>
              任务描述
            </label>
            <TextArea
              value={editingTask.description}
              onChange={(e) => handleFieldChange('description', e.target.value)}
              placeholder="详细描述任务内容"
              rows={3}
              maxLength={500}
            />
          </div>

          {/* 任务属性 */}
          <div style={{ display: 'grid', gridTemplateColumns: '1fr 1fr', gap: 12 }}>
            <div>
              <label style={{ fontSize: '12px', color: '#666', marginBottom: 4, display: 'block' }}>
                优先级
              </label>
              <Select
                value={editingTask.priority}
                onChange={(value) => handleFieldChange('priority', value)}
                style={{ width: '100%' }}
              >
                <Option value="high">高优先级</Option>
                <Option value="medium">中优先级</Option>
                <Option value="low">低优先级</Option>
              </Select>
            </div>

            <div>
              <label style={{ fontSize: '12px', color: '#666', marginBottom: 4, display: 'block' }}>
                预估时间(分钟)
              </label>
              <InputNumber
                value={editingTask.estimatedTime}
                onChange={(value) => handleFieldChange('estimatedTime', value || 0)}
                min={1}
                max={480}
                style={{ width: '100%' }}
              />
            </div>
          </div>

          {/* 信心度和可执行性 */}
          <div style={{ display: 'grid', gridTemplateColumns: '1fr auto', gap: 12, alignItems: 'center' }}>
            <div>
              <label style={{ fontSize: '12px', color: '#666', marginBottom: 4, display: 'block' }}>
                信心度: {(editingTask.confidence * 100).toFixed(0)}%
              </label>
              <div style={{ display: 'flex', alignItems: 'center', gap: 8 }}>
                <input
                  type="range"
                  min="0"
                  max="1"
                  step="0.1"
                  value={editingTask.confidence}
                  onChange={(e) => handleFieldChange('confidence', parseFloat(e.target.value))}
                  style={{ flex: 1 }}
                />
                <div 
                  style={{ 
                    width: 20, 
                    height: 20, 
                    borderRadius: '50%', 
                    backgroundColor: getConfidenceColor(editingTask.confidence) 
                  }} 
                />
              </div>
            </div>

            <Checkbox
              checked={editingTask.actionable}
              onChange={(e) => handleFieldChange('actionable', e.target.checked)}
            >
              可直接执行
            </Checkbox>
          </div>

          {/* 所需资源 */}
          <div>
            <label style={{ fontSize: '12px', color: '#666', marginBottom: 4, display: 'block' }}>
              所需资源
            </label>
            <Select
              mode="tags"
              value={editingTask.resources}
              onChange={handleResourcesChange}
              placeholder="输入所需资源"
              style={{ width: '100%' }}
            >
              <Option value="电脑">电脑</Option>
              <Option value="网络">网络</Option>
              <Option value="文档">文档</Option>
              <Option value="工具">工具</Option>
              <Option value="协作">协作</Option>
            </Select>
          </div>
        </div>
      </Card>
    );
  }

  return (
    <Card
      ref={ref}
      size="small"
      style={{ 
        marginBottom: 8,
        opacity: isDragging ? 0.5 : 1,
        cursor: showDragHandle ? 'move' : 'default'
      }}
      bodyStyle={{ padding: '12px 16px' }}
    >
      <div style={{ display: 'flex', alignItems: 'flex-start', gap: 12 }}>
        {/* 拖拽手柄 */}
        {showDragHandle && (
          <div
            ref={drag}
            style={{ 
              cursor: 'grab', 
              color: '#999', 
              marginTop: 2,
              ':active': { cursor: 'grabbing' }
            }}
          >
            <DragOutlined />
          </div>
        )}

        {/* 任务内容 */}
        <div style={{ flex: 1 }}>
          <div style={{ display: 'flex', alignItems: 'center', justifyContent: 'space-between', marginBottom: 8 }}>
            <div style={{ display: 'flex', alignItems: 'center', gap: 8 }}>
              <span style={{ fontWeight: 500, fontSize: '14px' }}>{task.title}</span>
              <Tag color={getPriorityColor(task.priority)} size="small">
                {task.priority}
              </Tag>
              <Tag color="blue" size="small">
                <ClockCircleOutlined style={{ marginRight: 2 }} />
                {task.estimatedTime}分钟
              </Tag>
            </div>

            {editable && (
              <Space size="small">
                <Tooltip title="编辑">
                  <Button size="small" type="text" icon={<EditOutlined />} onClick={handleEdit} />
                </Tooltip>
                <Tooltip title="删除">
                  <Button size="small" type="text" danger icon={<DeleteOutlined />} onClick={handleDelete} />
                </Tooltip>
              </Space>
            )}
          </div>

          <div style={{ fontSize: '12px', color: '#666', marginBottom: 8 }}>
            {task.description}
          </div>

          <div style={{ display: 'flex', alignItems: 'center', justifyContent: 'space-between' }}>
            <div style={{ display: 'flex', alignItems: 'center', gap: 8 }}>
              <Tooltip title={`信心度: ${(task.confidence * 100).toFixed(0)}%`}>
                <div style={{ display: 'flex', alignItems: 'center', gap: 4 }}>
                  <div style={{ 
                    width: 30, 
                    height: 4, 
                    backgroundColor: '#f0f0f0', 
                    borderRadius: 2,
                    overflow: 'hidden'
                  }}>
                    <div 
                      style={{ 
                        width: `${task.confidence * 100}%`, 
                        height: '100%', 
                        backgroundColor: getConfidenceColor(task.confidence)
                      }} 
                    />
                  </div>
                  <span style={{ fontSize: '11px', color: '#999' }}>
                    {(task.confidence * 100).toFixed(0)}%
                  </span>
                </div>
              </Tooltip>

              {task.actionable && (
                <Tag color="green" size="small">
                  <FlagOutlined style={{ marginRight: 2 }} />
                  可执行
                </Tag>
              )}
            </div>

            {task.resources.length > 0 && (
              <div style={{ fontSize: '11px', color: '#999' }}>
                资源: {task.resources.join(', ')}
              </div>
            )}
          </div>
        </div>
      </div>
    </Card>
  );
};

export default EditableTaskCard;
