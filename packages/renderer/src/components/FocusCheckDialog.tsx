import React, { useState, useEffect } from 'react';
import { Modal, Button, Space, Typography, Rate, Radio, Input, Card, Progress, Alert } from 'antd';
import { 
  EyeOutlined, 
  ClockCircleOutlined, 
  CheckCircleOutlined,
  ExclamationCircleOutlined
} from '@ant-design/icons';
import { FocusCheckQuestion, focusMonitorService } from '../services/FocusMonitorService';

const { Title, Text } = Typography;
const { TextArea } = Input;

interface FocusCheckDialogProps {
  visible: boolean;
  onClose: () => void;
  questions: FocusCheckQuestion[];
  sessionId?: string;
}

const FocusCheckDialog: React.FC<FocusCheckDialogProps> = ({
  visible,
  onClose,
  questions,
  sessionId
}) => {
  const [answers, setAnswers] = useState<{ [questionId: string]: any }>({});
  const [currentQuestionIndex, setCurrentQuestionIndex] = useState(0);
  const [isCompleted, setIsCompleted] = useState(false);

  useEffect(() => {
    if (visible) {
      setAnswers({});
      setCurrentQuestionIndex(0);
      setIsCompleted(false);
    }
  }, [visible]);

  const currentQuestion = questions[currentQuestionIndex];
  const progress = ((currentQuestionIndex + 1) / questions.length) * 100;

  const handleAnswer = (questionId: string, answer: any) => {
    setAnswers(prev => ({
      ...prev,
      [questionId]: answer
    }));
  };

  const handleNext = () => {
    if (currentQuestionIndex < questions.length - 1) {
      setCurrentQuestionIndex(prev => prev + 1);
    } else {
      handleComplete();
    }
  };

  const handlePrevious = () => {
    if (currentQuestionIndex > 0) {
      setCurrentQuestionIndex(prev => prev - 1);
    }
  };

  const handleComplete = () => {
    // 提交答案到专注监控服务
    focusMonitorService.handleFocusCheckResult(answers);
    setIsCompleted(true);
    
    // 2秒后自动关闭
    setTimeout(() => {
      onClose();
    }, 2000);
  };

  const renderQuestion = (question: FocusCheckQuestion) => {
    const answer = answers[question.id];

    switch (question.type) {
      case 'rating':
        return (
          <div style={{ textAlign: 'center' }}>
            <div style={{ marginBottom: 16 }}>
              <Text type="secondary">请选择 1-5 分（1分最低，5分最高）</Text>
            </div>
            <Rate
              count={5}
              value={answer}
              onChange={(value) => handleAnswer(question.id, value)}
              style={{ fontSize: 32 }}
            />
            <div style={{ marginTop: 16 }}>
              <Space>
                <Text type="secondary">1分 - 完全无法专注</Text>
                <Text type="secondary">5分 - 高度专注</Text>
              </Space>
            </div>
          </div>
        );

      case 'choice':
        return (
          <Radio.Group
            value={answer}
            onChange={(e) => handleAnswer(question.id, e.target.value)}
            style={{ width: '100%' }}
          >
            <Space direction="vertical" style={{ width: '100%' }}>
              {question.options?.map((option, index) => (
                <Radio key={index} value={option} style={{ 
                  padding: '12px 16px',
                  border: '1px solid #f0f0f0',
                  borderRadius: '8px',
                  width: '100%',
                  margin: '4px 0'
                }}>
                  <Text style={{ fontSize: '14px' }}>{option}</Text>
                </Radio>
              ))}
            </Space>
          </Radio.Group>
        );

      case 'text':
        return (
          <TextArea
            value={answer}
            onChange={(e) => handleAnswer(question.id, e.target.value)}
            placeholder="请描述你的情况..."
            rows={4}
            style={{ fontSize: '14px' }}
          />
        );

      default:
        return null;
    }
  };

  const getFocusScoreColor = (score: number) => {
    if (score >= 80) return '#52c41a';
    if (score >= 60) return '#faad14';
    return '#ff4d4f';
  };

  const getFocusScoreText = (score: number) => {
    if (score >= 80) return '专注状态良好';
    if (score >= 60) return '专注状态一般';
    return '需要调整专注状态';
  };

  if (isCompleted) {
    const currentSession = focusMonitorService.getCurrentSession();
    const focusScore = currentSession?.focusScore || 0;

    return (
      <Modal
        title={null}
        open={visible}
        onCancel={onClose}
        footer={null}
        width={400}
        centered
        closable={false}
      >
        <div style={{ textAlign: 'center', padding: '20px 0' }}>
          <div style={{ fontSize: 48, marginBottom: 16 }}>
            <CheckCircleOutlined style={{ color: '#52c41a' }} />
          </div>
          <Title level={3} style={{ color: '#52c41a', marginBottom: 8 }}>
            检查完成！
          </Title>
          <Text type="secondary" style={{ fontSize: 16, marginBottom: 24, display: 'block' }}>
            感谢你的反馈，继续保持专注！
          </Text>
          
          <div style={{ marginBottom: 16 }}>
            <Text strong>当前专注分数：</Text>
            <div style={{ marginTop: 8 }}>
              <Progress
                type="circle"
                percent={focusScore}
                format={() => `${focusScore}`}
                strokeColor={getFocusScoreColor(focusScore)}
                size={80}
              />
            </div>
            <div style={{ marginTop: 8 }}>
              <Text style={{ color: getFocusScoreColor(focusScore) }}>
                {getFocusScoreText(focusScore)}
              </Text>
            </div>
          </div>
        </div>
      </Modal>
    );
  }

  return (
    <Modal
      title={
        <Space>
          <EyeOutlined />
          <span>专注状态检查</span>
          <Text type="secondary" style={{ fontSize: '12px' }}>
            ({currentQuestionIndex + 1}/{questions.length})
          </Text>
        </Space>
      }
      open={visible}
      onCancel={onClose}
      footer={null}
      width={500}
      centered
    >
      <div style={{ padding: '20px 0' }}>
        {/* 进度条 */}
        <div style={{ marginBottom: 24 }}>
          <Progress 
            percent={progress} 
            strokeColor="#1890ff"
            showInfo={false}
            size="small"
          />
          <div style={{ display: 'flex', justifyContent: 'space-between', marginTop: 4 }}>
            <Text type="secondary" style={{ fontSize: '12px' }}>
              问题 {currentQuestionIndex + 1}
            </Text>
            <Text type="secondary" style={{ fontSize: '12px' }}>
              共 {questions.length} 个问题
            </Text>
          </div>
        </div>

        {/* 当前问题 */}
        <Card style={{ marginBottom: 24 }}>
          <div style={{ marginBottom: 20 }}>
            <Title level={4} style={{ marginBottom: 8 }}>
              {currentQuestion?.question}
            </Title>
            <Text type="secondary" style={{ fontSize: '12px' }}>
              这个问题将帮助我们了解你的专注状态
            </Text>
          </div>
          
          {currentQuestion && renderQuestion(currentQuestion)}
        </Card>

        {/* 专注提示 */}
        <Alert
          message="💡 专注小贴士"
          description={
            currentQuestionIndex === 0 ? "诚实地评估你的专注状态，这将帮助你更好地管理注意力" :
            currentQuestionIndex === 1 ? "如果发现偏离了计划，不要担心，及时调整就好" :
            "记录分心的原因有助于找到改善专注的方法"
          }
          type="info"
          showIcon
          style={{ marginBottom: 24 }}
        />

        {/* 操作按钮 */}
        <div style={{ display: 'flex', justifyContent: 'space-between' }}>
          <Button 
            onClick={handlePrevious}
            disabled={currentQuestionIndex === 0}
          >
            上一题
          </Button>
          
          <Space>
            <Button onClick={onClose}>
              跳过检查
            </Button>
            <Button 
              type="primary"
              onClick={handleNext}
              disabled={!answers[currentQuestion?.id]}
            >
              {currentQuestionIndex === questions.length - 1 ? '完成检查' : '下一题'}
            </Button>
          </Space>
        </div>

        {/* 会话信息 */}
        <div style={{ 
          marginTop: 16, 
          padding: '12px', 
          backgroundColor: '#f9f9f9', 
          borderRadius: '6px',
          fontSize: '12px',
          color: '#666'
        }}>
          <Space>
            <ClockCircleOutlined />
            <Text type="secondary">
              当前专注会话已进行 {Math.round((Date.now() - (focusMonitorService.getCurrentSession()?.startTime.getTime() || Date.now())) / (1000 * 60))} 分钟
            </Text>
          </Space>
        </div>
      </div>
    </Modal>
  );
};

// 专注检查触发器组件
export const FocusCheckTrigger: React.FC = () => {
  const [showDialog, setShowDialog] = useState(false);
  const [questions, setQuestions] = useState<FocusCheckQuestion[]>([]);
  const [sessionId, setSessionId] = useState<string>();

  useEffect(() => {
    // 监听专注检查事件
    const handleFocusCheck = (event: CustomEvent) => {
      setQuestions(event.detail.questions);
      setSessionId(event.detail.sessionId);
      setShowDialog(true);
    };

    window.addEventListener('focus-check-required', handleFocusCheck as EventListener);

    return () => {
      window.removeEventListener('focus-check-required', handleFocusCheck as EventListener);
    };
  }, []);

  return (
    <FocusCheckDialog
      visible={showDialog}
      onClose={() => setShowDialog(false)}
      questions={questions}
      sessionId={sessionId}
    />
  );
};

export default FocusCheckDialog;
