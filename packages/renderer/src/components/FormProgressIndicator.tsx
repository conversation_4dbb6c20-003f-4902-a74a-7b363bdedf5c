import React, { useMemo } from 'react';
import { Progress, Space, Typography } from 'antd';
import { CheckCircleOutlined } from '@ant-design/icons';

const { Text } = Typography;

interface FormProgressIndicatorProps {
  formValues: Record<string, any>;
  requiredFields: string[];
  optionalFields?: string[];
  compact?: boolean;
}

const FormProgressIndicator: React.FC<FormProgressIndicatorProps> = React.memo(({
  formValues,
  requiredFields,
  optionalFields = [],
  compact = false
}) => {
  const progressData = useMemo(() => {
    const getFieldValue = (fieldName: string) => {
      const value = formValues[fieldName];
      if (Array.isArray(value)) {
        return value.length > 0;
      }
      if (typeof value === 'string') {
        return value.trim().length > 0;
      }
      return value !== undefined && value !== null;
    };

    const completedRequired = requiredFields.filter(field => getFieldValue(field)).length;
    const completedOptional = optionalFields.filter(field => getFieldValue(field)).length;

    const requiredProgress = Math.round((completedRequired / requiredFields.length) * 100);
    const totalFields = requiredFields.length + optionalFields.length;
    const totalCompleted = completedRequired + completedOptional;
    const totalProgress = totalFields > 0 ? Math.round((totalCompleted / totalFields) * 100) : 0;

    const incompleteRequired = requiredFields.filter(field => !getFieldValue(field));

    return {
      completedRequired,
      completedOptional,
      requiredProgress,
      totalFields,
      totalCompleted,
      totalProgress,
      incompleteRequired
    };
  }, [formValues, requiredFields, optionalFields]);

  const { completedRequired, completedOptional, requiredProgress, totalFields, totalCompleted, totalProgress, incompleteRequired } = progressData;

  const getProgressColor = (progress: number) => {
    if (progress >= 100) return '#52c41a';
    if (progress >= 80) return '#1890ff';
    if (progress >= 60) return '#faad14';
    return '#ff4d4f';
  };

  if (compact) {
    return (
      <div style={{ marginBottom: 16, padding: '12px 16px', backgroundColor: '#fafafa', borderRadius: '6px' }}>
        <Space direction="vertical" size="small" style={{ width: '100%' }}>
          <div style={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center' }}>
            <Text strong style={{ fontSize: '13px' }}>表单完成度</Text>
            <Text style={{ fontSize: '12px', color: '#666' }}>
              {completedRequired}/{requiredFields.length} 必填项
            </Text>
          </div>
          
          <Progress
            percent={requiredProgress}
            strokeColor={getProgressColor(requiredProgress)}
            size="small"
            showInfo={false}
          />
          
          {requiredProgress === 100 && (
            <div style={{ display: 'flex', alignItems: 'center', justifyContent: 'center' }}>
              <CheckCircleOutlined style={{ color: '#52c41a', marginRight: 4 }} />
              <Text style={{ color: '#52c41a', fontSize: '12px' }}>
                必填项已完成，可以提交了！
              </Text>
            </div>
          )}
        </Space>
      </div>
    );
  }

  return (
    <div style={{ marginBottom: 24, padding: '16px', border: '1px solid #f0f0f0', borderRadius: '8px' }}>
      <Space direction="vertical" size="middle" style={{ width: '100%' }}>
        <div>
          <div style={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', marginBottom: 8 }}>
            <Text strong>必填项完成度</Text>
            <Text style={{ color: getProgressColor(requiredProgress) }}>
              {completedRequired}/{requiredFields.length}
            </Text>
          </div>
          <Progress
            percent={requiredProgress}
            strokeColor={getProgressColor(requiredProgress)}
            size="small"
          />
        </div>

        {optionalFields.length > 0 && (
          <div>
            <div style={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', marginBottom: 8 }}>
              <Text>可选项完成度</Text>
              <Text style={{ color: '#666' }}>
                {completedOptional}/{optionalFields.length}
              </Text>
            </div>
            <Progress
              percent={optionalFields.length > 0 ? Math.round((completedOptional / optionalFields.length) * 100) : 0}
              strokeColor="#d9d9d9"
              size="small"
            />
          </div>
        )}

        <div>
          <div style={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', marginBottom: 8 }}>
            <Text strong>总体完成度</Text>
            <Text style={{ color: getProgressColor(totalProgress) }}>
              {totalCompleted}/{totalFields}
            </Text>
          </div>
          <Progress
            percent={totalProgress}
            strokeColor={getProgressColor(totalProgress)}
            size="small"
          />
        </div>

        {requiredProgress === 100 && (
          <div style={{ 
            padding: '8px 12px', 
            backgroundColor: '#f6ffed', 
            border: '1px solid #b7eb8f',
            borderRadius: '6px',
            textAlign: 'center'
          }}>
            <CheckCircleOutlined style={{ color: '#52c41a', marginRight: 8 }} />
            <Text style={{ color: '#52c41a' }}>
              必填项已完成，可以提交了！
            </Text>
          </div>
        )}

        {incompleteRequired.length > 0 && (
          <div style={{ fontSize: '12px', color: '#999' }}>
            <div>未完成的必填项：</div>
            <ul style={{ margin: '4px 0', paddingLeft: 16 }}>
              {incompleteRequired.map(field => {
                const fieldLabels: Record<string, string> = {
                  name: '目标名称',
                  description: '目标描述',
                  type: '目标类型',
                  whyPower: '核心驱动力',
                  repeatType: '重复周期'
                };
                return (
                  <li key={field}>{fieldLabels[field] || field}</li>
                );
              })}
            </ul>
          </div>
        )}
      </Space>
    </div>
  );
});

export default FormProgressIndicator;
