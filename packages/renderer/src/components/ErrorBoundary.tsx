import React, { Component, ErrorInfo, ReactNode } from 'react';
import { Result, Button, Typography, Card, Collapse, Space } from 'antd';
import { 
  BugOutlined, 
  ReloadOutlined, 
  HomeOutlined,
  WarningOutlined,
  InfoCircleOutlined
} from '@ant-design/icons';
import { errorManager, ErrorType, ErrorSeverity } from '../services/ErrorHandler';

const { Text, Paragraph } = Typography;
const { Panel } = Collapse;

interface Props {
  children: ReactNode;
  fallback?: ReactNode;
  onError?: (error: Error, errorInfo: ErrorInfo) => void;
  showDetails?: boolean;
  level?: 'page' | 'component' | 'widget';
}

interface State {
  hasError: boolean;
  error: Error | null;
  errorInfo: ErrorInfo | null;
  errorId: string | null;
}

export class ErrorBoundary extends Component<Props, State> {
  constructor(props: Props) {
    super(props);
    this.state = {
      hasError: false,
      error: null,
      errorInfo: null,
      errorId: null
    };
  }

  static getDerivedStateFromError(error: Error): Partial<State> {
    return {
      hasError: true,
      error,
      errorId: `error_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`
    };
  }

  componentDidCatch(error: Error, errorInfo: ErrorInfo) {
    this.setState({ errorInfo });

    // 记录错误到错误管理器
    errorManager.handleError({
      type: ErrorType.UNKNOWN,
      severity: ErrorSeverity.HIGH,
      message: error.message,
      details: {
        stack: error.stack,
        componentStack: errorInfo.componentStack,
        errorBoundary: this.props.level || 'component'
      },
      timestamp: Date.now(),
      context: {
        component: 'ErrorBoundary',
        action: 'componentDidCatch'
      }
    });

    // 调用自定义错误处理器
    if (this.props.onError) {
      this.props.onError(error, errorInfo);
    }

    console.error('ErrorBoundary caught an error:', error, errorInfo);
  }

  handleReload = () => {
    window.location.reload();
  };

  handleGoHome = () => {
    window.location.href = '/';
  };

  handleRetry = () => {
    this.setState({
      hasError: false,
      error: null,
      errorInfo: null,
      errorId: null
    });
  };

  getErrorTitle = (): string => {
    const { level = 'component' } = this.props;
    switch (level) {
      case 'page':
        return '页面加载失败';
      case 'component':
        return '组件渲染失败';
      case 'widget':
        return '功能模块异常';
      default:
        return '出现错误';
    }
  };

  getErrorSubTitle = (): string => {
    const { error } = this.state;
    if (!error) return '未知错误';

    const message = error.message.toLowerCase();
    
    if (message.includes('network') || message.includes('fetch')) {
      return '网络连接异常，请检查网络设置';
    } else if (message.includes('timeout')) {
      return '请求超时，请稍后重试';
    } else if (message.includes('permission')) {
      return '权限不足，请联系管理员';
    } else if (message.includes('not found')) {
      return '请求的资源不存在';
    } else {
      return '系统遇到了一个意外错误';
    }
  };

  getErrorActions = (): ReactNode[] => {
    const { level = 'component' } = this.props;
    const actions: ReactNode[] = [];

    // 重试按钮（适用于组件和小部件）
    if (level === 'component' || level === 'widget') {
      actions.push(
        <Button key="retry" type="primary" icon={<ReloadOutlined />} onClick={this.handleRetry}>
          重试
        </Button>
      );
    }

    // 刷新页面按钮
    actions.push(
      <Button key="reload" icon={<ReloadOutlined />} onClick={this.handleReload}>
        刷新页面
      </Button>
    );

    // 返回首页按钮（适用于页面级错误）
    if (level === 'page') {
      actions.push(
        <Button key="home" icon={<HomeOutlined />} onClick={this.handleGoHome}>
          返回首页
        </Button>
      );
    }

    return actions;
  };

  renderErrorDetails = (): ReactNode => {
    const { error, errorInfo, errorId } = this.state;
    const { showDetails = false } = this.props;

    if (!showDetails || !error) return null;

    return (
      <Card size="small" style={{ marginTop: 16, textAlign: 'left' }}>
        <Collapse ghost>
          <Panel 
            header={
              <Space>
                <InfoCircleOutlined />
                <Text type="secondary">错误详情</Text>
              </Space>
            } 
            key="details"
          >
            <div style={{ marginBottom: 12 }}>
              <Text strong>错误ID: </Text>
              <Text code>{errorId}</Text>
            </div>
            
            <div style={{ marginBottom: 12 }}>
              <Text strong>错误消息: </Text>
              <Text type="danger">{error.message}</Text>
            </div>

            <div style={{ marginBottom: 12 }}>
              <Text strong>错误类型: </Text>
              <Text>{error.name}</Text>
            </div>

            <div style={{ marginBottom: 12 }}>
              <Text strong>发生时间: </Text>
              <Text>{new Date().toLocaleString()}</Text>
            </div>

            {error.stack && (
              <div style={{ marginBottom: 12 }}>
                <Text strong>错误堆栈: </Text>
                <Paragraph>
                  <pre style={{ 
                    fontSize: '12px', 
                    backgroundColor: '#f5f5f5', 
                    padding: '8px',
                    borderRadius: '4px',
                    overflow: 'auto',
                    maxHeight: '200px'
                  }}>
                    {error.stack}
                  </pre>
                </Paragraph>
              </div>
            )}

            {errorInfo?.componentStack && (
              <div>
                <Text strong>组件堆栈: </Text>
                <Paragraph>
                  <pre style={{ 
                    fontSize: '12px', 
                    backgroundColor: '#f5f5f5', 
                    padding: '8px',
                    borderRadius: '4px',
                    overflow: 'auto',
                    maxHeight: '200px'
                  }}>
                    {errorInfo.componentStack}
                  </pre>
                </Paragraph>
              </div>
            )}
          </Panel>
        </Collapse>
      </Card>
    );
  };

  render() {
    const { hasError } = this.state;
    const { children, fallback, level = 'component' } = this.props;

    if (hasError) {
      // 如果提供了自定义fallback，使用它
      if (fallback) {
        return fallback;
      }

      // 根据错误级别选择不同的展示方式
      if (level === 'widget') {
        // 小部件级错误 - 简化显示
        return (
          <div style={{ 
            padding: '16px', 
            textAlign: 'center',
            border: '1px dashed #d9d9d9',
            borderRadius: '6px',
            backgroundColor: '#fafafa'
          }}>
            <WarningOutlined style={{ fontSize: '24px', color: '#faad14', marginBottom: '8px' }} />
            <div style={{ marginBottom: '8px' }}>
              <Text type="secondary">功能暂时不可用</Text>
            </div>
            <Button size="small" onClick={this.handleRetry}>
              重试
            </Button>
          </div>
        );
      }

      // 组件级和页面级错误 - 完整显示
      return (
        <div style={{ padding: level === 'page' ? '40px 20px' : '20px' }}>
          <Result
            status="error"
            icon={<BugOutlined />}
            title={this.getErrorTitle()}
            subTitle={this.getErrorSubTitle()}
            extra={this.getErrorActions()}
          />
          {this.renderErrorDetails()}
        </div>
      );
    }

    return children;
  }
}

// 高阶组件包装器
export function withErrorBoundary<P extends object>(
  Component: React.ComponentType<P>,
  errorBoundaryProps?: Omit<Props, 'children'>
) {
  const WrappedComponent = (props: P) => (
    <ErrorBoundary {...errorBoundaryProps}>
      <Component {...props} />
    </ErrorBoundary>
  );

  WrappedComponent.displayName = `withErrorBoundary(${Component.displayName || Component.name})`;
  
  return WrappedComponent;
}

// Hook for functional components
export function useErrorHandler() {
  const handleError = React.useCallback((error: Error, context?: any) => {
    errorManager.handleError(error, {
      component: 'useErrorHandler',
      ...context
    });
  }, []);

  const executeWithErrorHandling = React.useCallback(async <T>(
    operation: () => Promise<T>,
    context?: any
  ): Promise<T | null> => {
    try {
      return await operation();
    } catch (error) {
      handleError(error as Error, context);
      return null;
    }
  }, [handleError]);

  return {
    handleError,
    executeWithErrorHandling
  };
}

export default ErrorBoundary;
