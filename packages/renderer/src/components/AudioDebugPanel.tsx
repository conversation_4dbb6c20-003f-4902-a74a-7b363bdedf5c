import React, { useState, useEffect } from 'react';
import { 
  Card, 
  Button, 
  Space, 
  Typography, 
  <PERSON>, 
  Col, 
  Alert,
  Divider,
  Tag,
  Switch,
  Slider
} from 'antd';
import { 
  SoundOutlined,
  BugOutlined,
  PlayCircleOutlined,
  CheckCircleOutlined,
  CloseCircleOutlined,
  InfoCircleOutlined
} from '@ant-design/icons';
import { AudioService, SoundType } from '../services/AudioService';
import { NotificationService } from '../services/NotificationService';
import { useTheme } from '../contexts/ThemeContext';

const { Text, Title } = Typography;

interface AudioDebugPanelProps {
  className?: string;
}

interface DebugLog {
  timestamp: string;
  level: 'info' | 'warn' | 'error' | 'success';
  message: string;
}

const AudioDebugPanel: React.FC<AudioDebugPanelProps> = ({ className }) => {
  const { theme } = useTheme();
  const [debugLogs, setDebugLogs] = useState<DebugLog[]>([]);
  const [audioStatus, setAudioStatus] = useState<any>(null);
  const [isTestingDirect, setIsTestingDirect] = useState(false);
  const [isTestingNotification, setIsTestingNotification] = useState(false);

  const audioService = AudioService.getInstance();
  const notificationService = NotificationService.getInstance();

  const addLog = (level: DebugLog['level'], message: string) => {
    const timestamp = new Date().toLocaleTimeString();
    setDebugLogs(prev => [...prev.slice(-19), { timestamp, level, message }]);
  };

  const updateAudioStatus = () => {
    const status = audioService.getAudioStatus();
    setAudioStatus(status);
  };

  useEffect(() => {
    updateAudioStatus();
    const interval = setInterval(updateAudioStatus, 2000);
    return () => clearInterval(interval);
  }, []);

  const testDirectAudio = async (soundType: SoundType) => {
    setIsTestingDirect(true);
    addLog('info', `开始测试直接音频播放: ${soundType}`);
    
    try {
      await audioService.playSound(soundType);
      addLog('success', `✅ 直接音频播放成功: ${soundType}`);
    } catch (error) {
      addLog('error', `❌ 直接音频播放失败: ${soundType} - ${error}`);
    } finally {
      setIsTestingDirect(false);
    }
  };

  const testNotificationAudio = async (soundType: SoundType) => {
    setIsTestingNotification(true);
    addLog('info', `开始测试通知音频播放: ${soundType}`);
    
    try {
      await notificationService.sendNotification('音频测试', {
        body: `测试 ${soundType} 音频播放`,
        sound: true,
        soundType,
        duration: 3000
      });
      addLog('success', `✅ 通知音频播放成功: ${soundType}`);
    } catch (error) {
      addLog('error', `❌ 通知音频播放失败: ${soundType} - ${error}`);
    } finally {
      setIsTestingNotification(false);
    }
  };

  const testForceAudio = async (soundType: SoundType) => {
    addLog('info', `开始测试强制音频播放: ${soundType}`);
    
    try {
      await audioService.playNotificationSound(soundType, true);
      addLog('success', `✅ 强制音频播放成功: ${soundType}`);
    } catch (error) {
      addLog('error', `❌ 强制音频播放失败: ${soundType} - ${error}`);
    }
  };

  const clearLogs = () => {
    setDebugLogs([]);
  };

  const getStatusIcon = (status: boolean) => {
    return status ? 
      <CheckCircleOutlined style={{ color: '#52c41a' }} /> : 
      <CloseCircleOutlined style={{ color: '#ff4d4f' }} />;
  };

  const getContextStateColor = (state: string | null) => {
    switch (state) {
      case 'running': return 'green';
      case 'suspended': return 'orange';
      case 'closed': return 'red';
      default: return 'default';
    }
  };

  return (
    <div className={className}>
      <Card 
        className="ultra-fast-card"
        style={{ 
          background: theme.colors.cardBackground,
          border: `1px solid ${theme.colors.border}`,
          borderRadius: '12px'
        }}
      >
        <div style={{ marginBottom: '24px' }}>
          <Title 
            level={4} 
            style={{ 
              margin: '0 0 8px', 
              color: theme.colors.text,
              display: 'flex',
              alignItems: 'center',
              gap: '8px'
            }}
          >
            <BugOutlined style={{ color: theme.colors.primary }} />
            音频系统调试面板
          </Title>
          <Text type="secondary" style={{ color: theme.colors.textSecondary }}>
            诊断和测试音频播放问题，对比不同调用路径的行为差异
          </Text>
        </div>

        <Space direction="vertical" size="large" style={{ width: '100%' }}>
          {/* 音频状态信息 */}
          <div>
            <Title level={5} style={{ color: theme.colors.text, marginBottom: '16px' }}>
              <InfoCircleOutlined style={{ marginRight: '8px' }} />
              音频系统状态
            </Title>
            
            {audioStatus && (
              <Row gutter={[16, 8]}>
                <Col span={6}>
                  <Space>
                    {getStatusIcon(audioStatus.initialized)}
                    <Text>已初始化</Text>
                  </Space>
                </Col>
                <Col span={6}>
                  <Space>
                    {getStatusIcon(audioStatus.webAudioSupported)}
                    <Text>Web Audio支持</Text>
                  </Space>
                </Col>
                <Col span={6}>
                  <Space>
                    {getStatusIcon(audioStatus.htmlAudioSupported)}
                    <Text>HTML5 Audio支持</Text>
                  </Space>
                </Col>
                <Col span={6}>
                  <Space>
                    {getStatusIcon(audioStatus.userInteracted)}
                    <Text>用户已交互</Text>
                  </Space>
                </Col>
                <Col span={12}>
                  <Space>
                    <Text>音频上下文状态:</Text>
                    <Tag color={getContextStateColor(audioStatus.contextState)}>
                      {audioStatus.contextState || 'unknown'}
                    </Tag>
                  </Space>
                </Col>
                <Col span={12}>
                  <Space>
                    <Text>已加载音频:</Text>
                    <Text code>{audioStatus.loadedSounds.length} 个</Text>
                  </Space>
                </Col>
              </Row>
            )}
          </div>

          <Divider />

          {/* 音频测试按钮 */}
          <div>
            <Title level={5} style={{ color: theme.colors.text, marginBottom: '16px' }}>
              <PlayCircleOutlined style={{ marginRight: '8px' }} />
              音频播放测试
            </Title>
            
            <Alert
              message="测试说明"
              description="对比三种不同的音频调用方式，找出实际通知中音频播放失败的原因"
              type="info"
              showIcon
              style={{ marginBottom: '16px' }}
            />

            <Row gutter={[16, 16]}>
              <Col span={8}>
                <Card size="small" title="直接音频播放">
                  <Space direction="vertical" style={{ width: '100%' }}>
                    <Text type="secondary">AudioService.playSound()</Text>
                    <Button 
                      type="primary" 
                      block 
                      loading={isTestingDirect}
                      onClick={() => testDirectAudio('task-complete')}
                    >
                      测试任务完成音
                    </Button>
                    <Button 
                      block 
                      loading={isTestingDirect}
                      onClick={() => testDirectAudio('pomodoro-complete')}
                    >
                      测试番茄钟音
                    </Button>
                  </Space>
                </Card>
              </Col>

              <Col span={8}>
                <Card size="small" title="通知音频播放">
                  <Space direction="vertical" style={{ width: '100%' }}>
                    <Text type="secondary">NotificationService.sendNotification()</Text>
                    <Button 
                      type="primary" 
                      block 
                      loading={isTestingNotification}
                      onClick={() => testNotificationAudio('task-complete')}
                    >
                      测试任务完成通知
                    </Button>
                    <Button 
                      block 
                      loading={isTestingNotification}
                      onClick={() => testNotificationAudio('pomodoro-complete')}
                    >
                      测试番茄钟通知
                    </Button>
                  </Space>
                </Card>
              </Col>

              <Col span={8}>
                <Card size="small" title="强制音频播放">
                  <Space direction="vertical" style={{ width: '100%' }}>
                    <Text type="secondary">playNotificationSound(force=true)</Text>
                    <Button 
                      type="primary" 
                      block 
                      onClick={() => testForceAudio('task-complete')}
                    >
                      强制任务完成音
                    </Button>
                    <Button 
                      block 
                      onClick={() => testForceAudio('pomodoro-complete')}
                    >
                      强制番茄钟音
                    </Button>
                  </Space>
                </Card>
              </Col>
            </Row>
          </div>

          <Divider />

          {/* 调试日志 */}
          <div>
            <div style={{ 
              display: 'flex', 
              justifyContent: 'space-between', 
              alignItems: 'center',
              marginBottom: '16px'
            }}>
              <Title level={5} style={{ color: theme.colors.text, margin: 0 }}>
                <SoundOutlined style={{ marginRight: '8px' }} />
                调试日志
              </Title>
              <Button size="small" onClick={clearLogs}>
                清空日志
              </Button>
            </div>
            
            <div style={{ 
              height: '300px', 
              overflow: 'auto', 
              background: theme.colors.background,
              padding: '12px',
              borderRadius: '6px',
              border: `1px solid ${theme.colors.border}`,
              fontFamily: 'Monaco, Consolas, "Courier New", monospace',
              fontSize: '12px'
            }}>
              {debugLogs.length === 0 ? (
                <Text type="secondary">暂无日志，开始测试以查看详细信息...</Text>
              ) : (
                debugLogs.map((log, index) => (
                  <div 
                    key={index} 
                    style={{ 
                      marginBottom: '4px',
                      color: log.level === 'error' ? '#ff4d4f' : 
                             log.level === 'warn' ? '#faad14' :
                             log.level === 'success' ? '#52c41a' : theme.colors.text
                    }}
                  >
                    <span style={{ color: theme.colors.textSecondary }}>
                      [{log.timestamp}]
                    </span>
                    {' '}
                    {log.message}
                  </div>
                ))
              )}
            </div>
          </div>

          {/* 使用说明 */}
          <Alert
            message="调试指南"
            description={
              <ul style={{ margin: 0, paddingLeft: '20px' }}>
                <li>如果"直接音频播放"成功但"通知音频播放"失败，说明NotificationService调用有问题</li>
                <li>如果"强制音频播放"成功但其他失败，说明音频设置被禁用</li>
                <li>检查浏览器控制台是否有额外的错误信息</li>
                <li>确保音频文件已正确加载（查看"已加载音频"数量）</li>
                <li>如果音频上下文状态为"suspended"，尝试点击页面任意位置激活</li>
              </ul>
            }
            type="warning"
            showIcon
          />
        </Space>
      </Card>
    </div>
  );
};

export default AudioDebugPanel;
