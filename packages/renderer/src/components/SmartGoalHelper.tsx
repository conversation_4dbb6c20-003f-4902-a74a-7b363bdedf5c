import React, { useState, useMemo, useEffect } from 'react';
import { Card, Collapse, Checkbox, Tooltip, Button, Space, Typography } from 'antd';
import { InfoCircleOutlined, CheckCircleOutlined, QuestionCircleOutlined } from '@ant-design/icons';

const { Text, Paragraph } = Typography;
const { Panel } = Collapse;

interface SmartGoalHelperProps {
  goalDescription?: string;
  onDescriptionChange?: (description: string) => void;
  compact?: boolean;
}

interface SmartCriteria {
  specific: boolean;
  measurable: boolean;
  achievable: boolean;
  relevant: boolean;
  timeBound: boolean;
}

const SmartGoalHelper: React.FC<SmartGoalHelperProps> = React.memo(({
  goalDescription = '',
  onDescriptionChange,
  compact = false
}) => {
  const [smartChecklist, setSmartChecklist] = useState<SmartCriteria>({
    specific: false,
    measurable: false,
    achievable: false,
    relevant: false,
    timeBound: false
  });

  const [showHelper, setShowHelper] = useState(false);

  // 自动分析目标描述的SMART程度
  const autoAnalysis = useMemo(() => {
    if (!goalDescription || goalDescription.length < 10) {
      return {
        specific: false,
        measurable: false,
        achievable: false,
        relevant: false,
        timeBound: false,
        suggestions: []
      };
    }

    const text = goalDescription.toLowerCase();
    const suggestions: string[] = [];

    // 检查具体性 (Specific)
    const hasActionWords = /(学习|完成|提高|减少|增加|实现|掌握|获得|建立|改善|开发|创建|写|读|练习)/.test(text);
    const hasSpecificTerms = /(什么|如何|哪里|谁|为什么)/.test(text) || text.length > 50;
    const specific = hasActionWords && hasSpecificTerms;
    if (!specific) {
      suggestions.push('添加具体的行动词汇和详细描述');
    }

    // 检查可衡量性 (Measurable)
    const hasNumbers = /\d/.test(text);
    const hasMetrics = /(个|次|小时|天|周|月|年|公斤|分|页|章|级|分数|百分比|%|倍)/.test(text);
    const measurable = hasNumbers || hasMetrics;
    if (!measurable) {
      suggestions.push('添加具体的数量、时间或可验证的标准');
    }

    // 检查时限性 (Time-bound)
    const hasTimeWords = /(天|周|月|年|小时|分钟|日期|时间|期限|截止|完成|达到|之前|之后|内|后)/.test(text);
    const hasSpecificTime = /(一|二|三|四|五|六|七|八|九|十|\d+)\s*(天|周|月|年|小时)/.test(text);
    const timeBound = hasTimeWords || hasSpecificTime;
    if (!timeBound) {
      suggestions.push('设定明确的时间期限');
    }

    // 检查可实现性 (Achievable) - 基于描述长度和复杂度
    const wordCount = text.length;
    const hasRealisticTerms = /(逐步|渐进|每天|每周|分阶段|循序渐进)/.test(text);
    const achievable = wordCount > 30 && (hasRealisticTerms || !/(全部|所有|完全|彻底|立即|马上)/.test(text));
    if (!achievable) {
      suggestions.push('考虑目标的可实现性，避免过于宏大的表述');
    }

    // 检查相关性 (Relevant) - 基于是否提到意义或价值
    const hasRelevanceWords = /(重要|意义|价值|目的|为了|因为|希望|想要|需要|有助于|提升|改善)/.test(text);
    const relevant = hasRelevanceWords;
    if (!relevant) {
      suggestions.push('说明目标的重要性和相关性');
    }

    return {
      specific,
      measurable,
      achievable,
      relevant,
      timeBound,
      suggestions
    };
  }, [goalDescription]);

  // 当自动分析结果变化时，更新检查清单
  useEffect(() => {
    setSmartChecklist(autoAnalysis);
  }, [autoAnalysis]);

  const smartCriteriaData = useMemo(() => [
    {
      key: 'specific',
      label: 'Specific (具体的)',
      description: '目标是否明确具体？',
      tips: '避免模糊表述，明确说明要做什么、为什么做、如何做',
      examples: ['学习编程 → 学习React前端开发', '提高英语 → 通过雅思7分考试']
    },
    {
      key: 'measurable',
      label: 'Measurable (可衡量的)',
      description: '目标是否可以量化衡量？',
      tips: '设定明确的数字指标或可验证的标准',
      examples: ['减肥 → 减重10公斤', '多读书 → 每月读完2本书']
    },
    {
      key: 'achievable',
      label: 'Achievable (可实现的)',
      description: '目标是否现实可行？',
      tips: '考虑现有资源、能力和时间限制',
      examples: ['一个月学会所有编程语言 → 三个月掌握JavaScript基础']
    },
    {
      key: 'relevant',
      label: 'Relevant (相关的)',
      description: '目标是否与你的价值观和长期规划相关？',
      tips: '确保目标对你的人生或职业发展有意义',
      examples: ['为了跟风学习某技能 → 为了职业发展学习相关技能']
    },
    {
      key: 'timeBound',
      label: 'Time-bound (有时限的)',
      description: '目标是否有明确的时间期限？',
      tips: '设定具体的开始和结束时间',
      examples: ['学会游泳 → 在3个月内学会自由泳']
    }
  ], []);

  const handleChecklistChange = (key: keyof SmartCriteria, checked: boolean) => {
    setSmartChecklist(prev => ({
      ...prev,
      [key]: checked
    }));
  };

  const completionData = useMemo(() => {
    const completed = Object.values(smartChecklist).filter(Boolean).length;
    const rate = Math.round((completed / 5) * 100);
    const color = rate >= 80 ? '#52c41a' : rate >= 60 ? '#faad14' : '#ff4d4f';
    return { completed, rate, color };
  }, [smartChecklist]);

  if (compact) {
    return (
      <div style={{ marginBottom: 8 }}>
        <Space>
          <Tooltip title="SMART原则帮助你创建更好的目标">
            <Button
              type="link"
              size="small"
              icon={<InfoCircleOutlined />}
              onClick={() => setShowHelper(!showHelper)}
            >
              SMART原则指导
            </Button>
          </Tooltip>
          <Text style={{ color: completionData.color, fontSize: '12px' }}>
            完成度: {completionData.rate}%
          </Text>
          {goalDescription && autoAnalysis.suggestions.length > 0 && (
            <Tooltip title={`建议：${autoAnalysis.suggestions.join('；')}`}>
              <Text style={{ color: '#faad14', fontSize: '12px' }}>
                ({autoAnalysis.suggestions.length}条建议)
              </Text>
            </Tooltip>
          )}
        </Space>

        {showHelper && (
          <Card size="small" style={{ marginTop: 8 }}>
            <div style={{ marginBottom: 12 }}>
              <Text strong>SMART原则检查清单</Text>
              <Text type="secondary" style={{ marginLeft: 8, fontSize: '12px' }}>
                {goalDescription ? '自动分析结果' : '手动勾选已满足的条件'}
              </Text>
            </div>

            {/* 自动分析建议 */}
            {goalDescription && autoAnalysis.suggestions.length > 0 && (
              <div style={{
                marginBottom: 12,
                padding: '8px 12px',
                backgroundColor: '#fff7e6',
                border: '1px solid #ffd591',
                borderRadius: '6px'
              }}>
                <Text strong style={{ fontSize: '12px', color: '#d46b08' }}>
                  💡 改进建议：
                </Text>
                <ul style={{ margin: '4px 0 0 0', paddingLeft: 16, fontSize: '11px', color: '#d46b08' }}>
                  {autoAnalysis.suggestions.map((suggestion, index) => (
                    <li key={index}>{suggestion}</li>
                  ))}
                </ul>
              </div>
            )}

            <Space direction="vertical" size="small" style={{ width: '100%' }}>
              {smartCriteriaData.map(criteria => (
                <div key={criteria.key}>
                  <Checkbox
                    checked={smartChecklist[criteria.key as keyof SmartCriteria]}
                    onChange={goalDescription ? undefined : (e) => handleChecklistChange(criteria.key as keyof SmartCriteria, e.target.checked)}
                    disabled={!!goalDescription}
                  >
                    <Text style={{ fontSize: '13px' }}>{criteria.label}</Text>
                  </Checkbox>
                  {smartChecklist[criteria.key as keyof SmartCriteria] && (
                    <CheckCircleOutlined style={{ color: '#52c41a', marginLeft: 8, fontSize: '12px' }} />
                  )}
                  <Tooltip title={criteria.tips}>
                    <QuestionCircleOutlined style={{ marginLeft: 4, color: '#999', fontSize: '12px' }} />
                  </Tooltip>
                </div>
              ))}
            </Space>
          </Card>
        )}
      </div>
    );
  }

  return (
    <Card 
      title={
        <Space>
          <InfoCircleOutlined />
          <span>SMART原则指导</span>
          <Text type="secondary" style={{ fontWeight: 'normal', fontSize: '12px' }}>
            让你的目标更清晰有效
          </Text>
        </Space>
      }
      size="small"
      style={{ marginBottom: 16 }}
    >
      <Paragraph style={{ marginBottom: 16, fontSize: '13px', color: '#666' }}>
        SMART原则帮助你制定更有效的目标。请检查你的目标描述是否符合以下标准：
      </Paragraph>

      <div style={{ marginBottom: 16 }}>
        <div style={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', marginBottom: 8 }}>
          <Text strong>完成度检查</Text>
          <Text style={{ color: completionData.color }}>
            {completionData.rate}% ({completionData.completed}/5)
          </Text>
        </div>
        
        <Space direction="vertical" size="small" style={{ width: '100%' }}>
          {smartCriteriaData.map(criteria => (
            <div key={criteria.key} style={{ 
              padding: '8px 12px', 
              border: '1px solid #f0f0f0', 
              borderRadius: '6px',
              backgroundColor: smartChecklist[criteria.key as keyof SmartCriteria] ? '#f6ffed' : '#fafafa'
            }}>
              <div style={{ display: 'flex', alignItems: 'center', marginBottom: 4 }}>
                <Checkbox
                  checked={smartChecklist[criteria.key as keyof SmartCriteria]}
                  onChange={(e) => handleChecklistChange(criteria.key as keyof SmartCriteria, e.target.checked)}
                >
                  <Text strong style={{ fontSize: '13px' }}>{criteria.label}</Text>
                </Checkbox>
                {smartChecklist[criteria.key as keyof SmartCriteria] && (
                  <CheckCircleOutlined style={{ color: '#52c41a', marginLeft: 8 }} />
                )}
              </div>
              
              <Text style={{ fontSize: '12px', color: '#666', marginLeft: 24 }}>
                {criteria.description}
              </Text>
              
              <Collapse ghost size="small" style={{ marginLeft: 20, marginTop: 4 }} defaultActiveKey={['1']}>
                <Panel
                  header={<Text style={{ fontSize: '11px', color: '#999' }}>查看提示和示例</Text>}
                  key="1"
                  showArrow={false}
                >
                  <div style={{ fontSize: '11px', color: '#666' }}>
                    <div style={{ marginBottom: 4 }}>
                      <Text strong>提示：</Text> {criteria.tips}
                    </div>
                    <div>
                      <Text strong>示例：</Text>
                      <ul style={{ margin: '4px 0', paddingLeft: 16 }}>
                        {criteria.examples.map((example, index) => (
                          <li key={index}>{example}</li>
                        ))}
                      </ul>
                    </div>
                  </div>
                </Panel>
              </Collapse>
            </div>
          ))}
        </Space>
      </div>

      {completionData.rate === 100 && (
        <div style={{ 
          padding: '8px 12px', 
          backgroundColor: '#f6ffed', 
          border: '1px solid #b7eb8f',
          borderRadius: '6px',
          textAlign: 'center'
        }}>
          <CheckCircleOutlined style={{ color: '#52c41a', marginRight: 8 }} />
          <Text style={{ color: '#52c41a', fontSize: '13px' }}>
            太棒了！你的目标符合SMART原则
          </Text>
        </div>
      )}
    </Card>
  );
});

export default SmartGoalHelper;
