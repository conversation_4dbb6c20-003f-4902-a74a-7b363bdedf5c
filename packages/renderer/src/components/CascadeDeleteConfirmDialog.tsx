import React from 'react';
import { Modal, Alert, Typography, Divider, List, Space, Tag, Spin } from 'antd';
import { 
  ExclamationCircleOutlined, 
  DeleteOutlined, 
  FolderOutlined, 
  CheckCircleOutlined, 
  ClockCircleOutlined,
  FileTextOutlined,
  SettingOutlined,
  HistoryOutlined
} from '@ant-design/icons';
import { useTheme } from '../contexts/ThemeContext';

const { Title, Text, Paragraph } = Typography;

export interface DeleteAnalysis {
  goalId: string;
  goalName: string;
  subGoalsCount: number;
  milestonesCount: number;
  tasksCount: number;
  pomodoroSessionsCount: number;
  decompositionSessionsCount: number;
  userModificationsCount: number;
  totalItemsCount: number;
  subGoals: Array<{
    id: string;
    name: string;
    milestonesCount: number;
    tasksCount: number;
  }>;
  milestones: Array<{
    id: string;
    name: string;
    tasksCount: number;
  }>;
  tasks: Array<{
    id: string;
    title: string;
    parentType: string;
    parentId: string;
  }>;
}

export interface CascadeDeleteConfirmDialogProps {
  visible: boolean;
  analysis: DeleteAnalysis | null;
  loading: boolean;
  onConfirm: () => void;
  onCancel: () => void;
}

const CascadeDeleteConfirmDialog: React.FC<CascadeDeleteConfirmDialogProps> = ({
  visible,
  analysis,
  loading,
  onConfirm,
  onCancel
}) => {
  const { theme } = useTheme();

  if (!analysis) {
    return (
      <Modal
        title="分析删除影响"
        open={visible}
        confirmLoading={loading}
        onCancel={onCancel}
        footer={null}
        width={600}
      >
        <div style={{ textAlign: 'center', padding: '40px 0' }}>
          <Spin size="large" />
          <div style={{ marginTop: '16px' }}>
            <Text type="secondary">正在分析删除影响...</Text>
          </div>
        </div>
      </Modal>
    );
  }

  const hasChildren = analysis.totalItemsCount > 1;

  return (
    <Modal
      title={
        <Space>
          <ExclamationCircleOutlined style={{ color: '#ff4d4f' }} />
          确认删除目标
        </Space>
      }
      open={visible}
      onOk={onConfirm}
      onCancel={onCancel}
      confirmLoading={loading}
      okText="确认删除"
      cancelText="取消"
      okButtonProps={{ 
        danger: true,
        icon: <DeleteOutlined />
      }}
      width={700}
      style={{
        top: 50
      }}
    >
      <div style={{ maxHeight: '70vh', overflowY: 'auto' }}>
        {/* 目标基本信息 */}
        <Alert
          message={
            <Space direction="vertical" style={{ width: '100%' }}>
              <Text strong style={{ fontSize: '16px' }}>
                您即将删除目标: "{analysis.goalName}"
              </Text>
              <Text type="secondary">
                此操作不可撤销，请仔细确认。
              </Text>
            </Space>
          }
          type="warning"
          showIcon
          style={{ marginBottom: '20px' }}
        />

        {/* 删除统计 */}
        <div style={{ 
          background: theme.colors.surface, 
          padding: '16px', 
          borderRadius: '8px',
          marginBottom: '20px'
        }}>
          <Title level={5} style={{ margin: '0 0 12px 0', color: theme.colors.text }}>
            📊 删除影响统计
          </Title>
          <div style={{ 
            display: 'grid', 
            gridTemplateColumns: 'repeat(4, 1fr)', 
            gap: '12px' 
          }}>
            <div style={{ textAlign: 'center' }}>
              <div style={{ fontSize: '24px', fontWeight: 'bold', color: theme.colors.primary }}>
                1
              </div>
              <Text type="secondary" style={{ fontSize: '12px' }}>目标</Text>
            </div>
            <div style={{ textAlign: 'center' }}>
              <div style={{ fontSize: '24px', fontWeight: 'bold', color: theme.colors.warning }}>
                {analysis.subGoalsCount}
              </div>
              <Text type="secondary" style={{ fontSize: '12px' }}>子目标</Text>
            </div>
            <div style={{ textAlign: 'center' }}>
              <div style={{ fontSize: '24px', fontWeight: 'bold', color: theme.colors.info }}>
                {analysis.milestonesCount}
              </div>
              <Text type="secondary" style={{ fontSize: '12px' }}>里程碑</Text>
            </div>
            <div style={{ textAlign: 'center' }}>
              <div style={{ fontSize: '24px', fontWeight: 'bold', color: theme.colors.success }}>
                {analysis.tasksCount}
              </div>
              <Text type="secondary" style={{ fontSize: '12px' }}>任务</Text>
            </div>
          </div>
          
          {(analysis.pomodoroSessionsCount > 0 || analysis.decompositionSessionsCount > 0) && (
            <div style={{ 
              display: 'grid', 
              gridTemplateColumns: 'repeat(3, 1fr)', 
              gap: '12px',
              marginTop: '12px',
              paddingTop: '12px',
              borderTop: `1px solid ${theme.colors.border}`
            }}>
              <div style={{ textAlign: 'center' }}>
                <div style={{ fontSize: '18px', fontWeight: 'bold', color: theme.colors.textSecondary }}>
                  {analysis.pomodoroSessionsCount}
                </div>
                <Text type="secondary" style={{ fontSize: '11px' }}>番茄钟记录</Text>
              </div>
              <div style={{ textAlign: 'center' }}>
                <div style={{ fontSize: '18px', fontWeight: 'bold', color: theme.colors.textSecondary }}>
                  {analysis.decompositionSessionsCount}
                </div>
                <Text type="secondary" style={{ fontSize: '11px' }}>AI分解记录</Text>
              </div>
              <div style={{ textAlign: 'center' }}>
                <div style={{ fontSize: '18px', fontWeight: 'bold', color: theme.colors.textSecondary }}>
                  {analysis.userModificationsCount}
                </div>
                <Text type="secondary" style={{ fontSize: '11px' }}>修改记录</Text>
              </div>
            </div>
          )}

          <Divider style={{ margin: '16px 0 12px 0' }} />
          <div style={{ textAlign: 'center' }}>
            <Text strong style={{ fontSize: '16px', color: theme.colors.error }}>
              总计将删除 {analysis.totalItemsCount} 项数据
            </Text>
          </div>
        </div>

        {/* 详细列表 */}
        {hasChildren && (
          <div>
            <Title level={5} style={{ color: theme.colors.text }}>
              📋 详细删除列表
            </Title>
            
            {/* 子目标列表 */}
            {analysis.subGoals.length > 0 && (
              <div style={{ marginBottom: '16px' }}>
                <Text strong style={{ color: theme.colors.warning }}>
                  <FolderOutlined /> 子目标 ({analysis.subGoals.length}个)
                </Text>
                <List
                  size="small"
                  dataSource={analysis.subGoals}
                  renderItem={(subGoal) => (
                    <List.Item style={{ padding: '8px 0' }}>
                      <div style={{ width: '100%' }}>
                        <div style={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center' }}>
                          <Text style={{ color: theme.colors.text }}>{subGoal.name}</Text>
                          <Space>
                            {subGoal.milestonesCount > 0 && (
                              <Tag color="blue" size="small">
                                {subGoal.milestonesCount} 里程碑
                              </Tag>
                            )}
                            {subGoal.tasksCount > 0 && (
                              <Tag color="green" size="small">
                                {subGoal.tasksCount} 任务
                              </Tag>
                            )}
                          </Space>
                        </div>
                      </div>
                    </List.Item>
                  )}
                  style={{ 
                    maxHeight: '150px', 
                    overflowY: 'auto',
                    background: theme.colors.background,
                    border: `1px solid ${theme.colors.border}`,
                    borderRadius: '6px',
                    marginTop: '8px'
                  }}
                />
              </div>
            )}

            {/* 里程碑列表 */}
            {analysis.milestones.length > 0 && (
              <div style={{ marginBottom: '16px' }}>
                <Text strong style={{ color: theme.colors.info }}>
                  <CheckCircleOutlined /> 里程碑 ({analysis.milestones.length}个)
                </Text>
                <List
                  size="small"
                  dataSource={analysis.milestones.slice(0, 5)} // 只显示前5个
                  renderItem={(milestone) => (
                    <List.Item style={{ padding: '6px 0' }}>
                      <div style={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', width: '100%' }}>
                        <Text style={{ color: theme.colors.text }}>{milestone.name}</Text>
                        {milestone.tasksCount > 0 && (
                          <Tag color="green" size="small">
                            {milestone.tasksCount} 任务
                          </Tag>
                        )}
                      </div>
                    </List.Item>
                  )}
                  style={{ 
                    maxHeight: '120px', 
                    overflowY: 'auto',
                    background: theme.colors.background,
                    border: `1px solid ${theme.colors.border}`,
                    borderRadius: '6px',
                    marginTop: '8px'
                  }}
                />
                {analysis.milestones.length > 5 && (
                  <Text type="secondary" style={{ fontSize: '12px' }}>
                    ...还有 {analysis.milestones.length - 5} 个里程碑
                  </Text>
                )}
              </div>
            )}

            {/* 任务列表 */}
            {analysis.tasks.length > 0 && (
              <div style={{ marginBottom: '16px' }}>
                <Text strong style={{ color: theme.colors.success }}>
                  <FileTextOutlined /> 任务 ({analysis.tasks.length}个)
                </Text>
                <List
                  size="small"
                  dataSource={analysis.tasks.slice(0, 8)} // 只显示前8个
                  renderItem={(task) => (
                    <List.Item style={{ padding: '4px 0' }}>
                      <Text style={{ color: theme.colors.text, fontSize: '13px' }}>
                        {task.title}
                      </Text>
                    </List.Item>
                  )}
                  style={{ 
                    maxHeight: '100px', 
                    overflowY: 'auto',
                    background: theme.colors.background,
                    border: `1px solid ${theme.colors.border}`,
                    borderRadius: '6px',
                    marginTop: '8px'
                  }}
                />
                {analysis.tasks.length > 8 && (
                  <Text type="secondary" style={{ fontSize: '12px' }}>
                    ...还有 {analysis.tasks.length - 8} 个任务
                  </Text>
                )}
              </div>
            )}

            {/* 其他数据提示 */}
            {(analysis.pomodoroSessionsCount > 0 || analysis.decompositionSessionsCount > 0 || analysis.userModificationsCount > 0) && (
              <Alert
                message="其他关联数据"
                description={
                  <Space direction="vertical" style={{ width: '100%' }}>
                    {analysis.pomodoroSessionsCount > 0 && (
                      <Text type="secondary">
                        <ClockCircleOutlined /> {analysis.pomodoroSessionsCount} 个番茄钟会话记录
                      </Text>
                    )}
                    {analysis.decompositionSessionsCount > 0 && (
                      <Text type="secondary">
                        <SettingOutlined /> {analysis.decompositionSessionsCount} 个AI分解会话
                      </Text>
                    )}
                    {analysis.userModificationsCount > 0 && (
                      <Text type="secondary">
                        <HistoryOutlined /> {analysis.userModificationsCount} 个用户修改记录
                      </Text>
                    )}
                  </Space>
                }
                type="info"
                showIcon
                style={{ marginTop: '16px' }}
              />
            )}
          </div>
        )}

        {/* 最终确认 */}
        <Alert
          message="⚠️ 最终确认"
          description={
            <div>
              <Paragraph style={{ margin: 0 }}>
                删除操作将立即执行且<Text strong style={{ color: theme.colors.error }}>无法撤销</Text>。
                所有关联的子目标、任务、里程碑和历史记录都将被永久删除。
              </Paragraph>
              <Paragraph style={{ margin: '8px 0 0 0' }}>
                请确保您确实要删除目标 "<Text strong>{analysis.goalName}</Text>" 及其所有关联数据。
              </Paragraph>
            </div>
          }
          type="error"
          showIcon
          style={{ marginTop: '20px' }}
        />
      </div>
    </Modal>
  );
};

export default CascadeDeleteConfirmDialog;