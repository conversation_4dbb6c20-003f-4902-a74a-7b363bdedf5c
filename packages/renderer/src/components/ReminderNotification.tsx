import React, { useState, useEffect } from 'react';
import { notification, <PERSON>ton, Space, Card, Typography, Tag } from 'antd';
import { 
  BellOutlined, 
  ClockCircleOutlined, 
  EyeOutlined,
  PlayCircleOutlined,
  PauseCircleOutlined,
  CloseOutlined
} from '@ant-design/icons';
import { ReminderNotification, reminderService } from '../services/ReminderService';

const { Text } = Typography;

interface ReminderNotificationComponentProps {
  onActionClick?: (action: string, notification: ReminderNotification) => void;
}

const ReminderNotificationComponent: React.FC<ReminderNotificationComponentProps> = ({
  onActionClick
}) => {
  const [notifications, setNotifications] = useState<ReminderNotification[]>([]);

  useEffect(() => {
    // 注册通知监听器
    const unsubscribe = reminderService.onNotification((reminderNotification) => {
      showNotification(reminderNotification);
      
      // 添加到通知列表
      setNotifications(prev => [reminderNotification, ...prev.slice(0, 9)]); // 最多保留10条
    });

    return unsubscribe;
  }, []);

  const showNotification = (reminderNotification: ReminderNotification) => {
    const { type, title, message, actions = [] } = reminderNotification;

    // 获取图标
    const getIcon = () => {
      switch (type) {
        case 'task-deadline':
          return <ClockCircleOutlined style={{ color: '#faad14' }} />;
        case 'focus-pacer':
          return <EyeOutlined style={{ color: '#1890ff' }} />;
        case 'inactivity':
          return <PauseCircleOutlined style={{ color: '#ff4d4f' }} />;
        default:
          return <BellOutlined />;
      }
    };

    // 获取通知类型
    const getNotificationType = () => {
      switch (type) {
        case 'task-deadline':
          return 'warning';
        case 'focus-pacer':
          return 'info';
        case 'inactivity':
          return 'error';
        default:
          return 'info';
      }
    };

    // 创建操作按钮
    const actionButtons = actions.map((action, index) => (
      <Button
        key={index}
        size="small"
        type={index === 0 ? 'primary' : 'default'}
        onClick={() => {
          if (onActionClick) {
            onActionClick(action.action, reminderNotification);
          }
          notification.destroy();
        }}
        style={{ marginRight: 8 }}
      >
        {action.label}
      </Button>
    ));

    // 显示系统通知
    notification.open({
      message: (
        <div style={{ display: 'flex', alignItems: 'center', gap: 8 }}>
          {getIcon()}
          <span style={{ fontWeight: 500 }}>{title}</span>
        </div>
      ),
      description: (
        <div>
          <div style={{ marginBottom: 12 }}>{message}</div>
          {actionButtons.length > 0 && (
            <div>
              {actionButtons}
            </div>
          )}
        </div>
      ),
      type: getNotificationType() as any,
      duration: type === 'task-deadline' ? 0 : 10, // 截止提醒不自动关闭
      placement: 'topRight',
      style: {
        width: 400,
      },
    });

    // 如果支持浏览器通知，也发送浏览器通知
    if ('Notification' in window && Notification.permission === 'granted') {
      new Notification(title, {
        body: message,
        icon: '/icon.png', // 应用图标
        tag: `focusOS-${type}`,
        requireInteraction: type === 'task-deadline', // 截止提醒需要用户交互
      });
    }
  };

  const handleActionClick = (action: string, reminderNotification: ReminderNotification) => {
    if (onActionClick) {
      onActionClick(action, reminderNotification);
    }
  };

  const getTypeColor = (type: string) => {
    switch (type) {
      case 'task-deadline': return 'orange';
      case 'focus-pacer': return 'blue';
      case 'inactivity': return 'red';
      default: return 'default';
    }
  };

  const getTypeText = (type: string) => {
    switch (type) {
      case 'task-deadline': return '截止提醒';
      case 'focus-pacer': return '专注检查';
      case 'inactivity': return '活动提醒';
      default: return '提醒';
    }
  };

  const formatTime = (date: Date) => {
    return date.toLocaleTimeString('zh-CN', {
      hour: '2-digit',
      minute: '2-digit'
    });
  };

  return (
    <div>
      {/* 通知历史列表 */}
      {notifications.length > 0 && (
        <div style={{ 
          position: 'fixed', 
          bottom: 20, 
          right: 20, 
          width: 300,
          maxHeight: 400,
          overflowY: 'auto',
          zIndex: 1000,
          display: 'none' // 默认隐藏，可以通过状态控制显示
        }}>
          <Card 
            title="提醒历史" 
            size="small"
            extra={
              <Button 
                type="text" 
                size="small" 
                icon={<CloseOutlined />}
                onClick={() => setNotifications([])}
              />
            }
          >
            <div style={{ maxHeight: 300, overflowY: 'auto' }}>
              {notifications.map((notif, index) => (
                <div 
                  key={notif.id}
                  style={{ 
                    padding: '8px 0', 
                    borderBottom: index < notifications.length - 1 ? '1px solid #f0f0f0' : 'none'
                  }}
                >
                  <div style={{ display: 'flex', justifyContent: 'space-between', alignItems: 'flex-start' }}>
                    <div style={{ flex: 1 }}>
                      <div style={{ display: 'flex', alignItems: 'center', gap: 4, marginBottom: 4 }}>
                        <Tag color={getTypeColor(notif.type)} size="small">
                          {getTypeText(notif.type)}
                        </Tag>
                        <Text type="secondary" style={{ fontSize: '11px' }}>
                          {formatTime(notif.timestamp)}
                        </Text>
                      </div>
                      <div style={{ fontSize: '12px', fontWeight: 500, marginBottom: 2 }}>
                        {notif.title}
                      </div>
                      <div style={{ fontSize: '11px', color: '#666', lineHeight: '1.3' }}>
                        {notif.message}
                      </div>
                    </div>
                  </div>
                  
                  {notif.actions && notif.actions.length > 0 && (
                    <div style={{ marginTop: 6 }}>
                      <Space size={4}>
                        {notif.actions.slice(0, 2).map((action, actionIndex) => (
                          <Button
                            key={actionIndex}
                            size="small"
                            type="link"
                            style={{ padding: '0 4px', height: 'auto', fontSize: '10px' }}
                            onClick={() => handleActionClick(action.action, notif)}
                          >
                            {action.label}
                          </Button>
                        ))}
                      </Space>
                    </div>
                  )}
                </div>
              ))}
            </div>
          </Card>
        </div>
      )}
    </div>
  );
};

// 请求浏览器通知权限
export const requestNotificationPermission = async (): Promise<boolean> => {
  if (!('Notification' in window)) {
    console.warn('此浏览器不支持桌面通知');
    return false;
  }

  if (Notification.permission === 'granted') {
    return true;
  }

  if (Notification.permission === 'denied') {
    return false;
  }

  const permission = await Notification.requestPermission();
  return permission === 'granted';
};

// 提醒设置组件
export const ReminderSettings: React.FC<{
  onConfigChange?: (config: any) => void;
}> = ({ onConfigChange }) => {
  const [config, setConfig] = useState(reminderService.getConfig());

  const handleConfigUpdate = (updates: any) => {
    const newConfig = { ...config, ...updates };
    setConfig(newConfig);
    reminderService.updateConfig(updates);
    
    if (onConfigChange) {
      onConfigChange(newConfig);
    }
  };

  return (
    <Card title="提醒设置" size="small">
      <div style={{ display: 'flex', flexDirection: 'column', gap: 16 }}>
        {/* 任务截止提醒 */}
        <div>
          <div style={{ display: 'flex', alignItems: 'center', gap: 8, marginBottom: 8 }}>
            <input
              type="checkbox"
              checked={config.taskDeadlineReminder}
              onChange={(e) => handleConfigUpdate({ taskDeadlineReminder: e.target.checked })}
            />
            <span>任务截止提醒</span>
          </div>
          {config.taskDeadlineReminder && (
            <div style={{ marginLeft: 24 }}>
              <label>
                提前
                <input
                  type="number"
                  value={config.taskDeadlineAdvanceMinutes}
                  onChange={(e) => handleConfigUpdate({ taskDeadlineAdvanceMinutes: parseInt(e.target.value) })}
                  style={{ width: 60, margin: '0 4px' }}
                  min="1"
                  max="1440"
                />
                分钟提醒
              </label>
            </div>
          )}
        </div>

        {/* 专注检查 */}
        <div>
          <div style={{ display: 'flex', alignItems: 'center', gap: 8, marginBottom: 8 }}>
            <input
              type="checkbox"
              checked={config.focusPacerEnabled}
              onChange={(e) => handleConfigUpdate({ focusPacerEnabled: e.target.checked })}
            />
            <span>专注检查 (Focus Pacer)</span>
          </div>
          {config.focusPacerEnabled && (
            <div style={{ marginLeft: 24 }}>
              <label>
                每
                <input
                  type="number"
                  value={config.focusPacerInterval}
                  onChange={(e) => handleConfigUpdate({ focusPacerInterval: parseInt(e.target.value) })}
                  style={{ width: 60, margin: '0 4px' }}
                  min="5"
                  max="120"
                />
                分钟检查一次
              </label>
            </div>
          )}
        </div>

        {/* 不活跃提醒 */}
        <div>
          <div style={{ display: 'flex', alignItems: 'center', gap: 8, marginBottom: 8 }}>
            <input
              type="checkbox"
              checked={config.inactivityReminderEnabled}
              onChange={(e) => handleConfigUpdate({ inactivityReminderEnabled: e.target.checked })}
            />
            <span>不活跃提醒</span>
          </div>
          {config.inactivityReminderEnabled && (
            <div style={{ marginLeft: 24 }}>
              <label>
                超过
                <input
                  type="number"
                  value={config.inactivityThresholdMinutes}
                  onChange={(e) => handleConfigUpdate({ inactivityThresholdMinutes: parseInt(e.target.value) })}
                  style={{ width: 60, margin: '0 4px' }}
                  min="5"
                  max="60"
                />
                分钟无活动时提醒
              </label>
            </div>
          )}
        </div>
      </div>
    </Card>
  );
};

export default ReminderNotificationComponent;
