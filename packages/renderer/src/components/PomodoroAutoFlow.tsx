import React, { useState, useEffect } from 'react';
import { Modal, Button, Space, Typography, Progress, Card, Tag, Alert } from 'antd';
import {
  PlayCircleOutlined,
  PauseCircleOutlined,
  StepForwardOutlined,
  ClockCircleOutlined,
  CoffeeOutlined,
  CheckCircleOutlined
} from '@ant-design/icons';

const { Title, Text } = Typography;

interface PomodoroAutoFlowProps {
  visible: boolean;
  sessionType: 'work' | 'short-break' | 'long-break';
  remainingTime: number;
  totalTime: number;
  sessionCount: number;
  onStart: () => void;
  onSkip: () => void;
  onClose: () => void;
  autoStartEnabled: boolean;
  autoStartDelay: number; // 自动开始延迟秒数
}

const PomodoroAutoFlow: React.FC<PomodoroAutoFlowProps> = ({
  visible,
  sessionType,
  remainingTime,
  totalTime,
  sessionCount,
  onStart,
  onSkip,
  onClose,
  autoStartEnabled,
  autoStartDelay = 5
}) => {
  const [autoStartCountdown, setAutoStartCountdown] = useState(autoStartDelay);
  const [isAutoStarting, setIsAutoStarting] = useState(false);

  // 自动开始倒计时
  useEffect(() => {
    if (visible && autoStartEnabled && !isAutoStarting) {
      setAutoStartCountdown(autoStartDelay);
      setIsAutoStarting(true);
      
      const interval = setInterval(() => {
        setAutoStartCountdown(prev => {
          if (prev <= 1) {
            clearInterval(interval);
            onStart();
            return 0;
          }
          return prev - 1;
        });
      }, 1000);

      return () => {
        clearInterval(interval);
        setIsAutoStarting(false);
      };
    }
  }, [visible, autoStartEnabled, autoStartDelay, onStart]);

  const cancelAutoStart = () => {
    setIsAutoStarting(false);
    setAutoStartCountdown(autoStartDelay);
  };

  const getSessionInfo = () => {
    switch (sessionType) {
      case 'work':
        return {
          title: '🎯 工作时间',
          description: '专注完成你的任务',
          color: '#ff4d4f',
          icon: <ClockCircleOutlined />,
          tips: [
            '关闭不必要的通知和干扰',
            '专注于单一任务',
            '如果有想法，快速记录后继续专注'
          ]
        };
      case 'short-break':
        return {
          title: '☕ 短休息',
          description: '放松一下，为下一个番茄做准备',
          color: '#52c41a',
          icon: <CoffeeOutlined />,
          tips: [
            '站起来走动走动',
            '做一些简单的伸展运动',
            '喝点水或看看远方'
          ]
        };
      case 'long-break':
        return {
          title: '🌟 长休息',
          description: '好好休息，恢复精力',
          color: '#1890ff',
          icon: <CheckCircleOutlined />,
          tips: [
            '离开工作区域',
            '做一些放松的活动',
            '回顾刚才的工作成果'
          ]
        };
      default:
        return {
          title: '番茄时间',
          description: '',
          color: '#666',
          icon: <ClockCircleOutlined />,
          tips: []
        };
    }
  };

  const sessionInfo = getSessionInfo();
  const formatTime = (seconds: number) => {
    const minutes = Math.floor(seconds / 60);
    const remainingSeconds = seconds % 60;
    return `${minutes.toString().padStart(2, '0')}:${remainingSeconds.toString().padStart(2, '0')}`;
  };

  const getMotivationalMessage = () => {
    const messages = {
      work: [
        '专注是成功的关键！',
        '每一个番茄都让你更接近目标',
        '保持专注，你正在做得很好！',
        '深呼吸，专注当下的任务'
      ],
      'short-break': [
        '短暂的休息让大脑更清晰',
        '放松一下，你值得这个休息',
        '休息是为了走更远的路',
        '给自己一个小小的奖励'
      ],
      'long-break': [
        '恭喜完成一轮番茄！',
        '你的专注力正在提升',
        '好好休息，为下一轮做准备',
        '回顾一下刚才的成就'
      ]
    };
    
    const typeMessages = messages[sessionType] || messages.work;
    return typeMessages[Math.floor(Math.random() * typeMessages.length)];
  };

  return (
    <Modal
      title={null}
      open={visible}
      onCancel={onClose}
      footer={null}
      width={500}
      centered
      closable={false}
      maskClosable={false}
    >
      <div style={{ textAlign: 'center', padding: '20px 0' }}>
        {/* 会话类型标题 */}
        <div style={{ marginBottom: 24 }}>
          <div style={{ fontSize: 48, marginBottom: 8 }}>
            {sessionInfo.icon}
          </div>
          <Title level={2} style={{ color: sessionInfo.color, margin: 0 }}>
            {sessionInfo.title}
          </Title>
          <Text type="secondary" style={{ fontSize: 16 }}>
            {sessionInfo.description}
          </Text>
        </div>

        {/* 时间显示 */}
        <div style={{ marginBottom: 24 }}>
          <Title level={1} style={{ 
            fontSize: 48, 
            margin: 0, 
            color: sessionInfo.color,
            fontFamily: 'monospace'
          }}>
            {formatTime(totalTime * 60)}
          </Title>
          <div style={{ marginTop: 8 }}>
            <Tag color={sessionInfo.color} style={{ fontSize: 14, padding: '4px 12px' }}>
              第 {sessionCount + 1} 个番茄
            </Tag>
          </div>
        </div>

        {/* 激励信息 */}
        <Alert
          message={getMotivationalMessage()}
          type="info"
          showIcon
          style={{ marginBottom: 24 }}
        />

        {/* 小贴士 */}
        <Card size="small" style={{ marginBottom: 24, textAlign: 'left' }}>
          <Text strong>💡 小贴士：</Text>
          <ul style={{ margin: '8px 0 0 0', paddingLeft: 20 }}>
            {sessionInfo.tips.map((tip, index) => (
              <li key={index} style={{ marginBottom: 4 }}>
                <Text type="secondary">{tip}</Text>
              </li>
            ))}
          </ul>
        </Card>

        {/* 自动开始倒计时 */}
        {isAutoStarting && (
          <div style={{ marginBottom: 24 }}>
            <Progress
              type="circle"
              percent={(autoStartDelay - autoStartCountdown) / autoStartDelay * 100}
              format={() => autoStartCountdown}
              size={80}
              strokeColor={sessionInfo.color}
            />
            <div style={{ marginTop: 8 }}>
              <Text type="secondary">
                {autoStartCountdown} 秒后自动开始
              </Text>
            </div>
          </div>
        )}

        {/* 操作按钮 */}
        <Space size="large">
          {isAutoStarting ? (
            <>
              <Button size="large" onClick={cancelAutoStart}>
                取消自动开始
              </Button>
              <Button 
                type="primary" 
                size="large" 
                icon={<PlayCircleOutlined />}
                onClick={() => {
                  cancelAutoStart();
                  onStart();
                }}
              >
                立即开始
              </Button>
            </>
          ) : (
            <>
              <Button
                size="large"
                icon={<StepForwardOutlined />}
                onClick={onSkip}
              >
                跳过
              </Button>
              <Button 
                type="primary" 
                size="large" 
                icon={<PlayCircleOutlined />}
                onClick={onStart}
              >
                开始 {sessionInfo.title}
              </Button>
            </>
          )}
        </Space>

        {/* 会话进度指示器 */}
        <div style={{ marginTop: 24, padding: '16px 0', borderTop: '1px solid #f0f0f0' }}>
          <Text type="secondary" style={{ fontSize: 12 }}>
            今日进度
          </Text>
          <div style={{ marginTop: 8 }}>
            {Array.from({ length: Math.max(sessionCount, 4) }, (_, index) => (
              <span
                key={index}
                style={{
                  display: 'inline-block',
                  width: 12,
                  height: 12,
                  borderRadius: '50%',
                  backgroundColor: index < sessionCount ? sessionInfo.color : '#f0f0f0',
                  margin: '0 4px',
                  border: index === sessionCount ? `2px solid ${sessionInfo.color}` : 'none'
                }}
              />
            ))}
          </div>
        </div>
      </div>
    </Modal>
  );
};

// 番茄钟完成庆祝组件
export const PomodoroCompletionCelebration: React.FC<{
  visible: boolean;
  sessionType: 'work' | 'short-break' | 'long-break';
  sessionCount: number;
  onNext: () => void;
  onClose: () => void;
}> = ({ visible, sessionType, sessionCount, onNext, onClose }) => {
  const getCelebrationContent = () => {
    if (sessionType === 'work') {
      return {
        emoji: '🎉',
        title: '番茄完成！',
        message: '恭喜你完成了一个专注时段！',
        color: '#52c41a'
      };
    } else {
      return {
        emoji: '✨',
        title: '休息结束！',
        message: '休息时间结束，准备开始新的专注！',
        color: '#1890ff'
      };
    }
  };

  const content = getCelebrationContent();

  return (
    <Modal
      title={null}
      open={visible}
      onCancel={onClose}
      footer={null}
      width={400}
      centered
      closable={false}
    >
      <div style={{ textAlign: 'center', padding: '20px 0' }}>
        <div style={{ fontSize: 64, marginBottom: 16 }}>
          {content.emoji}
        </div>
        <Title level={2} style={{ color: content.color, marginBottom: 8 }}>
          {content.title}
        </Title>
        <Text type="secondary" style={{ fontSize: 16, marginBottom: 24, display: 'block' }}>
          {content.message}
        </Text>
        
        <div style={{ marginBottom: 24 }}>
          <Tag color={content.color} style={{ fontSize: 14, padding: '4px 12px' }}>
            今日第 {sessionCount} 个番茄
          </Tag>
        </div>

        <Space>
          <Button onClick={onClose}>
            稍后继续
          </Button>
          <Button type="primary" onClick={onNext}>
            继续下一个
          </Button>
        </Space>
      </div>
    </Modal>
  );
};

export default PomodoroAutoFlow;
