import React, { useState, useEffect } from 'react';
import { Card, Switch, Slider, Select, Button, Space, Typography, Alert, Row, Col, Divider } from 'antd';
import { 
  SoundOutlined, 
  PlayCircleOutlined, 
  CheckCircleOutlined,
  BellOutlined,
  SettingOutlined,
  InfoCircleOutlined
} from '@ant-design/icons';
import { AudioService, SoundType, AudioSettings as AudioSettingsType } from '../services/AudioService';
import { useTheme } from '../contexts/ThemeContext';

const { Text, Title } = Typography;
const { Option } = Select;

interface AudioSettingsProps {
  className?: string;
}

const AudioSettings: React.FC<AudioSettingsProps> = ({ className }) => {
  const { theme } = useTheme();
  const [audioService] = useState(() => AudioService.getInstance());
  const [settings, setSettings] = useState<AudioSettingsType>(audioService.getSettings());
  const [audioStatus, setAudioStatus] = useState(audioService.getAudioStatus());
  const [testingSound, setTestingSound] = useState<SoundType | null>(null);

  // 音效类型配置
  const soundTypes: { key: SoundType; label: string; description: string; icon: React.ReactNode }[] = [
    {
      key: 'task-complete',
      label: '任务完成',
      description: '完成任务时播放',
      icon: <CheckCircleOutlined style={{ color: '#52c41a' }} />
    },
    {
      key: 'pomodoro-complete',
      label: '番茄钟完成',
      description: '工作时段结束时播放',
      icon: <BellOutlined style={{ color: '#fa8c16' }} />
    },
    {
      key: 'break-complete',
      label: '休息结束',
      description: '休息时间结束时播放',
      icon: <PlayCircleOutlined style={{ color: '#1890ff' }} />
    },
    {
      key: 'goal-achieved',
      label: '目标达成',
      description: '重要目标完成时播放',
      icon: <CheckCircleOutlined style={{ color: '#722ed1' }} />
    },
    {
      key: 'system-alert',
      label: '系统提示',
      description: '重要系统通知时播放',
      icon: <InfoCircleOutlined style={{ color: '#ff4d4f' }} />
    },
    {
      key: 'gentle-reminder',
      label: '轻柔提醒',
      description: '一般提醒时播放',
      icon: <SoundOutlined style={{ color: '#13c2c2' }} />
    }
  ];

  // 音效主题选项
  const soundThemes = [
    { value: 'default', label: '默认音效', description: '清晰简洁的提示音' },
    { value: 'gentle', label: '轻柔音效', description: '温和舒缓的提示音' },
    { value: 'energetic', label: '活力音效', description: '明快有力的提示音' }
  ];

  useEffect(() => {
    // 初始化音频服务
    const initAudio = async () => {
      try {
        await audioService.initialize();
        setAudioStatus(audioService.getAudioStatus());
      } catch (error) {
        console.warn('Audio service initialization failed:', error);
      }
    };

    initAudio();
  }, [audioService]);

  // 更新设置
  const handleSettingsChange = (newSettings: Partial<AudioSettingsType>) => {
    const updatedSettings = { ...settings, ...newSettings };
    setSettings(updatedSettings);
    audioService.updateSettings(updatedSettings);
  };

  // 测试播放音效
  const handleTestSound = async (soundType: SoundType) => {
    if (testingSound) return; // 防止重复点击

    setTestingSound(soundType);
    try {
      await audioService.testSound(soundType);
    } catch (error) {
      console.warn('Failed to test sound:', error);
    } finally {
      setTimeout(() => setTestingSound(null), 1000); // 1秒后重置状态
    }
  };

  // 音量标记
  const volumeMarks = {
    0: '静音',
    25: '25%',
    50: '50%',
    75: '75%',
    100: '100%'
  };

  return (
    <div className={className}>
      <Card 
        className="ultra-fast-card"
        style={{ 
          background: theme.colors.cardBackground,
          border: `1px solid ${theme.colors.border}`,
          borderRadius: '12px'
        }}
      >
        <div style={{ marginBottom: '24px' }}>
          <Title 
            level={4} 
            style={{ 
              margin: '0 0 8px', 
              color: theme.colors.text,
              display: 'flex',
              alignItems: 'center',
              gap: '8px'
            }}
          >
            <SoundOutlined style={{ color: theme.colors.primary }} />
            音频设置
          </Title>
          <Text type="secondary" style={{ color: theme.colors.textSecondary }}>
            配置通知提示音和音频播放选项
          </Text>
        </div>

        {/* 音频状态检查 */}
        {!audioStatus.webAudioSupported && !audioStatus.htmlAudioSupported && (
          <Alert
            message="音频不支持"
            description="您的浏览器不支持音频播放功能，提示音将无法正常工作。"
            type="error"
            showIcon
            style={{ marginBottom: '16px' }}
          />
        )}

        {!audioStatus.userInteracted && audioStatus.webAudioSupported && (
          <Alert
            message="需要用户交互"
            description="由于浏览器安全策略，请先点击页面任意位置以启用音频功能。"
            type="warning"
            showIcon
            style={{ marginBottom: '16px' }}
          />
        )}

        <Space direction="vertical" size="large" style={{ width: '100%' }}>
          {/* 基础设置 */}
          <div>
            <Title level={5} style={{ color: theme.colors.text, marginBottom: '16px' }}>
              基础设置
            </Title>
            
            <Row gutter={[16, 16]}>
              <Col span={12}>
                <div style={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center' }}>
                  <Text style={{ color: theme.colors.text }}>启用提示音</Text>
                  <Switch
                    checked={settings.enabled}
                    onChange={(enabled) => handleSettingsChange({ enabled })}
                  />
                </div>
              </Col>
            </Row>
          </div>

          {/* 音量控制 */}
          {settings.enabled && (
            <div>
              <Title level={5} style={{ color: theme.colors.text, marginBottom: '16px' }}>
                音量控制
              </Title>
              
              <div style={{ padding: '0 8px' }}>
                <Slider
                  min={0}
                  max={100}
                  step={5}
                  value={Math.round(settings.volume * 100)}
                  onChange={(value) => handleSettingsChange({ volume: value / 100 })}
                  marks={volumeMarks}
                  tooltip={{ formatter: (value) => `${value}%` }}
                />
              </div>
            </div>
          )}

          {/* 音效主题 */}
          {settings.enabled && (
            <div>
              <Title level={5} style={{ color: theme.colors.text, marginBottom: '16px' }}>
                音效主题
              </Title>
              
              <Select
                style={{ width: '100%' }}
                value={settings.soundTheme}
                onChange={(soundTheme) => handleSettingsChange({ soundTheme })}
              >
                {soundThemes.map(theme => (
                  <Option key={theme.value} value={theme.value}>
                    <div>
                      <div style={{ fontWeight: 500 }}>{theme.label}</div>
                      <div style={{ fontSize: '12px', color: '#666' }}>{theme.description}</div>
                    </div>
                  </Option>
                ))}
              </Select>
            </div>
          )}

          <Divider style={{ margin: '16px 0' }} />

          {/* 音效预览 */}
          {settings.enabled && (
            <div>
              <Title level={5} style={{ color: theme.colors.text, marginBottom: '16px' }}>
                音效预览
              </Title>
              
              <div style={{ display: 'grid', gridTemplateColumns: 'repeat(auto-fit, minmax(280px, 1fr))', gap: '12px' }}>
                {soundTypes.map(({ key, label, description, icon }) => (
                  <div
                    key={key}
                    style={{
                      display: 'flex',
                      alignItems: 'center',
                      justifyContent: 'space-between',
                      padding: '12px 16px',
                      background: theme.colors.background,
                      border: `1px solid ${theme.colors.border}`,
                      borderRadius: '8px',
                    }}
                  >
                    <div style={{ display: 'flex', alignItems: 'center', gap: '12px', flex: 1 }}>
                      {icon}
                      <div>
                        <div style={{ fontWeight: 500, color: theme.colors.text }}>{label}</div>
                        <div style={{ fontSize: '12px', color: theme.colors.textSecondary }}>
                          {description}
                        </div>
                      </div>
                    </div>
                    
                    <Button
                      type="text"
                      icon={<PlayCircleOutlined />}
                      loading={testingSound === key}
                      onClick={() => handleTestSound(key)}
                      style={{ 
                        color: theme.colors.primary,
                        border: 'none'
                      }}
                    >
                      试听
                    </Button>
                  </div>
                ))}
              </div>
            </div>
          )}

          {/* 音频状态信息 */}
          <div>
            <Title level={5} style={{ color: theme.colors.text, marginBottom: '16px' }}>
              音频状态
            </Title>
            
            <div style={{ 
              padding: '12px 16px', 
              background: theme.colors.background,
              border: `1px solid ${theme.colors.border}`,
              borderRadius: '8px'
            }}>
              <Row gutter={[16, 8]}>
                <Col span={12}>
                  <Text type="secondary">Web Audio API: </Text>
                  <Text style={{ color: audioStatus.webAudioSupported ? '#52c41a' : '#ff4d4f' }}>
                    {audioStatus.webAudioSupported ? '支持' : '不支持'}
                  </Text>
                </Col>
                <Col span={12}>
                  <Text type="secondary">HTML5 Audio: </Text>
                  <Text style={{ color: audioStatus.htmlAudioSupported ? '#52c41a' : '#ff4d4f' }}>
                    {audioStatus.htmlAudioSupported ? '支持' : '不支持'}
                  </Text>
                </Col>
                <Col span={12}>
                  <Text type="secondary">已加载音效: </Text>
                  <Text>{audioStatus.loadedSounds.length} / {soundTypes.length}</Text>
                </Col>
                <Col span={12}>
                  <Text type="secondary">用户交互: </Text>
                  <Text style={{ color: audioStatus.userInteracted ? '#52c41a' : '#faad14' }}>
                    {audioStatus.userInteracted ? '已激活' : '待激活'}
                  </Text>
                </Col>
              </Row>
            </div>
          </div>
        </Space>
      </Card>
    </div>
  );
};

export default AudioSettings;
