import React, { ReactNode, CSSProperties } from 'react';
import { useTheme } from '../../contexts/ThemeContext';

interface GlassCardProps {
  children: ReactNode;
  className?: string;
  style?: CSSProperties;
  hoverable?: boolean;
  pressable?: boolean;
  padding?: number | string;
  blur?: string;
  opacity?: number;
  onClick?: () => void;
}

export const GlassCard: React.FC<GlassCardProps> = ({
  children,
  className = '',
  style = {},
  hoverable = false,
  pressable = false,
  padding = '24px',
  blur = 'blur(20px)',
  opacity = 0.15,
  onClick,
}) => {
  const { theme } = useTheme();

  const baseStyle: CSSProperties = {
    background: `rgba(255, 255, 255, ${opacity})`,
    border: theme.glassMorphism.border,
    borderRadius: theme.glassMorphism.borderRadius,
    boxShadow: theme.glassMorphism.boxShadow,
    padding,
    position: 'relative',
    overflow: 'hidden',
    ...style,
  };

  const hoverStyle: CSSProperties = hoverable ? {
    boxShadow: `0 12px 40px ${theme.colors.shadow}, inset 0 1px 0 rgba(255, 255, 255, 0.6)`,
    background: `rgba(255, 255, 255, ${opacity + 0.05})`,
  } : {};

  const pressStyle: CSSProperties = pressable ? {
    boxShadow: `0 4px 16px ${theme.colors.shadowLight}, inset 0 1px 0 rgba(255, 255, 255, 0.3)`,
  } : {};

  return (
    <div
      className={`glass-no-blur force-gpu ${hoverable ? 'fast-button' : ''} ${className}`}
      style={baseStyle}
      onClick={onClick}
      onMouseEnter={(e) => {
        if (hoverable) {
          Object.assign(e.currentTarget.style, hoverStyle);
        }
      }}
      onMouseLeave={(e) => {
        if (hoverable) {
          Object.assign(e.currentTarget.style, baseStyle);
        }
      }}
      onMouseDown={(e) => {
        if (pressable) {
          Object.assign(e.currentTarget.style, { ...baseStyle, ...pressStyle });
        }
      }}
      onMouseUp={(e) => {
        if (pressable) {
          Object.assign(e.currentTarget.style, hoverable ? hoverStyle : baseStyle);
        }
      }}
    >
      {/* 反光效果 */}
      <div
        style={{
          position: 'absolute',
          top: 0,
          left: 0,
          right: 0,
          height: '1px',
          background: 'linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.8), transparent)',
          zIndex: 1,
        }}
      />
      
      {/* 内容 */}
      <div style={{ position: 'relative', zIndex: 2 }}>
        {children}
      </div>
    </div>
  );
};