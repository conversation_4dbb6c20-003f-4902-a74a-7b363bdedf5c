import React, { ReactNode, CSSProperties } from 'react';
import { useTheme } from '../../contexts/ThemeContext';

interface GlassButtonProps {
  children: ReactNode;
  type?: 'primary' | 'secondary' | 'ghost';
  size?: 'small' | 'medium' | 'large';
  disabled?: boolean;
  loading?: boolean;
  icon?: ReactNode;
  className?: string;
  style?: CSSProperties;
  onClick?: () => void;
}

export const GlassButton: React.FC<GlassButtonProps> = ({
  children,
  type = 'secondary',
  size = 'medium',
  disabled = false,
  loading = false,
  icon,
  className = '',
  style = {},
  onClick,
}) => {
  const { theme } = useTheme();

  const sizeStyles = {
    small: { padding: '8px 16px', fontSize: '14px', height: '32px' },
    medium: { padding: '12px 24px', fontSize: '16px', height: '40px' },
    large: { padding: '16px 32px', fontSize: '18px', height: '48px' },
  };

  const getTypeStyles = (): CSSProperties => {
    switch (type) {
      case 'primary':
        return {
          background: `linear-gradient(135deg, ${theme.colors.primary}, ${theme.colors.primaryHover})`,
          color: 'white',
          border: `1px solid ${theme.colors.primary}`,
          boxShadow: `0 8px 32px ${theme.colors.shadow}, inset 0 1px 0 rgba(255, 255, 255, 0.3)`,
        };
      case 'ghost':
        return {
          background: 'transparent',
          color: theme.colors.primary,
          border: `1px solid ${theme.colors.border}`,
          boxShadow: 'none',
        };
      default: // secondary
        return {
          background: `rgba(255, 255, 255, 0.2)`,
          color: theme.colors.text,
          border: theme.glassMorphism.border,
          boxShadow: theme.glassMorphism.boxShadow,
        };
    }
  };

  const baseStyle: CSSProperties = {
    ...sizeStyles[size],
    ...getTypeStyles(),
    borderRadius: '12px',
    cursor: disabled ? 'not-allowed' : 'pointer',
    display: 'inline-flex',
    alignItems: 'center',
    justifyContent: 'center',
    gap: icon ? '8px' : '0',
    fontWeight: 600,
    position: 'relative',
    overflow: 'hidden',
    opacity: disabled ? 0.6 : 1,
    userSelect: 'none',
    ...style,
  };

  const handleMouseEnter = (e: React.MouseEvent<HTMLButtonElement>) => {
    if (disabled) return;
    
    const target = e.currentTarget;
    if (type === 'primary') {
      target.style.boxShadow = `0 12px 40px ${theme.colors.shadow}, inset 0 1px 0 rgba(255, 255, 255, 0.4)`;
    } else if (type === 'secondary') {
      target.style.background = 'rgba(255, 255, 255, 0.25)';
      target.style.boxShadow = `0 12px 40px ${theme.colors.shadowLight}, inset 0 1px 0 rgba(255, 255, 255, 0.6)`;
    } else {
      target.style.background = 'rgba(255, 255, 255, 0.1)';
    }
  };

  const handleMouseLeave = (e: React.MouseEvent<HTMLButtonElement>) => {
    if (disabled) return;
    
    const target = e.currentTarget;
    Object.assign(target.style, baseStyle);
  };

  const handleMouseDown = (e: React.MouseEvent<HTMLButtonElement>) => {
    if (disabled) return;
    // GPU加速的变化由CSS类处理
  };

  const handleMouseUp = (e: React.MouseEvent<HTMLButtonElement>) => {
    if (disabled) return;
    // GPU加速的变化由CSS类处理
  };

  return (
    <button
      className={`fast-button force-gpu ${className}`}
      style={baseStyle}
      onClick={disabled ? undefined : onClick}
      onMouseEnter={handleMouseEnter}
      onMouseLeave={handleMouseLeave}
      onMouseDown={handleMouseDown}
      onMouseUp={handleMouseUp}
      disabled={disabled}
    >
      {/* 反光效果 */}
      <div
        style={{
          position: 'absolute',
          top: 0,
          left: '-100%',
          width: '100%',
          height: '100%',
          background: 'linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.3), transparent)',
          transition: 'left 0.6s ease',
          zIndex: 1,
          pointerEvents: 'none',
        }}
        className="shine-effect"
      />
      
      {/* 内容 */}
      <div style={{ position: 'relative', zIndex: 2, display: 'flex', alignItems: 'center', gap: '8px' }}>
        {loading ? (
          <div
            style={{
              width: '16px',
              height: '16px',
              border: '2px solid currentColor',
              borderTop: '2px solid transparent',
              borderRadius: '50%',
              animation: 'spin 1s linear infinite',
            }}
          />
        ) : icon}
        {children}
      </div>
    </button>
  );
};