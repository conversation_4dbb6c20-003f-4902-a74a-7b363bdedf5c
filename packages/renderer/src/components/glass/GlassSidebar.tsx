import React, { ReactNode } from 'react';
import { useTheme } from '../../contexts/ThemeContext';

interface GlassSidebarProps {
  children: ReactNode;
  collapsed?: boolean;
  width?: number;
  collapsedWidth?: number;
}

export const GlassSidebar: React.FC<GlassSidebarProps> = ({
  children,
  collapsed = false,
  width = 280,
  collapsedWidth = 80,
}) => {
  const { theme } = useTheme();

  const sidebarStyle = {
    width: collapsed ? collapsedWidth : width,
    height: '100vh',
    background: `linear-gradient(180deg, 
      rgba(255, 255, 255, 0.25) 0%, 
      rgba(255, 255, 255, 0.15) 50%, 
      rgba(255, 255, 255, 0.1) 100%)`,
    borderRight: `1px solid ${theme.colors.border}`,
    boxShadow: `
      4px 0 24px rgba(0, 0, 0, 0.1),
      inset -1px 0 0 rgba(255, 255, 255, 0.3),
      inset 0 1px 0 rgba(255, 255, 255, 0.4)
    `,
    position: 'relative' as const,
    overflow: 'hidden',
    zIndex: 10,
  };

  return (
    <div className="glass-no-blur force-gpu" style={sidebarStyle}>
      {/* 顶部渐变高光 */}
      <div
        style={{
          position: 'absolute',
          top: 0,
          left: 0,
          right: 0,
          height: '2px',
          background: `linear-gradient(90deg, 
            transparent, 
            ${theme.colors.primary}40, 
            transparent)`,
          zIndex: 1,
        }}
      />
      
      {/* 左侧边缘高光 */}
      <div
        style={{
          position: 'absolute',
          top: 0,
          left: 0,
          bottom: 0,
          width: '1px',
          background: `linear-gradient(180deg, 
            rgba(255, 255, 255, 0.6), 
            rgba(255, 255, 255, 0.2), 
            rgba(255, 255, 255, 0.6))`,
          zIndex: 1,
        }}
      />
      
      {/* 内容区域 */}
      <div
        style={{
          position: 'relative',
          zIndex: 2,
          height: '100%',
          padding: collapsed ? '16px 8px' : '24px 16px',
          overflow: 'hidden',
        }}
      >
        {children}
      </div>
    </div>
  );
};