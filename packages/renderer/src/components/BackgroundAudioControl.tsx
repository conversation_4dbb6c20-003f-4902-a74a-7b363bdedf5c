import React, { useState, useEffect } from 'react';
import {
  Card,
  Button,
  Select,
  Slider,
  Space,
  Typography,
  Tooltip,
  Switch
} from 'antd';
import {
  SoundOutlined,
  PlayCircleOutlined,
  PauseCircleOutlined,
  SettingOutlined,
  MutedOutlined
} from '@ant-design/icons';
import { useTheme } from '../contexts/ThemeContext';
import { 
  backgroundAudioService, 
  BackgroundSoundType 
} from '../services/BackgroundAudioService';

const { Text } = Typography;
const { Option } = Select;

interface BackgroundAudioControlProps {
  embedded?: boolean;
  showFullControls?: boolean;
  style?: React.CSSProperties;
  className?: string;
}

const BackgroundAudioControl: React.FC<BackgroundAudioControlProps> = ({
  embedded = false,
  showFullControls = true,
  style,
  className
}) => {
  const { theme } = useTheme();
  const [settings, setSettings] = useState(backgroundAudioService.getSettings());
  const [isPlaying, setIsPlaying] = useState(false);
  const [currentSound, setCurrentSound] = useState<BackgroundSoundType>('none');
  const [volume, setVolume] = useState(0.3);

  useEffect(() => {
    // 初始化状态
    setIsPlaying(backgroundAudioService.isCurrentlyPlaying());
    setCurrentSound(backgroundAudioService.getCurrentSoundType());
    setVolume(backgroundAudioService.getVolume());
  }, []);

  const soundConfigs = backgroundAudioService.getSoundConfigs();

  const handleToggleEnabled = async (enabled: boolean) => {
    backgroundAudioService.saveSettings({ enabled });
    setSettings(prev => ({ ...prev, enabled }));
    
    if (!enabled && isPlaying) {
      await backgroundAudioService.stopBackgroundSound();
      setIsPlaying(false);
      setCurrentSound('none');
    }
  };

  const handleSoundChange = async (soundType: BackgroundSoundType) => {
    backgroundAudioService.saveSettings({ soundType });
    setSettings(prev => ({ ...prev, soundType }));
    
    if (settings.enabled) {
      try {
        await backgroundAudioService.startBackgroundSound(soundType);
        setIsPlaying(soundType !== 'none');
        setCurrentSound(soundType);
      } catch (error) {
        console.error('切换背景音效失败:', error);
      }
    }
  };

  const handleVolumeChange = async (newVolume: number) => {
    setVolume(newVolume);
    await backgroundAudioService.setVolume(newVolume);
  };

  const handleTogglePlayback = async () => {
    if (isPlaying) {
      await backgroundAudioService.stopBackgroundSound();
      setIsPlaying(false);
      setCurrentSound('none');
    } else {
      if (settings.soundType !== 'none') {
        await backgroundAudioService.startBackgroundSound(settings.soundType);
        setIsPlaying(true);
        setCurrentSound(settings.soundType);
      }
    }
  };

  const getCurrentSoundConfig = () => {
    return soundConfigs[currentSound] || soundConfigs['none'];
  };

  if (embedded) {
    // 嵌入式简化版本
    return (
      <div 
        style={{ 
          display: 'flex', 
          alignItems: 'center', 
          gap: '12px',
          padding: '8px 12px',
          backgroundColor: theme.colors.cardBackground,
          borderRadius: '8px',
          border: `1px solid ${theme.colors.border}`,
          ...style 
        }}
        className={className}
      >
        <div style={{ display: 'flex', alignItems: 'center', gap: '8px' }}>
          <SoundOutlined style={{ color: theme.colors.textSecondary }} />
          <Text type="secondary" style={{ fontSize: '12px' }}>背景音</Text>
        </div>
        
        <Switch
          size="small"
          checked={settings.enabled}
          onChange={handleToggleEnabled}
        />
        
        {settings.enabled && (
          <>
            <Select
              size="small"
              value={settings.soundType}
              onChange={handleSoundChange}
              style={{ width: '120px' }}
            >
              {Object.entries(soundConfigs).map(([soundType, config]) => (
                <Option key={soundType} value={soundType}>
                  <Space size={4}>
                    <span style={{ fontSize: '12px' }}>{config.icon}</span>
                    <span style={{ fontSize: '12px' }}>{config.name}</span>
                  </Space>
                </Option>
              ))}
            </Select>
            
            <Button
              size="small"
              type="text"
              icon={isPlaying ? <PauseCircleOutlined /> : <PlayCircleOutlined />}
              onClick={handleTogglePlayback}
              disabled={settings.soundType === 'none'}
            />
            
            <div style={{ width: '60px' }}>
              <Slider
                size="small"
                min={0}
                max={1}
                step={0.1}
                value={volume}
                onChange={handleVolumeChange}
                tooltip={{ formatter: (value) => `${Math.round((value || 0) * 100)}%` }}
              />
            </div>
          </>
        )}
      </div>
    );
  }

  // 完整版本
  return (
    <Card
      title={
        <Space>
          <SoundOutlined />
          <span>背景音效</span>
        </Space>
      }
      size="small"
      style={style}
      className={className}
      extra={
        <Tooltip title={settings.enabled ? '背景音效已启用' : '背景音效已禁用'}>
          {settings.enabled ? (
            <SoundOutlined style={{ color: theme.colors.primary }} />
          ) : (
            <MutedOutlined style={{ color: theme.colors.textSecondary }} />
          )}
        </Tooltip>
      }
    >
      <Space direction="vertical" style={{ width: '100%' }}>
        {/* 总开关 */}
        <div style={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center' }}>
          <Text>启用背景音效</Text>
          <Switch
            checked={settings.enabled}
            onChange={handleToggleEnabled}
            checkedChildren="开"
            unCheckedChildren="关"
          />
        </div>

        {settings.enabled && (
          <>
            {/* 音效选择 */}
            <div>
              <Text style={{ marginBottom: '8px', display: 'block' }}>音效类型</Text>
              <Select
                value={settings.soundType}
                onChange={handleSoundChange}
                style={{ width: '100%' }}
              >
                {Object.entries(soundConfigs).map(([soundType, config]) => (
                  <Option key={soundType} value={soundType}>
                    <Space>
                      <span>{config.icon}</span>
                      <span>{config.name}</span>
                    </Space>
                  </Option>
                ))}
              </Select>
            </div>

            {/* 播放控制 */}
            <div style={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center' }}>
              <Text>播放控制</Text>
              <Space>
                <Button
                  type={isPlaying ? 'primary' : 'default'}
                  icon={isPlaying ? <PauseCircleOutlined /> : <PlayCircleOutlined />}
                  onClick={handleTogglePlayback}
                  disabled={settings.soundType === 'none'}
                  size="small"
                >
                  {isPlaying ? '暂停' : '播放'}
                </Button>
              </Space>
            </div>

            {/* 音量控制 */}
            <div>
              <div style={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', marginBottom: '8px' }}>
                <Text>音量</Text>
                <Text type="secondary" style={{ fontSize: '12px' }}>
                  {Math.round(volume * 100)}%
                </Text>
              </div>
              <Slider
                min={0}
                max={1}
                step={0.1}
                value={volume}
                onChange={handleVolumeChange}
                marks={{
                  0: '静音',
                  0.5: '50%',
                  1: '100%'
                }}
              />
            </div>

            {/* 当前状态 */}
            {currentSound !== 'none' && (
              <div style={{ 
                padding: '8px 12px', 
                backgroundColor: theme.colors.background,
                borderRadius: '6px',
                border: `1px solid ${theme.colors.border}`
              }}>
                <div style={{ display: 'flex', alignItems: 'center', gap: '8px' }}>
                  <span style={{ fontSize: '16px' }}>{getCurrentSoundConfig().icon}</span>
                  <div>
                    <Text strong style={{ fontSize: '12px' }}>
                      正在播放: {getCurrentSoundConfig().name}
                    </Text>
                    <br />
                    <Text type="secondary" style={{ fontSize: '11px' }}>
                      {getCurrentSoundConfig().description}
                    </Text>
                  </div>
                </div>
              </div>
            )}
          </>
        )}
      </Space>
    </Card>
  );
};

export default BackgroundAudioControl;
