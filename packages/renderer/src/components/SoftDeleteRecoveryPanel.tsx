import React, { useState, useEffect } from 'react';
import { 
  Card, 
  List, 
  Button, 
  Space, 
  Typography, 
  Tag, 
  Alert,
  Modal,
  message,
  Empty,
  Tooltip,
  Popconfirm
} from 'antd';
import { 
  UndoOutlined,
  DeleteOutlined,
  ClockCircleOutlined,
  ExclamationCircleOutlined,
  FolderOutlined,
  CalendarOutlined
} from '@ant-design/icons';
import { useTheme } from '../contexts/ThemeContext';
import { GoalCascadeDeleteService, SoftDeleteInfo } from '../services/GoalCascadeDeleteService';

const { Title, Text, Paragraph } = Typography;

interface SoftDeleteRecoveryPanelProps {
  visible: boolean;
  onClose: () => void;
  onGoalRestored?: (goalId: string) => void;
}

const SoftDeleteRecoveryPanel: React.FC<SoftDeleteRecoveryPanelProps> = ({
  visible,
  onClose,
  onGoalRestored
}) => {
  const { theme } = useTheme();
  const [softDeletedItems, setSoftDeletedItems] = useState<SoftDeleteInfo[]>([]);
  const [loading, setLoading] = useState(false);
  const [restoring, setRestoring] = useState<string | null>(null);
  const [permanentDeleting, setPermanentDeleting] = useState<string | null>(null);

  // 加载软删除项目
  const loadSoftDeletedItems = () => {
    try {
      const items = GoalCascadeDeleteService.getSoftDeletedItems();
      setSoftDeletedItems(items);
    } catch (error) {
      console.error('加载软删除项目失败:', error);
      message.error('加载已删除项目失败');
    }
  };

  useEffect(() => {
    if (visible) {
      loadSoftDeletedItems();
    }
  }, [visible]);

  // 恢复软删除项目
  const handleRestore = async (item: SoftDeleteInfo) => {
    setRestoring(item.id);
    try {
      await GoalCascadeDeleteService.restoreSoftDeletedGoal(item.id);
      message.success('目标恢复成功');
      loadSoftDeletedItems(); // 重新加载列表
      onGoalRestored?.(item.id);
    } catch (error) {
      console.error('恢复失败:', error);
      message.error(error instanceof Error ? error.message : '恢复失败');
    } finally {
      setRestoring(null);
    }
  };

  // 永久删除
  const handlePermanentDelete = async (item: SoftDeleteInfo) => {
    setPermanentDeleting(item.id);
    try {
      await GoalCascadeDeleteService.cascadeDeleteGoal(item.id);
      message.success('目标已永久删除');
      loadSoftDeletedItems(); // 重新加载列表
    } catch (error) {
      console.error('永久删除失败:', error);
      message.error(error instanceof Error ? error.message : '永久删除失败');
    } finally {
      setPermanentDeleting(null);
    }
  };

  // 清理过期项目
  const handleCleanupExpired = async () => {
    setLoading(true);
    try {
      const cleanedCount = await GoalCascadeDeleteService.cleanupExpiredSoftDeletes();
      if (cleanedCount > 0) {
        message.success(`已清理 ${cleanedCount} 个过期项目`);
        loadSoftDeletedItems();
      } else {
        message.info('没有过期项目需要清理');
      }
    } catch (error) {
      console.error('清理过期项目失败:', error);
      message.error('清理过期项目失败');
    } finally {
      setLoading(false);
    }
  };

  // 计算剩余天数
  const getDaysRemaining = (expiresAt: Date): number => {
    const now = new Date();
    const expires = new Date(expiresAt);
    const diffTime = expires.getTime() - now.getTime();
    const diffDays = Math.ceil(diffTime / (1000 * 60 * 60 * 24));
    return Math.max(0, diffDays);
  };

  // 获取过期状态
  const getExpirationStatus = (item: SoftDeleteInfo) => {
    const daysRemaining = getDaysRemaining(item.expiresAt);
    const isExpired = daysRemaining <= 0;
    const isExpiringSoon = daysRemaining <= 3 && daysRemaining > 0;

    if (isExpired) {
      return {
        color: 'red',
        text: '已过期',
        icon: <ExclamationCircleOutlined />
      };
    } else if (isExpiringSoon) {
      return {
        color: 'orange',
        text: `${daysRemaining}天后过期`,
        icon: <ClockCircleOutlined />
      };
    } else {
      return {
        color: 'blue',
        text: `${daysRemaining}天后过期`,
        icon: <CalendarOutlined />
      };
    }
  };

  // 格式化删除时间
  const formatDeletedTime = (deletedAt: Date): string => {
    const now = new Date();
    const deleted = new Date(deletedAt);
    const diffTime = now.getTime() - deleted.getTime();
    const diffDays = Math.floor(diffTime / (1000 * 60 * 60 * 24));
    const diffHours = Math.floor(diffTime / (1000 * 60 * 60));

    if (diffDays > 0) {
      return `${diffDays}天前删除`;
    } else if (diffHours > 0) {
      return `${diffHours}小时前删除`;
    } else {
      return '刚刚删除';
    }
  };

  return (
    <Modal
      title={
        <Space>
          <UndoOutlined />
          回收站
        </Space>
      }
      open={visible}
      onCancel={onClose}
      footer={[
        <Button 
          key="cleanup" 
          onClick={handleCleanupExpired}
          loading={loading}
          style={{ marginRight: 'auto' }}
        >
          清理过期项目
        </Button>,
        <Button key="close" onClick={onClose}>
          关闭
        </Button>
      ]}
      width={800}
      style={{ top: 50 }}
    >
      <div style={{ maxHeight: '70vh', overflowY: 'auto' }}>
        {/* 说明信息 */}
        <Alert
          message="回收站说明"
          description={
            <Space direction="vertical" style={{ width: '100%' }}>
              <Text>删除的目标将在回收站保留30天，期间可以恢复。</Text>
              <Text>过期后将被自动永久删除，无法恢复。</Text>
              <Text type="secondary">建议定期检查并清理不需要的项目。</Text>
            </Space>
          }
          type="info"
          showIcon
          style={{ marginBottom: '20px' }}
        />

        {/* 软删除项目列表 */}
        {softDeletedItems.length === 0 ? (
          <Empty
            image={Empty.PRESENTED_IMAGE_SIMPLE}
            description="回收站为空"
            style={{ margin: '40px 0' }}
          />
        ) : (
          <List
            dataSource={softDeletedItems}
            renderItem={(item) => {
              const expirationStatus = getExpirationStatus(item);
              const isExpired = getDaysRemaining(item.expiresAt) <= 0;
              const isRestoring = restoring === item.id;
              const isDeleting = permanentDeleting === item.id;

              return (
                <List.Item
                  style={{
                    background: theme.colors.surface,
                    border: `1px solid ${theme.colors.border}`,
                    borderRadius: '8px',
                    margin: '8px 0',
                    padding: '16px',
                    opacity: isExpired ? 0.6 : 1
                  }}
                  actions={[
                    <Tooltip title={isExpired ? '已过期，无法恢复' : '恢复目标'}>
                      <Button
                        type="primary"
                        icon={<UndoOutlined />}
                        onClick={() => handleRestore(item)}
                        loading={isRestoring}
                        disabled={isExpired || isDeleting}
                        size="small"
                      >
                        恢复
                      </Button>
                    </Tooltip>,
                    <Popconfirm
                      title="确认永久删除？"
                      description="此操作不可撤销，将彻底删除该目标及所有关联数据。"
                      onConfirm={() => handlePermanentDelete(item)}
                      okText="确认删除"
                      cancelText="取消"
                      okButtonProps={{ danger: true }}
                    >
                      <Button
                        danger
                        icon={<DeleteOutlined />}
                        loading={isDeleting}
                        disabled={isRestoring}
                        size="small"
                      >
                        永久删除
                      </Button>
                    </Popconfirm>
                  ]}
                >
                  <List.Item.Meta
                    avatar={
                      <div style={{
                        width: '40px',
                        height: '40px',
                        borderRadius: '8px',
                        background: theme.colors.primary,
                        display: 'flex',
                        alignItems: 'center',
                        justifyContent: 'center',
                        color: 'white'
                      }}>
                        <FolderOutlined />
                      </div>
                    }
                    title={
                      <Space direction="vertical" size={4} style={{ width: '100%' }}>
                        <div style={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center' }}>
                          <Text 
                            strong 
                            style={{ 
                              color: theme.colors.text,
                              textDecoration: isExpired ? 'line-through' : 'none'
                            }}
                          >
                            {item.originalData?.name || '未知目标'}
                          </Text>
                          <Tag 
                            color={expirationStatus.color}
                            icon={expirationStatus.icon}
                            size="small"
                          >
                            {expirationStatus.text}
                          </Tag>
                        </div>
                        <Text type="secondary" style={{ fontSize: '12px' }}>
                          {formatDeletedTime(item.deletedAt)}
                        </Text>
                      </Space>
                    }
                    description={
                      <div>
                        {item.originalData?.description && (
                          <Paragraph 
                            ellipsis={{ rows: 2 }} 
                            style={{ 
                              margin: '4px 0 8px 0', 
                              color: theme.colors.textSecondary 
                            }}
                          >
                            {item.originalData.description}
                          </Paragraph>
                        )}
                        <Space wrap>
                          <Tag size="small">{item.originalData?.type || '未知类型'}</Tag>
                          <Tag size="small">{item.originalData?.status || '未知状态'}</Tag>
                          {item.originalData?.domains && item.originalData.domains.length > 0 && (
                            <Tag size="small" color="blue">
                              {item.originalData.domains.length} 个领域
                            </Tag>
                          )}
                        </Space>
                      </div>
                    }
                  />
                </List.Item>
              );
            }}
          />
        )}

        {/* 操作提示 */}
        {softDeletedItems.length > 0 && (
          <Alert
            message="操作提示"
            description={
              <Space direction="vertical" style={{ width: '100%' }}>
                <Text>• 点击"恢复"可以将目标重新激活</Text>
                <Text>• 点击"永久删除"将彻底删除目标，无法恢复</Text>
                <Text>• 过期的项目无法恢复，只能永久删除</Text>
              </Space>
            }
            type="warning"
            showIcon
            style={{ marginTop: '20px' }}
          />
        )}
      </div>
    </Modal>
  );
};

export default SoftDeleteRecoveryPanel;