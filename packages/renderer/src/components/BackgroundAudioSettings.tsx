import React, { useState, useEffect } from 'react';
import {
  Card,
  Switch,
  Slider,
  Select,
  Row,
  Col,
  Typography,
  Space,
  Button,
  Divider,
  Tag,
  Tooltip,
  Alert,
  InputNumber
} from 'antd';
import {
  PlayCircleOutlined,
  PauseCircleOutlined,
  SoundOutlined,
  SettingOutlined,
  InfoCircleOutlined
} from '@ant-design/icons';
import { useTheme } from '../contexts/ThemeContext';
import { 
  backgroundAudioService, 
  BackgroundSoundType, 
  BackgroundAudioSettings 
} from '../services/BackgroundAudioService';

const { Title, Text, Paragraph } = Typography;
const { Option } = Select;

interface BackgroundAudioSettingsProps {
  style?: React.CSSProperties;
  className?: string;
}

const BackgroundAudioSettingsComponent: React.FC<BackgroundAudioSettingsProps> = ({
  style,
  className
}) => {
  const { theme } = useTheme();
  const [settings, setSettings] = useState<BackgroundAudioSettings>(
    backgroundAudioService.getSettings()
  );
  const [isPlaying, setIsPlaying] = useState(false);
  const [currentSound, setCurrentSound] = useState<BackgroundSoundType>('none');
  const [testingSound, setTestingSound] = useState<BackgroundSoundType | null>(null);

  useEffect(() => {
    // 初始化状态
    setIsPlaying(backgroundAudioService.isCurrentlyPlaying());
    setCurrentSound(backgroundAudioService.getCurrentSoundType());
  }, []);

  const soundConfigs = backgroundAudioService.getSoundConfigs();

  const handleSettingChange = (key: keyof BackgroundAudioSettings, value: any) => {
    const newSettings = { ...settings, [key]: value };
    setSettings(newSettings);
    backgroundAudioService.saveSettings({ [key]: value });

    // 如果改变了音量，立即应用
    if (key === 'volume') {
      backgroundAudioService.setVolume(value);
    }
  };

  const handleTestSound = async (soundType: BackgroundSoundType) => {
    if (testingSound === soundType) {
      // 停止测试
      await backgroundAudioService.stopBackgroundSound();
      setTestingSound(null);
      setIsPlaying(false);
    } else {
      // 开始测试
      try {
        setTestingSound(soundType);
        await backgroundAudioService.startBackgroundSound(soundType);
        setIsPlaying(true);
        setCurrentSound(soundType);
        
        // 3秒后自动停止测试
        setTimeout(async () => {
          if (testingSound === soundType) {
            await backgroundAudioService.stopBackgroundSound();
            setTestingSound(null);
            setIsPlaying(false);
          }
        }, 3000);
      } catch (error) {
        console.error('测试音效失败:', error);
        setTestingSound(null);
      }
    }
  };

  const handleToggleEnabled = async (enabled: boolean) => {
    handleSettingChange('enabled', enabled);
    
    if (!enabled && isPlaying) {
      await backgroundAudioService.stopBackgroundSound();
      setIsPlaying(false);
      setCurrentSound('none');
    }
  };

  const getSoundsByCategory = () => {
    const categories: Record<string, BackgroundSoundType[]> = {
      nature: [],
      ambient: [],
      noise: [],
      urban: []
    };

    Object.entries(soundConfigs).forEach(([soundType, config]) => {
      if (soundType !== 'none') {
        categories[config.category].push(soundType as BackgroundSoundType);
      }
    });

    return categories;
  };

  const renderSoundCard = (soundType: BackgroundSoundType) => {
    const config = soundConfigs[soundType];
    const isTesting = testingSound === soundType;
    const isSelected = settings.soundType === soundType;

    return (
      <Card
        key={soundType}
        size="small"
        hoverable
        className={`sound-card ${isSelected ? 'selected' : ''}`}
        style={{
          border: isSelected ? `2px solid ${theme.colors.primary}` : '1px solid #d9d9d9',
          backgroundColor: isSelected ? `${theme.colors.primary}10` : theme.colors.cardBackground,
          cursor: 'pointer'
        }}
        onClick={() => handleSettingChange('soundType', soundType)}
        actions={[
          <Button
            key="test"
            type="text"
            icon={isTesting ? <PauseCircleOutlined /> : <PlayCircleOutlined />}
            onClick={(e) => {
              e.stopPropagation();
              handleTestSound(soundType);
            }}
            loading={testingSound === soundType}
          >
            {isTesting ? '停止' : '试听'}
          </Button>
        ]}
      >
        <div style={{ textAlign: 'center' }}>
          <div style={{ fontSize: '24px', marginBottom: '8px' }}>
            {config.icon}
          </div>
          <div style={{ marginBottom: '4px' }}>
            <Text strong>{config.name}</Text>
          </div>
          <div>
            <Text type="secondary" style={{ fontSize: '12px' }}>
              {config.description}
            </Text>
          </div>
          <div style={{ marginTop: '8px' }}>
            <Tag color={config.color} size="small">
              {config.category}
            </Tag>
          </div>
        </div>
      </Card>
    );
  };

  const categories = getSoundsByCategory();

  return (
    <div style={style} className={className}>
      <Card
        title={
          <Space>
            <SoundOutlined />
            <span>背景音效设置</span>
          </Space>
        }
        extra={
          <Tooltip title="背景音效可以帮助您更好地专注，屏蔽外界干扰">
            <InfoCircleOutlined style={{ color: theme.colors.textSecondary }} />
          </Tooltip>
        }
      >
        {/* 总开关 */}
        <Row gutter={[16, 16]} style={{ marginBottom: '24px' }}>
          <Col span={24}>
            <div style={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center' }}>
              <div>
                <Text strong>启用背景音效</Text>
                <br />
                <Text type="secondary" style={{ fontSize: '12px' }}>
                  在番茄钟专注时段播放背景音效
                </Text>
              </div>
              <Switch
                checked={settings.enabled}
                onChange={handleToggleEnabled}
                checkedChildren="开启"
                unCheckedChildren="关闭"
              />
            </div>
          </Col>
        </Row>

        {settings.enabled && (
          <>
            {/* 音量控制 */}
            <Row gutter={[16, 16]} style={{ marginBottom: '24px' }}>
              <Col span={24}>
                <div style={{ marginBottom: '8px' }}>
                  <Text strong>音量</Text>
                </div>
                <Slider
                  min={0}
                  max={1}
                  step={0.1}
                  value={settings.volume}
                  onChange={(value) => handleSettingChange('volume', value)}
                  marks={{
                    0: '静音',
                    0.5: '适中',
                    1: '最大'
                  }}
                />
              </Col>
            </Row>

            {/* 淡入淡出设置 */}
            <Row gutter={[16, 16]} style={{ marginBottom: '24px' }}>
              <Col span={12}>
                <div style={{ marginBottom: '8px' }}>
                  <Text strong>淡入时长 (秒)</Text>
                </div>
                <InputNumber
                  min={0}
                  max={10}
                  step={0.5}
                  value={settings.fadeInDuration}
                  onChange={(value) => handleSettingChange('fadeInDuration', value || 3)}
                  style={{ width: '100%' }}
                />
              </Col>
              <Col span={12}>
                <div style={{ marginBottom: '8px' }}>
                  <Text strong>淡出时长 (秒)</Text>
                </div>
                <InputNumber
                  min={0}
                  max={10}
                  step={0.5}
                  value={settings.fadeOutDuration}
                  onChange={(value) => handleSettingChange('fadeOutDuration', value || 2)}
                  style={{ width: '100%' }}
                />
              </Col>
            </Row>

            <Divider />

            {/* 自动切换设置 */}
            <Row gutter={[16, 16]} style={{ marginBottom: '24px' }}>
              <Col span={24}>
                <div style={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', marginBottom: '16px' }}>
                  <div>
                    <Text strong>智能切换音效</Text>
                    <br />
                    <Text type="secondary" style={{ fontSize: '12px' }}>
                      工作和休息时段使用不同的背景音效
                    </Text>
                  </div>
                  <Switch
                    checked={settings.autoSwitchSounds}
                    onChange={(value) => handleSettingChange('autoSwitchSounds', value)}
                    checkedChildren="开启"
                    unCheckedChildren="关闭"
                  />
                </div>

                {settings.autoSwitchSounds && (
                  <Row gutter={[16, 16]}>
                    <Col span={12}>
                      <div style={{ marginBottom: '8px' }}>
                        <Text strong>工作时段音效</Text>
                      </div>
                      <Select
                        value={settings.workSessionSound}
                        onChange={(value) => handleSettingChange('workSessionSound', value)}
                        style={{ width: '100%' }}
                      >
                        {Object.entries(soundConfigs).map(([soundType, config]) => (
                          soundType !== 'none' && (
                            <Option key={soundType} value={soundType}>
                              <Space>
                                <span>{config.icon}</span>
                                <span>{config.name}</span>
                              </Space>
                            </Option>
                          )
                        ))}
                      </Select>
                    </Col>
                    <Col span={12}>
                      <div style={{ marginBottom: '8px' }}>
                        <Text strong>休息时段音效</Text>
                      </div>
                      <Select
                        value={settings.breakSessionSound}
                        onChange={(value) => handleSettingChange('breakSessionSound', value)}
                        style={{ width: '100%' }}
                      >
                        {Object.entries(soundConfigs).map(([soundType, config]) => (
                          soundType !== 'none' && (
                            <Option key={soundType} value={soundType}>
                              <Space>
                                <span>{config.icon}</span>
                                <span>{config.name}</span>
                              </Space>
                            </Option>
                          )
                        ))}
                      </Select>
                    </Col>
                  </Row>
                )}
              </Col>
            </Row>

            <Divider />

            {/* 音效选择 */}
            <div style={{ marginBottom: '16px' }}>
              <Title level={5}>选择背景音效</Title>
              <Paragraph type="secondary">
                点击音效卡片选择，点击试听按钮预览效果
              </Paragraph>
            </div>

            {/* 自然音效 */}
            {categories.nature.length > 0 && (
              <div style={{ marginBottom: '24px' }}>
                <Title level={5} style={{ color: theme.colors.success }}>
                  🌿 自然音效
                </Title>
                <Row gutter={[12, 12]}>
                  {categories.nature.map(soundType => (
                    <Col key={soundType} xs={12} sm={8} md={6}>
                      {renderSoundCard(soundType)}
                    </Col>
                  ))}
                </Row>
              </div>
            )}

            {/* 环境音效 */}
            {categories.ambient.length > 0 && (
              <div style={{ marginBottom: '24px' }}>
                <Title level={5} style={{ color: theme.colors.warning }}>
                  🏠 环境音效
                </Title>
                <Row gutter={[12, 12]}>
                  {categories.ambient.map(soundType => (
                    <Col key={soundType} xs={12} sm={8} md={6}>
                      {renderSoundCard(soundType)}
                    </Col>
                  ))}
                </Row>
              </div>
            )}

            {/* 噪音音效 */}
            {categories.noise.length > 0 && (
              <div style={{ marginBottom: '24px' }}>
                <Title level={5} style={{ color: theme.colors.textSecondary }}>
                  📻 噪音音效
                </Title>
                <Row gutter={[12, 12]}>
                  {categories.noise.map(soundType => (
                    <Col key={soundType} xs={12} sm={8} md={6}>
                      {renderSoundCard(soundType)}
                    </Col>
                  ))}
                </Row>
              </div>
            )}

            {/* 城市音效 */}
            {categories.urban.length > 0 && (
              <div style={{ marginBottom: '24px' }}>
                <Title level={5} style={{ color: theme.colors.info }}>
                  🏙️ 城市音效
                </Title>
                <Row gutter={[12, 12]}>
                  {categories.urban.map(soundType => (
                    <Col key={soundType} xs={12} sm={8} md={6}>
                      {renderSoundCard(soundType)}
                    </Col>
                  ))}
                </Row>
              </div>
            )}

            {/* 提示信息 */}
            <Alert
              message="音效说明"
              description="如果预置音频文件不可用，系统会自动使用程序生成的音效。您可以将自己的音频文件放在 public/sounds/background/ 目录下。"
              type="info"
              showIcon
              style={{ marginTop: '16px' }}
            />
          </>
        )}
      </Card>
    </div>
  );
};

export default BackgroundAudioSettingsComponent;
