import React, { useState, useMemo } from 'react';
import { Card, Collapse, Checkbox, Tooltip, Button, Space, Typography, Alert, Progress, Tag } from 'antd';
import { 
  InfoCircleOutlined, 
  CheckCircleOutlined, 
  QuestionCircleOutlined, 
  ExclamationCircleOutlined,
  BulbOutlined 
} from '@ant-design/icons';

const { Text, Paragraph } = Typography;
const { Panel } = Collapse;

interface DecompositionQualityHelperProps {
  decompositionResult?: any;
  onQualityChange?: (quality: QualityAssessment) => void;
  compact?: boolean;
}

interface QualityAssessment {
  smartCompliance: number; // 0-100
  taskGranularity: 'good' | 'too-large' | 'too-small';
  timeRealistic: boolean;
  dependenciesClear: boolean;
  actionable: boolean;
  suggestions: string[];
}

interface TaskQualityCheck {
  isSpecific: boolean;
  isMeasurable: boolean;
  isAchievable: boolean;
  isRelevant: boolean;
  isTimeBound: boolean;
  isActionable: boolean;
}

// 分析单个任务的质量 - 移到组件外部避免hoisting问题
const analyzeTaskQuality = (task: any): TaskQualityCheck => {
  const name = task.name || task.title || '';
  const description = task.description || '';
  const text = (name + ' ' + description).toLowerCase();

  return {
    isSpecific: /\b(学习|完成|创建|编写|设计|实现|测试|部署|优化)\b/.test(text) && text.length > 10,
    isMeasurable: /\d+|完成|通过|达到|实现/.test(text),
    isAchievable: text.length > 5 && text.length < 200, // 简化的可实现性检查
    isRelevant: true, // 假设AI分解的任务都是相关的
    isTimeBound: task.estimatedTime > 0 || /\b(天|周|小时|分钟|期限|截止)\b/.test(text),
    isActionable: /\b(学习|完成|创建|编写|设计|实现|测试|部署|优化|阅读|练习|制作)\b/.test(text)
  };
};

const DecompositionQualityHelper: React.FC<DecompositionQualityHelperProps> = ({
  decompositionResult,
  onQualityChange,
  compact = false
}) => {
  const [showHelper, setShowHelper] = useState(false);

  // 分析分解结果的质量
  const qualityAssessment = useMemo((): QualityAssessment => {
    if (!decompositionResult) {
      return {
        smartCompliance: 0,
        taskGranularity: 'good',
        timeRealistic: true,
        dependenciesClear: true,
        actionable: true,
        suggestions: ['请先完成目标分解']
      };
    }

    const suggestions: string[] = [];
    let smartScore = 0;
    let totalTasks = 0;
    let actionableTasks = 0;
    let specificTasks = 0;
    let measurableTasks = 0;
    let timeBoundTasks = 0;

    // 分析所有任务
    decompositionResult.subGoals?.forEach((subGoal: any) => {
      subGoal.milestones?.forEach((milestone: any) => {
        milestone.tasks?.forEach((task: any) => {
          totalTasks++;

          const taskQuality = analyzeTaskQuality(task);

          if (taskQuality.isActionable) actionableTasks++;
          if (taskQuality.isSpecific) specificTasks++;
          if (taskQuality.isMeasurable) measurableTasks++;
          if (taskQuality.isTimeBound) timeBoundTasks++;
        });
      });
    });

    // 计算SMART合规性 - 将变量定义移到外层作用域
    let specificRate = 0;
    let measurableRate = 0;
    let actionableRate = 0;
    let timeBoundRate = 0;

    if (totalTasks > 0) {
      specificRate = specificTasks / totalTasks;
      measurableRate = measurableTasks / totalTasks;
      actionableRate = actionableTasks / totalTasks;
      timeBoundRate = timeBoundTasks / totalTasks;

      smartScore = Math.round((specificRate + measurableRate + actionableRate + timeBoundRate) * 25);
    }

    // 检查任务粒度
    let taskGranularity: 'good' | 'too-large' | 'too-small' = 'good';
    const avgTasksPerMilestone = totalTasks / (decompositionResult.subGoals?.reduce(
      (sum: number, sg: any) => sum + (sg.milestones?.length || 0), 0
    ) || 1);

    if (avgTasksPerMilestone > 8) {
      taskGranularity = 'too-small';
      suggestions.push('任务粒度过细，建议合并一些相关任务');
    } else if (avgTasksPerMilestone < 2) {
      taskGranularity = 'too-large';
      suggestions.push('任务粒度过大，建议进一步分解复杂任务');
    }

    // 检查时间估算合理性
    const totalTime = decompositionResult.estimatedTotalTime || 0;
    const timeRealistic = totalTime > 0 && totalTime < 1000; // 假设超过1000小时不太现实
    if (!timeRealistic) {
      suggestions.push('时间估算可能不够现实，建议重新评估');
    }

    // 检查依赖关系
    const dependenciesClear = true; // 简化实现，实际可以检查任务间的逻辑依赖

    // 检查可执行性
    const actionable = actionableRate > 0.8;
    if (!actionable) {
      suggestions.push('部分任务缺乏明确的行动指导，建议添加具体的执行步骤');
    }

    // 质量建议
    if (smartScore < 60) {
      suggestions.push('建议优化任务描述，使其更符合SMART原则');
    }
    if (specificRate < 0.7) {
      suggestions.push('增加任务的具体性，明确要做什么');
    }
    if (measurableRate < 0.7) {
      suggestions.push('为任务添加可衡量的标准或指标');
    }
    if (timeBoundRate < 0.7) {
      suggestions.push('为更多任务设定明确的时间期限');
    }

    const assessment = {
      smartCompliance: smartScore,
      taskGranularity,
      timeRealistic,
      dependenciesClear,
      actionable,
      suggestions
    };

    // 通知父组件质量变化
    if (onQualityChange) {
      onQualityChange(assessment);
    }

    return assessment;
  }, [decompositionResult, onQualityChange]);

  const getQualityColor = (score: number) => {
    if (score >= 80) return '#52c41a';
    if (score >= 60) return '#faad14';
    return '#ff4d4f';
  };

  const getQualityText = (score: number) => {
    if (score >= 80) return '优秀';
    if (score >= 60) return '良好';
    if (score >= 40) return '一般';
    return '需改进';
  };

  if (compact) {
    return (
      <div style={{ marginBottom: 16 }}>
        <Space>
          <Tooltip title="分解质量检查帮助你评估目标分解的质量">
            <Button 
              type="link" 
              size="small" 
              icon={<BulbOutlined />}
              onClick={() => setShowHelper(!showHelper)}
            >
              分解质量检查
            </Button>
          </Tooltip>
          {decompositionResult && (
            <Tag color={qualityAssessment.smartCompliance >= 80 ? 'green' : 
                        qualityAssessment.smartCompliance >= 60 ? 'orange' : 'red'}>
              质量: {getQualityText(qualityAssessment.smartCompliance)}
            </Tag>
          )}
        </Space>
        
        {showHelper && decompositionResult && (
          <Card size="small" style={{ marginTop: 8 }}>
            <div style={{ marginBottom: 12 }}>
              <Text strong>分解质量评估</Text>
            </div>
            
            <div style={{ marginBottom: 12 }}>
              <div style={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', marginBottom: 4 }}>
                <Text>SMART合规性</Text>
                <Text style={{ color: getQualityColor(qualityAssessment.smartCompliance) }}>
                  {qualityAssessment.smartCompliance}%
                </Text>
              </div>
              <Progress 
                percent={qualityAssessment.smartCompliance} 
                strokeColor={getQualityColor(qualityAssessment.smartCompliance)}
                size="small"
              />
            </div>

            {qualityAssessment.suggestions.length > 0 && (
              <Alert
                message="改进建议"
                description={
                  <ul style={{ margin: '4px 0', paddingLeft: 16, fontSize: '12px' }}>
                    {qualityAssessment.suggestions.slice(0, 3).map((suggestion, index) => (
                      <li key={index}>{suggestion}</li>
                    ))}
                  </ul>
                }
                type="info"
                showIcon
                style={{ fontSize: '12px' }}
              />
            )}
          </Card>
        )}
      </div>
    );
  }

  return (
    <Card 
      title={
        <Space>
          <BulbOutlined />
          <span>分解质量检查</span>
          <Text type="secondary" style={{ fontWeight: 'normal', fontSize: '12px' }}>
            确保分解结果符合最佳实践
          </Text>
        </Space>
      }
      size="small"
      style={{ marginBottom: 16 }}
    >
      {!decompositionResult ? (
        <Alert
          message="等待分解结果"
          description="完成目标分解后，这里将显示质量评估和改进建议。"
          type="info"
          showIcon
        />
      ) : (
        <div>
          <div style={{ marginBottom: 16 }}>
            <div style={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', marginBottom: 8 }}>
              <Text strong>整体质量评分</Text>
              <Text style={{ color: getQualityColor(qualityAssessment.smartCompliance), fontSize: '18px', fontWeight: 'bold' }}>
                {qualityAssessment.smartCompliance}/100
              </Text>
            </div>
            <Progress 
              percent={qualityAssessment.smartCompliance} 
              strokeColor={getQualityColor(qualityAssessment.smartCompliance)}
            />
            <Text type="secondary" style={{ fontSize: '12px' }}>
              基于SMART原则的综合评估
            </Text>
          </div>

          <div style={{ display: 'grid', gridTemplateColumns: 'repeat(auto-fit, minmax(150px, 1fr))', gap: 8, marginBottom: 16 }}>
            <div style={{ textAlign: 'center', padding: '8px', border: '1px solid #f0f0f0', borderRadius: '6px' }}>
              <div style={{ fontSize: '14px', fontWeight: 'bold', color: qualityAssessment.taskGranularity === 'good' ? '#52c41a' : '#faad14' }}>
                {qualityAssessment.taskGranularity === 'good' ? '合适' : 
                 qualityAssessment.taskGranularity === 'too-large' ? '过大' : '过小'}
              </div>
              <div style={{ fontSize: '12px', color: '#666' }}>任务粒度</div>
            </div>
            
            <div style={{ textAlign: 'center', padding: '8px', border: '1px solid #f0f0f0', borderRadius: '6px' }}>
              <div style={{ fontSize: '14px', fontWeight: 'bold', color: qualityAssessment.timeRealistic ? '#52c41a' : '#ff4d4f' }}>
                {qualityAssessment.timeRealistic ? '合理' : '需调整'}
              </div>
              <div style={{ fontSize: '12px', color: '#666' }}>时间估算</div>
            </div>
            
            <div style={{ textAlign: 'center', padding: '8px', border: '1px solid #f0f0f0', borderRadius: '6px' }}>
              <div style={{ fontSize: '14px', fontWeight: 'bold', color: qualityAssessment.actionable ? '#52c41a' : '#ff4d4f' }}>
                {qualityAssessment.actionable ? '清晰' : '模糊'}
              </div>
              <div style={{ fontSize: '12px', color: '#666' }}>可执行性</div>
            </div>
          </div>

          {qualityAssessment.suggestions.length > 0 && (
            <Alert
              message={`发现 ${qualityAssessment.suggestions.length} 个改进建议`}
              description={
                <ul style={{ margin: '8px 0', paddingLeft: 16 }}>
                  {qualityAssessment.suggestions.map((suggestion, index) => (
                    <li key={index} style={{ marginBottom: 4 }}>{suggestion}</li>
                  ))}
                </ul>
              }
              type="warning"
              showIcon
            />
          )}

          <Collapse ghost size="small" defaultActiveKey={['1']}>
            <Panel header="查看SMART原则检查详情" key="1">
              <div style={{ fontSize: '12px' }}>
                <Paragraph>
                  <Text strong>SMART原则检查：</Text>
                  <br />
                  • <Text strong>S</Text>pecific (具体的): 任务描述是否明确具体
                  <br />
                  • <Text strong>M</Text>easurable (可衡量的): 是否有明确的完成标准
                  <br />
                  • <Text strong>A</Text>chievable (可实现的): 任务是否现实可行
                  <br />
                  • <Text strong>R</Text>elevant (相关的): 任务是否与目标相关
                  <br />
                  • <Text strong>T</Text>ime-bound (有时限的): 是否有明确的时间要求
                </Paragraph>
              </div>
            </Panel>
          </Collapse>
        </div>
      )}
    </Card>
  );
};

export default DecompositionQualityHelper;
