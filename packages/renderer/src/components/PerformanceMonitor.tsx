import React, { useState, useEffect, useRef } from 'react';
import { Card, Progress, Typography, Space, Tag, Alert, Button, Statistic, Row, Col } from 'antd';
import {
  DashboardOutlined,
  ThunderboltOutlined,
  DatabaseOutlined,
  ClockCircleOutlined,
  WarningOutlined,
  CheckCircleOutlined
} from '@ant-design/icons';

const { Title, Text } = Typography;

interface PerformanceMetrics {
  fps: number;
  memoryUsage: number;
  renderTime: number;
  componentCount: number;
  bundleSize: number;
  loadTime: number;
  interactionTime: number;
}

interface PerformanceAlert {
  type: 'warning' | 'error' | 'info';
  message: string;
  suggestion: string;
  timestamp: Date;
}

const PerformanceMonitor: React.FC<{
  enabled?: boolean;
  showDetails?: boolean;
}> = ({ enabled = false, showDetails = false }) => {
  const [metrics, setMetrics] = useState<PerformanceMetrics>({
    fps: 60,
    memoryUsage: 0,
    renderTime: 0,
    componentCount: 0,
    bundleSize: 0,
    loadTime: 0,
    interactionTime: 0
  });

  const [alerts, setAlerts] = useState<PerformanceAlert[]>([]);
  const [isMonitoring, setIsMonitoring] = useState(enabled);
  const frameCountRef = useRef(0);
  const lastTimeRef = useRef(performance.now());
  const monitoringIntervalRef = useRef<NodeJS.Timeout>();

  useEffect(() => {
    if (isMonitoring) {
      startMonitoring();
    } else {
      stopMonitoring();
    }

    return () => stopMonitoring();
  }, [isMonitoring]);

  const startMonitoring = () => {
    // 监控FPS
    const measureFPS = () => {
      frameCountRef.current++;
      const now = performance.now();
      
      if (now - lastTimeRef.current >= 1000) {
        const fps = Math.round((frameCountRef.current * 1000) / (now - lastTimeRef.current));
        setMetrics(prev => ({ ...prev, fps }));
        
        // 检查FPS性能
        if (fps < 30) {
          addAlert('warning', `FPS过低: ${fps}`, '考虑减少动画效果或优化渲染');
        }
        
        frameCountRef.current = 0;
        lastTimeRef.current = now;
      }
      
      if (isMonitoring) {
        requestAnimationFrame(measureFPS);
      }
    };
    
    requestAnimationFrame(measureFPS);

    // 定期收集其他指标
    monitoringIntervalRef.current = setInterval(() => {
      collectMetrics();
    }, 2000);

    // 监听性能事件
    if ('PerformanceObserver' in window) {
      try {
        // 监控长任务
        const longTaskObserver = new PerformanceObserver((list) => {
          for (const entry of list.getEntries()) {
            if (entry.duration > 50) {
              addAlert('warning', `检测到长任务: ${Math.round(entry.duration)}ms`, '考虑将任务分解为更小的块');
            }
          }
        });
        longTaskObserver.observe({ entryTypes: ['longtask'] });

        // 监控布局偏移
        const layoutShiftObserver = new PerformanceObserver((list) => {
          for (const entry of list.getEntries()) {
            if ((entry as any).value > 0.1) {
              addAlert('warning', '检测到布局偏移', '检查图片尺寸和动态内容加载');
            }
          }
        });
        layoutShiftObserver.observe({ entryTypes: ['layout-shift'] });
      } catch (error) {
        console.warn('Performance Observer not fully supported:', error);
      }
    }
  };

  const stopMonitoring = () => {
    if (monitoringIntervalRef.current) {
      clearInterval(monitoringIntervalRef.current);
    }
  };

  const collectMetrics = () => {
    // 内存使用情况
    if ('memory' in performance) {
      const memory = (performance as any).memory;
      const memoryUsage = Math.round((memory.usedJSHeapSize / memory.jsHeapSizeLimit) * 100);
      setMetrics(prev => ({ ...prev, memoryUsage }));
      
      if (memoryUsage > 80) {
        addAlert('error', `内存使用过高: ${memoryUsage}%`, '考虑清理未使用的数据或优化内存使用');
      }
    }

    // 渲染时间
    const renderStart = performance.now();
    setTimeout(() => {
      const renderTime = performance.now() - renderStart;
      setMetrics(prev => ({ ...prev, renderTime }));
    }, 0);

    // 组件数量（估算）
    const componentCount = document.querySelectorAll('[data-reactroot] *').length;
    setMetrics(prev => ({ ...prev, componentCount }));

    // 页面加载时间
    if (performance.timing) {
      const loadTime = performance.timing.loadEventEnd - performance.timing.navigationStart;
      setMetrics(prev => ({ ...prev, loadTime }));
    }

    // 交互时间（简化估算）
    const interactionTime = performance.now() - (window as any).startTime || 0;
    setMetrics(prev => ({ ...prev, interactionTime }));
  };

  const addAlert = (type: PerformanceAlert['type'], message: string, suggestion: string) => {
    const alert: PerformanceAlert = {
      type,
      message,
      suggestion,
      timestamp: new Date()
    };

    setAlerts(prev => {
      // 避免重复的警告
      const exists = prev.some(a => a.message === message && 
        Date.now() - a.timestamp.getTime() < 5000);
      
      if (exists) return prev;
      
      // 只保留最近的10个警告
      return [alert, ...prev.slice(0, 9)];
    });
  };

  const getPerformanceScore = () => {
    const fpsScore = Math.min(metrics.fps / 60 * 100, 100);
    const memoryScore = Math.max(100 - metrics.memoryUsage, 0);
    const renderScore = metrics.renderTime < 16 ? 100 : Math.max(100 - (metrics.renderTime - 16) * 5, 0);
    
    return Math.round((fpsScore + memoryScore + renderScore) / 3);
  };

  const getScoreColor = (score: number) => {
    if (score >= 80) return '#52c41a';
    if (score >= 60) return '#faad14';
    return '#ff4d4f';
  };

  const formatBytes = (bytes: number) => {
    if (bytes === 0) return '0 B';
    const k = 1024;
    const sizes = ['B', 'KB', 'MB', 'GB'];
    const i = Math.floor(Math.log(bytes) / Math.log(k));
    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
  };

  const formatTime = (ms: number) => {
    if (ms < 1000) return `${Math.round(ms)}ms`;
    return `${(ms / 1000).toFixed(1)}s`;
  };

  if (!showDetails && !isMonitoring) {
    return (
      <Button
        type="text"
        icon={<DashboardOutlined />}
        onClick={() => setIsMonitoring(true)}
        style={{
          position: 'fixed',
          bottom: 20,
          left: 20,
          zIndex: 1000,
          backgroundColor: 'rgba(255, 255, 255, 0.9)',
          border: '1px solid #d9d9d9',
          borderRadius: '50%',
          width: 40,
          height: 40
        }}
      />
    );
  }

  const performanceScore = getPerformanceScore();

  return (
    <div style={{
      position: showDetails ? 'relative' : 'fixed',
      bottom: showDetails ? 'auto' : 20,
      left: showDetails ? 'auto' : 20,
      zIndex: showDetails ? 'auto' : 1000,
      width: showDetails ? '100%' : 320,
      maxWidth: showDetails ? 'none' : 320
    }}>
      <Card
        title={
          <Space>
            <DashboardOutlined />
            <span>性能监控</span>
            {!showDetails && (
              <Button
                type="text"
                size="small"
                onClick={() => setIsMonitoring(false)}
              >
                ×
              </Button>
            )}
          </Space>
        }
        size="small"
        style={{
          backgroundColor: showDetails ? 'transparent' : 'rgba(255, 255, 255, 0.95)',
          backdropFilter: showDetails ? 'none' : 'blur(10px)',
          border: showDetails ? 'none' : '1px solid #d9d9d9'
        }}
      >
        {/* 性能评分 */}
        <div style={{ textAlign: 'center', marginBottom: 16 }}>
          <Progress
            type="circle"
            percent={performanceScore}
            format={() => performanceScore}
            strokeColor={getScoreColor(performanceScore)}
            size={showDetails ? 120 : 80}
          />
          <div style={{ marginTop: 8 }}>
            <Text style={{ color: getScoreColor(performanceScore), fontWeight: 'bold' }}>
              {performanceScore >= 80 ? '优秀' : performanceScore >= 60 ? '良好' : '需优化'}
            </Text>
          </div>
        </div>

        {/* 详细指标 */}
        <Row gutter={[8, 8]}>
          <Col span={showDetails ? 6 : 12}>
            <Statistic
              title="FPS"
              value={metrics.fps}
              prefix={<ThunderboltOutlined />}
              valueStyle={{ fontSize: showDetails ? 16 : 14 }}
            />
          </Col>
          <Col span={showDetails ? 6 : 12}>
            <Statistic
              title="内存"
              value={metrics.memoryUsage}
              suffix="%"
              prefix={<DatabaseOutlined />}
              valueStyle={{ 
                fontSize: showDetails ? 16 : 14,
                color: metrics.memoryUsage > 80 ? '#ff4d4f' : undefined
              }}
            />
          </Col>
          {showDetails && (
            <>
              <Col span={6}>
                <Statistic
                  title="渲染时间"
                  value={metrics.renderTime.toFixed(1)}
                  suffix="ms"
                  prefix={<ClockCircleOutlined />}
                  valueStyle={{ fontSize: 16 }}
                />
              </Col>
              <Col span={6}>
                <Statistic
                  title="组件数"
                  value={metrics.componentCount}
                  valueStyle={{ fontSize: 16 }}
                />
              </Col>
            </>
          )}
        </Row>

        {/* 性能警告 */}
        {alerts.length > 0 && (
          <div style={{ marginTop: 16 }}>
            <Text strong style={{ fontSize: 12 }}>性能警告:</Text>
            <div style={{ maxHeight: showDetails ? 200 : 100, overflowY: 'auto', marginTop: 8 }}>
              {alerts.slice(0, showDetails ? 10 : 3).map((alert, index) => (
                <Alert
                  key={index}
                  message={alert.message}
                  description={showDetails ? alert.suggestion : undefined}
                  type={alert.type}
                  size="small"
                  style={{ marginBottom: 4, fontSize: 11 }}
                  showIcon
                />
              ))}
            </div>
          </div>
        )}

        {/* 性能建议 */}
        {showDetails && performanceScore < 80 && (
          <div style={{ marginTop: 16 }}>
            <Alert
              message="性能优化建议"
              description={
                <ul style={{ margin: 0, paddingLeft: 16, fontSize: 12 }}>
                  {metrics.fps < 60 && <li>减少动画效果和复杂渲染</li>}
                  {metrics.memoryUsage > 70 && <li>清理未使用的数据和组件</li>}
                  {metrics.renderTime > 16 && <li>优化组件渲染逻辑</li>}
                  {metrics.componentCount > 1000 && <li>考虑虚拟化长列表</li>}
                </ul>
              }
              type="info"
              showIcon
            />
          </div>
        )}

        {/* 控制按钮 */}
        {!showDetails && (
          <div style={{ textAlign: 'center', marginTop: 12 }}>
            <Space>
              <Button
                size="small"
                onClick={() => setAlerts([])}
                disabled={alerts.length === 0}
              >
                清除警告
              </Button>
              <Button
                size="small"
                onClick={() => collectMetrics()}
              >
                刷新
              </Button>
            </Space>
          </div>
        )}
      </Card>
    </div>
  );
};

export default PerformanceMonitor;
