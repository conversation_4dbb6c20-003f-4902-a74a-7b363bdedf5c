import React, { createContext, useContext, useState, useEffect, ReactNode, useMemo, useCallback } from 'react';
import { ThemeConfig, getTheme } from '../themes';
import { DatabaseAPI } from '../services/api';

interface ThemeContextType {
  theme: ThemeConfig;
  themeId: string;
  setTheme: (themeId: string) => void;
  isLoading: boolean;
}

const ThemeContext = createContext<ThemeContextType | undefined>(undefined);

export const useTheme = () => {
  const context = useContext(ThemeContext);
  if (context === undefined) {
    throw new Error('useTheme must be used within a ThemeProvider');
  }
  return context;
};

interface ThemeProviderProps {
  children: ReactNode;
}

export const ThemeProvider: React.FC<ThemeProviderProps> = ({ children }) => {
  const [themeId, setThemeId] = useState('skyBlue');
  const [theme, setThemeState] = useState<ThemeConfig>(getTheme('skyBlue'));
  const [isLoading, setIsLoading] = useState(true);

  // 从数据库加载主题设置
  useEffect(() => {
    const loadTheme = async () => {
      try {
        const savedTheme = await DatabaseAPI.getSetting('theme');
        if (savedTheme && savedTheme !== 'light') {
          setThemeId(savedTheme);
          setThemeState(getTheme(savedTheme));
        }
      } catch (error) {
        console.error('加载主题设置失败:', error);
      } finally {
        setIsLoading(false);
      }
    };

    loadTheme();
  }, []);

  // 切换主题 - 使用useCallback缓存函数
  const setTheme = useCallback(async (newThemeId: string) => {
    try {
      setThemeId(newThemeId);
      const newTheme = getTheme(newThemeId);
      setThemeState(newTheme);

      // 立即应用新主题的CSS变量
      const root = document.documentElement;

      // 设置主题标识
      root.setAttribute('data-theme', newThemeId);

      // 立即设置CSS变量 - 确保所有变量都被正确映射
      Object.entries(newTheme.colors).forEach(([key, value]) => {
        // 将camelCase转换为kebab-case
        const cssKey = key.replace(/([A-Z])/g, '-$1').toLowerCase();
        root.style.setProperty(`--color-${cssKey}`, value);
      });

      // 立即设置背景
      document.body.style.background = newTheme.colors.background;
      document.documentElement.style.background = newTheme.colors.background;

      // 为深色主题立即添加特殊处理
      if (['darkNight', 'darkBlue'].includes(newThemeId)) {
        document.body.style.backgroundColor = '#000000';
        document.documentElement.style.backgroundColor = '#000000';
        root.classList.add('dark-theme');
        root.classList.remove('light-theme');
      } else {
        root.classList.add('light-theme');
        root.classList.remove('dark-theme');
      }

      // 强制重绘
      document.body.style.display = 'none';
      document.body.offsetHeight; // 触发重排
      document.body.style.display = '';

      await DatabaseAPI.setSetting('theme', newThemeId);
    } catch (error) {
      console.error('保存主题设置失败:', error);
    }
  }, []);

  // 应用CSS变量
  useEffect(() => {
    const root = document.documentElement;

    // 设置主题标识
    root.setAttribute('data-theme', themeId);

    // 设置CSS变量 - 确保所有变量都被正确映射
    Object.entries(theme.colors).forEach(([key, value]) => {
      // 将camelCase转换为kebab-case
      const cssKey = key.replace(/([A-Z])/g, '-$1').toLowerCase();
      root.style.setProperty(`--color-${cssKey}`, value);
    });

    // 设置Glass morphism变量
    Object.entries(theme.glassMorphism).forEach(([key, value]) => {
      root.style.setProperty(`--glass-${key}`, value);
    });

    // 提取主色调的RGB值用于透明度计算
    const primaryColor = theme.colors.primary;
    if (primaryColor.startsWith('#')) {
      const hex = primaryColor.slice(1);
      const r = parseInt(hex.slice(0, 2), 16);
      const g = parseInt(hex.slice(2, 4), 16);
      const b = parseInt(hex.slice(4, 6), 16);
      root.style.setProperty('--color-primary-rgb', `${r}, ${g}, ${b}`);
    }

    // 设置背景 - 同时设置body和html
    document.body.style.background = theme.colors.background;
    document.documentElement.style.background = theme.colors.background;

    // 为深色主题添加特殊处理
    if (['darkNight', 'darkBlue'].includes(themeId)) {
      // 强制设置深色背景
      document.body.style.backgroundColor = '#000000';
      document.documentElement.style.backgroundColor = '#000000';

      // 设置深色主题标识
      root.classList.add('dark-theme');
      root.classList.remove('light-theme');
    } else {
      // 浅色主题
      root.classList.add('light-theme');
      root.classList.remove('dark-theme');
    }
  }, [theme, themeId]);

  // 使用useMemo缓存context值，避免不必要的重渲染
  const value: ThemeContextType = useMemo(() => ({
    theme,
    themeId,
    setTheme,
    isLoading,
  }), [theme, themeId, setTheme, isLoading]);

  return (
    <ThemeContext.Provider value={value}>
      {children}
    </ThemeContext.Provider>
  );
};