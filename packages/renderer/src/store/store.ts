import { configureStore } from '@reduxjs/toolkit';
import goalsReducer from './slices/goalsSlice';
import tasksReducer from './slices/tasksSlice';
import pomodoroReducer from './slices/pomodoroSlice';
import { performanceMiddlewares } from './middleware/performanceMiddleware';

export const store = configureStore({
  reducer: {
    goals: goalsReducer,
    tasks: tasksReducer,
    pomodoro: pomodoroReducer,
  },
  middleware: (getDefaultMiddleware) =>
    getDefaultMiddleware({
      serializableCheck: {
        // 忽略这些action类型的序列化检查
        ignoredActions: [
          'persist/PERSIST',
          'persist/REHYDRATE',
          'goals/loadGoals/fulfilled',
          'goals/createGoalAsync/fulfilled',
          'goals/updateGoalAsync/fulfilled'
        ],
        // 忽略这些路径的序列化检查
        ignoredPaths: [
          'register',
          'goals.goals',
          'tasks.tasks',
          'pomodoro.sessions'
        ],
        // 忽略这些action路径 - 使用通配符忽略所有Date字段
        ignoredActionPaths: [
          'payload',
          'meta.arg',
          'meta.requestId'
        ],
      },
      // 在生产环境中禁用不可变性检查以提高性能
      immutableCheck: process.env.NODE_ENV === 'development' ? {
        warnAfter: 32,
      } : false,
      // 启用thunk检查
      thunk: {
        extraArgument: {
          // 可以在这里添加额外的参数给thunk
        },
      },
    }).concat(performanceMiddlewares),

  // 开发工具配置
  devTools: process.env.NODE_ENV === 'development' && {
    // 限制action历史记录数量
    maxAge: 30,
    // 禁用trace以提高性能
    trace: false,
    // 启用跳跃调试
    jumpToAction: false,
  },

  // 预加载状态
  preloadedState: undefined,

  // 增强器
  enhancers: (getDefaultEnhancers) => getDefaultEnhancers(),
});

export type RootState = ReturnType<typeof store.getState>;
export type AppDispatch = typeof store.dispatch;

// 导出store的类型化hooks
export const useAppDispatch = () => store.dispatch;
export const useAppSelector = <T>(selector: (state: RootState) => T) => selector(store.getState());