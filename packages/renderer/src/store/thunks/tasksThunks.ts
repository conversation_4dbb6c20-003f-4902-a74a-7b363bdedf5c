import { createAsyncThunk } from '@reduxjs/toolkit';
import { DatabaseAPI } from '../../services/api';
import { Task, CreateTaskDto } from '../../types';

// 加载所有任务
export const loadTasks = createAsyncThunk(
  'tasks/loadTasks',
  async () => {
    return await DatabaseAPI.getTasks();
  }
);

// 创建任务
export const createTaskAsync = createAsyncThunk(
  'tasks/createTask',
  async (taskData: CreateTaskDto) => {
    return await DatabaseAPI.createTask(taskData);
  }
);

// 更新任务
export const updateTaskAsync = createAsyncThunk(
  'tasks/updateTask',
  async ({ id, updates }: { id: string; updates: Partial<Task> }) => {
    await DatabaseAPI.updateTask(id, updates);
    return { id, updates };
  }
);

// 删除任务
export const deleteTaskAsync = createAsyncThunk(
  'tasks/deleteTask',
  async (id: string) => {
    await DatabaseAPI.deleteTask(id);
    return id;
  }
);