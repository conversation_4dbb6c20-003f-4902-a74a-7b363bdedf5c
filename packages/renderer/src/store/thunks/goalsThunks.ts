import { createAsyncThunk } from '@reduxjs/toolkit';
import { DatabaseAPI } from '../../services/api';
import { Goal, CreateGoalDto } from '../../types';

// 数据转换函数
const convertDatesInGoal = (goal: any): Goal => {
  return {
    ...goal,
    startDate: goal.startDate ? new Date(goal.startDate) : undefined,
    deadline: goal.deadline ? new Date(goal.deadline) : undefined,
    createdAt: new Date(goal.createdAt),
    updatedAt: new Date(goal.updatedAt),
    analysis: goal.analysis ? {
      ...goal.analysis,
      suggestedDeadline: goal.analysis.suggestedDeadline ? new Date(goal.analysis.suggestedDeadline) : undefined,
      analyzedAt: new Date(goal.analysis.analyzedAt)
    } : undefined
  };
};

// 加载所有目标
export const loadGoals = createAsyncThunk(
  'goals/loadGoals',
  async () => {
    const goals = await DatabaseAPI.getGoals();
    // 将字符串日期转换为Date对象
    return goals.map(convertDatesInGoal);
  }
);

// 创建目标
export const createGoalAsync = createAsyncThunk(
  'goals/createGoal',
  async (goalData: CreateGoalDto) => {
    const goal = await DatabaseAPI.createGoal(goalData);
    return convertDatesInGoal(goal);
  }
);

// 更新目标
export const updateGoalAsync = createAsyncThunk(
  'goals/updateGoal',
  async ({ id, updates }: { id: string; updates: Partial<Goal> }) => {
    await DatabaseAPI.updateGoal(id, updates);
    return { id, updates };
  }
);

// 删除目标
export const deleteGoalAsync = createAsyncThunk(
  'goals/deleteGoal',
  async (id: string) => {
    await DatabaseAPI.deleteGoal(id);
    return id;
  }
);