import { createAsyncThunk } from '@reduxjs/toolkit';
import { DatabaseAPI } from '../../services/api';
import { PomodoroSession, CreatePomodoroSessionDto } from '../../types';

// 加载番茄钟会话历史
export const loadPomodoroSessions = createAsyncThunk(
  'pomodoro/loadSessions',
  async () => {
    try {
      const sessions = await DatabaseAPI.getPomodoroSessions();
      return sessions;
    } catch (error) {
      console.error('加载番茄钟会话失败:', error);
      throw error;
    }
  }
);

// 创建新的番茄钟会话
export const createPomodoroSessionAsync = createAsyncThunk(
  'pomodoro/createSession',
  async (sessionData: CreatePomodoroSessionDto) => {
    try {
      // 确保传递完整的会话数据，包含所有必需字段的默认值
      const completeSessionData = {
        ...sessionData,
        endTime: undefined, // 新会话没有结束时间
        isCompleted: false, // 新会话未完成
        wasInterrupted: false, // 新会话未被中断
      };

      const session = await DatabaseAPI.createPomodoroSession(completeSessionData);
      return session;
    } catch (error) {
      console.error('创建番茄钟会话失败:', error);
      throw error;
    }
  }
);

// 更新番茄钟会话
export const updatePomodoroSessionAsync = createAsyncThunk(
  'pomodoro/updateSession',
  async ({ id, updates }: { id: string; updates: Partial<PomodoroSession> }) => {
    try {
      const session = await DatabaseAPI.updatePomodoroSession(id, updates);
      return session;
    } catch (error) {
      console.error('更新番茄钟会话失败:', error);
      throw error;
    }
  }
);

// 完成番茄钟会话
export const completePomodoroSessionAsync = createAsyncThunk(
  'pomodoro/completeSession',
  async ({ sessionId, actualDuration, wasInterrupted = false }: { 
    sessionId: string; 
    actualDuration: number; 
    wasInterrupted?: boolean; 
  }) => {
    try {
      const session = await DatabaseAPI.updatePomodoroSession(sessionId, {
        endTime: new Date(),
        duration: actualDuration,
        isCompleted: true,
        wasInterrupted
      });
      return session;
    } catch (error) {
      console.error('完成番茄钟会话失败:', error);
      throw error;
    }
  }
);

// 获取番茄钟统计数据
export const getPomodoroStatsAsync = createAsyncThunk(
  'pomodoro/getStats',
  async (options: { 
    startDate?: Date; 
    endDate?: Date; 
    taskId?: string; 
  } = {}) => {
    try {
      const sessions = await DatabaseAPI.getPomodoroSessions();
      
      let filteredSessions = sessions;
      
      // 按日期过滤
      if (options.startDate) {
        filteredSessions = filteredSessions.filter(
          session => new Date(session.startTime) >= options.startDate!
        );
      }
      
      if (options.endDate) {
        filteredSessions = filteredSessions.filter(
          session => new Date(session.startTime) <= options.endDate!
        );
      }
      
      // 按任务过滤
      if (options.taskId) {
        filteredSessions = filteredSessions.filter(
          session => session.taskId === options.taskId
        );
      }
      
      // 计算统计数据
      const stats = {
        totalSessions: filteredSessions.length,
        completedSessions: filteredSessions.filter(s => s.isCompleted).length,
        totalFocusTime: filteredSessions
          .filter(s => s.type === 'work' && s.isCompleted)
          .reduce((total, session) => total + session.duration, 0),
        averageSessionDuration: 0,
        interruptionRate: 0,
        dailyStats: {} as Record<string, number>,
        weeklyStats: {} as Record<string, number>
      };
      
      if (stats.completedSessions > 0) {
        stats.averageSessionDuration = stats.totalFocusTime / stats.completedSessions;
        stats.interruptionRate = filteredSessions.filter(s => s.wasInterrupted).length / stats.totalSessions;
      }
      
      // 计算每日统计
      filteredSessions.forEach(session => {
        const date = new Date(session.startTime).toISOString().split('T')[0];
        if (session.type === 'work' && session.isCompleted) {
          stats.dailyStats[date] = (stats.dailyStats[date] || 0) + session.duration;
        }
      });
      
      return stats;
    } catch (error) {
      console.error('获取番茄钟统计失败:', error);
      throw error;
    }
  }
);

// 加载番茄钟设置
export const loadPomodoroSettingsAsync = createAsyncThunk(
  'pomodoro/loadSettings',
  async () => {
    try {
      const settings = await DatabaseAPI.getAllSettings();
      return {
        workDuration: parseInt(settings.pomodoro_work_duration || '25'),
        shortBreakDuration: parseInt(settings.pomodoro_short_break_duration || '5'),
        longBreakDuration: parseInt(settings.pomodoro_long_break_duration || '15'),
        sessionsUntilLongBreak: parseInt(settings.pomodoro_sessions_until_long_break || '4'),
        autoStartBreaks: settings.pomodoro_auto_start_breaks === 'true',
        autoStartNextSession: settings.pomodoro_auto_start_next_session === 'true'
      };
    } catch (error) {
      console.error('加载番茄钟设置失败:', error);
      throw error;
    }
  }
);

// 保存番茄钟设置
export const savePomodoroSettingsAsync = createAsyncThunk(
  'pomodoro/saveSettings',
  async (settings: {
    workDuration?: number;
    shortBreakDuration?: number;
    longBreakDuration?: number;
    sessionsUntilLongBreak?: number;
    autoStartBreaks?: boolean;
    autoStartNextSession?: boolean;
  }) => {
    try {
      // 只保存提供的字段
      const promises = [];
      
      if (settings.workDuration !== undefined) {
        promises.push(DatabaseAPI.setSetting('pomodoro_work_duration', settings.workDuration.toString()));
      }
      if (settings.shortBreakDuration !== undefined) {
        promises.push(DatabaseAPI.setSetting('pomodoro_short_break_duration', settings.shortBreakDuration.toString()));
      }
      if (settings.longBreakDuration !== undefined) {
        promises.push(DatabaseAPI.setSetting('pomodoro_long_break_duration', settings.longBreakDuration.toString()));
      }
      if (settings.sessionsUntilLongBreak !== undefined) {
        promises.push(DatabaseAPI.setSetting('pomodoro_sessions_until_long_break', settings.sessionsUntilLongBreak.toString()));
      }
      if (settings.autoStartBreaks !== undefined) {
        promises.push(DatabaseAPI.setSetting('pomodoro_auto_start_breaks', settings.autoStartBreaks.toString()));
      }
      if (settings.autoStartNextSession !== undefined) {
        promises.push(DatabaseAPI.setSetting('pomodoro_auto_start_next_session', settings.autoStartNextSession.toString()));
      }
      
      await Promise.all(promises);
      
      // 返回完整的设置对象（加载最新值）
      const allSettings = await DatabaseAPI.getAllSettings();
      return {
        workDuration: parseInt(allSettings.pomodoro_work_duration || '25'),
        shortBreakDuration: parseInt(allSettings.pomodoro_short_break_duration || '5'),
        longBreakDuration: parseInt(allSettings.pomodoro_long_break_duration || '15'),
        sessionsUntilLongBreak: parseInt(allSettings.pomodoro_sessions_until_long_break || '4'),
        autoStartBreaks: allSettings.pomodoro_auto_start_breaks === 'true',
        autoStartNextSession: allSettings.pomodoro_auto_start_next_session === 'true'
      };
    } catch (error) {
      console.error('保存番茄钟设置失败:', error);
      throw error;
    }
  }
);