import { createSlice, PayloadAction } from '@reduxjs/toolkit';
import { Goal, CreateGoalDto } from '../../types';
import { loadGoals, createGoalAsync, updateGoalAsync, deleteGoalAsync } from '../thunks/goalsThunks';

interface GoalsState {
  goals: Goal[];
  loading: boolean;
  error: string | null;
}

const initialState: GoalsState = {
  goals: [],
  loading: false,
  error: null,
};

const goalsSlice = createSlice({
  name: 'goals',
  initialState,
  reducers: {
    setLoading: (state, action: PayloadAction<boolean>) => {
      state.loading = action.payload;
    },
    setError: (state, action: PayloadAction<string | null>) => {
      state.error = action.payload;
    },
    setGoals: (state, action: PayloadAction<Goal[]>) => {
      state.goals = action.payload;
    },
    addGoal: (state, action: PayloadAction<Goal>) => {
      state.goals.push(action.payload);
    },
    updateGoal: (state, action: PayloadAction<Goal>) => {
      const index = state.goals.findIndex(goal => goal.id === action.payload.id);
      if (index !== -1) {
        state.goals[index] = action.payload;
      }
    },
    deleteGoal: (state, action: PayloadAction<string>) => {
      state.goals = state.goals.filter(goal => goal.id !== action.payload);
    },
  },
  extraReducers: (builder) => {
    builder
      // 加载目标
      .addCase(loadGoals.pending, (state) => {
        state.loading = true;
        state.error = null;
      })
      .addCase(loadGoals.fulfilled, (state, action) => {
        state.loading = false;
        state.goals = action.payload;
      })
      .addCase(loadGoals.rejected, (state, action) => {
        state.loading = false;
        state.error = action.error.message || '加载目标失败';
      })
      // 创建目标
      .addCase(createGoalAsync.pending, (state) => {
        state.loading = true;
        state.error = null;
      })
      .addCase(createGoalAsync.fulfilled, (state, action) => {
        state.loading = false;
        state.goals.push(action.payload);
      })
      .addCase(createGoalAsync.rejected, (state, action) => {
        state.loading = false;
        state.error = action.error.message || '创建目标失败';
      })
      // 更新目标
      .addCase(updateGoalAsync.fulfilled, (state, action) => {
        const { id, updates } = action.payload;
        const index = state.goals.findIndex(goal => goal.id === id);
        if (index !== -1) {
          state.goals[index] = { ...state.goals[index], ...updates };
        }
      })
      // 删除目标
      .addCase(deleteGoalAsync.fulfilled, (state, action) => {
        state.goals = state.goals.filter(goal => goal.id !== action.payload);
      });
  },
});

export const { setLoading, setError, setGoals, addGoal, updateGoal, deleteGoal } = goalsSlice.actions;
export default goalsSlice.reducer;