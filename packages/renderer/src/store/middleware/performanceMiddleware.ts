import { Middleware } from '@reduxjs/toolkit';

// 防抖中间件 - 防止频繁的相同操作
const createDebounceMiddleware = (): Middleware => {
  const debounceMap = new Map<string, NodeJS.Timeout>();

  return (store) => (next) => (action) => {
    // 只对特定的action类型进行防抖
    const debounceActions = [
      'tasks/updateTask',
      'goals/updateGoal',
      'pomodoro/updateSession',
    ];

    if (debounceActions.some(type => action.type.includes(type))) {
      const key = `${action.type}_${JSON.stringify(action.payload)}`;
      
      // 清除之前的定时器
      if (debounceMap.has(key)) {
        clearTimeout(debounceMap.get(key)!);
      }

      // 设置新的定时器
      const timeoutId = setTimeout(() => {
        next(action);
        debounceMap.delete(key);
      }, 300); // 300ms防抖

      debounceMap.set(key, timeoutId);
      return;
    }

    return next(action);
  };
};

// 缓存中间件 - 缓存API响应
const createCacheMiddleware = (): Middleware => {
  const cache = new Map<string, { data: any; timestamp: number; ttl: number }>();
  const CACHE_TTL = 5 * 60 * 1000; // 5分钟缓存

  return (store) => (next) => (action) => {
    // 只对读取操作进行缓存
    const cacheableActions = [
      'tasks/loadTasks',
      'goals/loadGoals',
      'pomodoro/loadSessions',
      'pomodoro/getStats',
    ];

    if (cacheableActions.includes(action.type)) {
      const cacheKey = `${action.type}_${JSON.stringify(action.payload || {})}`;
      const cached = cache.get(cacheKey);

      // 检查缓存是否有效
      if (cached && Date.now() - cached.timestamp < cached.ttl) {
        // 直接返回缓存的数据
        store.dispatch({
          type: `${action.type}/fulfilled`,
          payload: cached.data,
        });
        return;
      }

      // 执行原始action
      const result = next(action);

      // 如果是Promise，缓存结果
      if (result && typeof result.then === 'function') {
        result.then((resolvedAction: any) => {
          if (resolvedAction.type.endsWith('/fulfilled')) {
            cache.set(cacheKey, {
              data: resolvedAction.payload,
              timestamp: Date.now(),
              ttl: CACHE_TTL,
            });
          }
        });
      }

      return result;
    }

    return next(action);
  };
};

// 批处理中间件 - 批量处理多个action
const createBatchMiddleware = (): Middleware => {
  let batchQueue: any[] = [];
  let batchTimeout: NodeJS.Timeout | null = null;

  return (store) => (next) => (action) => {
    // 需要批处理的action类型
    const batchableActions = [
      'tasks/setTasks',
      'goals/setGoals',
      'pomodoro/setSessions',
    ];

    if (batchableActions.includes(action.type)) {
      batchQueue.push(action);

      // 清除之前的定时器
      if (batchTimeout) {
        clearTimeout(batchTimeout);
      }

      // 设置批处理定时器
      batchTimeout = setTimeout(() => {
        // 合并相同类型的action
        const mergedActions = new Map<string, any>();
        
        batchQueue.forEach(batchAction => {
          const existing = mergedActions.get(batchAction.type);
          if (existing) {
            // 合并payload
            mergedActions.set(batchAction.type, {
              ...batchAction,
              payload: [...existing.payload, ...batchAction.payload],
            });
          } else {
            mergedActions.set(batchAction.type, batchAction);
          }
        });

        // 执行合并后的actions
        mergedActions.forEach(mergedAction => {
          next(mergedAction);
        });

        // 清空队列
        batchQueue = [];
        batchTimeout = null;
      }, 50); // 50ms批处理延迟

      return;
    }

    return next(action);
  };
};

// 性能监控中间件
const createPerformanceMiddleware = (): Middleware => {
  return (store) => (next) => (action) => {
    const start = performance.now();
    
    const result = next(action);
    
    const end = performance.now();
    const duration = end - start;

    // 记录慢操作
    if (duration > 100) { // 超过100ms的操作
      console.warn(`Slow action detected: ${action.type} took ${duration.toFixed(2)}ms`);
    }

    // 在开发环境下记录所有action的性能
    if (process.env.NODE_ENV === 'development') {
      console.log(`Action ${action.type}: ${duration.toFixed(2)}ms`);
    }

    return result;
  };
};

// 错误处理中间件
const createErrorHandlingMiddleware = (): Middleware => {
  return (store) => (next) => (action) => {
    try {
      return next(action);
    } catch (error) {
      console.error('Redux action error:', {
        action,
        error,
        state: store.getState(),
      });

      // 可以在这里添加错误上报逻辑
      // errorReporting.captureException(error, { action });

      // 继续抛出错误，让上层处理
      throw error;
    }
  };
};

// 状态持久化中间件
const createPersistenceMiddleware = (): Middleware => {
  const PERSIST_ACTIONS = [
    'pomodoro/updateSettings',
    'theme/setTheme',
  ];

  return (store) => (next) => (action) => {
    const result = next(action);

    // 对特定action进行状态持久化
    if (PERSIST_ACTIONS.some(type => action.type.includes(type))) {
      // 使用requestIdleCallback在空闲时持久化
      if ('requestIdleCallback' in window) {
        window.requestIdleCallback(() => {
          const state = store.getState();
          // 这里可以实现状态持久化逻辑
          // localStorage.setItem('app-state', JSON.stringify(state));
        });
      }
    }

    return result;
  };
};

// 导出所有中间件
export const performanceMiddlewares = [
  createErrorHandlingMiddleware(),
  createPerformanceMiddleware(),
  createDebounceMiddleware(),
  createCacheMiddleware(),
  createBatchMiddleware(),
  createPersistenceMiddleware(),
];

// 清理缓存的工具函数
export const clearCache = () => {
  // 这里可以实现缓存清理逻辑
  console.log('Cache cleared');
};

// 获取性能统计的工具函数
export const getPerformanceStats = () => {
  // 这里可以返回性能统计数据
  return {
    cacheHitRate: 0,
    averageActionDuration: 0,
    slowActions: [],
  };
};
