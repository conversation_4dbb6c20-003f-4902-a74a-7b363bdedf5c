import React from 'react';
import { ThemeProvider } from './contexts/ThemeContext';
import AppLayout from './components/layout/AppLayout';
import PerformanceMonitor from './components/performance/PerformanceMonitor';
import { InterventionManager } from './components/InterventionModal';

// 加载音频调试工具（仅在开发环境）
if (process.env.NODE_ENV === 'development') {
  import('./utils/audioTestRunner');
}

const App: React.FC = () => {
  return (
    <ThemeProvider>
      <AppLayout />
      {/* 全局干预管理器 */}
      <InterventionManager />
      {/* 只在开发环境显示性能监控 */}
      {process.env.NODE_ENV === 'development' && <PerformanceMonitor />}
    </ThemeProvider>
  );
};

export default App;