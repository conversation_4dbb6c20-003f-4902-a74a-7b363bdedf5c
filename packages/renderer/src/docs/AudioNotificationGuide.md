# FocusOS 音频通知系统使用指南

## 概述

FocusOS 的音频通知系统为各种应用场景提供了丰富的提示音功能，包括任务完成、番茄钟提醒、目标达成等。系统采用现代Web Audio API技术，同时提供HTML5 Audio作为备用方案，确保在各种浏览器环境下的兼容性。

## 功能特性

### 🎵 多种提示音类型
- **任务完成** - 完成任务时的成就感音效
- **番茄钟完成** - 工作时段结束的清脆提醒
- **休息结束** - 温和的工作开始提醒
- **目标达成** - 重要目标完成的庆祝音效
- **系统提示** - 重要系统通知的简洁音效
- **轻柔提醒** - 日常提醒的温和音效

### 🔧 灵活的配置选项
- 提示音开关控制
- 音量调节（0-100%）
- 音效主题选择（默认/轻柔/活力）
- 单独的音效预览功能

### 🌐 浏览器兼容性
- 自动检测Web Audio API支持
- HTML5 Audio备用方案
- 智能处理浏览器自动播放策略
- 用户交互状态管理

## 使用方法

### 基础使用

```typescript
import { NotificationService } from '../services/NotificationService';

const notificationService = NotificationService.getInstance();

// 任务完成通知
await notificationService.notifyTaskComplete('完成了重要任务');

// 番茄钟完成通知
await notificationService.notifyPomodoroComplete('work', 3);

// 目标达成通知
await notificationService.notifyGoalAchieved('月度目标');
```

### 高级配置

```typescript
import { AudioService } from '../services/AudioService';

const audioService = AudioService.getInstance();

// 更新音频设置
audioService.updateSettings({
  enabled: true,
  volume: 0.8,
  soundTheme: 'gentle'
});

// 测试特定音效
await audioService.testSound('task-complete');

// 获取音频状态
const status = audioService.getAudioStatus();
console.log('音频支持状态:', status);
```

## 设置界面

### 访问音频设置
1. 打开应用设置页面
2. 切换到"通知设置"标签
3. 找到"音频设置"卡片

### 配置选项说明

#### 基础设置
- **启用提示音**: 全局开关，控制所有音效播放
- **音量控制**: 滑块调节，支持0-100%音量设置

#### 音效主题
- **默认音效**: 清晰简洁的标准提示音
- **轻柔音效**: 温和舒缓的柔和提示音
- **活力音效**: 明快有力的动感提示音

#### 音效预览
- 每种音效类型都提供"试听"按钮
- 实时预览当前音量和主题设置
- 支持在设置过程中即时测试

## 技术实现

### 架构设计

```
AudioService (音频播放服务)
├── AudioContextManager (音频上下文管理)
├── 音频文件预加载
├── Web Audio API播放
├── HTML5 Audio备用
└── 合成音频生成

NotificationService (通知服务)
├── 桌面通知
├── 音频播放集成
├── Electron系统通知
└── 通知类型管理
```

### 浏览器兼容性处理

1. **自动播放策略**
   - 检测用户交互状态
   - 自动激活音频上下文
   - 提供用户交互提示

2. **音频API降级**
   - 优先使用Web Audio API
   - 自动降级到HTML5 Audio
   - 最终降级到合成音频

3. **错误处理**
   - 音频加载失败处理
   - 播放错误恢复
   - 状态监控和报告

## 性能优化

### 音频预加载
- 应用启动时预加载所有音效文件
- 使用Map缓存音频缓冲区
- 支持异步加载和错误恢复

### 内存管理
- 音频缓冲区复用
- 及时释放音频节点
- 避免内存泄漏

### 播放优化
- 音频节点池化
- 避免重复创建AudioContext
- 智能音量控制

## 故障排除

### 常见问题

1. **音效无法播放**
   - 检查浏览器是否支持音频播放
   - 确认用户已与页面交互
   - 验证音频文件是否正确加载

2. **音量过小或过大**
   - 调整设置中的音量滑块
   - 检查系统音量设置
   - 确认音效主题选择

3. **延迟播放**
   - 检查网络连接状态
   - 验证音频文件加载状态
   - 考虑使用合成音频备用

### 调试工具

使用内置的音频测试面板：
1. 打开设置页面
2. 找到"音频功能测试"卡片
3. 运行各种测试场景
4. 查看详细的状态信息

## 最佳实践

### 用户体验
- 提供清晰的音频状态反馈
- 支持音效预览功能
- 保持音效简洁不刺耳
- 遵循Apple简约设计原则

### 开发建议
- 始终提供音频播放的备用方案
- 正确处理异步音频操作
- 实现适当的错误处理和用户提示
- 考虑不同使用环境的音量需求

### 性能考虑
- 避免频繁创建音频对象
- 合理使用音频预加载
- 监控内存使用情况
- 优化音频文件大小

## 更新日志

### v1.0.0
- 初始版本发布
- 支持6种基础音效类型
- Web Audio API和HTML5 Audio双重支持
- 完整的设置界面和测试工具

### 未来计划
- 支持自定义音效上传
- 添加更多音效主题
- 实现音效可视化效果
- 支持音效时长自定义
