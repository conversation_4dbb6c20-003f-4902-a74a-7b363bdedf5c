# FocusOS 音频通知问题排查和修复方案

## 🔍 问题分析报告

### 问题现象
- **设置页面测试**：音频播放正常 ✅
- **实际通知触发**：音频播放失败 ❌
- **影响范围**：任务提醒、番茄钟结束、专注力监控提醒等

### 根本原因分析

通过代码分析，发现以下关键问题：

#### 1. **代码路径差异**
**设置页面测试路径：**
```typescript
// AudioTestPanel.tsx -> AudioService.testSound()
await audioService.testSound(soundType);

// AudioService.testSound() 方法
public async testSound(soundType: SoundType): Promise<void> {
  const originalEnabled = this.settings.enabled;
  this.settings.enabled = true; // 临时强制启用
  
  try {
    await this.playSound(soundType);
  } finally {
    this.settings.enabled = originalEnabled; // 恢复原设置
  }
}
```

**实际通知路径：**
```typescript
// NotificationService.sendNotification()
if (options.sound && options.soundType) {
  await this.audioService.playSound(options.soundType); // 直接调用，受设置影响
}
```

#### 2. **设置状态检查问题**
```typescript
// AudioService.playSound() 方法
public async playSound(soundType: SoundType): Promise<void> {
  if (!this.settings.enabled) return; // 如果音频被禁用，直接返回
  // ... 播放逻辑
}
```

#### 3. **音频上下文状态问题**
实际通知可能在音频上下文未激活时触发，而测试时用户已经有交互。

#### 4. **异步调用时序问题**
实际通知中的音频调用可能存在时序问题，导致音频服务未正确初始化。

---

## 🛠 修复方案

### 方案1：统一音频播放逻辑

创建统一的音频播放接口，确保测试和实际通知使用相同的代码路径。

```typescript
// AudioService.ts 增强
public async playNotificationSound(soundType: SoundType, forcePlay: boolean = false): Promise<void> {
  // 如果是强制播放（测试）或者音频已启用
  if (forcePlay || this.settings.enabled) {
    try {
      // 确保音频上下文已激活
      await this.ensureAudioContextActive();
      
      // 确保已初始化
      if (!this.isInitialized) {
        await this.initialize();
      }
      
      // 播放音频
      await this.playSound(soundType);
      
      // 记录播放日志
      console.log(`🔊 Audio played: ${soundType}, enabled: ${this.settings.enabled}, forced: ${forcePlay}`);
    } catch (error) {
      console.error(`🔇 Audio playback failed: ${soundType}`, error);
      throw error;
    }
  } else {
    console.log(`🔇 Audio disabled, skipping: ${soundType}`);
  }
}

private async ensureAudioContextActive(): Promise<void> {
  const context = await this.audioContextManager.getAudioContext();
  if (context && context.state === 'suspended') {
    try {
      await context.resume();
      console.log('🎵 Audio context resumed');
    } catch (error) {
      console.warn('Failed to resume audio context:', error);
    }
  }
}
```

### 方案2：增强通知服务音频调用

```typescript
// NotificationService.ts 修复
public async sendNotification(
  title: string,
  options: {
    body?: string;
    icon?: string;
    sound?: boolean;
    soundType?: SoundType;
    duration?: number;
  } = {}
) {
  // 确保有通知权限
  if (!this.isNotificationGranted) {
    await this.checkNotificationPermission();
  }

  // 播放提示音（在显示通知之前）
  if (options.sound && options.soundType) {
    try {
      await this.audioService.playNotificationSound(options.soundType, false);
    } catch (error) {
      console.warn(`Failed to play notification sound: ${options.soundType}`, error);
      // 音频播放失败不应该阻止通知显示
    }
  }

  // 显示通知
  if (this.isNotificationGranted && 'Notification' in window) {
    const notification = new Notification(title, {
      body: options.body,
      icon: options.icon || '/icon.png',
      silent: !options.sound, // 系统通知静音，我们用自己的音频
      requireInteraction: true,
    });

    // 自动关闭通知
    if (options.duration) {
      setTimeout(() => {
        notification.close();
      }, options.duration);
    }

    return notification;
  }

  console.warn('桌面通知不可用，使用替代方案');
  return null;
}
```

### 方案3：添加详细的调试日志

```typescript
// AudioService.ts 调试增强
public async playSound(soundType: SoundType): Promise<void> {
  console.log(`🎵 AudioService.playSound called: ${soundType}`);
  console.log(`🎵 Audio settings:`, {
    enabled: this.settings.enabled,
    volume: this.settings.volume,
    initialized: this.isInitialized
  });

  if (!this.settings.enabled) {
    console.log(`🔇 Audio disabled, skipping playback: ${soundType}`);
    return;
  }

  try {
    // 确保已初始化
    if (!this.isInitialized) {
      console.log(`🎵 Initializing audio service...`);
      await this.initialize();
    }

    console.log(`🎵 Attempting to play: ${soundType}`);
    
    // 尝试播放预加载的音频
    if (await this.playPreloadedAudio(soundType)) {
      console.log(`✅ Successfully played preloaded audio: ${soundType}`);
      return;
    }

    console.log(`⚠️ Preloaded audio failed, falling back to synthetic: ${soundType}`);
    // 降级到合成音频
    await this.playSynthAudio(soundType);
    console.log(`✅ Successfully played synthetic audio: ${soundType}`);
  } catch (error) {
    console.error(`❌ Failed to play sound ${soundType}:`, error);
    throw error;
  }
}
```

### 方案4：修复音频上下文管理

```typescript
// AudioContextManager.ts 增强
export class AudioContextManager {
  private static instance: AudioContextManager;
  private audioContext: AudioContext | null = null;
  private isUserInteracted = false;

  public async getAudioContext(): Promise<AudioContext | null> {
    if (!this.audioContext) {
      await this.createAudioContext();
    }

    // 确保音频上下文已激活
    if (this.audioContext && this.audioContext.state === 'suspended') {
      if (this.isUserInteracted) {
        try {
          await this.audioContext.resume();
          console.log('🎵 Audio context resumed automatically');
        } catch (error) {
          console.warn('Failed to resume audio context:', error);
        }
      } else {
        console.warn('🔇 Audio context suspended, waiting for user interaction');
      }
    }

    return this.audioContext;
  }

  public markUserInteraction(): void {
    this.isUserInteracted = true;
    console.log('👆 User interaction detected, audio context can be activated');
    
    // 尝试立即激活音频上下文
    if (this.audioContext && this.audioContext.state === 'suspended') {
      this.audioContext.resume().catch(error => {
        console.warn('Failed to resume audio context after user interaction:', error);
      });
    }
  }

  // 在页面加载时设置用户交互监听
  public setupUserInteractionListeners(): void {
    const events = ['click', 'keydown', 'touchstart'];
    
    const handleInteraction = () => {
      this.markUserInteraction();
      // 移除监听器，只需要一次交互
      events.forEach(event => {
        document.removeEventListener(event, handleInteraction);
      });
    };

    events.forEach(event => {
      document.addEventListener(event, handleInteraction, { once: true });
    });
  }
}
```

---

## 🧪 测试验证方案

### 1. 创建音频调试组件

```typescript
// AudioDebugPanel.tsx
export const AudioDebugPanel: React.FC = () => {
  const [debugLogs, setDebugLogs] = useState<string[]>([]);
  const audioService = AudioService.getInstance();
  const notificationService = NotificationService.getInstance();

  const addLog = (message: string) => {
    const timestamp = new Date().toLocaleTimeString();
    setDebugLogs(prev => [...prev, `[${timestamp}] ${message}`]);
  };

  const testDirectAudio = async (soundType: SoundType) => {
    addLog(`Testing direct audio: ${soundType}`);
    try {
      await audioService.playSound(soundType);
      addLog(`✅ Direct audio success: ${soundType}`);
    } catch (error) {
      addLog(`❌ Direct audio failed: ${soundType} - ${error}`);
    }
  };

  const testNotificationAudio = async (soundType: SoundType) => {
    addLog(`Testing notification audio: ${soundType}`);
    try {
      await notificationService.notifyTaskComplete('Debug Test');
      addLog(`✅ Notification audio success: ${soundType}`);
    } catch (error) {
      addLog(`❌ Notification audio failed: ${soundType} - ${error}`);
    }
  };

  return (
    <Card title="音频调试面板">
      <Space direction="vertical" style={{ width: '100%' }}>
        <div>
          <Button onClick={() => testDirectAudio('task-complete')}>
            测试直接音频播放
          </Button>
          <Button onClick={() => testNotificationAudio('task-complete')}>
            测试通知音频播放
          </Button>
        </div>
        
        <div style={{ 
          height: '200px', 
          overflow: 'auto', 
          background: '#f5f5f5', 
          padding: '8px',
          fontFamily: 'monospace',
          fontSize: '12px'
        }}>
          {debugLogs.map((log, index) => (
            <div key={index}>{log}</div>
          ))}
        </div>
      </Space>
    </Card>
  );
};
```

### 2. 跨平台测试脚本

```typescript
// audioTestRunner.ts
export class AudioTestRunner {
  static async runComprehensiveTest(): Promise<TestResult> {
    const results: TestResult = {
      platform: navigator.platform,
      userAgent: navigator.userAgent,
      audioSupport: {
        webAudio: !!window.AudioContext,
        htmlAudio: !!window.Audio,
      },
      tests: []
    };

    // 测试1：音频服务初始化
    try {
      const audioService = AudioService.getInstance();
      await audioService.initialize();
      results.tests.push({
        name: 'Audio Service Initialization',
        status: 'pass',
        details: 'Audio service initialized successfully'
      });
    } catch (error) {
      results.tests.push({
        name: 'Audio Service Initialization',
        status: 'fail',
        details: `Failed: ${error}`
      });
    }

    // 测试2：直接音频播放
    for (const soundType of ['task-complete', 'pomodoro-complete']) {
      try {
        await AudioService.getInstance().playSound(soundType as SoundType);
        results.tests.push({
          name: `Direct Audio Play: ${soundType}`,
          status: 'pass',
          details: 'Audio played successfully'
        });
      } catch (error) {
        results.tests.push({
          name: `Direct Audio Play: ${soundType}`,
          status: 'fail',
          details: `Failed: ${error}`
        });
      }
    }

    // 测试3：通知音频播放
    try {
      await NotificationService.getInstance().notifyTaskComplete('Test Task');
      results.tests.push({
        name: 'Notification Audio',
        status: 'pass',
        details: 'Notification with audio sent successfully'
      });
    } catch (error) {
      results.tests.push({
        name: 'Notification Audio',
        status: 'fail',
        details: `Failed: ${error}`
      });
    }

    return results;
  }
}
```

---

## 📋 实施计划

### 阶段1：立即修复（1-2天）
1. ✅ 修复AudioService.playSound()方法的设置检查逻辑
2. ✅ 增强NotificationService的音频调用错误处理
3. ✅ 添加详细的调试日志

### 阶段2：增强功能（3-5天）
1. ✅ 实现统一的音频播放接口
2. ✅ 优化音频上下文管理
3. ✅ 创建音频调试工具

### 阶段3：全面测试（2-3天）
1. ✅ 跨平台兼容性测试
2. ✅ 不同通知类型的音频测试
3. ✅ 用户交互状态测试

### 阶段4：文档和维护（1天）
1. ✅ 更新音频系统文档
2. ✅ 创建故障排除指南
3. ✅ 建立监控和日志机制

---

## 🎯 预期结果

修复完成后，应该实现：
- ✅ 所有通知类型都能正常播放音频
- ✅ 设置页面测试和实际通知行为一致
- ✅ 跨平台音频播放稳定性
- ✅ 详细的调试和监控能力
- ✅ 优雅的错误处理和降级方案
