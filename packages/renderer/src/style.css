/* 导入苹果简约设计系统 */
@import './styles/apple-minimal-design.css';

/* 导入优化的动画样式 */
@import './styles/animations-optimized.css';

/* 性能优化CSS已禁用，使用简约设计系统 */

/* 基础布局已在apple-minimal-design.css中定义 */

/* 滚动条样式已在apple-minimal-design.css中定义 */

/* 简化的动画已在apple-minimal-design.css中定义 */

/* Ant Design 组件定制 - 简约风格 */
.ant-select-dropdown {
  background: var(--color-background) !important;
  border: 1px solid var(--color-border) !important;
  border-radius: var(--radius-medium) !important;
  box-shadow: var(--shadow-medium) !important;
}

.ant-select-item {
  border-radius: var(--radius-small) !important;
  margin: 2px 4px !important;
  background: transparent !important;
  color: var(--color-text) !important;
}

.ant-select-item-option-selected {
  background: var(--color-primary) !important;
  color: var(--color-text-inverse) !important;
}

.ant-select-item:hover {
  background: var(--color-surface) !important;
  color: var(--color-text) !important;
}

/* 浅色主题下拉列表背景修复 - 确保不透明 */
[data-theme="skyBlue"] .ant-select-dropdown,
[data-theme="mintGreen"] .ant-select-dropdown,
[data-theme="lavenderPurple"] .ant-select-dropdown,
[data-theme="sunsetOrange"] .ant-select-dropdown,
[data-theme="cherryPink"] .ant-select-dropdown,
[data-theme="oceanTeal"] .ant-select-dropdown,
[data-theme="goldenYellow"] .ant-select-dropdown,
[data-theme="forestGreen"] .ant-select-dropdown,
[data-theme="royalBlue"] .ant-select-dropdown,
[data-theme="cosmicGray"] .ant-select-dropdown {
  background: rgba(255, 255, 255, 0.95) !important;
  backdrop-filter: blur(20px) saturate(180%) !important;
  border: 1px solid rgba(255, 255, 255, 0.3) !important;
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1),
              0 4px 16px rgba(0, 0, 0, 0.05),
              inset 0 1px 0 rgba(255, 255, 255, 0.8) !important;
}

/* 浅色主题下拉项目样式优化 */
[data-theme="skyBlue"] .ant-select-item,
[data-theme="mintGreen"] .ant-select-item,
[data-theme="lavenderPurple"] .ant-select-item,
[data-theme="sunsetOrange"] .ant-select-item,
[data-theme="cherryPink"] .ant-select-item,
[data-theme="oceanTeal"] .ant-select-item,
[data-theme="goldenYellow"] .ant-select-item,
[data-theme="forestGreen"] .ant-select-item,
[data-theme="royalBlue"] .ant-select-item,
[data-theme="cosmicGray"] .ant-select-item {
  background: transparent !important;
  color: #1A1A1A !important;
}

[data-theme="skyBlue"] .ant-select-item:hover,
[data-theme="mintGreen"] .ant-select-item:hover,
[data-theme="lavenderPurple"] .ant-select-item:hover,
[data-theme="sunsetOrange"] .ant-select-item:hover,
[data-theme="cherryPink"] .ant-select-item:hover,
[data-theme="oceanTeal"] .ant-select-item:hover,
[data-theme="goldenYellow"] .ant-select-item:hover,
[data-theme="forestGreen"] .ant-select-item:hover,
[data-theme="royalBlue"] .ant-select-item:hover,
[data-theme="cosmicGray"] .ant-select-item:hover {
  background: rgba(255, 255, 255, 0.6) !important;
  color: #1A1A1A !important;
}

[data-theme="skyBlue"] .ant-select-item-option-selected,
[data-theme="mintGreen"] .ant-select-item-option-selected,
[data-theme="lavenderPurple"] .ant-select-item-option-selected,
[data-theme="sunsetOrange"] .ant-select-item-option-selected,
[data-theme="cherryPink"] .ant-select-item-option-selected,
[data-theme="oceanTeal"] .ant-select-item-option-selected,
[data-theme="goldenYellow"] .ant-select-item-option-selected,
[data-theme="forestGreen"] .ant-select-item-option-selected,
[data-theme="royalBlue"] .ant-select-item-option-selected,
[data-theme="cosmicGray"] .ant-select-item-option-selected {
  background: var(--color-primary) !important;
  color: #FFFFFF !important;
}

/* 浅色主题弹出层通用修复 - 确保不透明背景 */
[data-theme="skyBlue"] .ant-dropdown,
[data-theme="mintGreen"] .ant-dropdown,
[data-theme="lavenderPurple"] .ant-dropdown,
[data-theme="sunsetOrange"] .ant-dropdown,
[data-theme="cherryPink"] .ant-dropdown,
[data-theme="oceanTeal"] .ant-dropdown,
[data-theme="goldenYellow"] .ant-dropdown,
[data-theme="forestGreen"] .ant-dropdown,
[data-theme="royalBlue"] .ant-dropdown,
[data-theme="cosmicGray"] .ant-dropdown,
[data-theme="skyBlue"] .ant-tooltip-inner,
[data-theme="mintGreen"] .ant-tooltip-inner,
[data-theme="lavenderPurple"] .ant-tooltip-inner,
[data-theme="sunsetOrange"] .ant-tooltip-inner,
[data-theme="cherryPink"] .ant-tooltip-inner,
[data-theme="oceanTeal"] .ant-tooltip-inner,
[data-theme="goldenYellow"] .ant-tooltip-inner,
[data-theme="forestGreen"] .ant-tooltip-inner,
[data-theme="royalBlue"] .ant-tooltip-inner,
[data-theme="cosmicGray"] .ant-tooltip-inner,
[data-theme="skyBlue"] .ant-popover-inner,
[data-theme="mintGreen"] .ant-popover-inner,
[data-theme="lavenderPurple"] .ant-popover-inner,
[data-theme="sunsetOrange"] .ant-popover-inner,
[data-theme="cherryPink"] .ant-popover-inner,
[data-theme="oceanTeal"] .ant-popover-inner,
[data-theme="goldenYellow"] .ant-popover-inner,
[data-theme="forestGreen"] .ant-popover-inner,
[data-theme="royalBlue"] .ant-popover-inner,
[data-theme="cosmicGray"] .ant-popover-inner,
[data-theme="skyBlue"] .ant-picker-dropdown,
[data-theme="mintGreen"] .ant-picker-dropdown,
[data-theme="lavenderPurple"] .ant-picker-dropdown,
[data-theme="sunsetOrange"] .ant-picker-dropdown,
[data-theme="cherryPink"] .ant-picker-dropdown,
[data-theme="oceanTeal"] .ant-picker-dropdown,
[data-theme="goldenYellow"] .ant-picker-dropdown,
[data-theme="forestGreen"] .ant-picker-dropdown,
[data-theme="royalBlue"] .ant-picker-dropdown,
[data-theme="cosmicGray"] .ant-picker-dropdown,
[data-theme="skyBlue"] .ant-picker-panel-container,
[data-theme="mintGreen"] .ant-picker-panel-container,
[data-theme="lavenderPurple"] .ant-picker-panel-container,
[data-theme="sunsetOrange"] .ant-picker-panel-container,
[data-theme="cherryPink"] .ant-picker-panel-container,
[data-theme="oceanTeal"] .ant-picker-panel-container,
[data-theme="goldenYellow"] .ant-picker-panel-container,
[data-theme="forestGreen"] .ant-picker-panel-container,
[data-theme="royalBlue"] .ant-picker-panel-container,
[data-theme="cosmicGray"] .ant-picker-panel-container {
  background: rgba(255, 255, 255, 0.95) !important;
  border: 1px solid rgba(255, 255, 255, 0.3) !important;
  backdrop-filter: blur(20px) saturate(180%) !important;
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1),
              0 4px 16px rgba(0, 0, 0, 0.05),
              inset 0 1px 0 rgba(255, 255, 255, 0.8) !important;
  z-index: 9999 !important;
}

/* 深色模式弹出层通用修复 */
[data-theme="darkNight"] .ant-select-dropdown,
[data-theme="darkBlue"] .ant-select-dropdown,
[data-theme="darkNight"] .ant-dropdown,
[data-theme="darkBlue"] .ant-dropdown,
[data-theme="darkNight"] .ant-tooltip-inner,
[data-theme="darkBlue"] .ant-tooltip-inner,
[data-theme="darkNight"] .ant-popover-inner,
[data-theme="darkBlue"] .ant-popover-inner,
[data-theme="darkNight"] .ant-notification,
[data-theme="darkBlue"] .ant-notification,
[data-theme="darkNight"] .ant-picker-dropdown,
[data-theme="darkBlue"] .ant-picker-dropdown,
[data-theme="darkNight"] .ant-picker-panel-container,
[data-theme="darkBlue"] .ant-picker-panel-container {
  background: rgba(28, 28, 30, 0.98) !important;
  border: 1px solid rgba(255, 255, 255, 0.15) !important;
  backdrop-filter: blur(20px) !important;
  z-index: 9999 !important;
}

/* 浅色主题下拉菜单项目样式 */
[data-theme="skyBlue"] .ant-dropdown-menu-item,
[data-theme="mintGreen"] .ant-dropdown-menu-item,
[data-theme="lavenderPurple"] .ant-dropdown-menu-item,
[data-theme="sunsetOrange"] .ant-dropdown-menu-item,
[data-theme="cherryPink"] .ant-dropdown-menu-item,
[data-theme="oceanTeal"] .ant-dropdown-menu-item,
[data-theme="goldenYellow"] .ant-dropdown-menu-item,
[data-theme="forestGreen"] .ant-dropdown-menu-item,
[data-theme="royalBlue"] .ant-dropdown-menu-item,
[data-theme="cosmicGray"] .ant-dropdown-menu-item {
  background: transparent !important;
  color: #1A1A1A !important;
}

[data-theme="skyBlue"] .ant-dropdown-menu-item:hover,
[data-theme="mintGreen"] .ant-dropdown-menu-item:hover,
[data-theme="lavenderPurple"] .ant-dropdown-menu-item:hover,
[data-theme="sunsetOrange"] .ant-dropdown-menu-item:hover,
[data-theme="cherryPink"] .ant-dropdown-menu-item:hover,
[data-theme="oceanTeal"] .ant-dropdown-menu-item:hover,
[data-theme="goldenYellow"] .ant-dropdown-menu-item:hover,
[data-theme="forestGreen"] .ant-dropdown-menu-item:hover,
[data-theme="royalBlue"] .ant-dropdown-menu-item:hover,
[data-theme="cosmicGray"] .ant-dropdown-menu-item:hover {
  background: rgba(255, 255, 255, 0.6) !important;
  color: #1A1A1A !important;
}

[data-theme="darkNight"] .ant-select-item,
[data-theme="darkBlue"] .ant-select-item {
  background: transparent !important;
  color: #FFFFFF !important;
}

[data-theme="darkNight"] .ant-select-item:hover,
[data-theme="darkBlue"] .ant-select-item:hover {
  background: rgba(255, 255, 255, 0.1) !important;
  color: #FFFFFF !important;
}

[data-theme="darkNight"] .ant-select-item-option-selected,
[data-theme="darkBlue"] .ant-select-item-option-selected {
  background: var(--color-primary) !important;
  color: #FFFFFF !important;
}

/* 修复下拉列表的空状态显示 */
[data-theme="skyBlue"] .ant-empty-description,
[data-theme="mintGreen"] .ant-empty-description,
[data-theme="lavenderPurple"] .ant-empty-description,
[data-theme="sunsetOrange"] .ant-empty-description,
[data-theme="cherryPink"] .ant-empty-description,
[data-theme="oceanTeal"] .ant-empty-description,
[data-theme="goldenYellow"] .ant-empty-description,
[data-theme="forestGreen"] .ant-empty-description,
[data-theme="royalBlue"] .ant-empty-description,
[data-theme="cosmicGray"] .ant-empty-description {
  color: #666666 !important;
}

[data-theme="darkNight"] .ant-empty-description,
[data-theme="darkBlue"] .ant-empty-description {
  color: #98989D !important;
}

.ant-modal-content {
  background: var(--color-background) !important;
  border: 1px solid var(--color-border);
  border-radius: var(--radius-large) !important;
  box-shadow: var(--shadow-large) !important;
}

/* 深色模式模态框专门修复 */
[data-theme="darkNight"] .ant-modal-content,
[data-theme="darkBlue"] .ant-modal-content {
  background: rgba(28, 28, 30, 0.98) !important;
  border: 1px solid rgba(255, 255, 255, 0.15) !important;
  backdrop-filter: blur(20px) !important;
  color: #FFFFFF !important;
}

[data-theme="darkNight"] .ant-modal-header,
[data-theme="darkBlue"] .ant-modal-header {
  background: transparent !important;
  border-bottom: 1px solid rgba(255, 255, 255, 0.15) !important;
}

[data-theme="darkNight"] .ant-modal-title,
[data-theme="darkBlue"] .ant-modal-title {
  color: #FFFFFF !important;
}

[data-theme="darkNight"] .ant-modal-close,
[data-theme="darkBlue"] .ant-modal-close {
  color: #FFFFFF !important;
}

[data-theme="darkNight"] .ant-modal-mask,
[data-theme="darkBlue"] .ant-modal-mask {
  background: rgba(0, 0, 0, 0.6) !important;
}

.ant-modal-header {
  background: transparent !important;
  border-bottom: 1px solid var(--color-border) !important;
}

.ant-form-item-label {
  font-weight: var(--font-weight-medium);
  color: var(--color-text);
}

.ant-input, .ant-input-number, .ant-select-selector {
  background: var(--color-background) !important;
  border: 1px solid var(--color-border) !important;
  border-radius: var(--radius-small) !important;
  transition: all var(--transition-fast);
  color: var(--color-text) !important;
}

/* Textarea专门样式修复 */
.ant-input {
  background: var(--color-background) !important;
  border: 1px solid var(--color-border) !important;
  color: var(--color-text) !important;
}

/* Placeholder文字样式修复 - 浅色模式 */
.ant-input::placeholder,
.ant-input::-webkit-input-placeholder,
.ant-input::-moz-placeholder,
.ant-input:-ms-input-placeholder {
  color: var(--color-placeholder, var(--color-text-secondary)) !important;
  opacity: 0.65 !important;
  font-weight: 400 !important;
}

/* 深色模式下Input、TextArea和Select样式修复 */
[data-theme="darkNight"] .ant-input,
[data-theme="darkNight"] .ant-input-number,
[data-theme="darkNight"] .ant-select-selector,
[data-theme="darkBlue"] .ant-input,
[data-theme="darkBlue"] .ant-input-number,
[data-theme="darkBlue"] .ant-select-selector {
  background: rgba(28, 28, 30, 0.95) !important;
  border: 1px solid rgba(255, 255, 255, 0.15) !important;
  color: #FFFFFF !important;
}

/* 浅色主题Select组件内部样式 */
[data-theme="skyBlue"] .ant-select-selection-search-input,
[data-theme="mintGreen"] .ant-select-selection-search-input,
[data-theme="lavenderPurple"] .ant-select-selection-search-input,
[data-theme="sunsetOrange"] .ant-select-selection-search-input,
[data-theme="cherryPink"] .ant-select-selection-search-input,
[data-theme="oceanTeal"] .ant-select-selection-search-input,
[data-theme="goldenYellow"] .ant-select-selection-search-input,
[data-theme="forestGreen"] .ant-select-selection-search-input,
[data-theme="royalBlue"] .ant-select-selection-search-input,
[data-theme="cosmicGray"] .ant-select-selection-search-input {
  color: #1A1A1A !important;
}

[data-theme="skyBlue"] .ant-select-selection-placeholder,
[data-theme="mintGreen"] .ant-select-selection-placeholder,
[data-theme="lavenderPurple"] .ant-select-selection-placeholder,
[data-theme="sunsetOrange"] .ant-select-selection-placeholder,
[data-theme="cherryPink"] .ant-select-selection-placeholder,
[data-theme="oceanTeal"] .ant-select-selection-placeholder,
[data-theme="goldenYellow"] .ant-select-selection-placeholder,
[data-theme="forestGreen"] .ant-select-selection-placeholder,
[data-theme="royalBlue"] .ant-select-selection-placeholder,
[data-theme="cosmicGray"] .ant-select-selection-placeholder {
  color: #666666 !important;
  opacity: 0.85 !important;
}

[data-theme="skyBlue"] .ant-select-selection-item,
[data-theme="mintGreen"] .ant-select-selection-item,
[data-theme="lavenderPurple"] .ant-select-selection-item,
[data-theme="sunsetOrange"] .ant-select-selection-item,
[data-theme="cherryPink"] .ant-select-selection-item,
[data-theme="oceanTeal"] .ant-select-selection-item,
[data-theme="goldenYellow"] .ant-select-selection-item,
[data-theme="forestGreen"] .ant-select-selection-item,
[data-theme="royalBlue"] .ant-select-selection-item,
[data-theme="cosmicGray"] .ant-select-selection-item {
  color: #1A1A1A !important;
}

[data-theme="skyBlue"] .ant-select-arrow,
[data-theme="mintGreen"] .ant-select-arrow,
[data-theme="lavenderPurple"] .ant-select-arrow,
[data-theme="sunsetOrange"] .ant-select-arrow,
[data-theme="cherryPink"] .ant-select-arrow,
[data-theme="oceanTeal"] .ant-select-arrow,
[data-theme="goldenYellow"] .ant-select-arrow,
[data-theme="forestGreen"] .ant-select-arrow,
[data-theme="royalBlue"] .ant-select-arrow,
[data-theme="cosmicGray"] .ant-select-arrow {
  color: #666666 !important;
}

[data-theme="skyBlue"] .ant-select-clear,
[data-theme="mintGreen"] .ant-select-clear,
[data-theme="lavenderPurple"] .ant-select-clear,
[data-theme="sunsetOrange"] .ant-select-clear,
[data-theme="cherryPink"] .ant-select-clear,
[data-theme="oceanTeal"] .ant-select-clear,
[data-theme="goldenYellow"] .ant-select-clear,
[data-theme="forestGreen"] .ant-select-clear,
[data-theme="royalBlue"] .ant-select-clear,
[data-theme="cosmicGray"] .ant-select-clear {
  color: #666666 !important;
  background: rgba(102, 102, 102, 0.1) !important;
}

[data-theme="darkNight"] .ant-select-selection-search-input,
[data-theme="darkBlue"] .ant-select-selection-search-input {
  color: #FFFFFF !important;
}

[data-theme="darkNight"] .ant-select-selection-placeholder,
[data-theme="darkBlue"] .ant-select-selection-placeholder {
  color: #98989D !important;
  opacity: 0.85 !important;
}

[data-theme="darkNight"] .ant-select:hover .ant-select-selector,
[data-theme="darkBlue"] .ant-select:hover .ant-select-selector {
  border-color: rgba(255, 255, 255, 0.25) !important;
}

[data-theme="darkNight"] .ant-select-focused .ant-select-selector,
[data-theme="darkBlue"] .ant-select-focused .ant-select-selector {
  border-color: var(--color-primary) !important;
  box-shadow: 0 0 0 3px rgba(10, 132, 255, 0.2) !important;
}

[data-theme="darkNight"] .ant-select-arrow,
[data-theme="darkBlue"] .ant-select-arrow {
  color: #98989D !important;
}

[data-theme="darkNight"] .ant-select-clear,
[data-theme="darkBlue"] .ant-select-clear {
  color: #98989D !important;
  background: rgba(152, 152, 157, 0.2) !important;
}

[data-theme="darkNight"] .ant-select-selection-item,
[data-theme="darkBlue"] .ant-select-selection-item {
  color: #FFFFFF !important;
}

[data-theme="darkNight"] .ant-input:focus,
[data-theme="darkNight"] .ant-input-number:focus,
[data-theme="darkBlue"] .ant-input:focus,
[data-theme="darkBlue"] .ant-input-number:focus {
  background: rgba(28, 28, 30, 0.98) !important;
  border-color: var(--color-primary) !important;
  box-shadow: 0 0 0 3px rgba(10, 132, 255, 0.2) !important;
}

/* 深色模式下Form Label样式修复 */
[data-theme="darkNight"] .ant-form-item-label > label,
[data-theme="darkBlue"] .ant-form-item-label > label {
  color: #FFFFFF !important;
}

[data-theme="darkNight"] .ant-form-item-explain,
[data-theme="darkBlue"] .ant-form-item-explain,
[data-theme="darkNight"] .ant-form-item-extra,
[data-theme="darkBlue"] .ant-form-item-extra {
  color: #98989D !important;
}

/* 深色模式下Placeholder专门修复 */
[data-theme="darkNight"] .ant-input::placeholder,
[data-theme="darkNight"] .ant-input::-webkit-input-placeholder,
[data-theme="darkNight"] .ant-input::-moz-placeholder,
[data-theme="darkNight"] .ant-input:-ms-input-placeholder,
[data-theme="darkBlue"] .ant-input::placeholder,
[data-theme="darkBlue"] .ant-input::-webkit-input-placeholder,
[data-theme="darkBlue"] .ant-input::-moz-placeholder,
[data-theme="darkBlue"] .ant-input:-ms-input-placeholder {
  color: var(--color-placeholder, #98989D) !important;
  opacity: 0.85 !important;
  font-weight: 400 !important;
}

/* 确保深色模式下所有表单元素placeholder都有正确颜色 */
[data-theme="darkNight"] input::placeholder,
[data-theme="darkNight"] textarea::placeholder,
[data-theme="darkBlue"] input::placeholder,
[data-theme="darkBlue"] textarea::placeholder {
  color: var(--color-placeholder, #98989D) !important;
  opacity: 0.85 !important;
}

/* Firefox兼容性 */
[data-theme="darkNight"] input::-moz-placeholder,
[data-theme="darkNight"] textarea::-moz-placeholder,
[data-theme="darkBlue"] input::-moz-placeholder,
[data-theme="darkBlue"] textarea::-moz-placeholder {
  color: var(--color-placeholder, #98989D) !important;
  opacity: 0.85 !important;
}

/* IE/Edge兼容性 */
[data-theme="darkNight"] input:-ms-input-placeholder,
[data-theme="darkNight"] textarea:-ms-input-placeholder,
[data-theme="darkBlue"] input:-ms-input-placeholder,
[data-theme="darkBlue"] textarea:-ms-input-placeholder {
  color: var(--color-placeholder, #98989D) !important;
  opacity: 0.85 !important;
}

.ant-input:focus, .ant-input-number:focus, .ant-select-focused .ant-select-selector {
  background: var(--color-background) !important;
  border-color: var(--color-primary) !important;
  box-shadow: 0 0 0 3px rgba(0, 113, 227, 0.1) !important;
}

.ant-btn {
  border-radius: var(--radius-small) !important;
  font-weight: var(--font-weight-medium);
  transition: all var(--transition-fast);
}

.ant-btn-primary {
  background: var(--color-primary) !important;
  border-color: var(--color-primary) !important;
  color: var(--color-text-inverse) !important;
}

.ant-btn-primary:hover {
  background: var(--color-primary-hover) !important;
  border-color: var(--color-primary-hover) !important;
}

.ant-btn-primary:active {
  background: var(--color-primary-active) !important;
  border-color: var(--color-primary-active) !important;
}

.ant-card {
  background: var(--color-background) !important;
  border: 1px solid var(--color-border) !important;
  border-radius: var(--radius-medium) !important;
  box-shadow: var(--shadow-small) !important;
}

.ant-card:hover {
  box-shadow: var(--shadow-medium) !important;
}

.ant-table {
  background: var(--color-background) !important;
}

.ant-table-thead > tr > th {
  background: var(--color-background-secondary) !important;
  border-bottom: 1px solid var(--color-border) !important;
  font-weight: var(--font-weight-semibold);
  color: var(--color-text) !important;
}

.ant-table-tbody > tr > td {
  border-bottom: 1px solid var(--color-border-light) !important;
  color: var(--color-text) !important;
}

.ant-table-tbody > tr:hover > td {
  background: var(--color-background-tertiary) !important;
}

/* 布局样式 */
.ant-layout {
  background: var(--color-background) !important;
}

.ant-layout-sider {
  background: var(--color-background-secondary) !important;
  border-right: 1px solid var(--color-border) !important;
}

.ant-menu {
  background: transparent !important;
  border: none !important;
}

.ant-menu-item {
  color: var(--color-text) !important;
}

.ant-menu-item:hover {
  background: var(--color-background-tertiary) !important;
}

.ant-menu-item-selected {
  background: var(--color-primary) !important;
  color: var(--color-text-inverse) !important;
}

/* 确保菜单选中项的图标和文字都使用正确颜色 */
.ant-menu-item-selected .anticon,
.ant-menu-item-selected span {
  color: var(--color-text-inverse) !important;
}

/* 深色主题强制文字可见性修复 */
[data-theme="darkNight"],
[data-theme="darkBlue"] {
  color-scheme: dark;
}

/* 强制深色主题弹出层和表单元素文字可见 */
[data-theme="darkNight"] .ant-select-dropdown *,
[data-theme="darkBlue"] .ant-select-dropdown *,
[data-theme="darkNight"] .ant-dropdown *,
[data-theme="darkBlue"] .ant-dropdown *,
[data-theme="darkNight"] .ant-modal *,
[data-theme="darkBlue"] .ant-modal *,
[data-theme="darkNight"] .ant-popover *,
[data-theme="darkBlue"] .ant-popover *,
[data-theme="darkNight"] .ant-tooltip *,
[data-theme="darkBlue"] .ant-tooltip *,
[data-theme="darkNight"] .ant-notification *,
[data-theme="darkBlue"] .ant-notification *,
[data-theme="darkNight"] .ant-drawer *,
[data-theme="darkBlue"] .ant-drawer *,
[data-theme="darkNight"] .ant-picker-dropdown *,
[data-theme="darkBlue"] .ant-picker-dropdown *,
[data-theme="darkNight"] .ant-picker-panel-container *,
[data-theme="darkBlue"] .ant-picker-panel-container *,
[data-theme="darkNight"] .ant-form-item-label,
[data-theme="darkBlue"] .ant-form-item-label {
  color: #FFFFFF !important;
}

/* 深色模式抽屉组件修复 */
[data-theme="darkNight"] .ant-drawer-content,
[data-theme="darkBlue"] .ant-drawer-content {
  background: rgba(28, 28, 30, 0.98) !important;
  border: 1px solid rgba(255, 255, 255, 0.15) !important;
}

[data-theme="darkNight"] .ant-drawer-mask,
[data-theme="darkBlue"] .ant-drawer-mask {
  background: rgba(0, 0, 0, 0.6) !important;
}

/* 保持特殊元素的颜色 */
[data-theme="darkNight"] .ant-btn-primary,
[data-theme="darkBlue"] .ant-btn-primary {
  color: var(--color-text-inverse) !important;
  background: var(--color-primary) !important;
}

[data-theme="darkNight"] .ant-menu-item-selected,
[data-theme="darkBlue"] .ant-menu-item-selected {
  color: var(--color-text-inverse) !important;
  background: var(--color-primary) !important;
}

/* 深色主题菜单选中项的图标和文字 */
[data-theme="darkNight"] .ant-menu-item-selected .anticon,
[data-theme="darkNight"] .ant-menu-item-selected span,
[data-theme="darkBlue"] .ant-menu-item-selected .anticon,
[data-theme="darkBlue"] .ant-menu-item-selected span {
  color: var(--color-text-inverse) !important;
}

/* 强制设置特定组件的背景和文字 */
[data-theme="darkNight"] .ant-layout-sider,
[data-theme="darkBlue"] .ant-layout-sider {
  background: rgba(28, 28, 30, 0.95) !important;
}

[data-theme="darkNight"] .ant-layout-content,
[data-theme="darkBlue"] .ant-layout-content {
  background: linear-gradient(135deg, #000000 0%, #1C1C1E 50%, #2C2C2E 100%) !important;
}

/* 深色模式确认对话框修复 */
[data-theme="darkNight"] .ant-popconfirm-inner,
[data-theme="darkBlue"] .ant-popconfirm-inner {
  background: rgba(28, 28, 30, 0.98) !important;
  border: 1px solid rgba(255, 255, 255, 0.15) !important;
  backdrop-filter: blur(20px) !important;
}

[data-theme="darkNight"] .ant-popconfirm-message,
[data-theme="darkBlue"] .ant-popconfirm-message,
[data-theme="darkNight"] .ant-popconfirm-description,
[data-theme="darkBlue"] .ant-popconfirm-description {
  color: #FFFFFF !important;
}

/* 深色模式消息组件修复 */
[data-theme="darkNight"] .ant-message,
[data-theme="darkBlue"] .ant-message {
  z-index: 10000 !important;
}

[data-theme="darkNight"] .ant-message-notice-content,
[data-theme="darkBlue"] .ant-message-notice-content {
  background: rgba(28, 28, 30, 0.98) !important;
  border: 1px solid rgba(255, 255, 255, 0.15) !important;
  backdrop-filter: blur(20px) !important;
  color: #FFFFFF !important;
}

/* 深色模式DatePicker/TimePicker专门修复 */
[data-theme="darkNight"] .ant-picker,
[data-theme="darkBlue"] .ant-picker {
  background: rgba(28, 28, 30, 0.95) !important;
  border: 1px solid rgba(255, 255, 255, 0.15) !important;
  color: #FFFFFF !important;
}

[data-theme="darkNight"] .ant-picker:hover,
[data-theme="darkBlue"] .ant-picker:hover {
  border-color: rgba(255, 255, 255, 0.25) !important;
}

[data-theme="darkNight"] .ant-picker-focused,
[data-theme="darkBlue"] .ant-picker-focused {
  border-color: var(--color-primary) !important;
  box-shadow: 0 0 0 3px rgba(10, 132, 255, 0.2) !important;
}

[data-theme="darkNight"] .ant-picker-input > input,
[data-theme="darkBlue"] .ant-picker-input > input {
  color: #FFFFFF !important;
  background: transparent !important;
}

[data-theme="darkNight"] .ant-picker-input > input::placeholder,
[data-theme="darkBlue"] .ant-picker-input > input::placeholder {
  color: #98989D !important;
  opacity: 0.85 !important;
}

[data-theme="darkNight"] .ant-picker-suffix,
[data-theme="darkBlue"] .ant-picker-suffix {
  color: #98989D !important;
}

/* DatePicker弹出面板修复 */
[data-theme="darkNight"] .ant-picker-panel,
[data-theme="darkBlue"] .ant-picker-panel {
  background: rgba(28, 28, 30, 0.98) !important;
  border: 1px solid rgba(255, 255, 255, 0.15) !important;
}

[data-theme="darkNight"] .ant-picker-header,
[data-theme="darkBlue"] .ant-picker-header {
  background: transparent !important;
  border-bottom: 1px solid rgba(255, 255, 255, 0.15) !important;
}

[data-theme="darkNight"] .ant-picker-header-view,
[data-theme="darkBlue"] .ant-picker-header-view {
  color: #FFFFFF !important;
}

[data-theme="darkNight"] .ant-picker-prev-icon,
[data-theme="darkNight"] .ant-picker-next-icon,
[data-theme="darkNight"] .ant-picker-super-prev-icon,
[data-theme="darkNight"] .ant-picker-super-next-icon,
[data-theme="darkBlue"] .ant-picker-prev-icon,
[data-theme="darkBlue"] .ant-picker-next-icon,
[data-theme="darkBlue"] .ant-picker-super-prev-icon,
[data-theme="darkBlue"] .ant-picker-super-next-icon {
  color: #FFFFFF !important;
}

[data-theme="darkNight"] .ant-picker-cell,
[data-theme="darkBlue"] .ant-picker-cell {
  color: #FFFFFF !important;
  background: transparent !important;
}

[data-theme="darkNight"] .ant-picker-cell:hover,
[data-theme="darkBlue"] .ant-picker-cell:hover {
  background: rgba(255, 255, 255, 0.1) !important;
}

[data-theme="darkNight"] .ant-picker-cell-selected,
[data-theme="darkBlue"] .ant-picker-cell-selected {
  background: var(--color-primary) !important;
  color: #FFFFFF !important;
}

[data-theme="darkNight"] .ant-picker-cell-today,
[data-theme="darkBlue"] .ant-picker-cell-today {
  border-color: var(--color-primary) !important;
  color: var(--color-primary) !important;
}

[data-theme="darkNight"] .ant-picker-time-panel,
[data-theme="darkBlue"] .ant-picker-time-panel {
  background: rgba(28, 28, 30, 0.98) !important;
  border-left: 1px solid rgba(255, 255, 255, 0.15) !important;
}

[data-theme="darkNight"] .ant-picker-time-panel-column,
[data-theme="darkBlue"] .ant-picker-time-panel-column {
  background: transparent !important;
}

[data-theme="darkNight"] .ant-picker-time-panel-cell,
[data-theme="darkBlue"] .ant-picker-time-panel-cell {
  color: #FFFFFF !important;
}

[data-theme="darkNight"] .ant-picker-time-panel-cell:hover,
[data-theme="darkBlue"] .ant-picker-time-panel-cell:hover {
  background: rgba(255, 255, 255, 0.1) !important;
}

[data-theme="darkNight"] .ant-picker-time-panel-cell-selected,
[data-theme="darkBlue"] .ant-picker-time-panel-cell-selected {
  background: var(--color-primary) !important;
}

[data-theme="darkNight"] .ant-picker-footer,
[data-theme="darkBlue"] .ant-picker-footer {
  background: transparent !important;
  border-top: 1px solid rgba(255, 255, 255, 0.15) !important;
}

[data-theme="darkNight"] .ant-picker-now-btn,
[data-theme="darkBlue"] .ant-picker-now-btn {
  color: var(--color-primary) !important;
}

/* 深色模式其他常见组件修复 */
[data-theme="darkNight"] .ant-radio-wrapper,
[data-theme="darkBlue"] .ant-radio-wrapper {
  color: #FFFFFF !important;
}

[data-theme="darkNight"] .ant-checkbox-wrapper,
[data-theme="darkBlue"] .ant-checkbox-wrapper {
  color: #FFFFFF !important;
}

[data-theme="darkNight"] .ant-switch,
[data-theme="darkBlue"] .ant-switch {
  background: rgba(255, 255, 255, 0.25) !important;
}

[data-theme="darkNight"] .ant-switch-checked,
[data-theme="darkBlue"] .ant-switch-checked {
  background: var(--color-primary) !important;
}

[data-theme="darkNight"] .ant-rate,
[data-theme="darkBlue"] .ant-rate {
  color: #98989D !important;
}

[data-theme="darkNight"] .ant-rate-star-focused,
[data-theme="darkNight"] .ant-rate-star-half .ant-rate-star-first,
[data-theme="darkNight"] .ant-rate-star-full,
[data-theme="darkBlue"] .ant-rate-star-focused,
[data-theme="darkBlue"] .ant-rate-star-half .ant-rate-star-first,
[data-theme="darkBlue"] .ant-rate-star-full {
  color: var(--color-primary) !important;
}

[data-theme="darkNight"] .ant-slider-rail,
[data-theme="darkBlue"] .ant-slider-rail {
  background: rgba(255, 255, 255, 0.15) !important;
}

[data-theme="darkNight"] .ant-slider-track,
[data-theme="darkBlue"] .ant-slider-track {
  background: var(--color-primary) !important;
}

[data-theme="darkNight"] .ant-slider-handle,
[data-theme="darkBlue"] .ant-slider-handle {
  border-color: var(--color-primary) !important;
  background: var(--color-primary) !important;
}

[data-theme="darkNight"] .ant-progress-text,
[data-theme="darkBlue"] .ant-progress-text {
  color: #FFFFFF !important;
}

[data-theme="darkNight"] .ant-upload,
[data-theme="darkBlue"] .ant-upload {
  background: rgba(28, 28, 30, 0.95) !important;
  border: 1px solid rgba(255, 255, 255, 0.15) !important;
}

[data-theme="darkNight"] .ant-upload-text,
[data-theme="darkBlue"] .ant-upload-text {
  color: #FFFFFF !important;
}

[data-theme="darkNight"] .ant-upload-hint,
[data-theme="darkBlue"] .ant-upload-hint {
  color: #98989D !important;
}

/* 确保弹出层z-index正确 */
.ant-select-dropdown,
.ant-dropdown,
.ant-tooltip,
.ant-popover,
.ant-notification,
.ant-modal,
.ant-drawer,
.ant-popconfirm,
.ant-message,
.ant-picker-dropdown {
  z-index: 9999 !important;
}

/* 减少动画偏好支持 */
@media (prefers-reduced-motion: reduce) {
  * {
    animation-duration: 0.01ms !important;
    animation-iteration-count: 1 !important;
    transition-duration: 0.01ms !important;
  }
}