import React, { useEffect, useRef, useCallback, useMemo } from 'react';
import { useSelector, useDispatch } from 'react-redux';
import { RootState } from '../store/store';

// 防抖Hook
export const useDebounce = <T>(value: T, delay: number): T => {
  const [debouncedValue, setDebouncedValue] = React.useState<T>(value);

  useEffect(() => {
    const handler = setTimeout(() => {
      setDebouncedValue(value);
    }, delay);

    return () => {
      clearTimeout(handler);
    };
  }, [value, delay]);

  return debouncedValue;
};

// 节流Hook
export const useThrottle = <T>(value: T, limit: number): T => {
  const [throttledValue, setThrottledValue] = React.useState<T>(value);
  const lastRan = useRef<number>(Date.now());

  useEffect(() => {
    const handler = setTimeout(() => {
      if (Date.now() - lastRan.current >= limit) {
        setThrottledValue(value);
        lastRan.current = Date.now();
      }
    }, limit - (Date.now() - lastRan.current));

    return () => {
      clearTimeout(handler);
    };
  }, [value, limit]);

  return throttledValue;
};

// 优化的数据获取Hook
interface UseOptimizedDataOptions {
  cacheTime?: number; // 缓存时间（毫秒）
  staleTime?: number; // 数据过期时间（毫秒）
  refetchOnWindowFocus?: boolean;
  refetchOnReconnect?: boolean;
  retry?: number;
  retryDelay?: number;
}

const dataCache = new Map<string, {
  data: any;
  timestamp: number;
  cacheTime: number;
  staleTime: number;
}>();

export const useOptimizedData = <T>(
  key: string,
  fetcher: () => Promise<T>,
  options: UseOptimizedDataOptions = {}
) => {
  const {
    cacheTime = 5 * 60 * 1000, // 5分钟
    staleTime = 1 * 60 * 1000,  // 1分钟
    refetchOnWindowFocus = true,
    refetchOnReconnect = true,
    retry = 3,
    retryDelay = 1000,
  } = options;

  const [data, setData] = React.useState<T | null>(null);
  const [loading, setLoading] = React.useState(false);
  const [error, setError] = React.useState<Error | null>(null);
  const retryCount = useRef(0);
  const abortController = useRef<AbortController | null>(null);

  // 检查缓存
  const getCachedData = useCallback(() => {
    const cached = dataCache.get(key);
    if (cached && Date.now() - cached.timestamp < cached.cacheTime) {
      return cached.data;
    }
    return null;
  }, [key]);

  // 检查数据是否过期
  const isStale = useCallback(() => {
    const cached = dataCache.get(key);
    if (!cached) return true;
    return Date.now() - cached.timestamp > cached.staleTime;
  }, [key, staleTime]);

  // 获取数据
  const fetchData = useCallback(async (force = false) => {
    // 如果有缓存且不强制刷新，使用缓存
    const cachedData = getCachedData();
    if (cachedData && !force && !isStale()) {
      setData(cachedData);
      return cachedData;
    }

    // 取消之前的请求
    if (abortController.current) {
      abortController.current.abort();
    }

    abortController.current = new AbortController();
    setLoading(true);
    setError(null);

    try {
      const result = await fetcher();
      
      // 缓存数据
      dataCache.set(key, {
        data: result,
        timestamp: Date.now(),
        cacheTime,
        staleTime,
      });

      setData(result);
      retryCount.current = 0;
      return result;
    } catch (err) {
      const error = err as Error;
      
      // 如果请求被取消，不处理错误
      if (error.name === 'AbortError') {
        return;
      }

      // 重试逻辑
      if (retryCount.current < retry) {
        retryCount.current++;
        setTimeout(() => {
          fetchData(force);
        }, retryDelay * retryCount.current);
        return;
      }

      setError(error);
      
      // 如果有缓存数据，继续使用
      const cachedData = getCachedData();
      if (cachedData) {
        setData(cachedData);
      }
    } finally {
      setLoading(false);
    }
  }, [key, fetcher, getCachedData, isStale, cacheTime, staleTime, retry, retryDelay]);

  // 初始化数据获取
  useEffect(() => {
    fetchData();

    return () => {
      if (abortController.current) {
        abortController.current.abort();
      }
    };
  }, [fetchData]);

  // 窗口焦点时重新获取
  useEffect(() => {
    if (!refetchOnWindowFocus) return;

    const handleFocus = () => {
      if (isStale()) {
        fetchData();
      }
    };

    window.addEventListener('focus', handleFocus);
    return () => window.removeEventListener('focus', handleFocus);
  }, [refetchOnWindowFocus, fetchData, isStale]);

  // 网络重连时重新获取
  useEffect(() => {
    if (!refetchOnReconnect) return;

    const handleOnline = () => {
      if (isStale()) {
        fetchData();
      }
    };

    window.addEventListener('online', handleOnline);
    return () => window.removeEventListener('online', handleOnline);
  }, [refetchOnReconnect, fetchData, isStale]);

  const refetch = useCallback(() => fetchData(true), [fetchData]);

  return {
    data,
    loading,
    error,
    refetch,
    isStale: isStale(),
  };
};

// 批量数据获取Hook
export const useBatchData = <T>(
  keys: string[],
  fetcher: (keys: string[]) => Promise<Record<string, T>>,
  options: UseOptimizedDataOptions = {}
) => {
  const [data, setData] = React.useState<Record<string, T>>({});
  const [loading, setLoading] = React.useState(false);
  const [error, setError] = React.useState<Error | null>(null);

  const fetchBatchData = useCallback(async () => {
    setLoading(true);
    setError(null);

    try {
      const result = await fetcher(keys);
      setData(result);
    } catch (err) {
      setError(err as Error);
    } finally {
      setLoading(false);
    }
  }, [keys, fetcher]);

  useEffect(() => {
    if (keys.length > 0) {
      fetchBatchData();
    }
  }, [fetchBatchData]);

  return {
    data,
    loading,
    error,
    refetch: fetchBatchData,
  };
};

// 无限滚动数据Hook
export const useInfiniteData = <T>(
  fetcher: (page: number, pageSize: number) => Promise<{ data: T[]; hasMore: boolean }>,
  pageSize: number = 20
) => {
  const [data, setData] = React.useState<T[]>([]);
  const [loading, setLoading] = React.useState(false);
  const [error, setError] = React.useState<Error | null>(null);
  const [hasMore, setHasMore] = React.useState(true);
  const [page, setPage] = React.useState(1);

  const fetchMore = useCallback(async () => {
    if (loading || !hasMore) return;

    setLoading(true);
    setError(null);

    try {
      const result = await fetcher(page, pageSize);
      setData(prev => [...prev, ...result.data]);
      setHasMore(result.hasMore);
      setPage(prev => prev + 1);
    } catch (err) {
      setError(err as Error);
    } finally {
      setLoading(false);
    }
  }, [fetcher, page, pageSize, loading, hasMore]);

  const reset = useCallback(() => {
    setData([]);
    setPage(1);
    setHasMore(true);
    setError(null);
  }, []);

  useEffect(() => {
    if (data.length === 0) {
      fetchMore();
    }
  }, [fetchMore, data.length]);

  return {
    data,
    loading,
    error,
    hasMore,
    fetchMore,
    reset,
  };
};

// 清理缓存的工具函数
export const clearDataCache = (pattern?: string) => {
  if (pattern) {
    const regex = new RegExp(pattern);
    for (const key of dataCache.keys()) {
      if (regex.test(key)) {
        dataCache.delete(key);
      }
    }
  } else {
    dataCache.clear();
  }
};

// 获取缓存统计
export const getCacheStats = () => {
  const now = Date.now();
  let validCount = 0;
  let expiredCount = 0;

  for (const [key, cached] of dataCache.entries()) {
    if (now - cached.timestamp < cached.cacheTime) {
      validCount++;
    } else {
      expiredCount++;
    }
  }

  return {
    total: dataCache.size,
    valid: validCount,
    expired: expiredCount,
  };
};
