/* 高性能动画和过渡效果 - 专门优化页面切换和交互动画 */

/* 页面切换动画 - 使用transform和opacity避免重排重绘 */
.page-enter {
  opacity: 0;
  transform: translateX(20px) translateZ(0);
}

.page-enter-active {
  opacity: 1;
  transform: translateX(0) translateZ(0);
  transition: opacity 0.2s cubic-bezier(0.4, 0, 0.2, 1),
              transform 0.2s cubic-bezier(0.4, 0, 0.2, 1);
}

.page-exit {
  opacity: 1;
  transform: translateX(0) translateZ(0);
}

.page-exit-active {
  opacity: 0;
  transform: translateX(-20px) translateZ(0);
  transition: opacity 0.15s cubic-bezier(0.4, 0, 0.2, 1),
              transform 0.15s cubic-bezier(0.4, 0, 0.2, 1);
}

/* 侧边栏动画优化 */
.sidebar-collapse-enter {
  width: 280px;
  transform: translateZ(0);
}

.sidebar-collapse-enter-active {
  width: 80px;
  transform: translateZ(0);
  transition: width 0.2s cubic-bezier(0.25, 0.46, 0.45, 0.94);
}

.sidebar-collapse-exit {
  width: 80px;
  transform: translateZ(0);
}

.sidebar-collapse-exit-active {
  width: 280px;
  transform: translateZ(0);
  transition: width 0.2s cubic-bezier(0.25, 0.46, 0.45, 0.94);
}

/* 卡片悬停动画 - 使用transform避免重排 */
.card-hover-optimized {
  transform: translateZ(0);
  transition: transform 0.2s cubic-bezier(0.4, 0, 0.2, 1),
              box-shadow 0.2s cubic-bezier(0.4, 0, 0.2, 1);
  will-change: transform, box-shadow;
}

.card-hover-optimized:hover {
  transform: translateY(-4px) translateZ(0);
  box-shadow: 0 12px 40px rgba(0, 122, 255, 0.15);
}

/* 按钮点击动画 */
.button-press-optimized {
  transform: translateZ(0);
  transition: transform 0.1s cubic-bezier(0.4, 0, 0.2, 1);
  will-change: transform;
}

.button-press-optimized:active {
  transform: scale(0.98) translateZ(0);
}

/* 列表项动画 */
.list-item-enter {
  opacity: 0;
  transform: translateY(20px) translateZ(0);
}

.list-item-enter-active {
  opacity: 1;
  transform: translateY(0) translateZ(0);
  transition: opacity 0.3s cubic-bezier(0.4, 0, 0.2, 1),
              transform 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

.list-item-exit {
  opacity: 1;
  transform: translateY(0) translateZ(0);
}

.list-item-exit-active {
  opacity: 0;
  transform: translateY(-20px) translateZ(0);
  transition: opacity 0.2s cubic-bezier(0.4, 0, 0.2, 1),
              transform 0.2s cubic-bezier(0.4, 0, 0.2, 1);
}

/* 模态框动画 */
.modal-enter {
  opacity: 0;
  transform: scale(0.9) translateZ(0);
}

.modal-enter-active {
  opacity: 1;
  transform: scale(1) translateZ(0);
  transition: opacity 0.2s cubic-bezier(0.4, 0, 0.2, 1),
              transform 0.2s cubic-bezier(0.4, 0, 0.2, 1);
}

.modal-exit {
  opacity: 1;
  transform: scale(1) translateZ(0);
}

.modal-exit-active {
  opacity: 0;
  transform: scale(0.9) translateZ(0);
  transition: opacity 0.15s cubic-bezier(0.4, 0, 0.2, 1),
              transform 0.15s cubic-bezier(0.4, 0, 0.2, 1);
}

/* 加载动画 - 使用transform避免重排 */
.loading-spinner {
  transform: translateZ(0);
  animation: spin 1s linear infinite;
  will-change: transform;
}

@keyframes spin {
  from {
    transform: rotate(0deg) translateZ(0);
  }
  to {
    transform: rotate(360deg) translateZ(0);
  }
}

/* 脉冲动画 */
.pulse-animation {
  transform: translateZ(0);
  animation: pulse 2s cubic-bezier(0.4, 0, 0.6, 1) infinite;
  will-change: transform;
}

@keyframes pulse {
  0%, 100% {
    transform: scale(1) translateZ(0);
  }
  50% {
    transform: scale(1.05) translateZ(0);
  }
}

/* 淡入动画 */
.fade-in-optimized {
  animation: fadeInOptimized 0.4s cubic-bezier(0.4, 0, 0.2, 1) forwards;
  will-change: opacity;
}

@keyframes fadeInOptimized {
  from {
    opacity: 0;
  }
  to {
    opacity: 1;
  }
}

/* 滑入动画 */
.slide-in-optimized {
  animation: slideInOptimized 0.4s cubic-bezier(0.4, 0, 0.2, 1) forwards;
  will-change: transform, opacity;
}

@keyframes slideInOptimized {
  from {
    opacity: 0;
    transform: translateY(30px) translateZ(0);
  }
  to {
    opacity: 1;
    transform: translateY(0) translateZ(0);
  }
}

/* 缩放动画 */
.scale-in-optimized {
  animation: scaleInOptimized 0.3s cubic-bezier(0.4, 0, 0.2, 1) forwards;
  will-change: transform, opacity;
}

@keyframes scaleInOptimized {
  from {
    opacity: 0;
    transform: scale(0.8) translateZ(0);
  }
  to {
    opacity: 1;
    transform: scale(1) translateZ(0);
  }
}

/* 进度条动画 */
.progress-bar-optimized {
  transform: translateZ(0);
  transition: width 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  will-change: width;
}

/* 开关动画 */
.switch-optimized {
  transform: translateZ(0);
  transition: transform 0.2s cubic-bezier(0.4, 0, 0.2, 1);
  will-change: transform;
}

.switch-optimized.checked {
  transform: translateX(20px) translateZ(0);
}

/* 抽屉动画 */
.drawer-enter {
  transform: translateX(-100%) translateZ(0);
}

.drawer-enter-active {
  transform: translateX(0) translateZ(0);
  transition: transform 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

.drawer-exit {
  transform: translateX(0) translateZ(0);
}

.drawer-exit-active {
  transform: translateX(-100%) translateZ(0);
  transition: transform 0.25s cubic-bezier(0.4, 0, 0.2, 1);
}

/* 工具提示动画 */
.tooltip-enter {
  opacity: 0;
  transform: scale(0.8) translateZ(0);
}

.tooltip-enter-active {
  opacity: 1;
  transform: scale(1) translateZ(0);
  transition: opacity 0.15s cubic-bezier(0.4, 0, 0.2, 1),
              transform 0.15s cubic-bezier(0.4, 0, 0.2, 1);
}

.tooltip-exit {
  opacity: 1;
  transform: scale(1) translateZ(0);
}

.tooltip-exit-active {
  opacity: 0;
  transform: scale(0.8) translateZ(0);
  transition: opacity 0.1s cubic-bezier(0.4, 0, 0.2, 1),
              transform 0.1s cubic-bezier(0.4, 0, 0.2, 1);
}

/* 响应式动画优化 */
@media (max-width: 768px) {
  /* 移动端减少动画时长 */
  .page-enter-active,
  .page-exit-active {
    transition-duration: 0.15s;
  }
  
  .card-hover-optimized {
    transition-duration: 0.15s;
  }
  
  .modal-enter-active,
  .modal-exit-active {
    transition-duration: 0.15s;
  }
}

/* 低性能设备优化 */
@media (max-resolution: 1.5dppx) {
  /* 低分辨率设备禁用复杂动画 */
  .card-hover-optimized:hover {
    transform: none;
  }
  
  .pulse-animation {
    animation: none;
  }
}

/* 减少动画偏好支持 */
@media (prefers-reduced-motion: reduce) {
  /* 用户偏好减少动画时的处理 */
  * {
    animation-duration: 0.01ms !important;
    animation-iteration-count: 1 !important;
    transition-duration: 0.01ms !important;
  }
  
  .loading-spinner {
    animation: none;
  }
}

/* Pulse animation for Focus Shield status indicator */
@keyframes pulse {
  0% { opacity: 1; }
  50% { opacity: 0.5; }
  100% { opacity: 1; }
}

.pulse-animation {
  animation: pulse 2s infinite;
}

/* 高刷新率设备优化 */
@media (min-refresh-rate: 90Hz) {
  /* 高刷新率设备使用更流畅的动画 */
  .page-enter-active,
  .page-exit-active {
    transition-duration: 0.12s;
  }

  .card-hover-optimized {
    transition-duration: 0.12s;
  }
}
