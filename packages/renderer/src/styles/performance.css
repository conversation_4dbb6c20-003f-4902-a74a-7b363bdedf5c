/* 性能优化样式 - 解决卡顿和滚动延迟 */

/* 启用硬件加速 */
.gpu-accelerated {
  transform: translateZ(0);
  will-change: transform;
  backface-visibility: hidden;
  perspective: 1000px;
}

/* 高性能滚动容器 */
.smooth-scroll {
  scroll-behavior: smooth;
  -webkit-overflow-scrolling: touch;
  overflow-anchor: none;
  /* 启用复合层 */
  transform: translateZ(0);
  will-change: scroll-position;
}

/* 优化的毛玻璃效果 - 减少blur计算量 */
.glass-optimized {
  /* 使用更轻量的模糊效果 */
  backdrop-filter: blur(12px) saturate(150%);
  /* 启用硬件加速 */
  transform: translateZ(0);
  will-change: backdrop-filter;
  /* 减少重绘区域 */
  contain: layout style paint;
  /* 优化渲染 */
  isolation: isolate;
}

/* 高性能动画类 */
.animate-optimized {
  /* 只在transform和opacity上变化，避免重排重绘 */
  transition: transform 0.3s cubic-bezier(0.4, 0, 0.2, 1), 
              opacity 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  will-change: transform, opacity;
  transform: translateZ(0);
}

/* 悬停效果优化 */
.hover-optimized:hover {
  /* 使用transform替代其他属性变化 */
  transform: translateZ(0) translateY(-2px) scale(1.02);
}

/* 滚动性能优化 */
.scroll-optimized {
  /* 启用滚动优化 */
  scroll-behavior: smooth;
  -webkit-overflow-scrolling: touch;
  /* 减少重绘 */
  contain: layout style paint;
  /* GPU加速 */
  transform: translateZ(0);
  will-change: scroll-position;
}

/* 内容容器优化 */
.content-optimized {
  /* 启用contain优化 */
  contain: layout style paint;
  /* GPU层 */
  transform: translateZ(0);
  /* 隔离渲染上下文 */
  isolation: isolate;
}

/* 减少模糊效果层级 */
.glass-light {
  backdrop-filter: blur(8px) saturate(120%);
  background: rgba(255, 255, 255, 0.1);
  transform: translateZ(0);
  will-change: backdrop-filter;
}

.glass-medium {
  backdrop-filter: blur(12px) saturate(140%);
  background: rgba(255, 255, 255, 0.15);
  transform: translateZ(0);
  will-change: backdrop-filter;
}

.glass-heavy {
  backdrop-filter: blur(16px) saturate(160%);
  background: rgba(255, 255, 255, 0.2);
  transform: translateZ(0);
  will-change: backdrop-filter;
}

/* 虚拟滚动优化 */
.virtual-scroll {
  height: 100%;
  overflow: auto;
  scroll-behavior: smooth;
  -webkit-overflow-scrolling: touch;
  /* 减少滚动时的重绘 */
  contain: layout style paint;
  transform: translateZ(0);
}

/* 避免不必要的重排重绘 */
.layout-optimized {
  /* 固定尺寸避免重排 */
  width: 100%;
  height: 100%;
  /* 包含优化 */
  contain: layout style;
  /* GPU加速 */
  transform: translateZ(0);
}

/* 延迟加载占位符 */
.loading-placeholder {
  background: linear-gradient(90deg, transparent, rgba(255,255,255,0.2), transparent);
  background-size: 200px 100%;
  animation: shimmer 1.5s infinite;
  transform: translateZ(0);
}

@keyframes shimmer {
  0% { background-position: -200px 0; }
  100% { background-position: calc(200px + 100%) 0; }
}

/* 高性能卡片容器 */
.card-container-optimized {
  /* 虚拟化支持 */
  contain: layout style paint;
  /* GPU加速 */
  transform: translateZ(0);
  /* 隔离 */
  isolation: isolate;
}

/* 文本渲染优化 */
.text-optimized {
  /* 优化文本渲染 */
  text-rendering: optimizeSpeed;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  /* 避免重排 */
  contain: layout style;
}

/* 图片加载优化 */
.image-optimized {
  /* 延迟解码 */
  decoding: async;
  /* 加载策略 */
  loading: lazy;
  /* GPU加速 */
  transform: translateZ(0);
  /* 避免重排 */
  contain: layout;
}

/* 列表项优化 */
.list-item-optimized {
  /* 独立渲染层 */
  contain: layout style paint;
  /* GPU加速 */
  transform: translateZ(0);
  /* 避免影响其他元素 */
  isolation: isolate;
}

/* 表格性能优化 */
.table-optimized {
  /* 固定表格布局 */
  table-layout: fixed;
  /* 包含优化 */
  contain: layout style;
  /* GPU加速 */
  transform: translateZ(0);
}

/* 模态框优化 */
.modal-optimized {
  /* 独立层 */
  isolation: isolate;
  /* GPU加速 */
  transform: translateZ(0);
  /* 包含优化 */
  contain: layout style paint;
}

/* 侧边栏优化 */
.sidebar-optimized {
  /* 固定定位避免重排 */
  position: fixed;
  /* GPU加速 */
  transform: translateZ(0);
  /* 包含优化 */
  contain: layout style paint;
  /* 独立层 */
  isolation: isolate;
}

/* 响应式优化 */
@media (max-width: 768px) {
  /* 移动端减少模糊效果 */
  .glass-optimized {
    backdrop-filter: blur(8px) saturate(120%);
  }
  
  /* 减少动画复杂度 */
  .animate-optimized {
    transition: transform 0.2s ease-out, opacity 0.2s ease-out;
  }
}

/* 低性能设备优化 */
@media (max-resolution: 1dppx) {
  .glass-optimized {
    /* 低分辨率设备使用更简单的效果 */
    backdrop-filter: blur(6px);
    background: rgba(255, 255, 255, 0.8);
  }
}

/* 高刷新率优化 */
@media (min-refresh-rate: 120Hz) {
  .animate-optimized {
    /* 高刷新率设备使用更流畅的动画 */
    transition: transform 0.15s cubic-bezier(0.4, 0, 0.2, 1),
                opacity 0.15s cubic-bezier(0.4, 0, 0.2, 1);
  }
}