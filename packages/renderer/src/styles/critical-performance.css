/* 关键渲染路径优化 - 专门解决页面切换卡顿 */

/* 消除页面切换延迟 */
.page-transition {
  /* 使用GPU硬件加速 */
  transform: translateZ(0);
  will-change: contents;
  
  /* 启用复合层 */
  isolation: isolate;
  
  /* 包含策略 - 防止影响其他元素 */
  contain: layout style paint;
  
  /* 强制浏览器提前准备渲染 */
  content-visibility: auto;
}

/* 侧边栏切换优化 */
.sidebar-transition {
  /* 使用3D变换强制GPU加速 */
  transform: translate3d(0, 0, 0);
  will-change: transform, width;
  
  /* 快速过渡 */
  transition: width 0.2s cubic-bezier(0.25, 0.46, 0.45, 0.94);
  
  /* 包含策略 */
  contain: layout style;
}

/* 内容区域切换优化 */
.content-transition {
  /* GPU层 */
  transform: translateZ(0);
  will-change: opacity, transform;
  
  /* 快速淡入淡出 */
  transition: opacity 0.15s ease-out;
  
  /* 包含策略 */
  contain: layout style paint;
  
  /* 内容可见性优化 */
  content-visibility: auto;
  contain-intrinsic-size: 800px 600px;
}

/* 消除毛玻璃效果的重排 */
.glass-no-blur {
  /* 完全移除backdrop-filter以消除性能瓶颈 */
  backdrop-filter: none !important;
  background: rgba(255, 255, 255, 0.85) !important;
  
  /* 使用简单边框替代复杂阴影 */
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1) !important;
  border: 1px solid rgba(255, 255, 255, 0.2) !important;
}

/* 高性能卡片容器 */
.ultra-fast-card {
  /* 最小化重绘 */
  background: rgba(255, 255, 255, 0.9);
  border: 1px solid rgba(0, 0, 0, 0.1);
  border-radius: 8px;
  
  /* GPU加速 */
  transform: translateZ(0);
  
  /* 包含优化 */
  contain: layout style paint;
  
  /* 避免重排 */
  width: 100%;
  box-sizing: border-box;
}

/* 快速列表项 */
.fast-list-item {
  /* 固定高度避免重排 */
  height: 60px;
  
  /* 独立渲染层 */
  contain: layout style paint;
  
  /* GPU加速 */
  transform: translateZ(0);
  
  /* 快速hover */
  transition: background-color 0.1s ease;
}

/* 文本渲染优化 */
.fast-text {
  /* 优化文本渲染性能 */
  text-rendering: optimizeSpeed;
  font-feature-settings: "kern" 0;
  
  /* 避免重排 */
  contain: layout;
  
  /* 字体平滑 */
  -webkit-font-smoothing: subpixel-antialiased;
}

/* 表单元素优化 */
.fast-input {
  /* 移除复杂样式 */
  backdrop-filter: none !important;
  background: rgba(255, 255, 255, 0.95) !important;
  border: 1px solid rgba(0, 0, 0, 0.15) !important;
  
  /* 快速focus */
  transition: border-color 0.1s ease, box-shadow 0.1s ease;
  
  /* 包含优化 */
  contain: layout style;
}

/* 按钮快速响应 */
.fast-button {
  /* 移除复杂效果 */
  backdrop-filter: none !important;
  
  /* 快速变换 */
  transition: all 0.1s ease;
  
  /* GPU加速 */
  transform: translateZ(0);
  
  /* 包含优化 */
  contain: layout style paint;
}

.fast-button:hover {
  /* 只使用简单变换 */
  transform: translateZ(0) scale(1.02);
}

.fast-button:active {
  /* 快速按下反馈 */
  transform: translateZ(0) scale(0.98);
}

/* 滚动容器极速优化 */
.ultra-scroll {
  /* 滚动性能最大化 */
  overflow: auto;
  scroll-behavior: auto; /* 移除smooth以提升性能 */
  -webkit-overflow-scrolling: touch;
  
  /* 强制GPU层 */
  transform: translateZ(0);
  will-change: scroll-position;
  
  /* 包含策略 */
  contain: layout style paint;
  
  /* 滚动捕捉禁用 */
  overscroll-behavior: none;
}

/* 网格布局优化 */
.fast-grid {
  /* 使用transform代替margin/padding变化 */
  display: grid;
  gap: 16px;
  
  /* 包含优化 */
  contain: layout style;
  
  /* GPU加速 */
  transform: translateZ(0);
}

/* 响应式优化 - 移动端性能优先 */
@media (max-width: 768px) {
  /* 移动端完全禁用毛玻璃效果 */
  .glass-optimized,
  .glass-light,
  .glass-medium,
  .glass-heavy {
    backdrop-filter: none !important;
    background: rgba(255, 255, 255, 0.95) !important;
  }
  
  /* 减少动画复杂度 */
  .animate-optimized {
    transition: none !important;
  }
  
  /* 简化hover效果 */
  .hover-optimized:hover {
    transform: none !important;
  }
}

/* 低性能设备优化 */
@media (max-resolution: 1.5dppx) {
  /* 低分辨率设备禁用所有特效 */
  * {
    backdrop-filter: none !important;
    box-shadow: none !important;
    transform: none !important;
    transition: none !important;
  }
}

/* 高刷新率设备优化 */
@media (min-refresh-rate: 90Hz) {
  .content-transition {
    /* 高刷新率设备使用更快的动画 */
    transition: opacity 0.08s ease-out;
  }
  
  .sidebar-transition {
    transition: width 0.12s cubic-bezier(0.25, 0.46, 0.45, 0.94);
  }
}

/* 强制硬件加速类 */
.force-gpu {
  transform: translate3d(0, 0, 0);
  will-change: transform;
  backface-visibility: hidden;
  perspective: 1000px;
}

/* 内容可见性优化 */
.lazy-content {
  content-visibility: auto;
  contain-intrinsic-size: 0 500px;
}

/* 防止Layout Shift */
.stable-layout {
  contain: layout;
  min-height: 1px; /* 防止collapse */
}

/* 关键CSS优化标记 */
.critical-path {
  /* 关键渲染路径元素 */
  contain: layout style paint;
  will-change: contents;
  transform: translateZ(0);
}