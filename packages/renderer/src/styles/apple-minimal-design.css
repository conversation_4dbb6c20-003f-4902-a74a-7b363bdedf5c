/* Apple Minimal Design System - 苹果官网简约风格 */

/* 设计原则：
 * 1. 简洁扁平化设计
 * 2. 高对比度和清晰层次
 * 3. 优秀的性能表现
 * 4. 无毛玻璃效果
 * 5. 纯色背景和清晰边框
 */

:root {
  /* 基础字体 - 苹果系统字体 */
  font-family: -apple-system, BlinkMacSystemFont, "SF Pro Display", "Segoe UI", Roboto, "Helvetica Neue", Arial, sans-serif;
  line-height: 1.5;
  font-weight: 400;
  
  /* 字体渲染优化 */
  font-synthesis: none;
  text-rendering: optimizeLegibility;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  -webkit-text-size-adjust: 100%;
  
  /* 苹果官网风格颜色方案 */
  --color-primary: #0071e3;           /* 苹果蓝 */
  --color-primary-hover: #0077ed;     /* 悬停蓝 */
  --color-primary-active: #006edb;    /* 激活蓝 */
  
  /* 背景颜色 - 纯色，无渐变 */
  --color-background: #ffffff;        /* 主背景 */
  --color-background-secondary: #f5f5f7; /* 次要背景 */
  --color-background-tertiary: #fbfbfd;  /* 第三级背景 */
  
  /* 文本颜色 */
  --color-text: #1d1d1f;             /* 主文本 */
  --color-text-secondary: #86868b;    /* 次要文本 */
  --color-text-tertiary: #a1a1a6;     /* 第三级文本 */
  --color-text-inverse: #ffffff;      /* 反色文本 */
  --color-placeholder: #86868b;       /* 占位符文本 */
  
  /* 边框颜色 */
  --color-border: #d2d2d7;           /* 主边框 */
  --color-border-light: #e8e8ed;     /* 浅边框 */
  --color-border-dark: #a1a1a6;      /* 深边框 */
  
  /* 状态颜色 */
  --color-success: #30d158;          /* 成功绿 */
  --color-warning: #ff9f0a;          /* 警告橙 */
  --color-error: #ff3b30;            /* 错误红 */
  --color-info: #007aff;             /* 信息蓝 */
  
  /* 阴影 - 简化版本 */
  --shadow-small: 0 1px 3px rgba(0, 0, 0, 0.1);
  --shadow-medium: 0 4px 12px rgba(0, 0, 0, 0.1);
  --shadow-large: 0 8px 24px rgba(0, 0, 0, 0.1);
  
  /* 圆角 */
  --radius-small: 6px;
  --radius-medium: 12px;
  --radius-large: 18px;
  
  /* 间距系统 */
  --spacing-xs: 4px;
  --spacing-sm: 8px;
  --spacing-md: 16px;
  --spacing-lg: 24px;
  --spacing-xl: 32px;
  --spacing-2xl: 48px;
  --spacing-3xl: 64px;
  
  /* 字体大小 */
  --font-size-xs: 12px;
  --font-size-sm: 14px;
  --font-size-base: 16px;
  --font-size-lg: 18px;
  --font-size-xl: 20px;
  --font-size-2xl: 24px;
  --font-size-3xl: 32px;
  --font-size-4xl: 40px;
  
  /* 字体粗细 */
  --font-weight-normal: 400;
  --font-weight-medium: 500;
  --font-weight-semibold: 600;
  --font-weight-bold: 700;
  
  /* 过渡动画 - 简化版本 */
  --transition-fast: 0.15s ease-out;
  --transition-normal: 0.25s ease-out;
  --transition-slow: 0.35s ease-out;
}

/* 深色模式CSS变量 - 通过主题系统控制 */
:root {
  /* 主色调RGB值，用于透明度计算 */
  --color-primary-rgb: 0, 122, 255;

  /* 字体设置 */
  --font-weight-normal: 400;
  --font-weight-medium: 500;
  --font-weight-semibold: 600;
  --font-weight-bold: 700;

  /* 字体大小 */
  --font-size-xs: 12px;
  --font-size-sm: 14px;
  --font-size-base: 16px;
  --font-size-lg: 18px;
  --font-size-xl: 20px;
  --font-size-2xl: 24px;

  /* 间距 */
  --spacing-xs: 4px;
  --spacing-sm: 8px;
  --spacing-md: 12px;
  --spacing-lg: 16px;
  --spacing-xl: 24px;
  --spacing-2xl: 32px;

  /* 圆角 */
  --radius-small: 6px;
  --radius-medium: 8px;
  --radius-large: 12px;
  --radius-xl: 16px;

  /* 过渡动画 */
  --transition-fast: 0.15s ease;
  --transition-normal: 0.25s ease;
  --transition-slow: 0.35s ease;

  /* Z-index层级 */
  --z-dropdown: 1000;
  --z-sticky: 1020;
  --z-fixed: 1030;
  --z-modal-backdrop: 1040;
  --z-modal: 1050;
  --z-popover: 1060;
  --z-tooltip: 1070;
}

/* 自动深色模式支持 */
@media (prefers-color-scheme: dark) {
  :root:not([data-theme]) {
    --color-background: linear-gradient(135deg, #000000 0%, #1C1C1E 50%, #2C2C2E 100%);
    --color-background-secondary: rgba(28, 28, 30, 0.95);
    --color-background-tertiary: rgba(44, 44, 46, 0.8);

    --color-text: #FFFFFF;
    --color-text-secondary: #98989D;
    --color-text-tertiary: #636366;
    --color-placeholder: #98989D;

    --color-border: rgba(255, 255, 255, 0.1);
    --color-border-light: rgba(255, 255, 255, 0.05);

    --color-primary: #0A84FF;
    --color-primary-hover: #409CFF;
    --color-primary-active: #0056CC;

    --color-surface: rgba(255, 255, 255, 0.05);
    --color-surface-hover: rgba(255, 255, 255, 0.08);
    --color-surface-pressed: rgba(255, 255, 255, 0.12);

    --color-shadow: rgba(0, 0, 0, 0.5);
    --color-shadow-light: rgba(0, 0, 0, 0.3);

    --color-success: #30D158;
    --color-warning: #FF9F0A;
    --color-error: #FF453A;
    --color-info: #64D2FF;
  }
}

/* 基础重置 */
* {
  box-sizing: border-box;
}

html, body, #root {
  height: 100%;
  margin: 0;
  padding: 0;
}

body {
  margin: 0;
  min-width: 320px;
  background: var(--color-background);
  color: var(--color-text);
  transition: background-color var(--transition-normal), color var(--transition-normal);
}

/* 选择文本样式 */
::selection {
  background: rgba(0, 113, 227, 0.2);
  color: var(--color-text);
}

/* 简化的滚动条样式 */
::-webkit-scrollbar {
  width: 8px;
  height: 8px;
}

::-webkit-scrollbar-track {
  background: transparent;
}

::-webkit-scrollbar-thumb {
  background: var(--color-border);
  border-radius: var(--radius-small);
}

::-webkit-scrollbar-thumb:hover {
  background: var(--color-border-dark);
}

/* 卡片样式 - 无毛玻璃效果 */
.card {
  background: var(--color-background);
  border: 1px solid var(--color-border);
  border-radius: var(--radius-medium);
  box-shadow: var(--shadow-small);
  transition: box-shadow var(--transition-normal);
}

.card:hover {
  box-shadow: var(--shadow-medium);
}

/* 按钮样式 */
.btn {
  display: inline-flex;
  align-items: center;
  justify-content: center;
  padding: var(--spacing-sm) var(--spacing-md);
  border: none;
  border-radius: var(--radius-small);
  font-size: var(--font-size-base);
  font-weight: var(--font-weight-medium);
  text-decoration: none;
  cursor: pointer;
  transition: all var(--transition-fast);
  outline: none;
}

.btn-primary {
  background: var(--color-primary);
  color: var(--color-text-inverse);
}

.btn-primary:hover {
  background: var(--color-primary-hover);
}

.btn-primary:active {
  background: var(--color-primary-active);
}

.btn-secondary {
  background: var(--color-background-secondary);
  color: var(--color-text);
  border: 1px solid var(--color-border);
}

.btn-secondary:hover {
  background: var(--color-background-tertiary);
}

/* 输入框样式 */
.input {
  width: 100%;
  padding: var(--spacing-sm) var(--spacing-md);
  border: 1px solid var(--color-border);
  border-radius: var(--radius-small);
  background: var(--color-background);
  color: var(--color-text);
  font-size: var(--font-size-base);
  transition: border-color var(--transition-fast);
}

.input:focus {
  outline: none;
  border-color: var(--color-primary);
  box-shadow: 0 0 0 3px rgba(0, 113, 227, 0.1);
}

/* 容器样式 */
.container {
  max-width: 1200px;
  margin: 0 auto;
  padding: 0 var(--spacing-md);
}

/* 网格布局 */
.grid {
  display: grid;
  gap: var(--spacing-md);
}

.grid-cols-1 { grid-template-columns: repeat(1, 1fr); }
.grid-cols-2 { grid-template-columns: repeat(2, 1fr); }
.grid-cols-3 { grid-template-columns: repeat(3, 1fr); }
.grid-cols-4 { grid-template-columns: repeat(4, 1fr); }

/* Flex布局 */
.flex {
  display: flex;
}

.flex-col {
  flex-direction: column;
}

.items-center {
  align-items: center;
}

.justify-center {
  justify-content: center;
}

.justify-between {
  justify-content: space-between;
}

/* 间距工具类 */
.p-xs { padding: var(--spacing-xs); }
.p-sm { padding: var(--spacing-sm); }
.p-md { padding: var(--spacing-md); }
.p-lg { padding: var(--spacing-lg); }
.p-xl { padding: var(--spacing-xl); }

.m-xs { margin: var(--spacing-xs); }
.m-sm { margin: var(--spacing-sm); }
.m-md { margin: var(--spacing-md); }
.m-lg { margin: var(--spacing-lg); }
.m-xl { margin: var(--spacing-xl); }

/* 文本样式 */
.text-xs { font-size: var(--font-size-xs); }
.text-sm { font-size: var(--font-size-sm); }
.text-base { font-size: var(--font-size-base); }
.text-lg { font-size: var(--font-size-lg); }
.text-xl { font-size: var(--font-size-xl); }
.text-2xl { font-size: var(--font-size-2xl); }
.text-3xl { font-size: var(--font-size-3xl); }

.font-normal { font-weight: var(--font-weight-normal); }
.font-medium { font-weight: var(--font-weight-medium); }
.font-semibold { font-weight: var(--font-weight-semibold); }
.font-bold { font-weight: var(--font-weight-bold); }

.text-primary { color: var(--color-text); }
.text-secondary { color: var(--color-text-secondary); }
.text-tertiary { color: var(--color-text-tertiary); }

/* 简化的动画 */
.fade-in {
  animation: fadeIn 0.3s ease-out;
}

.slide-up {
  animation: slideUp 0.3s ease-out;
}

@keyframes fadeIn {
  from { opacity: 0; }
  to { opacity: 1; }
}

@keyframes slideUp {
  from {
    opacity: 0;
    transform: translateY(10px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

/* 响应式设计 */
@media (max-width: 768px) {
  .container {
    padding: 0 var(--spacing-sm);
  }
  
  .grid-cols-2,
  .grid-cols-3,
  .grid-cols-4 {
    grid-template-columns: 1fr;
  }
}
