{"name": "@app/main", "type": "module", "scripts": {"build": "vite build", "typecheck": "tsc --noEmit"}, "exports": {".": {"types": "./src/index.ts", "default": "./dist/index.js"}}, "dependencies": {"@app/preload": "*", "@app/renderer": "*", "electron-updater": "6.6.2", "better-sqlite3": "^11.3.0", "axios": "^1.7.7", "@google/generative-ai": "^0.21.0"}, "devDependencies": {"@app/electron-versions": "*", "electron-devtools-installer": "3.2.0", "@types/better-sqlite3": "^7.6.11", "typescript": "5.8.3", "vite": "6.3.5"}}