import { AILogger, AILogLevel } from '../services/AILogger';

/**
 * AI日志配置管理
 */
export class AILogConfig {
  private static initialized = false;

  /**
   * 初始化AI日志配置
   */
  static initialize(): void {
    if (this.initialized) {
      return;
    }

    // 从环境变量读取日志级别
    const envLogLevel = process.env.AI_LOG_LEVEL;
    let logLevel = AILogLevel.INFO; // 默认级别

    if (envLogLevel) {
      const level = parseInt(envLogLevel);
      if (level >= 0 && level <= 4) {
        logLevel = level as AILogLevel;
      } else {
        console.warn(`无效的AI_LOG_LEVEL值: ${envLogLevel}，使用默认级别 INFO`);
      }
    } else {
      // 根据环境自动设置日志级别
      const isDevelopment = process.env.NODE_ENV === 'development';
      logLevel = isDevelopment ? AILogLevel.DEBUG : AILogLevel.INFO;
    }

    // 设置日志级别
    AILogger.setLogLevel(logLevel);

    // 记录初始化信息
    console.log(`🤖 AI日志系统已初始化 - 级别: ${this.getLogLevelName(logLevel)} (${logLevel})`);
    console.log(`🤖 环境: ${process.env.NODE_ENV || 'production'}`);
    
    // 显示日志级别说明
    if (logLevel >= AILogLevel.DEBUG) {
      console.log(`🤖 日志级别说明:`);
      console.log(`   0-ERROR: 只记录错误`);
      console.log(`   1-WARN: 记录警告和错误`);
      console.log(`   2-INFO: 记录基本信息、警告和错误`);
      console.log(`   3-DEBUG: 记录所有调试信息`);
      console.log(`   4-VERBOSE: 记录最详细的信息`);
      console.log(`🤖 设置环境变量 AI_LOG_LEVEL=0-4 可调整日志级别`);
    }

    this.initialized = true;
  }

  /**
   * 获取日志级别名称
   */
  private static getLogLevelName(level: AILogLevel): string {
    switch (level) {
      case AILogLevel.ERROR: return 'ERROR';
      case AILogLevel.WARN: return 'WARN';
      case AILogLevel.INFO: return 'INFO';
      case AILogLevel.DEBUG: return 'DEBUG';
      case AILogLevel.VERBOSE: return 'VERBOSE';
      default: return 'UNKNOWN';
    }
  }

  /**
   * 动态设置日志级别
   */
  static setLogLevel(level: AILogLevel): void {
    AILogger.setLogLevel(level);
    console.log(`🤖 AI日志级别已更新为: ${this.getLogLevelName(level)} (${level})`);
  }

  /**
   * 获取当前日志级别
   */
  static getCurrentLogLevel(): AILogLevel {
    return AILogger.getLogLevel();
  }

  /**
   * 检查是否启用了调试日志
   */
  static isDebugEnabled(): boolean {
    return AILogger.getLogLevel() >= AILogLevel.DEBUG;
  }

  /**
   * 检查是否启用了详细日志
   */
  static isVerboseEnabled(): boolean {
    return AILogger.getLogLevel() >= AILogLevel.VERBOSE;
  }

  /**
   * 启用开发模式调试
   */
  static enableDevMode(): void {
    this.setLogLevel(AILogLevel.DEBUG);
    console.log(`🤖 已启用开发模式调试`);
  }

  /**
   * 启用生产模式日志
   */
  static enableProdMode(): void {
    this.setLogLevel(AILogLevel.INFO);
    console.log(`🤖 已启用生产模式日志`);
  }

  /**
   * 启用详细模式（用于故障排查）
   */
  static enableVerboseMode(): void {
    this.setLogLevel(AILogLevel.VERBOSE);
    console.log(`🤖 已启用详细模式日志（用于故障排查）`);
  }

  /**
   * 显示日志配置帮助
   */
  static showHelp(): void {
    console.log(`
🤖 FocusOS AI日志系统配置说明

环境变量配置:
  AI_LOG_LEVEL=0    只记录错误
  AI_LOG_LEVEL=1    记录警告和错误
  AI_LOG_LEVEL=2    记录基本信息、警告和错误 (默认生产环境)
  AI_LOG_LEVEL=3    记录所有调试信息 (默认开发环境)
  AI_LOG_LEVEL=4    记录最详细的信息

代码中动态设置:
  AILogConfig.enableDevMode()      // 开发模式
  AILogConfig.enableProdMode()     // 生产模式
  AILogConfig.enableVerboseMode()  // 详细模式
  AILogConfig.setLogLevel(level)   // 自定义级别

当前设置:
  日志级别: ${this.getLogLevelName(this.getCurrentLogLevel())} (${this.getCurrentLogLevel()})
  环境: ${process.env.NODE_ENV || 'production'}
  调试模式: ${this.isDebugEnabled() ? '启用' : '禁用'}
  详细模式: ${this.isVerboseEnabled() ? '启用' : '禁用'}
    `);
  }
}