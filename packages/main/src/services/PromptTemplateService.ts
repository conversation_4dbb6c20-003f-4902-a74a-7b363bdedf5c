/**
 * Prompt模板服务
 * 提供灵活的AI提示词模板系统，支持不同类型的分解任务
 */

// 模板变量接口
export interface TemplateVariables {
  goalName: string;
  goalDescription: string;
  whyPower: string;
  preferences?: {
    maxDepth?: number;
    taskGranularity?: 'fine' | 'medium' | 'coarse';
    maxTaskDuration?: number;
    includeTimeEstimates?: boolean;
    focusAreas?: string[];
    domainContext?: string;
  };
  context?: {
    userExperience?: 'beginner' | 'intermediate' | 'expert';
    availableTime?: string;
    resources?: string[];
    constraints?: string[];
  };
}

// 模板类型
export type TemplateType = 
  | 'goal_decomposition'      // 基础目标分解
  | 'project_breakdown'       // 项目分解
  | 'skill_learning'          // 技能学习
  | 'habit_formation'         // 习惯养成
  | 'problem_solving'         // 问题解决
  | 'creative_project'        // 创意项目
  | 'business_goal'           // 商业目标
  | 'personal_development';   // 个人发展

// 模板配置
export interface TemplateConfig {
  type: TemplateType;
  name: string;
  description: string;
  systemPrompt: string;
  userPromptTemplate: string;
  outputFormat: string;
  examples?: string[];
  validationRules?: string[];
}

export class PromptTemplateService {
  private templates: Map<TemplateType, TemplateConfig> = new Map();

  constructor() {
    this.initializeTemplates();
  }

  /**
   * 获取指定类型的提示词
   */
  getPrompt(type: TemplateType, variables: TemplateVariables): string {
    const template = this.templates.get(type);
    if (!template) {
      throw new Error(`未找到模板类型: ${type}`);
    }

    const systemPrompt = this.interpolateTemplate(template.systemPrompt, variables);
    const userPrompt = this.interpolateTemplate(template.userPromptTemplate, variables);
    const outputFormat = this.interpolateTemplate(template.outputFormat, variables);

    return `${systemPrompt}\n\n${userPrompt}\n\n${outputFormat}`;
  }

  /**
   * 根据目标类型自动选择最佳模板
   */
  selectBestTemplate(goalDescription: string, goalType?: string): TemplateType {
    const description = goalDescription.toLowerCase();
    
    // 关键词匹配规则
    if (description.includes('学习') || description.includes('掌握') || description.includes('技能')) {
      return 'skill_learning';
    }
    if (description.includes('习惯') || description.includes('坚持') || description.includes('每天')) {
      return 'habit_formation';
    }
    if (description.includes('项目') || description.includes('开发') || description.includes('构建')) {
      return 'project_breakdown';
    }
    if (description.includes('问题') || description.includes('解决') || description.includes('改善')) {
      return 'problem_solving';
    }
    if (description.includes('创作') || description.includes('设计') || description.includes('创意')) {
      return 'creative_project';
    }
    if (description.includes('业务') || description.includes('商业') || description.includes('营收')) {
      return 'business_goal';
    }
    if (description.includes('成长') || description.includes('提升') || description.includes('发展')) {
      return 'personal_development';
    }

    // 默认使用基础目标分解
    return 'goal_decomposition';
  }

  /**
   * 获取所有可用模板
   */
  getAllTemplates(): TemplateConfig[] {
    return Array.from(this.templates.values());
  }

  /**
   * 模板变量插值
   */
  private interpolateTemplate(template: string, variables: TemplateVariables): string {
    let result = template;
    
    // 基础变量替换
    result = result.replace(/\{\{goalName\}\}/g, variables.goalName);
    result = result.replace(/\{\{goalDescription\}\}/g, variables.goalDescription);
    result = result.replace(/\{\{whyPower\}\}/g, variables.whyPower);
    
    // 偏好设置替换
    const prefs = variables.preferences || {};
    result = result.replace(/\{\{maxDepth\}\}/g, String(prefs.maxDepth || 3));
    result = result.replace(/\{\{taskGranularity\}\}/g, prefs.taskGranularity || 'medium');
    result = result.replace(/\{\{maxTaskDuration\}\}/g, String(prefs.maxTaskDuration || 120));
    result = result.replace(/\{\{includeTimeEstimates\}\}/g, String(prefs.includeTimeEstimates !== false));
    
    // 上下文信息替换
    const context = variables.context || {};
    result = result.replace(/\{\{userExperience\}\}/g, context.userExperience || 'intermediate');
    result = result.replace(/\{\{availableTime\}\}/g, context.availableTime || '每天1-2小时');
    
    // 数组类型的变量处理
    if (prefs.focusAreas && prefs.focusAreas.length > 0) {
      result = result.replace(/\{\{focusAreas\}\}/g, prefs.focusAreas.join('、'));
    } else {
      result = result.replace(/\{\{focusAreas\}\}/g, '无特定重点');
    }
    
    if (context.resources && context.resources.length > 0) {
      result = result.replace(/\{\{resources\}\}/g, context.resources.join('、'));
    } else {
      result = result.replace(/\{\{resources\}\}/g, '基础资源');
    }
    
    if (context.constraints && context.constraints.length > 0) {
      result = result.replace(/\{\{constraints\}\}/g, context.constraints.join('、'));
    } else {
      result = result.replace(/\{\{constraints\}\}/g, '无特殊限制');
    }

    return result;
  }

  /**
   * 初始化所有模板
   */
  private initializeTemplates(): void {
    // 基础目标分解模板
    this.templates.set('goal_decomposition', {
      type: 'goal_decomposition',
      name: '基础目标分解',
      description: '通用的目标分解模板，适用于大多数目标类型',
      systemPrompt: `你是一个专业的目标分解专家，擅长运用第一性原理将复杂目标分解为可执行的具体任务。
你的任务是帮助用户将目标科学地分解为：子目标 → 里程碑 → 具体任务的层级结构。

分解原则：
1. 应用第一性原理，从根本问题出发
2. 确保每个任务都是SMART的（具体、可衡量、可实现、相关、有时限）
3. 任务粒度适中，单个任务不超过{{maxTaskDuration}}分钟
4. 优先级设置合理，关键路径清晰
5. 时间估算准确，考虑实际执行难度`,

      userPromptTemplate: `请帮我分解以下目标：

目标名称：{{goalName}}
目标描述：{{goalDescription}}
动机原因：{{whyPower}}

分解要求：
- 最大层级：{{maxDepth}}层
- 任务粒度：{{taskGranularity}}
- 单任务时长：不超过{{maxTaskDuration}}分钟
- 包含时间估算：{{includeTimeEstimates}}
- 重点关注：{{focusAreas}}
- 用户经验：{{userExperience}}
- 可用时间：{{availableTime}}
- 可用资源：{{resources}}
- 限制条件：{{constraints}}`,

      outputFormat: `请严格按照JSON格式返回，不要包含解释文字：

{
  "subGoals": [
    {
      "name": "子目标名称",
      "description": "详细描述",
      "priority": "high|medium|low",
      "estimatedTime": 小时数,
      "confidence": 0.0-1.0,
      "reasoning": "设置原因",
      "milestones": [
        {
          "name": "里程碑名称",
          "description": "成果描述",
          "estimatedTime": 小时数,
          "confidence": 0.0-1.0,
          "tasks": [
            {
              "title": "任务标题",
              "description": "执行步骤",
              "estimatedTime": 分钟数,
              "priority": "high|medium|low",
              "confidence": 0.0-1.0,
              "actionable": true,
              "resources": ["工具列表"]
            }
          ]
        }
      ]
    }
  ],
  "estimatedTotalTime": 总小时数,
  "complexity": "low|medium|high",
  "confidence": 0.0-1.0,
  "suggestions": ["建议列表"],
  "warnings": ["注意事项"]
}`
    });

    // 技能学习模板
    this.templates.set('skill_learning', {
      type: 'skill_learning',
      name: '技能学习分解',
      description: '专门用于技能学习目标的分解模板',
      systemPrompt: `你是一个专业的学习规划师，擅长将技能学习目标分解为系统化的学习路径。
你深谙学习科学原理，能够设计符合认知规律的学习计划。

学习分解原则：
1. 遵循从基础到高级的认知阶梯
2. 理论学习与实践练习相结合
3. 设置合理的学习里程碑和检验点
4. 考虑遗忘曲线，安排复习计划
5. 包含项目实践和应用场景`,

      userPromptTemplate: `请为我制定以下技能的学习计划：

学习目标：{{goalName}}
技能描述：{{goalDescription}}
学习动机：{{whyPower}}

学习条件：
- 当前水平：{{userExperience}}
- 可用时间：{{availableTime}}
- 学习资源：{{resources}}
- 重点方向：{{focusAreas}}
- 限制条件：{{constraints}}`,

      outputFormat: `⚠️ 重要：请按照学习科学原理，以JSON格式返回学习计划，不要包含任何解释文字，只返回纯JSON：

{
  "subGoals": [
    {
      "name": "学习阶段名称（如：基础理论、实践应用、高级技巧）",
      "description": "该阶段的学习目标和预期成果",
      "priority": "high|medium|low",
      "estimatedTime": 预估学习小时数,
      "confidence": 0.0-1.0,
      "reasoning": "设置该阶段的学习科学依据",
      "milestones": [
        {
          "name": "学习里程碑",
          "description": "具体的学习成果和验收标准",
          "estimatedTime": 预估小时数,
          "confidence": 0.0-1.0,
          "dependencies": ["前置学习内容"],
          "tasks": [
            {
              "title": "具体学习任务",
              "description": "详细的学习活动和练习内容",
              "estimatedTime": 预估分钟数,
              "priority": "high|medium|low", 
              "confidence": 0.0-1.0,
              "actionable": true,
              "resources": ["学习资源和工具"]
            }
          ]
        }
      ]
    }
  ],
  "estimatedTotalTime": 总学习小时数,
  "complexity": "low|medium|high",
  "confidence": 整体可行性0.0-1.0,
  "suggestions": ["学习建议和技巧"],
  "warnings": ["学习难点和注意事项"]
}`
    });

    // 项目分解模板
    this.templates.set('project_breakdown', {
      type: 'project_breakdown',
      name: '项目分解',
      description: '适用于项目管理和开发任务的分解模板',
      systemPrompt: `你是一个经验丰富的项目经理，擅长将复杂项目分解为可管理的工作包。
你熟悉项目管理最佳实践，能够识别关键路径、依赖关系和风险点。

项目分解原则：
1. 按照项目生命周期划分阶段
2. 识别关键路径和依赖关系
3. 合理分配资源和时间
4. 设置质量检查点和里程碑
5. 考虑风险管理和应急计划`,

      userPromptTemplate: `请帮我分解以下项目：

项目名称：{{goalName}}
项目描述：{{goalDescription}}
项目价值：{{whyPower}}

项目条件：
- 团队经验：{{userExperience}}
- 项目周期：{{availableTime}}
- 可用资源：{{resources}}
- 重点模块：{{focusAreas}}
- 约束条件：{{constraints}}`,

      outputFormat: `⚠️ 重要：请按照项目管理标准，以JSON格式返回项目分解，不要包含任何解释文字，只返回纯JSON：

{
  "subGoals": [
    {
      "name": "项目阶段名称（如：需求分析、设计开发、测试部署）",
      "description": "该阶段的交付物和验收标准",
      "priority": "high|medium|low",
      "estimatedTime": 预估工时,
      "confidence": 0.0-1.0,
      "reasoning": "设置该阶段的项目管理依据",
      "milestones": [
        {
          "name": "项目里程碑",
          "description": "具体的交付成果和质量标准",
          "estimatedTime": 预估工时,
          "confidence": 0.0-1.0,
          "dependencies": ["前置工作包"],
          "tasks": [
            {
              "title": "具体工作任务",
              "description": "详细的工作内容和产出要求",
              "estimatedTime": 预估分钟数,
              "priority": "high|medium|low",
              "confidence": 0.0-1.0,
              "actionable": true,
              "resources": ["所需资源和工具"]
            }
          ]
        }
      ]
    }
  ],
  "estimatedTotalTime": 总项目工时,
  "complexity": "low|medium|high", 
  "confidence": 项目成功率0.0-1.0,
  "suggestions": ["项目管理建议"],
  "warnings": ["风险点和注意事项"]
}`
    });

    // 习惯养成模板
    this.templates.set('habit_formation', {
      type: 'habit_formation',
      name: '习惯养成',
      description: '专门用于习惯养成目标的分解模板',
      systemPrompt: `你是一个专业的习惯养成专家，深度理解行为心理学和习惯形成的科学原理。
你擅长将习惯养成目标分解为渐进式的行为改变计划。

习惯养成原则：
1. 遵循习惯回路：提示-行为-奖励
2. 从微习惯开始，逐步增强
3. 设计明确的触发机制
4. 建立即时反馈系统
5. 考虑习惯堆叠和环境设计`,

      userPromptTemplate: `请为我制定以下习惯的养成计划：

习惯目标：{{goalName}}
习惯描述：{{goalDescription}}
养成动机：{{whyPower}}

个人情况：
- 当前状态：{{userExperience}}
- 可用时间：{{availableTime}}
- 环境资源：{{resources}}
- 重点关注：{{focusAreas}}
- 限制条件：{{constraints}}`,

      outputFormat: `请基于行为心理学原理，以JSON格式返回习惯养成计划：

{
  "subGoals": [
    {
      "name": "习惯阶段名称（如：初期适应、稳定强化、长期维持）",
      "description": "该阶段的行为目标和预期效果",
      "priority": "high|medium|low",
      "estimatedTime": 预估天数,
      "confidence": 0.0-1.0,
      "reasoning": "设置该阶段的行为科学依据",
      "milestones": [
        {
          "name": "习惯里程碑",
          "description": "具体的行为改变指标和检验标准",
          "estimatedTime": 预估天数,
          "confidence": 0.0-1.0,
          "dependencies": ["前置习惯基础"],
          "tasks": [
            {
              "title": "具体习惯任务",
              "description": "详细的行为执行步骤和触发条件",
              "estimatedTime": 预估分钟数,
              "priority": "high|medium|low",
              "confidence": 0.0-1.0,
              "actionable": true,
              "resources": ["支持工具和环境设置"]
            }
          ]
        }
      ]
    }
  ],
  "estimatedTotalTime": 总养成周期天数,
  "complexity": "low|medium|high",
  "confidence": 习惯养成成功率0.0-1.0,
  "suggestions": ["习惯养成技巧和策略"],
  "warnings": ["常见失败原因和预防措施"]
}`
    });

    // 问题解决模板
    this.templates.set('problem_solving', {
      type: 'problem_solving',
      name: '问题解决',
      description: '专门用于问题解决目标的分解模板',
      systemPrompt: `你是一个专业的问题解决专家，擅长运用结构化思维分析和解决复杂问题。
你熟悉各种问题解决框架，能够系统性地分解问题并制定解决方案。

问题解决原则：
1. 精准定义问题本质
2. 分析问题根本原因
3. 生成多种解决方案
4. 评估方案可行性
5. 制定实施和验证计划`,

      userPromptTemplate: `请帮我分解以下问题的解决方案：

问题描述：{{goalName}}
问题详情：{{goalDescription}}
解决动机：{{whyPower}}

问题背景：
- 相关经验：{{userExperience}}
- 可用时间：{{availableTime}}
- 可用资源：{{resources}}
- 重点方向：{{focusAreas}}
- 限制条件：{{constraints}}`,

      outputFormat: `请按照问题解决框架，以JSON格式返回解决方案：

{
  "subGoals": [
    {
      "name": "解决阶段名称（如：问题分析、方案设计、实施验证）",
      "description": "该阶段的解决目标和预期成果",
      "priority": "high|medium|low",
      "estimatedTime": 预估小时数,
      "confidence": 0.0-1.0,
      "reasoning": "设置该阶段的问题解决逻辑",
      "milestones": [
        {
          "name": "解决里程碑",
          "description": "具体的解决成果和验证标准",
          "estimatedTime": 预估小时数,
          "confidence": 0.0-1.0,
          "dependencies": ["前置解决步骤"],
          "tasks": [
            {
              "title": "具体解决任务",
              "description": "详细的行动步骤和实施方法",
              "estimatedTime": 预估分钟数,
              "priority": "high|medium|low",
              "confidence": 0.0-1.0,
              "actionable": true,
              "resources": ["所需工具和支持"]
            }
          ]
        }
      ]
    }
  ],
  "estimatedTotalTime": 总解决时间,
  "complexity": "low|medium|high",
  "confidence": 解决成功率0.0-1.0,
  "suggestions": ["解决问题的建议和技巧"],
  "warnings": ["潜在风险和注意事项"]
}`
    });

    // 创意项目模板
    this.templates.set('creative_project', {
      type: 'creative_project',
      name: '创意项目',
      description: '专门用于创意项目目标的分解模板',
      systemPrompt: `你是一个专业的创意项目导师，擅长将创意想法转化为可执行的项目计划。
你深谙创意流程和项目管理的结合，能够平衡创造性与实用性。

创意项目原则：
1. 保护核心创意理念
2. 设计创意探索阶段
3. 平衡创新与可行性
4. 建立创意评估机制
5. 规划创意实现路径`,

      userPromptTemplate: `请帮我规划以下创意项目：

项目名称：{{goalName}}
创意描述：{{goalDescription}}
项目意义：{{whyPower}}

创作条件：
- 创作经验：{{userExperience}}
- 项目周期：{{availableTime}}
- 创作资源：{{resources}}
- 重点领域：{{focusAreas}}
- 限制条件：{{constraints}}`,

      outputFormat: `请按照创意项目流程，以JSON格式返回项目计划：

{
  "subGoals": [
    {
      "name": "创意阶段名称（如：概念设计、原型制作、完善优化）",
      "description": "该阶段的创意目标和预期产出",
      "priority": "high|medium|low",
      "estimatedTime": 预估小时数,
      "confidence": 0.0-1.0,
      "reasoning": "设置该阶段的创意逻辑和必要性",
      "milestones": [
        {
          "name": "创意里程碑",
          "description": "具体的创意成果和质量标准",
          "estimatedTime": 预估小时数,
          "confidence": 0.0-1.0,
          "dependencies": ["前置创意基础"],
          "tasks": [
            {
              "title": "具体创意任务",
              "description": "详细的创作活动和实现步骤",
              "estimatedTime": 预估分钟数,
              "priority": "high|medium|low",
              "confidence": 0.0-1.0,
              "actionable": true,
              "resources": ["创作工具和素材"]
            }
          ]
        }
      ]
    }
  ],
  "estimatedTotalTime": 总创作时间,
  "complexity": "low|medium|high",
  "confidence": 项目完成度0.0-1.0,
  "suggestions": ["创意项目建议和技巧"],
  "warnings": ["创意风险和注意事项"]
}`
    });

    // 商业目标模板
    this.templates.set('business_goal', {
      type: 'business_goal',
      name: '商业目标',
      description: '专门用于商业目标的分解模板',
      systemPrompt: `你是一个资深的商业战略顾问，擅长将商业目标分解为可执行的业务计划。
你深谙商业逻辑和市场规律，能够制定既有野心又切实可行的商业策略。

商业目标原则：
1. 明确商业价值主张
2. 分析市场和竞争环境
3. 设计可持续商业模式
4. 建立关键指标体系
5. 规划风险控制措施`,

      userPromptTemplate: `请帮我制定以下商业目标的实现计划：

商业目标：{{goalName}}
目标描述：{{goalDescription}}
商业动机：{{whyPower}}

商业条件：
- 商业经验：{{userExperience}}
- 项目周期：{{availableTime}}
- 可用资源：{{resources}}
- 重点领域：{{focusAreas}}
- 约束条件：{{constraints}}`,

      outputFormat: `请按照商业规划标准，以JSON格式返回商业计划：

{
  "subGoals": [
    {
      "name": "商业阶段名称（如：市场调研、产品开发、市场推广）",
      "description": "该阶段的商业目标和预期收益",
      "priority": "high|medium|low",
      "estimatedTime": 预估小时数,
      "confidence": 0.0-1.0,
      "reasoning": "设置该阶段的商业逻辑和战略意义",
      "milestones": [
        {
          "name": "商业里程碑",
          "description": "具体的商业成果和衡量指标",
          "estimatedTime": 预估小时数,
          "confidence": 0.0-1.0,
          "dependencies": ["前置商业基础"],
          "tasks": [
            {
              "title": "具体商业任务",
              "description": "详细的商业活动和执行步骤",
              "estimatedTime": 预估分钟数,
              "priority": "high|medium|low",
              "confidence": 0.0-1.0,
              "actionable": true,
              "resources": ["商业工具和资源"]
            }
          ]
        }
      ]
    }
  ],
  "estimatedTotalTime": 总商业周期,
  "complexity": "low|medium|high",
  "confidence": 商业成功率0.0-1.0,
  "suggestions": ["商业策略建议"],
  "warnings": ["商业风险和注意事项"]
}`
    });

    // 个人发展模板
    this.templates.set('personal_development', {
      type: 'personal_development',
      name: '个人发展',
      description: '专门用于个人发展目标的分解模板',
      systemPrompt: `你是一个专业的个人发展教练，擅长将个人成长目标转化为具体的发展计划。
你深谙人类发展心理学和成长规律，能够设计个性化的发展路径。

个人发展原则：
1. 建立清晰的发展愿景
2. 评估当前能力基线
3. 设计渐进式成长路径
4. 建立持续反馈机制
5. 整合多维度发展要素`,

      userPromptTemplate: `请为我制定以下个人发展计划：

发展目标：{{goalName}}
发展描述：{{goalDescription}}
发展动机：{{whyPower}}

个人状况：
- 当前水平：{{userExperience}}
- 可用时间：{{availableTime}}
- 发展资源：{{resources}}
- 重点方向：{{focusAreas}}
- 限制条件：{{constraints}}`,

      outputFormat: `请基于个人发展理论，以JSON格式返回发展计划：

{
  "subGoals": [
    {
      "name": "发展阶段名称（如：自我认知、技能提升、实践应用）",
      "description": "该阶段的发展目标和预期变化",
      "priority": "high|medium|low",
      "estimatedTime": 预估小时数,
      "confidence": 0.0-1.0,
      "reasoning": "设置该阶段的发展心理学依据",
      "milestones": [
        {
          "name": "发展里程碑",
          "description": "具体的成长成果和评估标准",
          "estimatedTime": 预估小时数,
          "confidence": 0.0-1.0,
          "dependencies": ["前置发展基础"],
          "tasks": [
            {
              "title": "具体发展任务",
              "description": "详细的成长活动和实践步骤",
              "estimatedTime": 预估分钟数,
              "priority": "high|medium|low",
              "confidence": 0.0-1.0,
              "actionable": true,
              "resources": ["发展工具和支持"]
            }
          ]
        }
      ]
    }
  ],
  "estimatedTotalTime": 总发展周期,
  "complexity": "low|medium|high",
  "confidence": 发展成功率0.0-1.0,
  "suggestions": ["个人发展建议和策略"],
  "warnings": ["发展挑战和注意事项"]
}`
    });
  }
}
