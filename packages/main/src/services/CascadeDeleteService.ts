import { DatabaseManager } from '../database/DatabaseManager';
import Database from 'better-sqlite3';

export interface DeleteAnalysis {
  goalId: string;
  goalName: string;
  subGoalsCount: number;
  milestonesCount: number;
  tasksCount: number;
  pomodoroSessionsCount: number;
  decompositionSessionsCount: number;
  userModificationsCount: number;
  totalItemsCount: number;
  subGoals: Array<{
    id: string;
    name: string;
    milestonesCount: number;
    tasksCount: number;
  }>;
  milestones: Array<{
    id: string;
    name: string;
    tasksCount: number;
  }>;
  tasks: Array<{
    id: string;
    title: string;
    parentType: string;
    parentId: string;
  }>;
}

export interface DeleteResult {
  success: boolean;
  deletedItems: {
    goals: number;
    subGoals: number;
    milestones: number;
    tasks: number;
    pomodoroSessions: number;
    decompositionSessions: number;
    userModifications: number;
  };
  error?: string;
  rollbackInfo?: any;
}

export class CascadeDeleteService {
  private dbManager: DatabaseManager;
  private db: Database.Database;

  constructor(dbManager: DatabaseManager) {
    this.dbManager = dbManager;
    this.db = dbManager.getDatabase();
  }

  /**
   * 分析删除目标将影响的所有数据
   */
  public async analyzeGoalDeletion(goalId: string): Promise<DeleteAnalysis> {
    const goal = this.dbManager.getGoalById(goalId);
    if (!goal) {
      throw new Error(`目标不存在: ${goalId}`);
    }

    // 获取所有子目标
    const subGoals = this.getSubGoalsByGoalId(goalId);
    
    // 获取所有里程碑（包括子目标下的）
    const milestones: any[] = [];
    for (const subGoal of subGoals) {
      const subGoalMilestones = this.getMilestonesBySubGoalId(subGoal.id);
      milestones.push(...subGoalMilestones);
    }

    // 获取所有任务（包括里程碑下的和直接关联的）
    const tasks: any[] = [];
    
    // 获取里程碑下的任务
    for (const milestone of milestones) {
      const milestoneTasks = this.getTasksByParent('milestone', milestone.id);
      tasks.push(...milestoneTasks);
    }
    
    // 获取子目标下的直接任务
    for (const subGoal of subGoals) {
      const subGoalTasks = this.getTasksByParent('subgoal', subGoal.id);
      tasks.push(...subGoalTasks);
    }

    // 获取番茄钟会话数量
    const pomodoroSessionsCount = this.getPomodoroSessionsCountByTasks(tasks.map(t => t.id));

    // 获取AI分解会话
    const decompositionSessionsCount = this.getDecompositionSessionsCountByGoalId(goalId);

    // 获取用户修改记录
    const userModificationsCount = this.getUserModificationsCountByGoalId(goalId);

    // 构建详细分析
    const subGoalsWithDetails = subGoals.map(subGoal => {
      const subGoalMilestones = milestones.filter(m => m.sub_goal_id === subGoal.id);
      const subGoalTasks = tasks.filter(t => 
        t.parent_type === 'subgoal' && t.parent_id === subGoal.id ||
        subGoalMilestones.some(m => m.id === t.parent_id && t.parent_type === 'milestone')
      );
      
      return {
        id: subGoal.id,
        name: subGoal.name,
        milestonesCount: subGoalMilestones.length,
        tasksCount: subGoalTasks.length
      };
    });

    const milestonesWithDetails = milestones.map(milestone => {
      const milestoneTasks = tasks.filter(t => 
        t.parent_type === 'milestone' && t.parent_id === milestone.id
      );
      
      return {
        id: milestone.id,
        name: milestone.name,
        tasksCount: milestoneTasks.length
      };
    });

    const tasksWithDetails = tasks.map(task => ({
      id: task.id,
      title: task.title,
      parentType: task.parent_type,
      parentId: task.parent_id
    }));

    const totalItemsCount = 1 + // 目标本身
      subGoals.length + 
      milestones.length + 
      tasks.length + 
      pomodoroSessionsCount + 
      decompositionSessionsCount + 
      userModificationsCount;

    return {
      goalId,
      goalName: goal.name,
      subGoalsCount: subGoals.length,
      milestonesCount: milestones.length,
      tasksCount: tasks.length,
      pomodoroSessionsCount,
      decompositionSessionsCount,
      userModificationsCount,
      totalItemsCount,
      subGoals: subGoalsWithDetails,
      milestones: milestonesWithDetails,
      tasks: tasksWithDetails
    };
  }

  /**
   * 执行级联删除操作
   */
  public async cascadeDeleteGoal(goalId: string): Promise<DeleteResult> {
    const transaction = this.db.transaction(() => {
      try {
        // 记录删除前的状态用于可能的回滚
        const rollbackInfo = this.captureRollbackInfo(goalId);

        // 执行级联删除
        const deletedItems = this.performCascadeDelete(goalId);

        return {
          success: true,
          deletedItems,
          rollbackInfo
        };
      } catch (error) {
        throw error; // 事务会自动回滚
      }
    });

    try {
      return transaction();
    } catch (error) {
      return {
        success: false,
        deletedItems: {
          goals: 0,
          subGoals: 0,
          milestones: 0,
          tasks: 0,
          pomodoroSessions: 0,
          decompositionSessions: 0,
          userModifications: 0
        },
        error: error instanceof Error ? error.message : '删除操作失败'
      };
    }
  }

  /**
   * 执行实际的删除操作
   */
  private performCascadeDelete(goalId: string): DeleteResult['deletedItems'] {
    const deletedItems = {
      goals: 0,
      subGoals: 0,
      milestones: 0,
      tasks: 0,
      pomodoroSessions: 0,
      decompositionSessions: 0,
      userModifications: 0
    };

    // 1. 删除番茄钟会话（最底层）
    const tasks = this.getAllTasksByGoalId(goalId);
    for (const task of tasks) {
      const pomodoroResult = this.db.prepare('DELETE FROM pomodoro_sessions WHERE task_id = ?').run(task.id);
      deletedItems.pomodoroSessions += pomodoroResult.changes;
    }

    // 2. 删除任务
    for (const task of tasks) {
      const taskResult = this.db.prepare('DELETE FROM tasks WHERE id = ?').run(task.id);
      deletedItems.tasks += taskResult.changes;
    }

    // 3. 删除里程碑
    const milestones = this.getAllMilestonesByGoalId(goalId);
    for (const milestone of milestones) {
      const milestoneResult = this.db.prepare('DELETE FROM milestones WHERE id = ?').run(milestone.id);
      deletedItems.milestones += milestoneResult.changes;
    }

    // 4. 删除用户修改记录
    const decompositionSessions = this.getDecompositionSessionsByGoalId(goalId);
    for (const session of decompositionSessions) {
      const userModResult = this.db.prepare('DELETE FROM user_modifications WHERE session_id = ?').run(session.id);
      deletedItems.userModifications += userModResult.changes;
    }

    // 5. 删除AI分解会话
    const decompResult = this.db.prepare('DELETE FROM decomposition_sessions WHERE goal_id = ?').run(goalId);
    deletedItems.decompositionSessions += decompResult.changes;

    // 6. 删除子目标
    const subGoals = this.getSubGoalsByGoalId(goalId);
    for (const subGoal of subGoals) {
      const subGoalResult = this.db.prepare('DELETE FROM sub_goals WHERE id = ?').run(subGoal.id);
      deletedItems.subGoals += subGoalResult.changes;
    }

    // 7. 最后删除目标本身（包括子目标，由于外键约束）
    const goalResult = this.db.prepare('DELETE FROM goals WHERE id = ? OR parent_id = ?').run(goalId, goalId);
    deletedItems.goals += goalResult.changes;

    return deletedItems;
  }

  /**
   * 捕获回滚信息
   */
  private captureRollbackInfo(goalId: string): any {
    // 这里可以实现软删除机制的数据备份
    // 暂时返回基本信息
    return {
      goalId,
      timestamp: new Date().toISOString(),
      // 可以在这里保存完整的数据快照用于恢复
    };
  }

  // 辅助查询方法
  private getSubGoalsByGoalId(goalId: string): any[] {
    const stmt = this.db.prepare('SELECT * FROM sub_goals WHERE parent_goal_id = ?');
    return stmt.all(goalId);
  }

  private getMilestonesBySubGoalId(subGoalId: string): any[] {
    const stmt = this.db.prepare('SELECT * FROM milestones WHERE sub_goal_id = ?');
    return stmt.all(subGoalId);
  }

  private getTasksByParent(parentType: string, parentId: string): any[] {
    const stmt = this.db.prepare('SELECT * FROM tasks WHERE parent_type = ? AND parent_id = ?');
    return stmt.all(parentType, parentId);
  }

  private getAllTasksByGoalId(goalId: string): any[] {
    const stmt = this.db.prepare(`
      SELECT DISTINCT t.* FROM tasks t
      LEFT JOIN sub_goals sg ON t.parent_type = 'subgoal' AND t.parent_id = sg.id
      LEFT JOIN milestones m ON t.parent_type = 'milestone' AND t.parent_id = m.id
      LEFT JOIN sub_goals sg2 ON m.sub_goal_id = sg2.id
      WHERE sg.parent_goal_id = ? OR sg2.parent_goal_id = ?
    `);
    return stmt.all(goalId, goalId);
  }

  private getAllMilestonesByGoalId(goalId: string): any[] {
    const stmt = this.db.prepare(`
      SELECT m.* FROM milestones m
      JOIN sub_goals sg ON m.sub_goal_id = sg.id
      WHERE sg.parent_goal_id = ?
    `);
    return stmt.all(goalId);
  }

  private getPomodoroSessionsCountByTasks(taskIds: string[]): number {
    if (taskIds.length === 0) return 0;
    const placeholders = taskIds.map(() => '?').join(',');
    const stmt = this.db.prepare(`SELECT COUNT(*) as count FROM pomodoro_sessions WHERE task_id IN (${placeholders})`);
    const result = stmt.get(...taskIds) as any;
    return result.count;
  }

  private getDecompositionSessionsCountByGoalId(goalId: string): number {
    const stmt = this.db.prepare('SELECT COUNT(*) as count FROM decomposition_sessions WHERE goal_id = ?');
    const result = stmt.get(goalId) as any;
    return result.count;
  }

  private getUserModificationsCountByGoalId(goalId: string): number {
    const stmt = this.db.prepare(`
      SELECT COUNT(*) as count FROM user_modifications um
      JOIN decomposition_sessions ds ON um.session_id = ds.id
      WHERE ds.goal_id = ?
    `);
    const result = stmt.get(goalId) as any;
    return result.count;
  }

  private getDecompositionSessionsByGoalId(goalId: string): any[] {
    const stmt = this.db.prepare('SELECT * FROM decomposition_sessions WHERE goal_id = ?');
    return stmt.all(goalId);
  }
}
