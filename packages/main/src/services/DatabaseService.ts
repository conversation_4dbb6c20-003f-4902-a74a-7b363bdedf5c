import { ipcMain } from 'electron';
import { DatabaseManager } from '../database/DatabaseManager.js';
import { CascadeDeleteService } from './CascadeDeleteService';
import { AITestService } from './AITestService';
import { AIDecompositionService } from './AIDecompositionService';

export class DatabaseService {
  private dbManager: DatabaseManager;
  private aiDecompositionService: AIDecompositionService;
  private cascadeDeleteService: CascadeDeleteService;

  constructor() {
    this.dbManager = DatabaseManager.getInstance();
    this.aiDecompositionService = new AIDecompositionService();
    this.cascadeDeleteService = new CascadeDeleteService(this.dbManager);
    this.setupIpcHandlers();
  }

  private setupIpcHandlers() {
    // 目标相关的IPC处理器
    ipcMain.handle('db:goals:create', async (_, goal) => {
      try {
        this.dbManager.createGoal(goal);
        return { success: true };
      } catch (error) {
        console.error('创建目标失败:', error);
        return { success: false, error: (error as Error).message };
      }
    });

    ipcMain.handle('db:goals:update', async (_, id, updates) => {
      try {
        this.dbManager.updateGoal(id, updates);
        return { success: true };
      } catch (error) {
        console.error('更新目标失败:', error);
        return { success: false, error: (error as Error).message };
      }
    });

    ipcMain.handle('db:goals:delete', async (_, id) => {
      try {
        this.dbManager.deleteGoal(id);
        return { success: true };
      } catch (error) {
        console.error('删除目标失败:', error);
        return { success: false, error: (error as Error).message };
      }
    });

    // 级联删除相关的IPC处理器
    ipcMain.handle('db:goals:analyze-deletion', async (_, goalId) => {
      try {
        const analysis = await this.cascadeDeleteService.analyzeGoalDeletion(goalId);
        return { success: true, data: analysis };
      } catch (error) {
        console.error('分析目标删除影响失败:', error);
        return { success: false, error: (error as Error).message };
      }
    });

    ipcMain.handle('db:goals:cascade-delete', async (_, goalId) => {
      try {
        const result = await this.cascadeDeleteService.cascadeDeleteGoal(goalId);
        return { success: true, data: result };
      } catch (error) {
        console.error('级联删除目标失败:', error);
        return { success: false, error: (error as Error).message };
      }
    });

    ipcMain.handle('db:goals:getAll', async () => {
      try {
        const goals = this.dbManager.getGoals();
        return { success: true, data: goals };
      } catch (error) {
        console.error('获取目标列表失败:', error);
        return { success: false, error: (error as Error).message };
      }
    });

    ipcMain.handle('db:goals:getById', async (_, id) => {
      try {
        const goal = this.dbManager.getGoalById(id);
        return { success: true, data: goal };
      } catch (error) {
        console.error('获取目标失败:', error);
        return { success: false, error: (error as Error).message };
      }
    });

    // 任务相关的IPC处理器
    ipcMain.handle('db:tasks:create', async (_, task) => {
      try {
        this.dbManager.createTask(task);
        return { success: true };
      } catch (error) {
        console.error('创建任务失败:', error);
        return { success: false, error: (error as Error).message };
      }
    });

    ipcMain.handle('db:tasks:update', async (_, id, updates) => {
      try {
        this.dbManager.updateTask(id, updates);
        return { success: true };
      } catch (error) {
        console.error('更新任务失败:', error);
        return { success: false, error: (error as Error).message };
      }
    });

    ipcMain.handle('db:tasks:delete', async (_, id) => {
      try {
        this.dbManager.deleteTask(id);
        return { success: true };
      } catch (error) {
        console.error('删除任务失败:', error);
        return { success: false, error: (error as Error).message };
      }
    });

    ipcMain.handle('db:tasks:getAll', async () => {
      try {
        const tasks = this.dbManager.getTasks();
        return { success: true, data: tasks };
      } catch (error) {
        console.error('获取任务列表失败:', error);
        return { success: false, error: (error as Error).message };
      }
    });

    // 番茄钟会话相关的IPC处理器
    ipcMain.handle('db:pomodoro:createSession', async (_, session) => {
      try {
        this.dbManager.createPomodoroSession(session);
        return { success: true };
      } catch (error) {
        console.error('创建番茄钟会话失败:', error);
        return { success: false, error: (error as Error).message };
      }
    });

    ipcMain.handle('db:pomodoro:getSessions', async () => {
      try {
        const sessions = this.dbManager.getPomodoroSessions();
        return { success: true, data: sessions };
      } catch (error) {
        console.error('获取番茄钟会话失败:', error);
        return { success: false, error: (error as Error).message };
      }
    });

    ipcMain.handle('db:pomodoro:updateSession', async (_, id, updates) => {
      try {
        this.dbManager.updatePomodoroSession(id, updates);
        return { success: true, data: updates };
      } catch (error) {
        console.error('更新番茄钟会话失败:', error);
        return { success: false, error: (error as Error).message };
      }
    });

    // 设置相关的IPC处理器
    ipcMain.handle('db:settings:get', async (_, key) => {
      try {
        const value = this.dbManager.getSetting(key);
        return { success: true, data: value };
      } catch (error) {
        console.error('获取设置失败:', error);
        return { success: false, error: (error as Error).message };
      }
    });

    ipcMain.handle('db:settings:set', async (_, key, value) => {
      try {
        this.dbManager.setSetting(key, value);
        return { success: true };
      } catch (error) {
        console.error('设置保存失败:', error);
        return { success: false, error: (error as Error).message };
      }
    });

    ipcMain.handle('db:settings:getAll', async () => {
      try {
        const settings = this.dbManager.getAllSettings();
        return { success: true, data: settings };
      } catch (error) {
        console.error('获取所有设置失败:', error);
        return { success: false, error: (error as Error).message };
      }
    });

    // AI Provider 相关的IPC处理器
    ipcMain.handle('db:ai-providers:create', async (_, provider) => {
      try {
        this.dbManager.createAIProvider(provider);
        return { success: true };
      } catch (error) {
        console.error('创建AI提供商失败:', error);
        return { success: false, error: (error as Error).message };
      }
    });

    ipcMain.handle('db:ai-providers:update', async (_, id, updates) => {
      try {
        this.dbManager.updateAIProvider(id, updates);
        return { success: true };
      } catch (error) {
        console.error('更新AI提供商失败:', error);
        return { success: false, error: (error as Error).message };
      }
    });

    ipcMain.handle('db:ai-providers:delete', async (_, id) => {
      try {
        this.dbManager.deleteAIProvider(id);
        return { success: true };
      } catch (error) {
        console.error('删除AI提供商失败:', error);
        return { success: false, error: (error as Error).message };
      }
    });

    ipcMain.handle('db:ai-providers:getAll', async () => {
      try {
        const providers = this.dbManager.getAIProviders();
        console.log('获取AI Providers:', providers.map(p => ({
          id: p.id,
          name: p.name,
          modelId: p.modelId,
          enabled: p.enabled,
          hasApiKey: !!p.apiKey
        })));
        return { success: true, data: providers };
      } catch (error) {
        console.error('获取AI提供商列表失败:', error);
        return { success: false, error: (error as Error).message };
      }
    });

    ipcMain.handle('db:ai-providers:getById', async (_, id) => {
      try {
        const provider = this.dbManager.getAIProviderById(id);
        return { success: true, data: provider };
      } catch (error) {
        console.error('获取AI提供商失败:', error);
        return { success: false, error: (error as Error).message };
      }
    });

    ipcMain.handle('db:ai-providers:test', async (_, id) => {
      try {
        const provider = this.dbManager.getAIProviderById(id);
        if (!provider) {
          return { success: false, message: 'AI提供商不存在' };
        }

        // 验证必要字段
        if (!provider.apiKey || !provider.baseUrl || !provider.modelId) {
          return {
            success: false,
            message: 'API Key、Base URL 或模型ID缺失'
          };
        }

        // 执行真实的API测试
        console.log(`开始测试AI提供商: ${provider.name}`);
        const testResult = await AITestService.testProvider({
          name: provider.name,
          baseUrl: provider.baseUrl,
          apiKey: provider.apiKey,
          modelId: provider.modelId
        });

        console.log(`测试结果: ${testResult.success ? '成功' : '失败'} - ${testResult.message}`);
        if (testResult.responseTime) {
          console.log(`响应时间: ${testResult.responseTime}ms`);
        }
        if (testResult.error) {
          console.log(`错误详情: ${testResult.error}`);
        }

        return testResult;
      } catch (error) {
        console.error('测试AI提供商失败:', error);
        return {
          success: false,
          message: 'API连接测试失败',
          error: (error as Error).message
        };
      }
    });

    // AI分解相关处理程序
    ipcMain.handle('ai-decomposition:start', async (_, request) => {
      try {
        console.log('开始AI分解:', request);
        const sessionId = await this.aiDecompositionService.startDecomposition(request);
        return { success: true, sessionId };
      } catch (error) {
        console.error('开始AI分解失败:', error);
        return { success: false, error: (error as Error).message };
      }
    });

    ipcMain.handle('ai-decomposition:perform', async (_, sessionId) => {
      try {
        console.log('执行AI分解:', sessionId);
        const result = await this.aiDecompositionService.performDecomposition(sessionId);
        return { success: true, result };
      } catch (error) {
        console.error('执行AI分解失败:', error);
        return { success: false, error: (error as Error).message };
      }
    });

    ipcMain.handle('ai-decomposition:get-result', async (_, sessionId) => {
      try {
        const result = await this.aiDecompositionService.getDecompositionResult(sessionId);
        return { success: true, result };
      } catch (error) {
        console.error('获取分解结果失败:', error);
        return { success: false, error: (error as Error).message };
      }
    });

    ipcMain.handle('ai-decomposition:get-templates', async () => {
      try {
        const templates = this.aiDecompositionService.getAvailableTemplates();
        return { success: true, templates };
      } catch (error) {
        console.error('获取模板列表失败:', error);
        return { success: false, error: (error as Error).message };
      }
    });

    ipcMain.handle('ai-decomposition:perform-with-template', async (_, sessionId, templateType) => {
      try {
        console.log('使用指定模板执行AI分解:', sessionId, templateType);
        const result = await this.aiDecompositionService.performDecompositionWithTemplate(sessionId, templateType);
        return { success: true, result };
      } catch (error) {
        console.error('指定模板AI分解失败:', error);
        return { success: false, error: (error as Error).message };
      }
    });

    ipcMain.handle('ai-decomposition:get-available-providers', async () => {
      try {
        const providers = await this.aiDecompositionService.getAvailableAIProviders();
        return { success: true, providers };
      } catch (error) {
        console.error('获取可用AI Provider失败:', error);
        return { success: false, error: (error as Error).message };
      }
    });

    // 分解会话管理
    ipcMain.handle('ai-decomposition:get-goal-sessions', async (_, goalId) => {
      try {
        const sessions = await this.aiDecompositionService.getGoalDecompositionSessions(goalId);
        return { success: true, sessions };
      } catch (error) {
        console.error('获取目标分解会话失败:', error);
        return { success: false, error: (error as Error).message };
      }
    });

    ipcMain.handle('ai-decomposition:get-session', async (_, sessionId) => {
      try {
        const session = await this.aiDecompositionService.getDecompositionSession(sessionId);
        return { success: true, session };
      } catch (error) {
        console.error('获取分解会话详情失败:', error);
        return { success: false, error: (error as Error).message };
      }
    });

    ipcMain.handle('ai-decomposition:cancel-session', async (_, sessionId) => {
      try {
        await this.aiDecompositionService.cancelDecompositionSession(sessionId);
        return { success: true };
      } catch (error) {
        console.error('取消分解会话失败:', error);
        return { success: false, error: (error as Error).message };
      }
    });

    ipcMain.handle('ai-decomposition:delete-session', async (_, sessionId) => {
      try {
        await this.aiDecompositionService.deleteDecompositionSession(sessionId);
        return { success: true };
      } catch (error) {
        console.error('删除分解会话失败:', error);
        return { success: false, error: (error as Error).message };
      }
    });

    // 重新分解目标
    ipcMain.handle('ai-decomposition:redecompose-goal', async (_, request) => {
      try {
        const sessionId = await this.aiDecompositionService.redecomposeGoal(request);
        return { success: true, sessionId };
      } catch (error) {
        console.error('重新分解目标失败:', error);
        return { success: false, error: (error as Error).message };
      }
    });

    // 获取分解历史
    ipcMain.handle('ai-decomposition:get-history', async (_, goalId) => {
      try {
        const history = await this.aiDecompositionService.getGoalDecompositionHistory(goalId);
        return { success: true, history };
      } catch (error) {
        console.error('获取分解历史失败:', error);
        return { success: false, error: (error as Error).message };
      }
    });

    // 回滚到指定版本
    ipcMain.handle('ai-decomposition:rollback-version', async (_, goalId, sessionId) => {
      try {
        await this.aiDecompositionService.rollbackToVersion(goalId, sessionId);
        return { success: true };
      } catch (error) {
        console.error('回滚分解版本失败:', error);
        return { success: false, error: (error as Error).message };
      }
    });

    ipcMain.handle('ai-decomposition:restart-session', async (_, sessionId) => {
      try {
        const newSessionId = await this.aiDecompositionService.restartDecomposition(sessionId);
        return { success: true, sessionId: newSessionId };
      } catch (error) {
        console.error('重新开始分解失败:', error);
        return { success: false, error: (error as Error).message };
      }
    });

    ipcMain.handle('ai-decomposition:get-stats', async () => {
      try {
        const stats = await this.aiDecompositionService.getDecompositionStats();
        return { success: true, stats };
      } catch (error) {
        console.error('获取分解统计失败:', error);
        return { success: false, error: (error as Error).message };
      }
    });

    ipcMain.handle('ai-decomposition:save-result', async (_, sessionId, confirmed) => {
      try {
        await this.aiDecompositionService.saveDecompositionResult(sessionId, confirmed);
        return { success: true };
      } catch (error) {
        console.error('保存分解结果失败:', error);
        return { success: false, error: (error as Error).message };
      }
    });

    ipcMain.handle('ai-decomposition:get-goal-structure', async (_, goalId) => {
      try {
        const structure = await this.aiDecompositionService.getGoalDecompositionStructure(goalId);
        return { success: true, structure };
      } catch (error) {
        console.error('获取目标分解结构失败:', error);
        return { success: false, error: (error as Error).message };
      }
    });
  }
}