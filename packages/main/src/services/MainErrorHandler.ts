/**
 * 主进程错误处理器
 * 处理主进程中的错误，包括数据库错误、AI服务错误等
 */

import { app, dialog } from 'electron';
import * as fs from 'fs';
import * as path from 'path';

// 错误类型
export enum MainErrorType {
  DATABASE = 'database',
  AI_SERVICE = 'ai_service',
  FILE_SYSTEM = 'file_system',
  IPC = 'ipc',
  NETWORK = 'network',
  VALIDATION = 'validation',
  UNKNOWN = 'unknown'
}

// 错误严重级别
export enum MainErrorSeverity {
  LOW = 'low',
  MEDIUM = 'medium',
  HIGH = 'high',
  CRITICAL = 'critical'
}

// 错误信息接口
export interface MainErrorInfo {
  type: MainErrorType;
  severity: MainErrorSeverity;
  code?: string;
  message: string;
  details?: any;
  timestamp: number;
  context?: {
    service?: string;
    method?: string;
    userId?: string;
    sessionId?: string;
  };
}

// 错误日志配置
interface ErrorLogConfig {
  enableFileLogging: boolean;
  logDirectory: string;
  maxLogFiles: number;
  maxLogSize: number; // bytes
}

export class MainErrorHandler {
  private static instance: MainErrorHandler;
  private config: ErrorLogConfig;
  private errorQueue: MainErrorInfo[] = [];
  private maxQueueSize = 1000;

  private constructor() {
    this.config = {
      enableFileLogging: true,
      logDirectory: path.join(app.getPath('userData'), 'logs'),
      maxLogFiles: 10,
      maxLogSize: 10 * 1024 * 1024 // 10MB
    };

    this.initializeLogging();
    this.setupGlobalErrorHandlers();
  }

  static getInstance(): MainErrorHandler {
    if (!MainErrorHandler.instance) {
      MainErrorHandler.instance = new MainErrorHandler();
    }
    return MainErrorHandler.instance;
  }

  /**
   * 初始化日志系统
   */
  private initializeLogging(): void {
    if (this.config.enableFileLogging) {
      // 确保日志目录存在
      if (!fs.existsSync(this.config.logDirectory)) {
        fs.mkdirSync(this.config.logDirectory, { recursive: true });
      }

      // 清理旧日志文件
      this.cleanupOldLogs();
    }
  }

  /**
   * 设置全局错误处理器
   */
  private setupGlobalErrorHandlers(): void {
    // 处理未捕获的异常
    process.on('uncaughtException', (error) => {
      this.handleError({
        type: MainErrorType.UNKNOWN,
        severity: MainErrorSeverity.CRITICAL,
        message: error.message,
        details: {
          stack: error.stack,
          name: error.name
        },
        timestamp: Date.now(),
        context: {
          service: 'global',
          method: 'uncaughtException'
        }
      });

      // 显示错误对话框
      dialog.showErrorBox('严重错误', `应用程序遇到严重错误：${error.message}`);
      
      // 优雅退出
      app.quit();
    });

    // 处理未处理的Promise拒绝
    process.on('unhandledRejection', (reason, promise) => {
      this.handleError({
        type: MainErrorType.UNKNOWN,
        severity: MainErrorSeverity.HIGH,
        message: `未处理的Promise拒绝: ${reason}`,
        details: {
          reason,
          promise: promise.toString()
        },
        timestamp: Date.now(),
        context: {
          service: 'global',
          method: 'unhandledRejection'
        }
      });
    });
  }

  /**
   * 处理错误
   */
  handleError(error: Error | MainErrorInfo, context?: Partial<MainErrorInfo['context']>): void {
    let errorInfo: MainErrorInfo;

    if (error instanceof Error) {
      errorInfo = this.parseError(error, context);
    } else {
      errorInfo = {
        ...error,
        context: { ...error.context, ...context }
      };
    }

    // 添加到错误队列
    this.addToQueue(errorInfo);

    // 控制台输出
    this.logToConsole(errorInfo);

    // 文件日志
    if (this.config.enableFileLogging) {
      this.logToFile(errorInfo);
    }

    // 对于严重错误，发送到渲染进程
    if (errorInfo.severity === MainErrorSeverity.CRITICAL) {
      this.notifyRenderer(errorInfo);
    }
  }

  /**
   * 解析原生错误对象
   */
  private parseError(error: Error, context?: Partial<MainErrorInfo['context']>): MainErrorInfo {
    let type = MainErrorType.UNKNOWN;
    let severity = MainErrorSeverity.MEDIUM;

    const message = error.message.toLowerCase();

    // 根据错误信息判断类型
    if (message.includes('database') || message.includes('sqlite')) {
      type = MainErrorType.DATABASE;
      severity = MainErrorSeverity.HIGH;
    } else if (message.includes('network') || message.includes('fetch') || message.includes('axios')) {
      type = MainErrorType.NETWORK;
      severity = MainErrorSeverity.MEDIUM;
    } else if (message.includes('file') || message.includes('enoent') || message.includes('permission')) {
      type = MainErrorType.FILE_SYSTEM;
      severity = MainErrorSeverity.MEDIUM;
    } else if (message.includes('ipc') || message.includes('channel')) {
      type = MainErrorType.IPC;
      severity = MainErrorSeverity.HIGH;
    } else if (message.includes('ai') || message.includes('openai') || message.includes('anthropic')) {
      type = MainErrorType.AI_SERVICE;
      severity = MainErrorSeverity.MEDIUM;
    } else if (message.includes('validation') || message.includes('invalid')) {
      type = MainErrorType.VALIDATION;
      severity = MainErrorSeverity.LOW;
    }

    return {
      type,
      severity,
      message: error.message,
      details: {
        stack: error.stack,
        name: error.name
      },
      timestamp: Date.now(),
      context
    };
  }

  /**
   * 添加到错误队列
   */
  private addToQueue(error: MainErrorInfo): void {
    this.errorQueue.push(error);
    
    if (this.errorQueue.length > this.maxQueueSize) {
      this.errorQueue.shift();
    }
  }

  /**
   * 控制台日志
   */
  private logToConsole(error: MainErrorInfo): void {
    const logLevel = this.getLogLevel(error.severity);
    const logMessage = `[${error.type.toUpperCase()}] ${error.message}`;
    
    console[logLevel](logMessage, {
      code: error.code,
      details: error.details,
      context: error.context,
      timestamp: new Date(error.timestamp).toISOString()
    });
  }

  /**
   * 文件日志
   */
  private logToFile(error: MainErrorInfo): void {
    try {
      const logEntry = {
        timestamp: new Date(error.timestamp).toISOString(),
        type: error.type,
        severity: error.severity,
        code: error.code,
        message: error.message,
        details: error.details,
        context: error.context
      };

      const logLine = JSON.stringify(logEntry) + '\n';
      const logFile = path.join(this.config.logDirectory, `error-${this.getDateString()}.log`);

      fs.appendFileSync(logFile, logLine);

      // 检查文件大小，如果超过限制则轮转
      const stats = fs.statSync(logFile);
      if (stats.size > this.config.maxLogSize) {
        this.rotateLogFile(logFile);
      }
    } catch (logError) {
      console.error('写入错误日志失败:', logError);
    }
  }

  /**
   * 通知渲染进程
   */
  private notifyRenderer(error: MainErrorInfo): void {
    // TODO: 实现向渲染进程发送错误通知
    console.log('通知渲染进程严重错误:', error);
  }

  /**
   * 获取日志级别
   */
  private getLogLevel(severity: MainErrorSeverity): 'log' | 'warn' | 'error' {
    switch (severity) {
      case MainErrorSeverity.LOW:
        return 'log';
      case MainErrorSeverity.MEDIUM:
        return 'warn';
      case MainErrorSeverity.HIGH:
      case MainErrorSeverity.CRITICAL:
        return 'error';
      default:
        return 'log';
    }
  }

  /**
   * 获取日期字符串
   */
  private getDateString(): string {
    const now = new Date();
    return now.toISOString().split('T')[0];
  }

  /**
   * 轮转日志文件
   */
  private rotateLogFile(logFile: string): void {
    try {
      const timestamp = Date.now();
      const rotatedFile = logFile.replace('.log', `-${timestamp}.log`);
      fs.renameSync(logFile, rotatedFile);
    } catch (error) {
      console.error('日志文件轮转失败:', error);
    }
  }

  /**
   * 清理旧日志文件
   */
  private cleanupOldLogs(): void {
    try {
      const files = fs.readdirSync(this.config.logDirectory);
      const logFiles = files
        .filter(file => file.startsWith('error-') && file.endsWith('.log'))
        .map(file => ({
          name: file,
          path: path.join(this.config.logDirectory, file),
          mtime: fs.statSync(path.join(this.config.logDirectory, file)).mtime
        }))
        .sort((a, b) => b.mtime.getTime() - a.mtime.getTime());

      // 删除超过限制的旧文件
      if (logFiles.length > this.config.maxLogFiles) {
        const filesToDelete = logFiles.slice(this.config.maxLogFiles);
        filesToDelete.forEach(file => {
          fs.unlinkSync(file.path);
        });
      }
    } catch (error) {
      console.error('清理旧日志文件失败:', error);
    }
  }

  /**
   * 创建错误包装器
   */
  createErrorWrapper<T extends (...args: any[]) => any>(
    func: T,
    context?: Partial<MainErrorInfo['context']>
  ): T {
    return ((...args: Parameters<T>) => {
      try {
        const result = func(...args);
        
        if (result instanceof Promise) {
          return result.catch(error => {
            this.handleError(error, context);
            throw error;
          });
        }
        
        return result;
      } catch (error) {
        this.handleError(error as Error, context);
        throw error;
      }
    }) as T;
  }

  /**
   * 获取错误统计
   */
  getErrorStats(): {
    total: number;
    byType: Record<MainErrorType, number>;
    bySeverity: Record<MainErrorSeverity, number>;
    recent: MainErrorInfo[];
  } {
    const stats = {
      total: this.errorQueue.length,
      byType: {} as Record<MainErrorType, number>,
      bySeverity: {} as Record<MainErrorSeverity, number>,
      recent: this.errorQueue.slice(-10)
    };

    // 初始化计数器
    Object.values(MainErrorType).forEach(type => {
      stats.byType[type] = 0;
    });
    Object.values(MainErrorSeverity).forEach(severity => {
      stats.bySeverity[severity] = 0;
    });

    // 统计错误
    this.errorQueue.forEach(error => {
      stats.byType[error.type]++;
      stats.bySeverity[error.severity]++;
    });

    return stats;
  }

  /**
   * 清理错误队列
   */
  clearErrorQueue(): void {
    this.errorQueue = [];
  }
}

// 导出单例实例
export const mainErrorHandler = MainErrorHandler.getInstance();
