import { v4 as uuidv4 } from 'uuid';

/**
 * AI API调用日志级别
 */
export enum AILogLevel {
  ERROR = 0,   // 只记录错误
  WARN = 1,    // 记录警告和错误
  INFO = 2,    // 记录基本信息、警告和错误
  DEBUG = 3,   // 记录所有调试信息
  VERBOSE = 4  // 记录最详细的信息
}

/**
 * AI API调用请求信息
 */
export interface AIRequestInfo {
  requestId: string;
  provider: string;
  modelId: string;
  endpoint: string;
  prompt: string;
  maxTokens?: number;
  temperature?: number;
  context?: string;
}

/**
 * AI API调用响应信息
 */
export interface AIResponseInfo {
  requestId: string;
  success: boolean;
  statusCode?: number;
  responseTime: number;
  responseLength: number;
  content?: string;
  reasoning?: string;
  error?: string;
  usage?: {
    promptTokens?: number;
    completionTokens?: number;
    totalTokens?: number;
  };
}

/**
 * AI调试日志工具类
 * 提供统一的AI API调用日志记录功能
 */
export class AILogger {
  private static logLevel: AILogLevel = AILogLevel.INFO;
  private static isDevelopment = process.env.NODE_ENV === 'development';
  
  // 表情符号常量
  private static readonly ICONS = {
    REQUEST: '🤖📤',
    RESPONSE: '🤖📥',
    SUCCESS: '✅',
    ERROR: '❌',
    WARNING: '⚠️',
    DEBUG: '🔍',
    TIMING: '⏱️',
    PROVIDER: '🏢',
    MODEL: '🧠',
    PROMPT: '💭',
    CONTENT: '📝',
    REASONING: '🧮',
    USAGE: '📊'
  };

  /**
   * 设置日志级别
   */
  static setLogLevel(level: AILogLevel): void {
    this.logLevel = level;
  }

  /**
   * 获取当前日志级别
   */
  static getLogLevel(): AILogLevel {
    // 从环境变量读取日志级别
    const envLevel = process.env.AI_LOG_LEVEL;
    if (envLevel) {
      const level = parseInt(envLevel);
      if (level >= 0 && level <= 4) {
        return level as AILogLevel;
      }
    }
    
    // 开发环境默认DEBUG，生产环境默认INFO
    return this.isDevelopment ? AILogLevel.DEBUG : AILogLevel.INFO;
  }

  /**
   * 判断是否应该记录指定级别的日志
   */
  private static shouldLog(level: AILogLevel): boolean {
    return level <= this.getLogLevel();
  }

  /**
   * 生成唯一的请求ID
   */
  static generateRequestId(): string {
    return uuidv4().split('-')[0];
  }

  /**
   * 脱敏API Key
   */
  private static maskApiKey(apiKey: string): string {
    if (!apiKey || apiKey.length < 8) {
      return '[HIDDEN]';
    }
    return `${apiKey.substring(0, 8)}...${apiKey.substring(apiKey.length - 4)}`;
  }

  /**
   * 截断长内容
   */
  private static truncateContent(content: string, maxLength: number = 500): string {
    if (!content) return '';
    if (content.length <= maxLength) return content;
    return content.substring(0, maxLength) + '...[截断]';
  }

  /**
   * 格式化时间戳
   */
  private static formatTimestamp(): string {
    return new Date().toISOString().replace('T', ' ').substring(0, 23);
  }

  /**
   * 记录AI API请求
   */
  static logRequest(requestInfo: AIRequestInfo): void {
    if (!this.shouldLog(AILogLevel.DEBUG)) return;

    const timestamp = this.formatTimestamp();
    const { requestId, provider, modelId, endpoint, prompt, maxTokens, temperature, context } = requestInfo;

    console.log(`\n${this.ICONS.REQUEST} [${timestamp}] AI API 请求开始`);
    console.log(`${this.ICONS.DEBUG} 请求ID: ${requestId}`);
    console.log(`${this.ICONS.PROVIDER} 提供商: ${provider}`);
    console.log(`${this.ICONS.MODEL} 模型: ${modelId}`);
    console.log(`${this.ICONS.DEBUG} 端点: ${endpoint}`);
    
    if (maxTokens) {
      console.log(`${this.ICONS.DEBUG} 最大tokens: ${maxTokens}`);
    }
    
    if (temperature !== undefined) {
      console.log(`${this.ICONS.DEBUG} 温度: ${temperature}`);
    }
    
    if (context) {
      console.log(`${this.ICONS.DEBUG} 上下文: ${this.truncateContent(context, 200)}`);
    }

    if (this.shouldLog(AILogLevel.VERBOSE)) {
      console.log(`${this.ICONS.PROMPT} 完整提示词:`);
      console.log(`────────────────────────────────────────`);
      console.log(prompt);
      console.log(`────────────────────────────────────────`);
    } else {
      console.log(`${this.ICONS.PROMPT} 提示词预览: ${this.truncateContent(prompt, 300)}`);
    }
  }

  /**
   * 记录AI API响应
   */
  static logResponse(responseInfo: AIResponseInfo): void {
    if (!this.shouldLog(AILogLevel.DEBUG)) return;

    const timestamp = this.formatTimestamp();
    const { 
      requestId, 
      success, 
      statusCode, 
      responseTime, 
      responseLength, 
      content, 
      reasoning,
      error, 
      usage 
    } = responseInfo;

    const statusIcon = success ? this.ICONS.SUCCESS : this.ICONS.ERROR;
    const statusText = success ? '成功' : '失败';

    console.log(`\n${this.ICONS.RESPONSE} [${timestamp}] AI API 响应 ${statusIcon}`);
    console.log(`${this.ICONS.DEBUG} 请求ID: ${requestId}`);
    console.log(`${this.ICONS.DEBUG} 状态: ${statusText}`);
    
    if (statusCode) {
      console.log(`${this.ICONS.DEBUG} HTTP状态码: ${statusCode}`);
    }
    
    console.log(`${this.ICONS.TIMING} 响应时间: ${responseTime}ms`);
    console.log(`${this.ICONS.DEBUG} 响应长度: ${responseLength} 字符`);

    if (usage) {
      console.log(`${this.ICONS.USAGE} Token使用情况:`);
      if (usage.promptTokens) console.log(`  - 提示词tokens: ${usage.promptTokens}`);
      if (usage.completionTokens) console.log(`  - 完成tokens: ${usage.completionTokens}`);
      if (usage.totalTokens) console.log(`  - 总tokens: ${usage.totalTokens}`);
    }

    if (success) {
      if (content) {
        if (this.shouldLog(AILogLevel.VERBOSE)) {
          console.log(`${this.ICONS.CONTENT} 完整响应内容:`);
          console.log(`────────────────────────────────────────`);
          console.log(content);
          console.log(`────────────────────────────────────────`);
        } else {
          console.log(`${this.ICONS.CONTENT} 响应内容预览: ${this.truncateContent(content, 300)}`);
        }
      }

      if (reasoning) {
        if (this.shouldLog(AILogLevel.VERBOSE)) {
          console.log(`${this.ICONS.REASONING} 完整推理过程:`);
          console.log(`────────────────────────────────────────`);
          console.log(reasoning);
          console.log(`────────────────────────────────────────`);
        } else {
          console.log(`${this.ICONS.REASONING} 推理过程预览: ${this.truncateContent(reasoning, 300)}`);
        }
      }
    } else if (error) {
      console.log(`${this.ICONS.ERROR} 错误信息: ${error}`);
    }
  }

  /**
   * 记录API调用总结
   */
  static logApiCallSummary(
    requestId: string, 
    provider: string, 
    modelId: string, 
    success: boolean, 
    responseTime: number,
    error?: string
  ): void {
    if (!this.shouldLog(AILogLevel.INFO)) return;

    const timestamp = this.formatTimestamp();
    const statusIcon = success ? this.ICONS.SUCCESS : this.ICONS.ERROR;
    const statusText = success ? '成功' : '失败';

    console.log(`${statusIcon} [${timestamp}] ${provider}/${modelId} 调用${statusText} (${requestId}) - ${responseTime}ms`);
    
    if (!success && error && this.shouldLog(AILogLevel.WARN)) {
      console.log(`${this.ICONS.ERROR} 错误: ${error}`);
    }
  }

  /**
   * 记录警告信息
   */
  static logWarning(message: string, context?: any): void {
    if (!this.shouldLog(AILogLevel.WARN)) return;

    const timestamp = this.formatTimestamp();
    console.log(`${this.ICONS.WARNING} [${timestamp}] AI警告: ${message}`);
    
    if (context && this.shouldLog(AILogLevel.DEBUG)) {
      console.log(`${this.ICONS.DEBUG} 上下文:`, context);
    }
  }

  /**
   * 记录错误信息
   */
  static logError(message: string, error?: any, context?: any): void {
    if (!this.shouldLog(AILogLevel.ERROR)) return;

    const timestamp = this.formatTimestamp();
    console.error(`${this.ICONS.ERROR} [${timestamp}] AI错误: ${message}`);
    
    if (error) {
      console.error(`${this.ICONS.ERROR} 详细错误:`, error);
    }
    
    if (context && this.shouldLog(AILogLevel.DEBUG)) {
      console.error(`${this.ICONS.DEBUG} 上下文:`, context);
    }
  }

  /**
   * 记录调试信息
   */
  static logDebug(message: string, data?: any): void {
    if (!this.shouldLog(AILogLevel.DEBUG)) return;

    const timestamp = this.formatTimestamp();
    console.log(`${this.ICONS.DEBUG} [${timestamp}] AI调试: ${message}`);
    
    if (data) {
      console.log(`${this.ICONS.DEBUG} 数据:`, data);
    }
  }

  /**
   * 记录配置信息（脱敏）
   */
  static logProviderConfig(provider: {
    name: string;
    baseUrl: string;
    apiKey: string;
    modelId: string;
  }): void {
    if (!this.shouldLog(AILogLevel.DEBUG)) return;

    const timestamp = this.formatTimestamp();
    console.log(`${this.ICONS.DEBUG} [${timestamp}] AI提供商配置:`);
    console.log(`${this.ICONS.PROVIDER} 名称: ${provider.name}`);
    console.log(`${this.ICONS.DEBUG} Base URL: ${provider.baseUrl}`);
    console.log(`${this.ICONS.DEBUG} API Key: ${this.maskApiKey(provider.apiKey)}`);
    console.log(`${this.ICONS.MODEL} 模型ID: ${provider.modelId}`);
  }
}