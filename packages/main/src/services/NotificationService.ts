import { Notification } from 'electron';
import { ipcMain } from 'electron';

export class NotificationService {
  private static instance: NotificationService;

  private constructor() {
    this.setupIpcHandlers();
  }

  public static getInstance(): NotificationService {
    if (!NotificationService.instance) {
      NotificationService.instance = new NotificationService();
    }
    return NotificationService.instance;
  }

  private setupIpcHandlers() {
    // 显示系统通知
    ipcMain.handle('notification:show', async (_, title: string, body: string, options: any = {}) => {
      try {
        if (Notification.isSupported()) {
          const notification = new Notification({
            title,
            body,
            icon: options.icon,
            silent: !options.sound,
            urgency: options.urgency || 'normal',
            timeoutType: options.timeoutType || 'default',
          });

          notification.show();

          // 处理通知点击事件
          notification.on('click', () => {
            // 可以在这里实现点击通知后的操作，比如聚焦应用窗口
          });

          return { success: true };
        } else {
          return { success: false, error: '系统不支持通知' };
        }
      } catch (error) {
        console.error('显示通知失败:', error);
        return { success: false, error: (error as Error).message };
      }
    });

    // 请求通知权限（在某些平台上可能需要）
    ipcMain.handle('notification:requestPermission', async () => {
      try {
        // Electron 的通知通常不需要显式请求权限
        // 但我们可以检查是否支持
        return { success: true, supported: Notification.isSupported() };
      } catch (error) {
        console.error('检查通知权限失败:', error);
        return { success: false, error: (error as Error).message };
      }
    });
  }

  // 直接显示系统通知（内部使用）
  public showNotification(title: string, body: string, options: any = {}) {
    if (Notification.isSupported()) {
      const notification = new Notification({
        title,
        body,
        icon: options.icon,
        silent: options.sound === false, // 默认播放声音
        urgency: options.urgency || 'normal',
        timeoutType: options.timeoutType || 'default',
      });

      notification.show();
      return notification;
    }
    return null;
  }

  // 番茄钟完成通知
  public showPomodoroCompleteNotification(type: 'work' | 'break', sessionCount: number) {
    const isWorkSession = type === 'work';
    
    const title = isWorkSession 
      ? '🍅 番茄钟完成！' 
      : '⏰ 休息时间到！';
      
    const body = isWorkSession
      ? `恭喜完成第 ${sessionCount} 个番茄钟！是时候休息一下了。`
      : '休息结束，准备开始下一个专注时段吧！';

    return this.showNotification(title, body, {
      silent: false,
      urgency: 'normal'
    });
  }

  // 番茄钟开始通知
  public showPomodoroStartNotification(taskTitle: string) {
    return this.showNotification('🚀 开始专注！', `专注任务：${taskTitle}`, {
      silent: false,
      urgency: 'low'
    });
  }

  // 应用程序级别的重要通知
  public showImportantNotification(title: string, body: string) {
    return this.showNotification(title, body, {
      silent: false,
      urgency: 'critical'
    });
  }
}