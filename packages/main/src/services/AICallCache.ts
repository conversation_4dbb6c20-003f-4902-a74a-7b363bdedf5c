/**
 * AI调用缓存服务
 * 提供AI调用结果的缓存和重试机制
 */

interface CacheEntry {
  result: string;
  timestamp: number;
  expiresAt: number;
}

interface RetryConfig {
  maxRetries: number;
  baseDelay: number;
  maxDelay: number;
  backoffFactor: number;
}

export class AICallCache {
  private cache: Map<string, CacheEntry> = new Map();
  private readonly defaultTTL = 24 * 60 * 60 * 1000; // 24小时
  private readonly maxCacheSize = 1000;

  private readonly defaultRetryConfig: RetryConfig = {
    maxRetries: 3,
    baseDelay: 1000,
    maxDelay: 10000,
    backoffFactor: 2
  };

  /**
   * 生成缓存键
   */
  private generateCacheKey(prompt: string, provider: string, modelId: string): string {
    const content = `${provider}:${modelId}:${prompt}`;
    // 简单的哈希函数
    let hash = 0;
    for (let i = 0; i < content.length; i++) {
      const char = content.charCodeAt(i);
      hash = ((hash << 5) - hash) + char;
      hash = hash & hash; // 转换为32位整数
    }
    return Math.abs(hash).toString(36);
  }

  /**
   * 获取缓存结果
   */
  get(prompt: string, provider: string, modelId: string): string | null {
    const key = this.generateCacheKey(prompt, provider, modelId);
    const entry = this.cache.get(key);

    if (!entry) {
      return null;
    }

    // 检查是否过期
    if (Date.now() > entry.expiresAt) {
      this.cache.delete(key);
      return null;
    }

    return entry.result;
  }

  /**
   * 设置缓存结果
   */
  set(prompt: string, provider: string, modelId: string, result: string, ttl?: number): void {
    const key = this.generateCacheKey(prompt, provider, modelId);
    const now = Date.now();
    const expiresAt = now + (ttl || this.defaultTTL);

    // 如果缓存已满，删除最旧的条目
    if (this.cache.size >= this.maxCacheSize) {
      this.evictOldest();
    }

    this.cache.set(key, {
      result,
      timestamp: now,
      expiresAt
    });
  }

  /**
   * 删除最旧的缓存条目
   */
  private evictOldest(): void {
    let oldestKey: string | null = null;
    let oldestTimestamp = Infinity;

    for (const [key, entry] of this.cache.entries()) {
      if (entry.timestamp < oldestTimestamp) {
        oldestTimestamp = entry.timestamp;
        oldestKey = key;
      }
    }

    if (oldestKey) {
      this.cache.delete(oldestKey);
    }
  }

  /**
   * 清理过期缓存
   */
  cleanup(): void {
    const now = Date.now();
    for (const [key, entry] of this.cache.entries()) {
      if (now > entry.expiresAt) {
        this.cache.delete(key);
      }
    }
  }

  /**
   * 获取缓存统计信息
   */
  getStats(): {
    size: number;
    maxSize: number;
    hitRate?: number;
  } {
    return {
      size: this.cache.size,
      maxSize: this.maxCacheSize
    };
  }

  /**
   * 带重试的AI调用
   */
  async callWithRetry<T>(
    operation: () => Promise<T>,
    retryConfig?: Partial<RetryConfig>
  ): Promise<T> {
    const config = { ...this.defaultRetryConfig, ...retryConfig };
    let lastError: Error;

    for (let attempt = 0; attempt <= config.maxRetries; attempt++) {
      try {
        return await operation();
      } catch (error) {
        lastError = error as Error;
        
        // 如果是最后一次尝试，抛出错误
        if (attempt === config.maxRetries) {
          break;
        }

        // 计算延迟时间
        const delay = Math.min(
          config.baseDelay * Math.pow(config.backoffFactor, attempt),
          config.maxDelay
        );

        console.warn(`AI调用失败，${delay}ms后重试 (${attempt + 1}/${config.maxRetries}):`, error);
        
        // 等待后重试
        await new Promise(resolve => setTimeout(resolve, delay));
      }
    }

    throw lastError!;
  }

  /**
   * 带缓存和重试的AI调用
   */
  async callWithCacheAndRetry(
    prompt: string,
    provider: string,
    modelId: string,
    operation: () => Promise<string>,
    options?: {
      ttl?: number;
      retryConfig?: Partial<RetryConfig>;
      useCache?: boolean;
    }
  ): Promise<string> {
    const { ttl, retryConfig, useCache = true } = options || {};

    // 尝试从缓存获取
    if (useCache) {
      const cached = this.get(prompt, provider, modelId);
      if (cached) {
        console.log('AI调用命中缓存');
        return cached;
      }
    }

    // 执行AI调用（带重试）
    const result = await this.callWithRetry(operation, retryConfig);

    // 缓存结果
    if (useCache && result) {
      this.set(prompt, provider, modelId, result, ttl);
    }

    return result;
  }

  /**
   * 预热缓存
   */
  async warmup(commonPrompts: Array<{
    prompt: string;
    provider: string;
    modelId: string;
    operation: () => Promise<string>;
  }>): Promise<void> {
    console.log(`开始预热AI调用缓存，共${commonPrompts.length}个请求`);
    
    const promises = commonPrompts.map(async ({ prompt, provider, modelId, operation }) => {
      try {
        await this.callWithCacheAndRetry(prompt, provider, modelId, operation);
      } catch (error) {
        console.warn('缓存预热失败:', error);
      }
    });

    await Promise.allSettled(promises);
    console.log('AI调用缓存预热完成');
  }

  /**
   * 清空缓存
   */
  clear(): void {
    this.cache.clear();
  }
}

// 单例实例
export const aiCallCache = new AICallCache();

// 定期清理过期缓存
setInterval(() => {
  aiCallCache.cleanup();
}, 60 * 60 * 1000); // 每小时清理一次
