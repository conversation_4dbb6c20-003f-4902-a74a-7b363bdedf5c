-- 目标表
CREATE TABLE IF NOT EXISTS goals (
    id TEXT PRIMARY KEY,
    user_id TEXT NOT NULL DEFAULT 'default-user',
    parent_id TEXT,
    name TEXT NOT NULL,
    description TEXT,
    type TEXT NOT NULL CHECK (type IN ('long-term', 'short-term', 'habit')),
    why_power TEXT NOT NULL,
    domains TEXT, -- JSON字符串存储数组
    start_date DATETIME,
    deadline DATETIME,
    status TEXT DEFAULT 'active' CHECK (status IN ('active', 'completed', 'paused', 'cancelled')),
    level INTEGER DEFAULT 0,
    created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
    updated_at DATETIME DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (parent_id) REFERENCES goals(id) ON DELETE CASCADE
);

-- 任务表
CREATE TABLE IF NOT EXISTS tasks (
    id TEXT PRIMARY KEY,
    goal_id TEXT NOT NULL,
    title TEXT NOT NULL,
    description TEXT,
    estimated_time INTEGER, -- 分钟
    actual_time INTEGER DEFAULT 0, -- 分钟
    priority TEXT DEFAULT 'medium' CHECK (priority IN ('high', 'medium', 'low')),
    deadline DATETIME,
    tags TEXT, -- JSON字符串存储数组
    status TEXT DEFAULT 'todo' CHECK (status IN ('todo', 'in-progress', 'completed', 'paused', 'cancelled')),
    completion_percentage INTEGER DEFAULT 0 CHECK (completion_percentage >= 0 AND completion_percentage <= 100),
    created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
    updated_at DATETIME DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (goal_id) REFERENCES goals(id) ON DELETE CASCADE
);

-- 番茄钟会话表
CREATE TABLE IF NOT EXISTS pomodoro_sessions (
    id TEXT PRIMARY KEY,
    task_id TEXT NOT NULL,
    type TEXT NOT NULL CHECK (type IN ('work', 'short-break', 'long-break')),
    start_time DATETIME NOT NULL,
    end_time DATETIME,
    duration INTEGER NOT NULL, -- 分钟
    is_completed BOOLEAN DEFAULT FALSE,
    was_interrupted BOOLEAN DEFAULT FALSE,
    created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (task_id) REFERENCES tasks(id) ON DELETE CASCADE
);

-- 设置表
CREATE TABLE IF NOT EXISTS settings (
    key TEXT PRIMARY KEY,
    value TEXT NOT NULL,
    created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
    updated_at DATETIME DEFAULT CURRENT_TIMESTAMP
);

-- AI提供商表
CREATE TABLE IF NOT EXISTS ai_providers (
    id TEXT PRIMARY KEY,
    name TEXT NOT NULL,
    base_url TEXT NOT NULL,
    api_key TEXT NOT NULL,
    model_id TEXT NOT NULL,
    enabled BOOLEAN DEFAULT TRUE,
    created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
    updated_at DATETIME DEFAULT CURRENT_TIMESTAMP
);

-- 创建索引以提高查询性能
CREATE INDEX IF NOT EXISTS idx_tasks_status ON tasks(status);
CREATE INDEX IF NOT EXISTS idx_tasks_deadline ON tasks(deadline);
CREATE INDEX IF NOT EXISTS idx_tasks_goal_id ON tasks(goal_id);
CREATE INDEX IF NOT EXISTS idx_pomodoro_sessions_task_id ON pomodoro_sessions(task_id);
CREATE INDEX IF NOT EXISTS idx_goals_status ON goals(status);
CREATE INDEX IF NOT EXISTS idx_goals_parent_id ON goals(parent_id);
CREATE INDEX IF NOT EXISTS idx_ai_providers_enabled ON ai_providers(enabled);

-- 插入默认设置
INSERT OR IGNORE INTO settings (key, value) VALUES 
('pomodoro_work_duration', '25'),
('pomodoro_short_break_duration', '5'),
('pomodoro_long_break_duration', '15'),
('pomodoro_sessions_until_long_break', '4'),
('pomodoro_auto_start_breaks', 'false'),
('pomodoro_auto_start_next_session', 'false'),
('theme', 'light'),
('language', 'zh-CN'),
('notifications_enabled', 'true'),
('auto_start', 'false');